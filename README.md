# PlayBeg - Song Request Platform

## Project Overview

PlayBeg is the ultimate song request platform for DJs, allowing audiences to scan QR codes and submit song requests while DJs maintain complete control over their playlists.

## Development Setup

**Local Development**

To work locally using your preferred IDE, clone this repo and push changes.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository
git clone https://github.com/djrobbieh/PlayBeg.git

# Step 2: Navigate to the project directory
cd PlayBeg

# Step 3: Install dependencies
npm install

# Step 4: Start the development server
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## Debugging and Production Builds

### Console Logging

This project uses `vite-plugin-remove-console` to automatically remove console logs in production builds:

- During development: All `console.log`, `console.info`, `console.debug`, and `console.warn` statements are visible
- In production: These statements are automatically stripped from the code

This approach allows developers to use console logging freely during development without worrying about cleaning them up for production.

### Supported Debug Statements

The following statements are automatically removed in production:
- `console.log()`
- `console.info()`
- `console.warn()`

Note that `console.error()` statements are **not** removed, as these typically indicate actual errors that should be visible in production.

## Deployment

This project can be deployed to various platforms:

- **Netlify**: Recommended for static hosting
- **Vercel**: Great for React applications
- **GitHub Pages**: Free hosting for public repositories

## Features

- 🎵 Song request system with QR code scanning
- 🎧 DJ dashboard for managing requests
- 📱 Mobile-responsive design
- 🔐 User authentication and profiles
- 💳 Subscription management
- 🎨 Modern UI with Tailwind CSS
