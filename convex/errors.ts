/**
 * Error Handling Utilities
 * 
 * This module provides standardized error handling for the PlayBeg application
 * with proper error types, messages, and logging.
 */

// Standard error types for the application
export enum ErrorType {
  AUTHENTICATION_REQUIRED = "AUTHENTICATION_REQUIRED",
  ACCESS_DENIED = "ACCESS_DENIED",
  NOT_FOUND = "NOT_FOUND",
  VALIDATION_ERROR = "VALIDATION_ERROR",
  DUPLICATE_RESOURCE = "DUPLICATE_RESOURCE",
  BUSINESS_RULE_VIOLATION = "BUSINESS_RULE_VIOLATION",
  EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR",
  RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED",
  INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS",
  RESOURCE_CONFLICT = "RESOURCE_CONFLICT",
}

// Standard error class with additional context
export class PlayBegError extends Error {
  public readonly type: ErrorType;
  public readonly code: string;
  public readonly context?: Record<string, any>;
  public readonly timestamp: number;

  constructor(
    type: ErrorType,
    message: string,
    code?: string,
    context?: Record<string, any>
  ) {
    super(message);
    this.name = "PlayBegError";
    this.type = type;
    this.code = code || type;
    this.context = context;
    this.timestamp = Date.now();
  }

  toJSON() {
    return {
      name: this.name,
      type: this.type,
      code: this.code,
      message: this.message,
      context: this.context,
      timestamp: this.timestamp,
    };
  }
}

// Authentication errors
export class AuthenticationError extends PlayBegError {
  constructor(message = "Authentication required", context?: Record<string, any>) {
    super(ErrorType.AUTHENTICATION_REQUIRED, message, "AUTH_001", context);
  }
}

export class AccessDeniedError extends PlayBegError {
  constructor(message = "Access denied", context?: Record<string, any>) {
    super(ErrorType.ACCESS_DENIED, message, "AUTH_002", context);
  }
}

// Resource errors
export class NotFoundError extends PlayBegError {
  constructor(resource: string, id?: string, context?: Record<string, any>) {
    const message = id 
      ? `${resource} with ID ${id} not found`
      : `${resource} not found`;
    super(ErrorType.NOT_FOUND, message, "RESOURCE_001", { resource, id, ...context });
  }
}

export class DuplicateResourceError extends PlayBegError {
  constructor(resource: string, field?: string, context?: Record<string, any>) {
    const message = field
      ? `${resource} with this ${field} already exists`
      : `${resource} already exists`;
    super(ErrorType.DUPLICATE_RESOURCE, message, "RESOURCE_002", { resource, field, ...context });
  }
}

// Validation errors
export class ValidationError extends PlayBegError {
  constructor(errors: string[], context?: Record<string, any>) {
    const message = `Validation failed: ${errors.join(", ")}`;
    super(ErrorType.VALIDATION_ERROR, message, "VALIDATION_001", { errors, ...context });
  }
}

// Business rule errors
export class BusinessRuleError extends PlayBegError {
  constructor(rule: string, message: string, context?: Record<string, any>) {
    super(ErrorType.BUSINESS_RULE_VIOLATION, message, "BUSINESS_001", { rule, ...context });
  }
}

// Rate limiting errors
export class RateLimitError extends PlayBegError {
  constructor(limit: number, timeframe: string, context?: Record<string, any>) {
    const message = `Rate limit exceeded: ${limit} requests per ${timeframe}`;
    super(ErrorType.RATE_LIMIT_EXCEEDED, message, "RATE_001", { limit, timeframe, ...context });
  }
}

// Helper functions for common error scenarios
export const ErrorHelpers = {
  // Authentication helpers
  requireAuth: (userId: string | null): string => {
    if (!userId) {
      throw new AuthenticationError("You must be logged in to perform this action");
    }
    return userId;
  },

  // Ownership verification
  verifyOwnership: (resourceUserId: string, currentUserId: string, resourceType: string) => {
    if (resourceUserId !== currentUserId) {
      throw new AccessDeniedError(
        `You can only access your own ${resourceType}`,
        { resourceUserId, currentUserId, resourceType }
      );
    }
  },

  // Resource existence check
  requireResource: <T>(resource: T | null | undefined, type: string, id?: string): T => {
    if (!resource) {
      throw new NotFoundError(type, id);
    }
    return resource;
  },

  // Validation helpers
  validateRequired: (value: any, fieldName: string) => {
    if (value === undefined || value === null || value === "") {
      throw new ValidationError([`${fieldName} is required`]);
    }
  },

  validateLength: (value: string, fieldName: string, min?: number, max?: number) => {
    const errors: string[] = [];
    
    if (min !== undefined && value.length < min) {
      errors.push(`${fieldName} must be at least ${min} characters`);
    }
    
    if (max !== undefined && value.length > max) {
      errors.push(`${fieldName} cannot exceed ${max} characters`);
    }
    
    if (errors.length > 0) {
      throw new ValidationError(errors);
    }
  },

  validateEmail: (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new ValidationError(["Invalid email format"]);
    }
  },

  // Business rule helpers
  requireOnboarding: (profile: { completedOnboarding: boolean }) => {
    if (!profile.completedOnboarding) {
      throw new BusinessRuleError(
        "onboarding_required",
        "You must complete onboarding before performing this action"
      );
    }
  },

  requireActiveSession: (session: { active: boolean }) => {
    if (!session.active) {
      throw new BusinessRuleError(
        "active_session_required",
        "This action requires an active session"
      );
    }
  },

  // Rate limiting helpers
  checkRateLimit: (
    requestCount: number,
    limit: number,
    timeframe: string,
    identifier?: string
  ) => {
    if (requestCount >= limit) {
      throw new RateLimitError(limit, timeframe, { requestCount, identifier });
    }
  },
};

// Error logging utility
export const logError = (error: Error, context?: Record<string, any>) => {
  const logData = {
    timestamp: Date.now(),
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    context,
  };

  if (error instanceof PlayBegError) {
    logData.error = { ...logData.error, ...error.toJSON() };
  }

  console.error("PlayBeg Error:", JSON.stringify(logData, null, 2));
};

// Error response formatter for consistent API responses
export const formatErrorResponse = (error: Error) => {
  if (error instanceof PlayBegError) {
    return {
      success: false,
      error: {
        type: error.type,
        code: error.code,
        message: error.message,
        context: error.context,
        timestamp: error.timestamp,
      },
    };
  }

  // Handle unknown errors
  return {
    success: false,
    error: {
      type: "UNKNOWN_ERROR",
      code: "UNKNOWN_001",
      message: error.message || "An unexpected error occurred",
      timestamp: Date.now(),
    },
  };
};

// Async error wrapper for mutations and actions
export const withErrorHandling = <T extends any[], R>(
  fn: (...args: T) => Promise<R>
) => {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      logError(error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  };
};

// Success response formatter
export const formatSuccessResponse = <T>(data: T, message?: string) => {
  return {
    success: true,
    data,
    message,
    timestamp: Date.now(),
  };
};
