/**
 * CRUD Operations Validation Suite
 * 
 * This module provides comprehensive validation and testing for all
 * CRUD operations to ensure they work correctly with authentication,
 * validation, and error handling.
 */

import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";
// Basic error handling for now

// Validation Query: Check all CRUD operations are working
export const validateCrudOperations = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    
    const validation = {
      timestamp: Date.now(),
      authentication: {
        isAuthenticated: !!userId,
        userId: userId,
      },
      database: {
        canRead: false,
        canQuery: false,
        hasIndexes: false,
      },
      schema: {
        tablesExist: false,
        relationshipsValid: false,
      },
      functions: {
        queriesWork: false,
        mutationsWork: false,
      },
      errors: [] as string[],
    };

    try {
      // Test basic database read
      const djProfiles = await ctx.db.query("djProfiles").take(1);
      validation.database.canRead = true;

      // Test index usage
      const profilesByUser = await ctx.db
        .query("djProfiles")
        .withIndex("by_onboarding", (q) => q.eq("completedOnboarding", true))
        .take(1);
      validation.database.hasIndexes = true;

      // Test complex query
      const users = await ctx.db.query("users").take(1);
      validation.database.canQuery = true;

      // Check if tables exist and have expected structure
      validation.schema.tablesExist = true;

      // Test relationships if we have data
      if (djProfiles.length > 0) {
        const profile = djProfiles[0];
        const user = await ctx.db.get(profile.userId);
        validation.schema.relationshipsValid = !!user;
      } else {
        validation.schema.relationshipsValid = true; // No data to test
      }

      validation.functions.queriesWork = true;

    } catch (error) {
      validation.errors.push(`Database validation failed: ${error}`);
    }

    return validation;
  },
});

// Test Mutation: Validate create operations
export const testCreateOperations = mutation({
  args: {
    testData: v.object({
      displayName: v.optional(v.string()),
      skipIfExists: v.optional(v.boolean()),
    }),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }
    
    const results = {
      timestamp: Date.now(),
      userId,
      tests: [] as any[],
      summary: {
        passed: 0,
        failed: 0,
        skipped: 0,
      },
    };

    // Test 1: Check if profile already exists
    try {
      const existingProfile = await ctx.db
        .query("djProfiles")
        .withIndex("by_user", (q) => q.eq("userId", userId))
        .first();

      if (existingProfile && args.testData.skipIfExists) {
        results.tests.push({
          name: "profile_exists_check",
          status: "skipped",
          message: "Profile already exists, skipping creation test",
          profileId: existingProfile._id,
        });
        results.summary.skipped++;
        return { success: true, data: results, message: "Test completed with existing profile" };
      }

      if (existingProfile) {
        results.tests.push({
          name: "profile_exists_check",
          status: "failed",
          message: "Profile already exists but skipIfExists not set",
        });
        results.summary.failed++;
        return { success: false, data: results, message: "Test failed - profile exists" };
      }

      results.tests.push({
        name: "profile_exists_check",
        status: "passed",
        message: "No existing profile found, can proceed with creation",
      });
      results.summary.passed++;

    } catch (error) {
      results.tests.push({
        name: "profile_exists_check",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Test 2: Create DJ profile
    try {
      const now = Date.now();
      const profileId = await ctx.db.insert("djProfiles", {
        userId,
        displayName: args.testData.displayName?.trim(),
        completedOnboarding: false,
        createdAt: now,
        updatedAt: now,
      });

      results.tests.push({
        name: "create_dj_profile",
        status: "passed",
        message: "DJ profile created successfully",
        profileId,
      });
      results.summary.passed++;

      // Test 3: Verify creation
      const createdProfile = await ctx.db.get(profileId);
      if (createdProfile) {
        results.tests.push({
          name: "verify_creation",
          status: "passed",
          message: "Created profile can be retrieved",
          profile: {
            id: createdProfile._id,
            displayName: createdProfile.displayName,
            completedOnboarding: createdProfile.completedOnboarding,
          },
        });
        results.summary.passed++;
      } else {
        results.tests.push({
          name: "verify_creation",
          status: "failed",
          message: "Created profile cannot be retrieved",
        });
        results.summary.failed++;
      }

    } catch (error) {
      results.tests.push({
        name: "create_dj_profile",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    return { success: true, data: results, message: "Create operations test completed" };
  },
});

// Test Mutation: Validate update operations
export const testUpdateOperations = mutation({
  args: {
    profileId: v.id("djProfiles"),
    updates: v.object({
      displayName: v.optional(v.string()),
      completedOnboarding: v.optional(v.boolean()),
    }),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }
    
    const results = {
      timestamp: Date.now(),
      userId,
      profileId: args.profileId,
      tests: [] as any[],
      summary: {
        passed: 0,
        failed: 0,
      },
    };

    // Test 1: Verify profile exists and ownership
    try {
      const profile = await ctx.db.get(args.profileId);
      if (!profile) {
        throw new Error("DJ profile not found");
      }
      if (profile.userId !== userId) {
        throw new Error("Access denied: You can only access your own profile");
      }

      results.tests.push({
        name: "ownership_verification",
        status: "passed",
        message: "Profile exists and user has ownership",
      });
      results.summary.passed++;

    } catch (error) {
      results.tests.push({
        name: "ownership_verification",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
      return { success: false, data: results, message: "Update test failed - ownership verification" };
    }

    // Test 2: Perform update
    try {
      const updateData: any = {
        updatedAt: Date.now(),
      };

      if (args.updates.displayName !== undefined) {
        const trimmed = args.updates.displayName.trim();
        if (trimmed.length === 0 || trimmed.length > 100) {
          throw new Error("Display name must be between 1 and 100 characters");
        }
        updateData.displayName = trimmed;
      }

      if (args.updates.completedOnboarding !== undefined) {
        updateData.completedOnboarding = args.updates.completedOnboarding;
      }

      await ctx.db.patch(args.profileId, updateData);

      results.tests.push({
        name: "update_profile",
        status: "passed",
        message: "Profile updated successfully",
        updatedFields: Object.keys(updateData),
      });
      results.summary.passed++;

      // Test 3: Verify update
      const updatedProfile = await ctx.db.get(args.profileId);
      if (updatedProfile) {
        const verificationPassed = 
          (!args.updates.displayName || updatedProfile.displayName === args.updates.displayName.trim()) &&
          (!args.updates.completedOnboarding || updatedProfile.completedOnboarding === args.updates.completedOnboarding);

        if (verificationPassed) {
          results.tests.push({
            name: "verify_update",
            status: "passed",
            message: "Update verification successful",
          });
          results.summary.passed++;
        } else {
          results.tests.push({
            name: "verify_update",
            status: "failed",
            message: "Update verification failed - values don't match",
          });
          results.summary.failed++;
        }
      }

    } catch (error) {
      results.tests.push({
        name: "update_profile",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    return { success: true, data: results, message: "Update operations test completed" };
  },
});

// Action: Run comprehensive CRUD test suite
export const runComprehensiveCrudTests = action({
  args: {
    includeCreateTest: v.optional(v.boolean()),
    includeUpdateTest: v.optional(v.boolean()),
    testDisplayName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const results = {
      timestamp: Date.now(),
      testSuite: "comprehensive_crud",
      tests: [] as any[],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
      },
    };

    // Test 1: Validation check
    try {
      const validation = await ctx.runQuery(api.crudValidation.validateCrudOperations, {});
      results.tests.push({
        name: "crud_validation",
        status: validation.errors.length === 0 ? "passed" : "failed",
        result: validation,
      });
      if (validation.errors.length === 0) results.summary.passed++;
      else results.summary.failed++;
    } catch (error) {
      results.tests.push({
        name: "crud_validation",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Test 2: User status check
    try {
      const userStatus = await ctx.runQuery(api.users.checkUserStatus, {});
      results.tests.push({
        name: "user_status_check",
        status: "passed",
        result: userStatus,
      });
      results.summary.passed++;
    } catch (error) {
      results.tests.push({
        name: "user_status_check",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Test 3: Create operations (if requested)
    if (args.includeCreateTest) {
      try {
        const createResult = await ctx.runMutation(api.crudValidation.testCreateOperations, {
          testData: {
            displayName: args.testDisplayName || "Test DJ Profile",
            skipIfExists: true,
          },
        });
        results.tests.push({
          name: "create_operations",
          status: "passed",
          result: createResult,
        });
        results.summary.passed++;
      } catch (error) {
        results.tests.push({
          name: "create_operations",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    // Calculate totals
    results.summary.total = results.tests.length;

    return { success: true, data: results, message: "Comprehensive CRUD test suite completed" };
  },
});
