/**
 * Test Runner for CRUD Operations
 * 
 * This module provides simple test functions to validate that our
 * CRUD operations are working correctly.
 */

import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Simple test to check if basic queries work
export const testBasicQueries = query({
  args: {},
  handler: async (ctx) => {
    const results = {
      timestamp: Date.now(),
      tests: [] as any[],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
      },
    };

    // Test 1: Check if we can query DJ profiles
    try {
      const profiles = await ctx.db.query("djProfiles").take(5);
      results.tests.push({
        name: "query_dj_profiles",
        status: "passed",
        result: `Found ${profiles.length} DJ profiles`,
      });
      results.summary.passed++;
    } catch (error) {
      results.tests.push({
        name: "query_dj_profiles",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Test 2: Check if we can query users
    try {
      const users = await ctx.db.query("users").take(5);
      results.tests.push({
        name: "query_users",
        status: "passed",
        result: `Found ${users.length} users`,
      });
      results.summary.passed++;
    } catch (error) {
      results.tests.push({
        name: "query_users",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Test 3: Check authentication
    try {
      const userId = await getAuthUserId(ctx);
      results.tests.push({
        name: "check_authentication",
        status: "passed",
        result: userId ? `Authenticated as ${userId}` : "Not authenticated",
      });
      results.summary.passed++;
    } catch (error) {
      results.tests.push({
        name: "check_authentication",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Test 4: Check schema indexes
    try {
      const profilesByOnboarding = await ctx.db
        .query("djProfiles")
        .withIndex("by_onboarding", (q) => q.eq("completedOnboarding", true))
        .take(3);
      results.tests.push({
        name: "test_indexes",
        status: "passed",
        result: `Found ${profilesByOnboarding.length} completed profiles`,
      });
      results.summary.passed++;
    } catch (error) {
      results.tests.push({
        name: "test_indexes",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    results.summary.total = results.tests.length;
    return results;
  },
});

// Test user status and profile operations
export const testUserOperations = query({
  args: {},
  handler: async (ctx) => {
    const results = {
      timestamp: Date.now(),
      authentication: {
        isAuthenticated: false,
        userId: null as any,
      },
      userOperations: [] as any[],
      djProfileOperations: [] as any[],
    };

    // Check authentication
    const userId = await getAuthUserId(ctx);
    results.authentication.isAuthenticated = !!userId;
    results.authentication.userId = userId;

    if (!userId) {
      return {
        ...results,
        message: "Not authenticated - cannot test user operations",
      };
    }

    // Test user operations
    try {
      const user = await ctx.db.get(userId);
      results.userOperations.push({
        name: "get_current_user",
        status: "passed",
        result: user ? "User found" : "User not found",
      });
    } catch (error) {
      results.userOperations.push({
        name: "get_current_user",
        status: "failed",
        error: String(error),
      });
    }

    // Test DJ profile operations
    try {
      const djProfile = await ctx.db
        .query("djProfiles")
        .withIndex("by_user", (q) => q.eq("userId", userId))
        .first();
      results.djProfileOperations.push({
        name: "get_dj_profile_by_user",
        status: "passed",
        result: djProfile ? "DJ profile found" : "No DJ profile",
        profileId: djProfile?._id,
      });
    } catch (error) {
      results.djProfileOperations.push({
        name: "get_dj_profile_by_user",
        status: "failed",
        error: String(error),
      });
    }

    return results;
  },
});

// Test mutation operations (safe operations only)
export const testSafeMutations = mutation({
  args: {
    testType: v.union(v.literal("login_update"), v.literal("validation_only")),
  },
  handler: async (ctx, args) => {
    const results = {
      timestamp: Date.now(),
      testType: args.testType,
      tests: [] as any[],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
      },
    };

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      results.tests.push({
        name: "authentication_check",
        status: "failed",
        error: "Not authenticated",
      });
      results.summary.failed++;
      results.summary.total = 1;
      return results;
    }

    if (args.testType === "login_update") {
      // Test updating last login (safe operation)
      try {
        const profile = await ctx.db
          .query("djProfiles")
          .withIndex("by_user", (q) => q.eq("userId", userId))
          .first();

        if (profile) {
          await ctx.db.patch(profile._id, {
            lastLoginAt: Date.now(),
            updatedAt: Date.now(),
          });
          results.tests.push({
            name: "update_last_login",
            status: "passed",
            result: "Last login updated successfully",
          });
          results.summary.passed++;
        } else {
          results.tests.push({
            name: "update_last_login",
            status: "skipped",
            result: "No DJ profile to update",
          });
        }
      } catch (error) {
        results.tests.push({
          name: "update_last_login",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    if (args.testType === "validation_only") {
      // Test validation logic without making changes
      try {
        // Test display name validation
        const testNames = ["", "a".repeat(101), "Valid Name"];
        for (const name of testNames) {
          try {
            if (name.trim().length === 0) {
              throw new Error("Display name cannot be empty");
            }
            if (name.length > 100) {
              throw new Error("Display name cannot exceed 100 characters");
            }
            results.tests.push({
              name: `validate_name_${name.length === 0 ? 'empty' : name.length > 100 ? 'long' : 'valid'}`,
              status: name === "Valid Name" ? "passed" : "failed",
              result: name === "Valid Name" ? "Valid name accepted" : "Should have failed",
            });
            if (name === "Valid Name") results.summary.passed++;
            else results.summary.failed++;
          } catch (error) {
            results.tests.push({
              name: `validate_name_${name.length === 0 ? 'empty' : name.length > 100 ? 'long' : 'valid'}`,
              status: name !== "Valid Name" ? "passed" : "failed",
              result: name !== "Valid Name" ? "Correctly rejected invalid name" : "Should have accepted valid name",
            });
            if (name !== "Valid Name") results.summary.passed++;
            else results.summary.failed++;
          }
        }
      } catch (error) {
        results.tests.push({
          name: "validation_tests",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    results.summary.total = results.tests.length;
    return results;
  },
});

// Comprehensive test action
export const runCrudTestSuite = action({
  args: {
    includeBasicTests: v.optional(v.boolean()),
    includeUserTests: v.optional(v.boolean()),
    includeMutationTests: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const results = {
      timestamp: Date.now(),
      testSuite: "crud_operations",
      tests: [] as any[],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
      },
    };

    // Run basic tests
    if (args.includeBasicTests !== false) {
      try {
        const basicTests = await ctx.runQuery(api.testRunner.testBasicQueries, {});
        results.tests.push({
          name: "basic_queries",
          status: basicTests.summary.failed === 0 ? "passed" : "failed",
          result: basicTests,
        });
        if (basicTests.summary.failed === 0) results.summary.passed++;
        else results.summary.failed++;
      } catch (error) {
        results.tests.push({
          name: "basic_queries",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    // Run user tests
    if (args.includeUserTests !== false) {
      try {
        const userTests = await ctx.runQuery(api.testRunner.testUserOperations, {});
        results.tests.push({
          name: "user_operations",
          status: "passed",
          result: userTests,
        });
        results.summary.passed++;
      } catch (error) {
        results.tests.push({
          name: "user_operations",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    // Run mutation tests
    if (args.includeMutationTests !== false) {
      try {
        const mutationTests = await ctx.runMutation(api.testRunner.testSafeMutations, {
          testType: "validation_only",
        });
        results.tests.push({
          name: "safe_mutations",
          status: mutationTests.summary.failed === 0 ? "passed" : "failed",
          result: mutationTests,
        });
        if (mutationTests.summary.failed === 0) results.summary.passed++;
        else results.summary.failed++;
      } catch (error) {
        results.tests.push({
          name: "safe_mutations",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    results.summary.total = results.tests.length;
    return results;
  },
});
