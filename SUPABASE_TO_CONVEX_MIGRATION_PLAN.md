# PlayBeg: Supabase to Convex Migration Plan

## Executive Summary

This document outlines a comprehensive migration strategy for transitioning PlayBeg from Supabase to Convex as the backend-as-a-service platform. The migration will be executed in phases to minimize downtime and ensure data integrity while maintaining all existing functionality.

## 1. Database Migration Strategy

### 1.1 Schema Transformation Analysis

**Current Supabase PostgreSQL Schema → Convex Document Structure**

```typescript
// Current Supabase Tables → Convex Collections

// sessions table → sessions collection
interface Session {
  _id: Id<"sessions">;
  djId: Id<"djProfiles">;
  name: string;
  active: boolean;
  acceptRequests: boolean;
  autoApproval: boolean;
  blockedGenres: string[];
  durationMinutes?: number;
  maxRequestsPerTimeframe?: number;
  maxRequestsPerUser?: number;
  enableIpLimiting: boolean;
  allowQuotaRequests: boolean;
  sponsorHeader?: string;
  sponsorLogoUrl?: string;
  sponsorMessage?: string;
  sponsorUrl?: string;
  weddingModeEnabled: boolean;
  weddingCoupleNames: string[];
  weddingDate?: string;
  weddingHashtag?: string;
  weddingTemplate?: string;
  createdAt: number;
  updatedAt: number;
}

// song_requests table → songRequests collection
interface SongRequest {
  _id: Id<"songRequests">;
  sessionId: Id<"sessions">;
  songTitle: string;
  artistName: string;
  requesterName: string;
  requesterIp?: string;
  appleMusicId?: string;
  albumArtwork?: string;
  playlistId?: string;
  appleMusicPlaylistId?: string;
  genre?: string;
  status: "pending" | "approved" | "auto-approved" | "declined" | "played";
  addedToPlaylist: boolean;
  createdAt: number;
}

// dj_profiles table → djProfiles collection
interface DjProfile {
  _id: Id<"djProfiles">;
  userId: Id<"users">;
  displayName: string;
  completedOnboarding: boolean;
  profilePictureUrl?: string;
  lastLoginAt?: number;
  createdAt: number;
  updatedAt: number;
}
```

### 1.2 Data Transformation Requirements

**Key Transformations:**
1. **Primary Keys**: UUID → Convex Id<T>
2. **Foreign Keys**: Direct references → Convex Id references
3. **Timestamps**: ISO strings → Unix timestamps (number)
4. **Arrays**: PostgreSQL arrays → JavaScript arrays
5. **JSON Fields**: JSONB → JavaScript objects
6. **Enums**: PostgreSQL enums → TypeScript union types

### 1.3 Relationship Mapping

```typescript
// Convex relationship patterns
export const sessions = defineTable({
  djId: v.id("djProfiles"),
  name: v.string(),
  active: v.boolean(),
  // ... other fields
}).index("by_dj", ["djId"])
  .index("by_active", ["active"])
  .index("by_created", ["createdAt"]);

export const songRequests = defineTable({
  sessionId: v.id("sessions"),
  songTitle: v.string(),
  artistName: v.string(),
  // ... other fields
}).index("by_session", ["sessionId"])
  .index("by_status", ["status"])
  .index("by_created", ["createdAt"]);
```

## 2. Authentication Migration

### 2.1 Current Supabase Auth → Convex Auth

**Migration Strategy:**
1. **User Data Export**: Export all user records from Supabase Auth
2. **Password Hash Migration**: Not possible - users must reset passwords
3. **Session Migration**: Implement temporary dual-auth during transition
4. **OAuth Providers**: Reconfigure Apple ID, Google, etc. with Convex Auth

### 2.2 Authentication Flow Changes

```typescript
// Before (Supabase)
const { data, error } = await supabase.auth.signInWithPassword({
  email,
  password,
});

// After (Convex)
const signIn = useMutation(api.auth.signIn);
const result = await signIn({ email, password });
```

### 2.3 Security Considerations

- **PKCE Flow**: Maintain PKCE for OAuth flows
- **JWT Tokens**: Transition from Supabase JWT to Convex tokens
- **Session Management**: Update frontend to use Convex auth state
- **Row Level Security**: Implement equivalent access controls in Convex functions

## 3. Real-time Functionality Migration

### 3.1 Supabase Channels → Convex Subscriptions

```typescript
// Before (Supabase Real-time)
const channel = supabase
  .channel('song-requests')
  .on('postgres_changes', {
    event: 'INSERT',
    schema: 'public',
    table: 'song_requests',
    filter: `session_id=eq.${sessionId}`
  }, handleNewRequest)
  .subscribe();

// After (Convex Subscriptions)
const songRequests = useQuery(api.songRequests.getBySession, { 
  sessionId 
});
// Convex automatically handles real-time updates
```

### 3.2 Real-time Features Migration

**Features to Migrate:**
1. **Live Song Requests**: Session-based request updates
2. **DJ Dashboard**: Real-time request queue updates
3. **Session Status**: Active/inactive state changes
4. **Subscription Changes**: Payment status updates
5. **Request Status Updates**: Approval/denial notifications

## 4. Edge Functions Migration

### 4.1 Supabase Edge Functions → Convex Functions

**Functions to Migrate:**

```typescript
// apple-music-search function
export const searchAppleMusic = action({
  args: {
    query: v.string(),
    sessionId: v.optional(v.id("sessions")),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Apple Music API integration
    const results = await searchAppleMusicAPI(args.query, args.limit);
    return results;
  },
});

// create-song-request function
export const createSongRequest = mutation({
  args: {
    sessionId: v.id("sessions"),
    songTitle: v.string(),
    artistName: v.string(),
    requesterName: v.string(),
    // ... other fields
  },
  handler: async (ctx, args) => {
    // Validation and rate limiting logic
    const requestId = await ctx.db.insert("songRequests", {
      ...args,
      status: "pending",
      createdAt: Date.now(),
    });
    return requestId;
  },
});

// stripe-webhooks function
export const handleStripeWebhook = httpAction(async (ctx, request) => {
  const signature = request.headers.get("stripe-signature");
  const body = await request.text();
  
  // Verify webhook signature
  const event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
  
  // Handle different event types
  switch (event.type) {
    case "checkout.session.completed":
      await handleCheckoutCompleted(ctx, event.data.object);
      break;
    // ... other cases
  }
  
  return new Response("OK");
});
```

### 4.2 Function Categories

1. **Mutations**: Data modification operations
2. **Queries**: Data retrieval operations  
3. **Actions**: External API integrations
4. **HTTP Actions**: Webhook handlers

## 5. File Storage Migration

### 5.1 Storage Strategy

**Options for File Storage:**
1. **Convex File Storage**: For small files (profile pictures)
2. **AWS S3**: For larger files and better CDN integration
3. **Cloudinary**: For image optimization and transformations

### 5.2 Migration Approach

```typescript
// File upload with Convex
export const generateUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    return await ctx.storage.generateUploadUrl();
  },
});

export const saveProfilePicture = mutation({
  args: {
    storageId: v.id("_storage"),
    djProfileId: v.id("djProfiles"),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.djProfileId, {
      profilePictureStorageId: args.storageId,
    });
  },
});
```

## 6. Third-party Integration Adjustments

### 6.1 Apple Music Integration

**Changes Required:**
- Update token storage from Supabase to Convex
- Modify search proxy endpoints
- Update playlist management functions

```typescript
// Apple Music token storage
export const storeAppleMusicToken = mutation({
  args: {
    userId: v.id("users"),
    token: v.string(),
    expiresAt: v.number(),
  },
  handler: async (ctx, args) => {
    await ctx.db.insert("appleMusicTokens", {
      userId: args.userId,
      token: args.token,
      expiresAt: args.expiresAt,
      isValid: true,
      createdAt: Date.now(),
    });
  },
});
```

### 6.2 Stripe Integration

**Webhook URL Updates:**
- Update Stripe webhook endpoints to point to Convex HTTP actions
- Modify subscription status handling
- Update checkout session creation

## 7. Migration Timeline

### Phase 1: Foundation (Weeks 1-2)
- [ ] Set up Convex project and development environment
- [ ] Define Convex schema and data models
- [ ] Implement basic CRUD operations
- [ ] Set up authentication system

### Phase 2: Core Features (Weeks 3-4)
- [ ] Migrate song request functionality
- [ ] Implement real-time subscriptions
- [ ] Migrate DJ dashboard features
- [ ] Set up file storage solution

### Phase 3: Advanced Features (Weeks 5-6)
- [ ] Migrate subscription system
- [ ] Implement Stripe webhook handlers
- [ ] Migrate Apple Music integration
- [ ] Set up blog system

### Phase 4: Testing & Validation (Weeks 7-8)
- [ ] Comprehensive testing of all features
- [ ] Performance optimization
- [ ] Security audit
- [ ] User acceptance testing

### Phase 5: Data Migration & Cutover (Week 9)
- [ ] Export data from Supabase
- [ ] Transform and import data to Convex
- [ ] DNS cutover and monitoring
- [ ] Post-migration validation

## 8. Risk Assessment

### 8.1 High-Risk Areas

**Data Loss Risks:**
- **Mitigation**: Multiple backups, staged migration, rollback plan
- **Impact**: Critical - could lose user data and sessions

**Authentication Disruption:**
- **Mitigation**: Dual-auth period, password reset communication
- **Impact**: High - users need to re-authenticate

**Real-time Feature Downtime:**
- **Mitigation**: Feature flags, gradual rollout
- **Impact**: Medium - temporary degraded experience

**Third-party Integration Failures:**
- **Mitigation**: Extensive testing, fallback mechanisms
- **Impact**: Medium - some features may be temporarily unavailable

### 8.2 Mitigation Strategies

1. **Blue-Green Deployment**: Run both systems in parallel
2. **Feature Flags**: Gradual feature rollout
3. **Data Validation**: Comprehensive data integrity checks
4. **Monitoring**: Real-time system health monitoring
5. **Rollback Plan**: Quick reversion to Supabase if needed

## 9. Code Changes Required

### 9.1 Frontend Client Updates

```typescript
// Before (Supabase client)
import { supabase } from '@/integrations/supabase/client';

// After (Convex client)
import { useQuery, useMutation } from 'convex/react';
import { api } from '../convex/_generated/api';

// Query pattern changes
// Before
const { data: sessions } = await supabase
  .from('sessions')
  .select('*')
  .eq('dj_id', djId);

// After  
const sessions = useQuery(api.sessions.getByDj, { djId });
```

### 9.2 Hook Updates

```typescript
// Update custom hooks to use Convex
export function useSessionData(sessionId: string) {
  const session = useQuery(api.sessions.getById, { sessionId });
  const requests = useQuery(api.songRequests.getBySession, { sessionId });
  
  return {
    session,
    requests,
    isLoading: session === undefined || requests === undefined,
  };
}
```

### 9.3 Component Updates

Major components requiring updates:
- `Dashboard.tsx` - Real-time data fetching
- `SongRequest.tsx` - Request submission
- `AuthContext.tsx` - Authentication state
- All data-fetching hooks and services

## 10. Testing Strategy

### 10.1 Testing Phases

**Unit Testing:**
- Test all Convex functions individually
- Validate data transformations
- Test authentication flows

**Integration Testing:**
- End-to-end user workflows
- Third-party service integrations
- Real-time functionality

**Performance Testing:**
- Load testing with concurrent users
- Real-time update performance
- Database query optimization

**User Acceptance Testing:**
- DJ workflow validation
- Audience request flow testing
- Mobile responsiveness

### 10.2 Data Integrity Validation

```typescript
// Data validation scripts
export const validateMigration = action({
  args: {},
  handler: async (ctx) => {
    const supabaseSessions = await fetchSupabaseSessions();
    const convexSessions = await ctx.db.query("sessions").collect();
    
    // Compare counts and key fields
    const validation = {
      sessionCount: {
        supabase: supabaseSessions.length,
        convex: convexSessions.length,
        match: supabaseSessions.length === convexSessions.length,
      },
      // Additional validation checks...
    };
    
    return validation;
  },
});
```

## Next Steps

1. **Project Setup**: Initialize Convex project and development environment
2. **Schema Design**: Finalize Convex schema based on current data model
3. **Prototype Development**: Build core features to validate approach
4. **Stakeholder Review**: Present migration plan for approval
5. **Resource Allocation**: Assign development team and timeline
6. **Risk Mitigation**: Implement backup and rollback procedures

## 11. Detailed Implementation Guide

### 11.1 Convex Schema Definition

```typescript
// convex/schema.ts
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  users: defineTable({
    email: v.string(),
    name: v.optional(v.string()),
    emailVerified: v.optional(v.boolean()),
    image: v.optional(v.string()),
    createdAt: v.number(),
  }).index("by_email", ["email"]),

  djProfiles: defineTable({
    userId: v.id("users"),
    displayName: v.string(),
    completedOnboarding: v.boolean(),
    profilePictureStorageId: v.optional(v.id("_storage")),
    lastLoginAt: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_user", ["userId"]),

  sessions: defineTable({
    djId: v.id("djProfiles"),
    name: v.string(),
    active: v.boolean(),
    acceptRequests: v.optional(v.boolean()),
    autoApproval: v.optional(v.boolean()),
    blockedGenres: v.optional(v.array(v.string())),
    durationMinutes: v.optional(v.number()),
    maxRequestsPerTimeframe: v.optional(v.number()),
    maxRequestsPerUser: v.optional(v.number()),
    enableIpLimiting: v.optional(v.boolean()),
    allowQuotaRequests: v.optional(v.boolean()),
    sponsorHeader: v.optional(v.string()),
    sponsorLogoStorageId: v.optional(v.id("_storage")),
    sponsorMessage: v.optional(v.string()),
    sponsorUrl: v.optional(v.string()),
    timeframeMinutes: v.optional(v.number()),
    weddingModeEnabled: v.optional(v.boolean()),
    weddingCoupleNames: v.optional(v.array(v.string())),
    weddingDate: v.optional(v.string()),
    weddingHashtag: v.optional(v.string()),
    weddingTemplate: v.optional(v.string()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_dj", ["djId"])
    .index("by_active", ["active"])
    .index("by_created", ["createdAt"]),

  songRequests: defineTable({
    sessionId: v.id("sessions"),
    songTitle: v.string(),
    artistName: v.string(),
    requesterName: v.string(),
    requesterIp: v.optional(v.string()),
    appleMusicId: v.optional(v.string()),
    albumArtwork: v.optional(v.string()),
    playlistId: v.optional(v.string()),
    appleMusicPlaylistId: v.optional(v.string()),
    genre: v.optional(v.string()),
    status: v.union(
      v.literal("pending"),
      v.literal("approved"),
      v.literal("auto-approved"),
      v.literal("declined"),
      v.literal("played")
    ),
    addedToPlaylist: v.optional(v.boolean()),
    createdAt: v.number(),
  })
    .index("by_session", ["sessionId"])
    .index("by_status", ["status"])
    .index("by_created", ["createdAt"])
    .index("by_session_status", ["sessionId", "status"]),

  subscriptionPlans: defineTable({
    name: v.string(),
    priceAmount: v.number(),
    priceCurrency: v.string(),
    durationHours: v.optional(v.number()),
    maxSessions: v.optional(v.number()),
    maxRequestsPerSession: v.optional(v.number()),
    features: v.array(v.string()),
    stripePriceId: v.optional(v.string()),
    active: v.boolean(),
    createdAt: v.number(),
  }).index("by_active", ["active"]),

  djSubscriptions: defineTable({
    djId: v.id("djProfiles"),
    planId: v.id("subscriptionPlans"),
    status: v.union(
      v.literal("active"),
      v.literal("canceled"),
      v.literal("past_due"),
      v.literal("unpaid")
    ),
    currentPeriodStart: v.optional(v.number()),
    currentPeriodEnd: v.optional(v.number()),
    cancelAtPeriodEnd: v.optional(v.boolean()),
    stripeCustomerId: v.optional(v.string()),
    stripeSubscriptionId: v.optional(v.string()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_dj", ["djId"])
    .index("by_status", ["status"])
    .index("by_stripe_customer", ["stripeCustomerId"]),

  appleMusicTokens: defineTable({
    userId: v.id("users"),
    appleMusicToken: v.string(),
    expiresAt: v.number(),
    isValid: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_user", ["userId"]),

  blogPosts: defineTable({
    title: v.string(),
    slug: v.string(),
    publishedDate: v.string(),
    author: v.string(),
    category: v.string(),
    excerpt: v.string(),
    coverImageStorageId: v.optional(v.id("_storage")),
    coverImageAlt: v.optional(v.string()),
    tags: v.array(v.string()),
    content: v.string(),
    status: v.union(
      v.literal("published"),
      v.literal("draft"),
      v.literal("archived")
    ),
    metaDescription: v.optional(v.string()),
    ogImageStorageId: v.optional(v.id("_storage")),
    viewCount: v.number(),
    featured: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_slug", ["slug"])
    .index("by_status", ["status"])
    .index("by_category", ["category"])
    .index("by_published_date", ["publishedDate"])
    .index("by_featured", ["featured"]),
});
```

### 11.2 Data Migration Scripts

```typescript
// Migration utility functions
export const migrateSupabaseData = action({
  args: {},
  handler: async (ctx) => {
    // This would be run as a one-time migration script

    // 1. Migrate users and DJ profiles
    const supabaseUsers = await fetchSupabaseUsers();
    for (const user of supabaseUsers) {
      const userId = await ctx.db.insert("users", {
        email: user.email,
        name: user.user_metadata?.display_name,
        emailVerified: user.email_confirmed_at ? true : false,
        createdAt: new Date(user.created_at).getTime(),
      });

      // Migrate DJ profile if exists
      if (user.dj_profile) {
        await ctx.db.insert("djProfiles", {
          userId,
          displayName: user.dj_profile.display_name,
          completedOnboarding: user.dj_profile.completed_onboarding,
          lastLoginAt: user.dj_profile.last_login_at
            ? new Date(user.dj_profile.last_login_at).getTime()
            : undefined,
          createdAt: new Date(user.dj_profile.created_at).getTime(),
          updatedAt: new Date(user.dj_profile.updated_at).getTime(),
        });
      }
    }

    // 2. Migrate sessions
    const supabaseSessions = await fetchSupabaseSessions();
    for (const session of supabaseSessions) {
      await ctx.db.insert("sessions", {
        djId: await getDjProfileId(session.dj_id),
        name: session.name,
        active: session.active ?? false,
        acceptRequests: session.accept_requests,
        autoApproval: session.auto_approval,
        blockedGenres: session.blocked_genres || [],
        durationMinutes: session.duration_minutes,
        maxRequestsPerTimeframe: session.max_requests_per_timeframe,
        maxRequestsPerUser: session.max_requests_per_user,
        enableIpLimiting: session.enable_ip_limiting,
        allowQuotaRequests: session.allow_quota_requests,
        sponsorHeader: session.sponsor_header,
        sponsorMessage: session.sponsor_message,
        sponsorUrl: session.sponsor_url,
        timeframeMinutes: session.timeframe_minutes,
        weddingModeEnabled: session.wedding_mode_enabled,
        weddingCoupleNames: [
          session.wedding_couple_name_1,
          session.wedding_couple_name_2
        ].filter(Boolean),
        weddingDate: session.wedding_date,
        weddingHashtag: session.wedding_hashtag,
        weddingTemplate: session.wedding_template,
        createdAt: new Date(session.created_at).getTime(),
        updatedAt: new Date(session.updated_at).getTime(),
      });
    }

    // 3. Migrate song requests
    const supabaseRequests = await fetchSupabaseSongRequests();
    for (const request of supabaseRequests) {
      await ctx.db.insert("songRequests", {
        sessionId: await getConvexSessionId(request.session_id),
        songTitle: request.song_title,
        artistName: request.artist_name,
        requesterName: request.requester_name,
        requesterIp: request.requester_ip,
        appleMusicId: request.apple_music_id,
        albumArtwork: request.album_artwork,
        playlistId: request.playlist_id,
        appleMusicPlaylistId: request.apple_music_playlist_id,
        genre: request.genre,
        status: request.status as any,
        addedToPlaylist: request.added_to_playlist,
        createdAt: new Date(request.created_at).getTime(),
      });
    }

    return { success: true, message: "Migration completed" };
  },
});
```

### 11.3 Environment Configuration

```typescript
// convex/environment.d.ts
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      APPLE_MUSIC_DEVELOPER_TOKEN: string;
      STRIPE_SECRET_KEY: string;
      STRIPE_WEBHOOK_SECRET: string;
      CONVEX_DEPLOYMENT: string;
    }
  }
}
```

## 12. Performance Optimization

### 12.1 Query Optimization

```typescript
// Optimized queries with proper indexing
export const getSessionWithRequests = query({
  args: { sessionId: v.id("sessions") },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) return null;

    // Use indexed query for better performance
    const requests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .order("desc")
      .take(100); // Limit results for performance

    return {
      session,
      requests,
      requestCount: requests.length,
    };
  },
});
```

### 12.2 Caching Strategy

```typescript
// Implement caching for frequently accessed data
export const getCachedDjProfile = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    // Convex automatically handles caching and invalidation
    const profile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    return profile;
  },
});
```

## 13. Monitoring and Observability

### 13.1 Logging Strategy

```typescript
// Structured logging for better observability
export const createSongRequestWithLogging = mutation({
  args: {
    sessionId: v.id("sessions"),
    songTitle: v.string(),
    artistName: v.string(),
    requesterName: v.string(),
  },
  handler: async (ctx, args) => {
    console.log("Creating song request", {
      sessionId: args.sessionId,
      songTitle: args.songTitle,
      timestamp: Date.now(),
    });

    try {
      const requestId = await ctx.db.insert("songRequests", {
        ...args,
        status: "pending",
        createdAt: Date.now(),
      });

      console.log("Song request created successfully", {
        requestId,
        sessionId: args.sessionId,
      });

      return requestId;
    } catch (error) {
      console.error("Failed to create song request", {
        error: error.message,
        sessionId: args.sessionId,
      });
      throw error;
    }
  },
});
```

### 13.2 Health Checks

```typescript
// Health check endpoints
export const healthCheck = httpAction(async (ctx, request) => {
  try {
    // Test database connectivity
    const testQuery = await ctx.db.query("users").take(1);

    return new Response(JSON.stringify({
      status: "healthy",
      timestamp: Date.now(),
      database: "connected",
    }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    return new Response(JSON.stringify({
      status: "unhealthy",
      error: error.message,
      timestamp: Date.now(),
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
});
```

## Conclusion

This comprehensive migration plan provides a structured approach to transitioning PlayBeg from Supabase to Convex while maintaining service availability and data integrity. The phased approach allows for thorough testing and validation at each stage, minimizing risks and ensuring a successful migration.

**Key Success Factors:**
1. **Careful Planning**: Detailed schema mapping and data transformation
2. **Comprehensive Testing**: Unit, integration, and performance testing
3. **Risk Mitigation**: Backup strategies and rollback plans
4. **Stakeholder Communication**: Regular updates and clear expectations
5. **Performance Monitoring**: Continuous monitoring during and after migration

The migration will result in improved real-time performance, better developer experience, and enhanced scalability for the PlayBeg platform.

## 14. Deployment Strategy

### 14.1 Blue-Green Deployment

```bash
# Production deployment strategy
# 1. Deploy Convex backend to staging
npx convex deploy --prod-url https://playbeg-staging.convex.cloud

# 2. Run data migration scripts
npx convex run migration:migrateSupabaseData --prod

# 3. Deploy frontend with feature flags
npm run build:staging
netlify deploy --prod --dir=dist

# 4. Gradual traffic routing
# Route 10% traffic to Convex backend
# Monitor for 24 hours
# Gradually increase to 100%

# 5. DNS cutover
# Update DNS to point to new infrastructure
# Monitor for issues and rollback if necessary
```

### 14.2 Feature Flags Implementation

```typescript
// Feature flag system for gradual rollout
export const useConvexBackend = () => {
  const [useConvex, setUseConvex] = useState(false);

  useEffect(() => {
    // Check feature flag from environment or remote config
    const convexEnabled = process.env.REACT_APP_USE_CONVEX === 'true' ||
                         localStorage.getItem('use-convex') === 'true';
    setUseConvex(convexEnabled);
  }, []);

  return useConvex;
};

// Usage in components
const Dashboard = () => {
  const useConvex = useConvexBackend();

  if (useConvex) {
    return <ConvexDashboard />;
  } else {
    return <SupabaseDashboard />;
  }
};
```

### 14.3 Rollback Strategy

```typescript
// Automated rollback triggers
export const monitorMigration = action({
  args: {},
  handler: async (ctx) => {
    const metrics = {
      errorRate: await calculateErrorRate(),
      responseTime: await calculateResponseTime(),
      activeUsers: await countActiveUsers(),
    };

    // Trigger rollback if metrics exceed thresholds
    if (metrics.errorRate > 0.05 || metrics.responseTime > 2000) {
      await triggerRollback();
      await notifyTeam("Migration rollback triggered", metrics);
    }

    return metrics;
  },
});
```

## 15. Post-Migration Optimization

### 15.1 Performance Tuning

```typescript
// Database optimization after migration
export const optimizeDatabase = action({
  args: {},
  handler: async (ctx) => {
    // Analyze query patterns and add missing indexes
    const slowQueries = await analyzeQueryPerformance();

    for (const query of slowQueries) {
      console.log(`Slow query detected: ${query.name}`, {
        avgTime: query.averageTime,
        callCount: query.callCount,
      });
    }

    // Suggestions for index optimization
    return {
      recommendations: generateIndexRecommendations(slowQueries),
      currentPerformance: await getCurrentPerformanceMetrics(),
    };
  },
});
```

### 15.2 Cost Optimization

```typescript
// Monitor and optimize Convex usage costs
export const analyzeCosts = action({
  args: {},
  handler: async (ctx) => {
    const usage = {
      functionCalls: await getFunctionCallCount(),
      databaseOperations: await getDatabaseOpCount(),
      storageUsage: await getStorageUsage(),
      bandwidthUsage: await getBandwidthUsage(),
    };

    const costProjection = calculateMonthlyCost(usage);

    return {
      currentUsage: usage,
      projectedMonthlyCost: costProjection,
      optimizationSuggestions: generateCostOptimizations(usage),
    };
  },
});
```

## 16. Documentation Updates

### 16.1 Developer Documentation

```markdown
# PlayBeg Convex Backend Documentation

## Quick Start

1. Install Convex CLI: `npm install -g convex`
2. Clone repository: `git clone https://github.com/djrobbieh/PlayBeg.git`
3. Install dependencies: `npm install`
4. Set up Convex: `npx convex dev`
5. Configure environment variables in `.env.local`

## API Reference

### Queries
- `api.sessions.getByDj(djId)` - Get sessions for a DJ
- `api.songRequests.getBySession(sessionId)` - Get requests for a session
- `api.djProfiles.getByUser(userId)` - Get DJ profile

### Mutations
- `api.sessions.create(sessionData)` - Create new session
- `api.songRequests.create(requestData)` - Create song request
- `api.songRequests.updateStatus(requestId, status)` - Update request status

### Actions
- `api.appleMusic.search(query, sessionId)` - Search Apple Music
- `api.stripe.createCheckoutSession(planId)` - Create Stripe checkout
```

### 16.2 User Migration Guide

```markdown
# User Migration Guide

## For DJs

### What's Changing
- Improved real-time performance
- Better reliability and uptime
- Enhanced security features

### Action Required
1. **Password Reset**: You'll need to reset your password after migration
2. **Apple Music**: Reconnect your Apple Music account
3. **Profile**: Verify your profile information

### Timeline
- **Migration Date**: [Date]
- **Downtime**: Approximately 2 hours
- **Testing Period**: 1 week with rollback capability

## For Audience Members

### What's Changing
- Faster song search and request submission
- More reliable real-time updates
- Improved mobile experience

### Action Required
- No action required
- Continue using QR codes as normal
- Report any issues to support
```

## 17. Success Metrics

### 17.1 Technical Metrics

```typescript
// Define success criteria for migration
export const migrationMetrics = {
  performance: {
    queryResponseTime: { target: "<500ms", current: "TBD" },
    realTimeLatency: { target: "<100ms", current: "TBD" },
    uptime: { target: "99.9%", current: "TBD" },
  },
  functionality: {
    featureParity: { target: "100%", current: "TBD" },
    dataIntegrity: { target: "100%", current: "TBD" },
    userSatisfaction: { target: ">95%", current: "TBD" },
  },
  business: {
    userRetention: { target: ">98%", current: "TBD" },
    subscriptionRetention: { target: ">99%", current: "TBD" },
    supportTickets: { target: "<5% increase", current: "TBD" },
  },
};
```

### 17.2 Monitoring Dashboard

```typescript
// Real-time monitoring dashboard
export const getMigrationStatus = query({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    const last24Hours = now - (24 * 60 * 60 * 1000);

    const metrics = {
      activeUsers: await ctx.db
        .query("djProfiles")
        .filter((q) => q.gt(q.field("lastLoginAt"), last24Hours))
        .collect()
        .then(profiles => profiles.length),

      activeSessions: await ctx.db
        .query("sessions")
        .withIndex("by_active", (q) => q.eq("active", true))
        .collect()
        .then(sessions => sessions.length),

      requestsToday: await ctx.db
        .query("songRequests")
        .filter((q) => q.gt(q.field("createdAt"), last24Hours))
        .collect()
        .then(requests => requests.length),

      errorRate: await calculateErrorRate(last24Hours),
      avgResponseTime: await calculateAvgResponseTime(last24Hours),
    };

    return {
      ...metrics,
      status: determineOverallStatus(metrics),
      lastUpdated: now,
    };
  },
});
```

## 18. Final Checklist

### Pre-Migration
- [ ] Convex project setup and configuration
- [ ] Schema definition and validation
- [ ] Core function implementation
- [ ] Authentication system setup
- [ ] File storage solution configured
- [ ] Third-party integrations tested
- [ ] Data migration scripts prepared
- [ ] Backup procedures verified
- [ ] Rollback plan documented
- [ ] Team training completed

### During Migration
- [ ] Data export from Supabase
- [ ] Data transformation and validation
- [ ] Data import to Convex
- [ ] DNS and routing updates
- [ ] Real-time monitoring active
- [ ] Support team on standby
- [ ] Communication sent to users
- [ ] Feature flags configured
- [ ] Performance metrics baseline

### Post-Migration
- [ ] Data integrity verification
- [ ] Feature functionality testing
- [ ] Performance optimization
- [ ] User feedback collection
- [ ] Support ticket monitoring
- [ ] Cost analysis and optimization
- [ ] Documentation updates
- [ ] Team retrospective
- [ ] Success metrics evaluation
- [ ] Supabase resource cleanup

This comprehensive migration plan ensures a smooth transition from Supabase to Convex while maintaining the high-quality user experience that PlayBeg users expect. The phased approach, extensive testing, and careful monitoring will minimize risks and maximize the benefits of the new architecture.
