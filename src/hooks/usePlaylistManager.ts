import { useState, useCallback, useRef, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import { createPlaylist as createAppleMusicPlaylist } from '@/utils/appleMusicService';
import { Playlist } from '@/types/playlist';

const FETCH_COOLDOWN = 5000; // 5 seconds cooldown between fetches

export function usePlaylistManager(userId?: string, onPlaylistSelected?: (playlistId: string | null) => void) {
  const { toast } = useToast();
  const [playlists, setPlaylists] = useState<Playlist[]>([]);
  const [selectedPlaylistId, setSelectedPlaylistId] = useState<string | null>(null);
  const [isPlaylistsLoading, setIsPlaylistsLoading] = useState(true);
  const [isCreatingPlaylist, setIsCreatingPlaylist] = useState(false);
  const [playlist, setPlaylist] = useState<Playlist | null>(null);
  const [isActivatingPlaylist, setIsActivatingPlaylist] = useState(false);
  const [activePlaylist, setActivePlaylist] = useState<Playlist | null>(null);
  const [isDeletingPlaylist, setIsDeletingPlaylist] = useState(false);
  const [playlistToDelete, setPlaylistToDelete] = useState<Playlist | null>(null);

  // Use refs to track component state
  const isMountedRef = useRef(true);
  const fetchingRef = useRef(false);
  const initializationRef = useRef(false);
  const lastFetchRef = useRef<number>(0);

  // Cleanup on unmount
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const fetchPlaylists = useCallback(async (forceRefresh = false) => {
    const now = Date.now();

    if ((fetchingRef.current && !forceRefresh) || !isMountedRef.current) return;
    if (initializationRef.current && !forceRefresh) return;
    if (!forceRefresh && (now - lastFetchRef.current) < FETCH_COOLDOWN) return;

    fetchingRef.current = true;
    lastFetchRef.current = now;
    setIsPlaylistsLoading(true);

    try {
      const { data, error } = await supabase
        .from("playlists")
        .select("*")
        .eq("user_id", userId)
        .order("created_at", { ascending: false });

      if (error) throw error;

      if (!isMountedRef.current) return;

      const playlistsData = data as Playlist[];
      setPlaylists(playlistsData);

      // Find active playlist
      const active = playlistsData.find(p => p.active);
      if (active) {
        setActivePlaylist(active);
        setSelectedPlaylistId(active.id);
        setPlaylist(active);
        onPlaylistSelected?.(active.id);
      }
    } catch (error) {
      console.error('Error fetching playlists:', error);
      if (isMountedRef.current) {
        toast({
          title: "Error",
          description: "Failed to fetch playlists. Please try again.",
          variant: "destructive",
        });
      }
    } finally {
      if (isMountedRef.current) {
        setIsPlaylistsLoading(false);
        initializationRef.current = true;
      }
      fetchingRef.current = false;
    }
  }, [userId, onPlaylistSelected, toast]);

  // Initial fetch
  useEffect(() => {
    if (!initializationRef.current) {
      fetchPlaylists();
    }

    if (initializationRef.current) {
      const handleVisibilityChange = () => {
        if (document.visibilityState === 'visible' && isMountedRef.current) {
          fetchPlaylists(true);
        }
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);
      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      };
    }
  }, [fetchPlaylists]);

  const handleNewPlaylist = (newPlaylist: Playlist) => {
    if (newPlaylist.active) {
      setActivePlaylist(newPlaylist);
    }

    setPlaylists([newPlaylist, ...playlists]);
    setSelectedPlaylistId(newPlaylist.id);
    setPlaylist(newPlaylist);
    onPlaylistSelected?.(newPlaylist.id);
  };

  const createPlaylist = useCallback(async (name: string, description: string, isAuthorized: boolean, sessionId?: string) => {
    if (!name.trim()) {
      toast({
        title: "Error",
        description: "Playlist name cannot be empty.",
        variant: "destructive",
      });
      return;
    }

    if (!isAuthorized) {
      toast({
        title: "Error",
        description: "You need to connect Apple Music first to create playlists.",
        variant: "destructive",
      });
      return;
    }

    setIsCreatingPlaylist(true);
    try {
      if (!userId) return;

      let appleMusicPlaylist;
      try {
        appleMusicPlaylist = await createAppleMusicPlaylist(name, description || "");

        if (!appleMusicPlaylist) {
          throw new Error("Failed to create Apple Music playlist - null response");
        }
      } catch (error) {
        console.error("Error creating Apple Music playlist:", error);
        toast({
          title: "Error",
          description: typeof error === 'string' ? error : "Failed to create playlist in Apple Music. Please try again.",
          variant: "destructive",
        });
        return;
      }

      const { data: rpcData, error: rpcError } = await supabase.rpc(
        'insert_playlist_with_apple_id',
        {
          p_user_id: userId,
          p_name: name,
          p_description: description || null,
          p_apple_music_id: appleMusicPlaylist.id
        }
      );

      if (rpcError) {
        const playlistData = {
          user_id: userId,
          name,
          description: description || null,
          apple_music_playlist_id: appleMusicPlaylist.id,
          active: !activePlaylist
        };

        const { data, error } = await supabase
          .from("playlists")
          .insert([playlistData])
          .select();

        if (error) throw error;

        if (data && data.length > 0) {
          const newPlaylist = data[0] as Playlist;
          handleNewPlaylist(newPlaylist);
        }
      } else {
        if (!activePlaylist) {
          await supabase
            .from("playlists")
            .update({ active: true })
            .eq("id", rpcData);
        }

        const { data: newPlaylist, error: fetchError } = await supabase
          .from("playlists")
          .select("*")
          .eq("id", rpcData)
          .maybeSingle();

        if (fetchError) {
          console.error("Error fetching new playlist:", fetchError);
        } else if (newPlaylist) {
          handleNewPlaylist(newPlaylist as Playlist);
        }
      }

      // If we have a session ID, update the session with the playlist information
      if (sessionId && appleMusicPlaylist && appleMusicPlaylist.id) {
        console.log(`Updating session ${sessionId} with playlist information`);

        // Get the playlist ID from the database
        let playlistId = rpcData;

        if (!playlistId && data && data.length > 0) {
          playlistId = data[0].id;
        }

        if (playlistId) {
          const { error: sessionError } = await supabase
            .from("sessions")
            .update({
              apple_music_playlist_id: appleMusicPlaylist.id,
              playlist_id: playlistId
            })
            .eq("id", sessionId);

          if (sessionError) {
            console.error("Error updating session with playlist information:", sessionError);
          } else {
            console.log(`Successfully updated session ${sessionId} with playlist information`);
          }
        } else {
          console.error("Could not find playlist ID to update session");
        }
      }

      toast({
        title: "Success",
        description: "Playlist created successfully in Apple Music!",
      });

    } catch (error) {
      console.error("Error creating playlist:", error);
      toast({
        title: "Error",
        description: "Failed to create playlist. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreatingPlaylist(false);
    }
  }, [userId, activePlaylist, toast, handleNewPlaylist]);



  const activatePlaylist = useCallback(async (playlistId: string, sessionId?: string) => {
    setIsActivatingPlaylist(true);
    try {
      // First, get the playlist details
      const { data: playlistData, error: playlistError } = await supabase
        .from("playlists")
        .select("*")
        .eq("id", playlistId)
        .single();

      if (playlistError) throw playlistError;

      // Update the playlist to be active
      const { error } = await supabase
        .from("playlists")
        .update({ active: true })
        .eq("id", playlistId);

      if (error) throw error;

      const updatedPlaylists = playlists.map(p => ({
        ...p,
        active: p.id === playlistId
      }));

      setPlaylists(updatedPlaylists);

      const newActive = updatedPlaylists.find(p => p.id === playlistId);
      if (newActive) {
        setActivePlaylist(newActive);
      }

      if (playlist && playlist.id === playlistId) {
        setPlaylist({
          ...playlist,
          active: true
        });
      }

      // If we have a session ID, update the session with the playlist information
      if (sessionId) {
        console.log(`Updating session ${sessionId} with playlist information`);
        const { error: sessionError } = await supabase
          .from("sessions")
          .update({
            apple_music_playlist_id: playlistData.apple_music_playlist_id,
            playlist_id: playlistId
          })
          .eq("id", sessionId);

        if (sessionError) {
          console.error("Error updating session with playlist information:", sessionError);
        } else {
          console.log(`Successfully updated session ${sessionId} with playlist information`);
        }
      }

      toast({
        title: "Success",
        description: "Playlist activated successfully!",
      });
    } catch (error) {
      console.error("Error activating playlist:", error);
      toast({
        title: "Error",
        description: "Failed to activate playlist. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsActivatingPlaylist(false);
    }
  }, [playlists, playlist, toast]);

  const deactivatePlaylist = useCallback(async (playlistId: string) => {
    try {
      const { error } = await supabase
        .from("playlists")
        .update({ active: false })
        .eq("id", playlistId);

      if (error) throw error;

      const updatedPlaylists = playlists.map(p => ({
        ...p,
        active: p.id === playlistId ? false : p.active
      }));

      setPlaylists(updatedPlaylists);
      setActivePlaylist(null);

      if (playlist && playlist.id === playlistId) {
        setPlaylist({
          ...playlist,
          active: false
        });
      }

      toast({
        title: "Success",
        description: "Playlist deactivated successfully.",
      });
    } catch (error) {
      console.error("Error deactivating playlist:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    }
  }, [playlists, playlist, toast]);

  const deletePlaylist = useCallback(async (playlistId: string) => {
    try {
      setIsDeletingPlaylist(true);

      const { error } = await supabase
        .from("playlists")
        .delete()
        .eq("id", playlistId);

      if (error) throw error;

      const updatedPlaylists = playlists.filter(p => p.id !== playlistId);
      setPlaylists(updatedPlaylists);

      if (selectedPlaylistId === playlistId) {
        setSelectedPlaylistId(null);
        setPlaylist(null);
        onPlaylistSelected?.(null);
      }

      if (activePlaylist?.id === playlistId) {
        setActivePlaylist(null);
      }

      toast({
        title: "Success",
        description: "Playlist deleted successfully from PlayBeg database.",
      });

      setPlaylistToDelete(null);
    } catch (error) {
      console.error("Error deleting playlist:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeletingPlaylist(false);
    }
  }, [playlists, selectedPlaylistId, activePlaylist, onPlaylistSelected, toast]);

  const selectPlaylist = useCallback((playlist: Playlist) => {
    setSelectedPlaylistId(playlist.id);
    setPlaylist(playlist);
  }, []);

  return {
    playlists,
    selectedPlaylistId,
    isPlaylistsLoading,
    isCreatingPlaylist,
    playlist,
    isActivatingPlaylist,
    activePlaylist,
    isDeletingPlaylist,
    playlistToDelete,
    setPlaylistToDelete,
    fetchPlaylists,
    createPlaylist,
    activatePlaylist,
    deletePlaylist,
    selectPlaylist,
  };
}