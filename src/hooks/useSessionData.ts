import { useState, useEffect, useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";
import { SessionWithSettings } from "@/types/session";
import { Tables } from "@/integrations/supabase/types";
import { AppleMusicTokenService } from "@/services/AppleMusicTokenService";

interface AppleMusicTokens {
  developerToken: string;
  userToken: string | null;
  isAuthorized: boolean;
}

interface SessionDataResult {
  session: SessionWithSettings | null;
  requests: Tables<"song_requests">[];
  sessionNotFound: boolean;
  isLoadingSession: boolean;
  isLoadingRequests: boolean;
  refetchSession: () => Promise<void>;
  refetchRequests: () => Promise<void>;
  setRequests: React.Dispatch<React.SetStateAction<Tables<"song_requests">[]>>;

  // For SongRequest.tsx compatibility
  sessionName: string | null;
  isSessionActive: boolean;
  isAcceptingRequests: boolean;
  isLoading: boolean;
  appleMusicTokens: AppleMusicTokens;
  debugInfo: string[];
  shouldShowTutorial: boolean;
  addDebugInfo: (info: string) => void;
  maxRequestsPerUser: number;
  sponsorHeader: string;
  sponsorMessage: string;
  sponsorLogoUrl: string | null;
  sponsorUrl: string | null;
  hasSponsor: boolean;
}

export function useSessionData(sessionId: string | null, isFirstVisit?: boolean): SessionDataResult {
  const [session, setSession] = useState<SessionWithSettings | null>(null);
  const [requests, setRequests] = useState<Tables<"song_requests">[]>([]);
  const [sessionNotFound, setSessionNotFound] = useState(false);
  const [isLoadingSession, setIsLoadingSession] = useState(true);
  const [isLoadingRequests, setIsLoadingRequests] = useState(true);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);

  // Default values for compatibility
  const isSessionActive = session?.active ?? false;
  const isAcceptingRequests = session?.accept_requests !== false; // Default to true if undefined
  const sessionName = session?.name ?? null;
  const maxRequestsPerUser = session?.max_requests_per_user ?? 3;
  const shouldShowTutorial = Boolean(isFirstVisit);
  const sponsorHeader = session?.sponsor_header ?? '';
  const sponsorMessage = session?.sponsor_message ?? '';
  const sponsorLogoUrl = session?.sponsor_logo_url ?? null;
  const sponsorUrl = session?.sponsor_url ?? null;
  const hasSponsor = Boolean(sponsorHeader || sponsorMessage || sponsorLogoUrl);

  // Default Apple Music tokens
  const [appleMusicTokens, setAppleMusicTokens] = useState<AppleMusicTokens>({
    developerToken: '',
    userToken: null,
    isAuthorized: false
  });

  const addDebugInfo = useCallback((info: string) => {
    setDebugInfo(prev => [...prev, info]);
  }, []);

  const fetchSession = useCallback(async () => {
    if (!sessionId) {
      setIsLoadingSession(false);
      return;
    }

    setIsLoadingSession(true);
    try {
      const { data, error } = await supabase
        .from('sessions')
        .select('*')
        .eq('id', sessionId)
        .maybeSingle();

      if (error) {
        console.error("Error fetching session:", error);
        setSessionNotFound(true);
        return;
      }

      if (!data) {
        setSessionNotFound(true);
        return;
      }

      setSession(data as SessionWithSettings);
      setSessionNotFound(false);

      // Fetch Apple Music tokens when session is loaded
      if (data && sessionId) {
        try {
          // Use the centralized token service to get the token
          addDebugInfo("Fetching Apple Music token using centralized service");

          const token = await AppleMusicTokenService.getTokenForSession(sessionId);

          if (token) {
            setAppleMusicTokens({
              developerToken: token,
              userToken: null,
              isAuthorized: true
            });
            addDebugInfo("Successfully retrieved Apple Music token");
          } else {
            addDebugInfo("No valid Apple Music token found");
            setAppleMusicTokens({
              developerToken: '',
              userToken: null,
              isAuthorized: false
            });
          }
        } catch (tokenErr) {
          console.error("Error fetching Apple Music token:", tokenErr);
          addDebugInfo(`Error fetching Apple Music token: ${tokenErr.message}`);

          // Set tokens to empty state on error
          setAppleMusicTokens({
            developerToken: '',
            userToken: null,
            isAuthorized: false
          });
        }
      }

    } catch (err) {
      console.error("Error in fetchSession:", err);
      setSessionNotFound(true);
    } finally {
      setIsLoadingSession(false);
    }
  }, [sessionId, addDebugInfo]);

  const fetchRequests = useCallback(async () => {
    if (!sessionId) {
      setIsLoadingRequests(false);
      return;
    }

    setIsLoadingRequests(true);
    try {
      const { data, error } = await supabase
        .from('song_requests')
        .select('*')
        .eq('session_id', sessionId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error("Error fetching requests:", error);
        return;
      }

      setRequests(data || []);
    } catch (err) {
      console.error("Error in fetchRequests:", err);
    } finally {
      setIsLoadingRequests(false);
    }
  }, [sessionId]);

  useEffect(() => {
    fetchSession();
    fetchRequests();
  }, [fetchSession, fetchRequests]);

  const isLoading = isLoadingSession || isLoadingRequests;

  return {
    session,
    requests,
    sessionNotFound,
    isLoadingSession,
    isLoadingRequests,
    refetchSession: fetchSession,
    refetchRequests: fetchRequests,
    setRequests,

    // Properties required by SongRequest.tsx
    sessionName,
    isSessionActive,
    isAcceptingRequests,
    isLoading,
    appleMusicTokens,
    debugInfo,
    shouldShowTutorial,
    addDebugInfo,
    maxRequestsPerUser,
    sponsorHeader,
    sponsorMessage,
    sponsorLogoUrl,
    sponsorUrl,
    hasSponsor
  };
}
