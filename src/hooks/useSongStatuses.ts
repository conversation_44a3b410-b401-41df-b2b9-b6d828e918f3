
import { useState, useEffect, useRef } from "react";
import { supabase } from "@/integrations/supabase/client";
import { SongSearchResult } from "@/utils/songSearchService";
import { addSongToPlaylist, addSongToPlaylistDirect } from "@/utils/appleMusicService";
import { useToast } from "@/components/ui/use-toast";
import { RealtimeChannel } from "@supabase/supabase-js";
import { subscribeToSessionEvents, unsubscribeFromSessionEvents } from "@/utils/sessionEventService";

export function useSongStatuses(sessionId: string | null, lastRequestId: string | null, selectedPlaylistId: string | null = null) {
  const [songStatuses, setSongStatuses] = useState<Record<string, string>>({});
  const [playlistData, setPlaylistData] = useState<{id: string, apple_music_playlist_id: string} | null>(null);
  const { toast } = useToast();

  // Use a ref to track the session events channel
  const sessionEventsChannelRef = useRef<RealtimeChannel | null>(null);

  // Fetch playlist details when selectedPlaylistId changes
  useEffect(() => {
    if (!selectedPlaylistId) {
      setPlaylistData(null);
      return;
    }

    const fetchPlaylistDetails = async () => {
      try {
        const { data, error } = await supabase
          .from('playlists')
          .select('id, apple_music_playlist_id')
          .eq('id', selectedPlaylistId)
          .maybeSingle();

        if (error) {
          console.error('Error fetching playlist details:', error);
          return;
        }

        if (data && data.apple_music_playlist_id) {
          console.log(`Loaded playlist details: ID ${data.id}, Apple Music ID: ${data.apple_music_playlist_id}`);
          setPlaylistData(data);
        } else {
          console.error('No Apple Music ID found for playlist');
          setPlaylistData(null);
        }
      } catch (error) {
        console.error("Error fetching playlist details:", error);
        setPlaylistData(null);
      }
    };

    fetchPlaylistDetails();
  }, [selectedPlaylistId]);

  // Fetch song statuses and set up realtime listeners
  useEffect(() => {
    if (!sessionId) return;

    const fetchSongStatuses = async () => {
      try {
        const { data, error } = await supabase
          .from('song_requests')
          .select('apple_music_id, status')
          .eq('session_id', sessionId);

        if (error) throw error;

        if (data && data.length > 0) {
          const statusMap: Record<string, string> = {};
          data.forEach(item => {
            if (item.apple_music_id) {
              // Keep the database status value for consistency
              statusMap[item.apple_music_id] = item.status;
            }
          });
          setSongStatuses(statusMap);
        }
      } catch (error) {
        console.error("Error fetching song statuses:", error);
      }
    };

    fetchSongStatuses();

    // Cleanup any existing channel
    if (sessionEventsChannelRef.current) {
      unsubscribeFromSessionEvents(sessionEventsChannelRef.current);
      sessionEventsChannelRef.current = null;
    }

    // Subscribe to session events as DJ role
    const channel = subscribeToSessionEvents(
      {
        sessionId,
        role: 'dj',
      },
      {
        onRequestCreated: (payload) => {
          console.log('onRequestCreated called with payload:', payload);

          // Add the new song to our status tracking
          if (payload && payload.apple_music_id) {
            console.log('Song has Apple Music ID:', payload.apple_music_id);

            setSongStatuses(prev => ({
              ...prev,
              [payload.apple_music_id]: payload.status
            }));

            // If the song was approved or auto-approved and has Apple Music ID,
            // we need to add it to the playlist
            if (payload.status === 'approved' || payload.status === 'auto-approved') {
              console.log('Auto-approved song detected, checking if it needs to be added to playlist:', payload);

              // Check if this song is already marked as added to playlist
              const isAlreadyAddedToPlaylist = payload.added_to_playlist === true;
              console.log('Is song already marked as added to playlist?', isAlreadyAddedToPlaylist);

              // For auto-approved songs in free tier sessions, we need to ensure they get added to the playlist
              // even if they're from a second user
              if (payload.status === 'auto-approved' && !isAlreadyAddedToPlaylist) {
                console.log('This is an auto-approved song that needs to be added to the playlist');

                // Check if the song is already being processed by the useRequestSubmission hook
                // This prevents duplicate calls to the Edge Function
                (async () => {
                  try {
                    // First, mark this request as being processed to prevent race conditions
                    const { error: updateError } = await supabase
                      .from('song_requests')
                      .update({ processing_playlist: true })
                      .eq('id', payload.id);

                    if (updateError) {
                      console.error('Error marking request as processing:', updateError);
                    } else {
                      console.log('Successfully marked request as processing');
                    }
                  } catch (error) {
                    console.error('Error updating processing flag:', error);
                  }
                })();
              }

              // Use the playlist ID from the payload or the selected playlist ID
              let playlistIdToUse = payload.playlist_id || selectedPlaylistId;
              let appleMusicPlaylistIdToUse = payload.apple_music_playlist_id;
              console.log('Playlist ID to use:', playlistIdToUse);
              console.log('Apple Music Playlist ID to use:', appleMusicPlaylistIdToUse);

              // If the playlist IDs are missing, try to find them
              if ((!playlistIdToUse || !appleMusicPlaylistIdToUse) && sessionId) {
                console.log('Playlist IDs are missing. Attempting to find them...');

                (async () => {
                  try {
                    // Get the DJ's user_id for this session
                    const { data: sessionData, error: sessionError } = await supabase
                      .from('sessions')
                      .select('dj_id')
                      .eq('id', sessionId)
                      .maybeSingle();

                    if (sessionError) {
                      console.error('Error getting DJ ID for session:', sessionError);
                      throw new Error('Could not get DJ ID for session');
                    }

                    const djId = sessionData.dj_id;
                    console.log(`Found DJ ID ${djId} for session ${sessionId}`);

                    // First try to get the active playlist for this DJ
                    const { data: activePlaylist, error: activePlaylistError } = await supabase
                      .from('playlists')
                      .select('id, apple_music_playlist_id')
                      .eq('user_id', djId)
                      .eq('active', true)
                      .maybeSingle();

                    if (!activePlaylistError && activePlaylist) {
                      console.log(`Found active playlist: id=${activePlaylist.id}, apple_music_id=${activePlaylist.apple_music_playlist_id}`);
                      playlistIdToUse = activePlaylist.id;
                      appleMusicPlaylistIdToUse = activePlaylist.apple_music_playlist_id;

                      // Update the request with the correct playlist IDs
                      const { error: updateError } = await supabase
                        .from('song_requests')
                        .update({
                          playlist_id: activePlaylist.id,
                          apple_music_playlist_id: activePlaylist.apple_music_playlist_id
                        })
                        .eq('id', payload.id);

                      if (updateError) {
                        console.error('Error updating request with playlist IDs:', updateError);
                      } else {
                        console.log('Successfully updated request with playlist IDs');
                      }
                    } else {
                      console.log('No active playlist found, checking for any playlist in this session');

                      // If no active playlist, try to get any playlist for this DJ
                      const { data: anyPlaylist, error: anyPlaylistError } = await supabase
                        .from('playlists')
                        .select('id, apple_music_playlist_id')
                        .eq('user_id', djId)
                        .order('created_at', { ascending: false })
                        .limit(1)
                        .maybeSingle();

                      if (!anyPlaylistError && anyPlaylist) {
                        console.log(`Using most recent playlist: id=${anyPlaylist.id}, apple_music_id=${anyPlaylist.apple_music_playlist_id}`);
                        playlistIdToUse = anyPlaylist.id;
                        appleMusicPlaylistIdToUse = anyPlaylist.apple_music_playlist_id;

                        // Update the request with the correct playlist IDs
                        const { error: updateError } = await supabase
                          .from('song_requests')
                          .update({
                            playlist_id: anyPlaylist.id,
                            apple_music_playlist_id: anyPlaylist.apple_music_playlist_id
                          })
                          .eq('id', payload.id);

                        if (updateError) {
                          console.error('Error updating request with playlist IDs:', updateError);
                        } else {
                          console.log('Successfully updated request with playlist IDs');
                        }
                      } else {
                        console.log('No playlists found for this session');
                      }
                    }
                  } catch (error) {
                    console.error('Error fetching playlist information:', error);
                  }
                })();
              }

              if (playlistIdToUse) {
                // Log the playlist ID from the payload
                if (!selectedPlaylistId && payload.playlist_id) {
                  console.log('Using playlist ID from payload:', payload.playlist_id);
                  // We don't need to set selectedPlaylistId here since we're using playlistIdToUse directly
                }

                // Create a song object for the updateSongStatus function
                const song = {
                  id: payload.apple_music_id,
                  title: payload.song_title || 'Unknown Title',
                  artist: payload.artist_name || 'Unknown Artist',
                  artwork: payload.album_artwork || ''
                };

                console.log('Created song object for updateSongStatus:', song);

                // Store the request ID and playlist ID for reference
                console.log('Request ID for reference:', payload.id);
                console.log('Apple Music playlist ID from payload:', payload.apple_music_playlist_id);
                console.log('Playlist ID from payload:', payload.playlist_id);

                // Call updateSongStatus to add the song to the Apple Music playlist
                // This function will check if the song has already been added to the playlist
                // Pass the request ID directly
                console.log('Calling updateSongStatus with auto-approved song and request ID:', payload.id);
                updateSongStatus(song, payload.status, payload.id);
              } else {
                console.log('No playlist ID available for auto-approved song');
              }
            } else {
              console.log('Song not approved. Status:', payload.status);
            }
          } else {
            console.log('Song does not have Apple Music ID:', payload);
          }
        },
        onRequestUpdated: (payload) => {
          // Update status when a song request is updated
          if (payload && payload.apple_music_id) {
            setSongStatuses(prev => ({
              ...prev,
              [payload.apple_music_id]: payload.status
            }));
          }
        },
        onRequestDeleted: (requestId) => {
          // We don't remove statuses when requests are deleted
          // because we want to preserve the status for the song
          console.log('Song request deleted:', requestId);
        }
      }
    );

    // Store the channel reference for cleanup
    sessionEventsChannelRef.current = channel;

    return () => {
      // Cleanup on unmount
      unsubscribeFromSessionEvents(sessionEventsChannelRef.current);
      sessionEventsChannelRef.current = null;
    };
  }, [sessionId]);

  // Function to update the song status and add to playlist if approved
  const updateSongStatus = async (song: SongSearchResult | null, status: string, requestId?: string) => {
    console.log('updateSongStatus called with song:', song, 'status:', status, 'requestId:', requestId);

    if (!song?.id) {
      console.log('No song ID provided, returning');
      return;
    }

    // Store the request ID for later use
    const requestIdToUse = requestId || lastRequestId;
    console.log('Using request ID:', requestIdToUse);

    if (!requestIdToUse) {
      console.warn('No request ID available for tracking this song update');
    }

    // Update UI state immediately
    setSongStatuses(prev => ({
      ...prev,
      [song.id]: status
    }));

    console.log('Updated song status in UI state');

    // If song is being approved and we have a playlist, add it to the playlist
    if (status === 'approved') { // Auto-approval feature has been removed
      console.log('Song is approved, checking if it needs to be added to a playlist');
      console.log('Status type:', status, '- Auto-approved songs from free tier sessions need special handling');

      // Check if the song is already being processed by another part of the code
      if (requestIdToUse) {
        // Check for the needs_playlist_processing flag (added in the latest update)
        try {
          const { data: processingCheck, error: processingCheckError } = await supabase
            .from('song_requests')
            .select('added_to_playlist, processing_playlist, needs_playlist_processing')
            .eq('id', requestIdToUse)
            .maybeSingle();

          if (!processingCheckError) {
            // If the song is already added to playlist or being processed, skip
            if (processingCheck?.added_to_playlist === true || processingCheck?.processing_playlist === true) {
              console.log('Song is already being processed or added to playlist, skipping duplicate processing');
              return;
            }

            // If the needs_playlist_processing flag is set, this is a high priority for processing
            // This flag is set by useRequestSubmission to indicate that it skipped adding to playlist
            if (processingCheck?.needs_playlist_processing === true) {
              console.log('Found needs_playlist_processing flag set to true, prioritizing this song for playlist addition');
            }
          }
        } catch (error) {
          // If there's an error (like the column doesn't exist yet), just continue
          console.log('Error checking processing status, column might not exist yet:', error);
        }

        // Mark this request as being processed to prevent race conditions
        const { error: updateError } = await supabase
          .from('song_requests')
          .update({ processing_playlist: true })
          .eq('id', requestIdToUse);

        if (updateError) {
          console.error('Error marking request as processing:', updateError);
        } else {
          console.log('Successfully marked request as processing');
        }
      }
      try {
        // First, check if the song has already been added to the playlist
        // by checking if any other request for this song has already been approved
        let query = supabase
          .from('song_requests')
          .select('id, added_to_playlist, apple_music_playlist_id')
          .eq('apple_music_id', song.id)
          .eq('session_id', sessionId)
          .in('status', ['approved', 'auto-approved'])
          .eq('added_to_playlist', true) // Only consider requests that were actually added to the playlist
          .order('created_at', { ascending: false })
          .limit(1);

        console.log('Checking if song has already been added to playlist. Session ID:', sessionId, 'Apple Music ID:', song.id);

        // Only add the neq condition if requestIdToUse is not null
        if (requestIdToUse) {
          console.log('Excluding current request ID:', requestIdToUse);
          query = query.neq('id', requestIdToUse); // Exclude the current request being processed
        }

        const { data: existingApprovals, error: approvalCheckError } = await query;

        console.log('Existing approvals query result:', existingApprovals, 'Error:', approvalCheckError);

        if (approvalCheckError) {
          console.error('Error checking for existing approvals:', approvalCheckError);
        }

        // Check if this song was already approved and added to the playlist before
        const hasBeenAddedToPlaylist = existingApprovals &&
                                     existingApprovals.length > 0 &&
                                     existingApprovals[0] &&
                                     existingApprovals[0].added_to_playlist === true;

        console.log('Has been added to playlist:', hasBeenAddedToPlaylist, 'Existing approvals:', existingApprovals);

        // Double-check the current request to see if it's already marked as added
        let currentRequestAlreadyAdded = false;
        if (requestIdToUse) {
          const { data: currentRequest, error: currentRequestError } = await supabase
            .from('song_requests')
            .select('added_to_playlist')
            .eq('id', requestIdToUse)
            .maybeSingle();

          console.log('Current request added_to_playlist status:', currentRequest?.added_to_playlist, 'Error:', currentRequestError);
          currentRequestAlreadyAdded = currentRequest?.added_to_playlist === true;
        }

        // If the song has already been added to the playlist by another request,
        // we'll mark this request as added but won't call Apple Music API again
        if (hasBeenAddedToPlaylist) {
          console.log('Song was already added to playlist previously');

          // Only update the database if this specific request isn't already marked as added
          if (!currentRequestAlreadyAdded && requestIdToUse) {
            console.log('Marking current request as added to playlist. Request ID:', requestIdToUse);

            // Update all requests for this song in this session to ensure consistency
            const { data: updateResult, error: updateError } = await supabase
              .from('song_requests')
              .update({ added_to_playlist: true })
              .eq('apple_music_id', song.id)
              .eq('session_id', sessionId)
              .in('status', ['approved', 'auto-approved']);

            console.log('Update result:', updateResult, 'Error:', updateError);
          } else {
            console.log('Current request is already marked as added to playlist or no request ID available');
          }

          return;
        }

        // If the current request is already marked as added, we can skip the Apple Music API call
        if (currentRequestAlreadyAdded) {
          console.log('Current request is already marked as added to playlist, skipping Apple Music API call');
          return;
        }

        // Proceed with normal checks and adding to playlist
        console.log('Proceeding with adding song to playlist');

        // First, try to get the Apple Music playlist ID from this specific request
        const { data: thisRequest, error: thisRequestError } = await supabase
          .from('song_requests')
          .select('apple_music_playlist_id, session_id')
          .eq('id', requestId)
          .maybeSingle();

        console.log('This request data:', thisRequest, 'Error:', thisRequestError);

        // Use the song's stored apple_music_playlist_id if available
        let appleMusicPlaylistId = thisRequest?.apple_music_playlist_id || null;
        console.log('Apple Music playlist ID from this request:', appleMusicPlaylistId);

        // If not found in this request, try to get it from the session first (prioritize session)
        if (!appleMusicPlaylistId && thisRequest?.session_id) {
          console.log('No playlist ID in request, checking session first');

          const { data: sessionData, error: sessionError } = await supabase
            .from('sessions')
            .select('apple_music_playlist_id, playlist_id')
            .eq('id', thisRequest.session_id)
            .maybeSingle();

          if (!sessionError && sessionData?.apple_music_playlist_id) {
            appleMusicPlaylistId = sessionData.apple_music_playlist_id;
            console.log('Using playlist ID from session:', appleMusicPlaylistId);

            // Update the request with the correct playlist ID
            if (requestId) {
              await supabase
                .from('song_requests')
                .update({
                  apple_music_playlist_id: appleMusicPlaylistId,
                  playlist_id: sessionData.playlist_id
                })
                .eq('id', requestId);

              console.log('Updated request with playlist ID from session');
            }
          }
        }

        // If not found in this request, try to get from other recent requests for this song
        if (!appleMusicPlaylistId) {
          const { data: recentRequests, error: recentRequestsError } = await supabase
            .from('song_requests')
            .select('apple_music_playlist_id')
            .eq('apple_music_id', song.id)
            .eq('session_id', sessionId)
            .order('created_at', { ascending: false })
            .limit(1);

          console.log('Recent requests for this song:', recentRequests, 'Error:', recentRequestsError);

          // Use the song's stored apple_music_playlist_id if available
          appleMusicPlaylistId = recentRequests && recentRequests.length > 0 ? recentRequests[0].apple_music_playlist_id : null;
          console.log('Apple Music playlist ID from recent request:', appleMusicPlaylistId);
        }

        // If not available from recent requests, try to get it from the active session
        if (!appleMusicPlaylistId) {
          const { data: sessionData, error: sessionError } = await supabase
            .from('sessions')
            .select('active_playlist_id')
            .eq('id', sessionId)
            .single();

          console.log('Session data for active playlist:', sessionData, 'Error:', sessionError);

          if (!sessionError && sessionData?.active_playlist_id) {
            // Get the Apple Music playlist ID for the active playlist
            const { data: activePlaylistData, error: activePlaylistError } = await supabase
              .from('playlists')
              .select('apple_music_playlist_id')
              .eq('id', sessionData.active_playlist_id)
              .single();

            console.log('Active playlist data:', activePlaylistData, 'Error:', activePlaylistError);

            if (!activePlaylistError && activePlaylistData?.apple_music_playlist_id) {
              appleMusicPlaylistId = activePlaylistData.apple_music_playlist_id;
              console.log('Found Apple Music playlist ID from active session playlist:', appleMusicPlaylistId);
            }
          }
        }

        // If still not available, check if we have playlist data cached
        if (!appleMusicPlaylistId && selectedPlaylistId) {
          // Check if we already have the playlist data cached
          appleMusicPlaylistId = playlistData?.apple_music_playlist_id;
          console.log('Apple Music playlist ID from cached playlist data:', appleMusicPlaylistId);

          // If not, fetch it from the database
          if (!appleMusicPlaylistId) {
            console.log(`Getting playlist data for ${selectedPlaylistId}`);
            const { data, error } = await supabase
              .from('playlists')
              .select('apple_music_playlist_id')
              .eq('id', selectedPlaylistId)
              .single();

            console.log('Playlist data from database:', data, 'Error:', error);

            if (error) {
              console.error('Error fetching playlist details:', error);
              toast({
                title: "Playlist error",
                description: "Couldn't find the Apple Music playlist ID.",
                variant: "destructive"
              });
              return;
            }

            appleMusicPlaylistId = data.apple_music_playlist_id;
            console.log('Apple Music playlist ID from database query:', appleMusicPlaylistId);

            // If we still don't have an Apple Music playlist ID, try getting it from the active session
            if (!appleMusicPlaylistId && sessionId) {
              console.log('Trying to get Apple Music playlist ID from active session');
              const { data: sessionData, error: sessionError } = await supabase
                .from('sessions')
                .select('active_playlist_id')
                .eq('id', sessionId)
                .single();

              console.log('Session data for active playlist:', sessionData, 'Error:', sessionError);

              if (!sessionError && sessionData?.active_playlist_id) {
                console.log('Found active playlist ID in session:', sessionData.active_playlist_id);

                // Get the Apple Music playlist ID for this active playlist
                const { data: activePlaylistData, error: activePlaylistError } = await supabase
                  .from('playlists')
                  .select('apple_music_playlist_id')
                  .eq('id', sessionData.active_playlist_id)
                  .single();

                console.log('Active playlist data:', activePlaylistData, 'Error:', activePlaylistError);

                if (!activePlaylistError && activePlaylistData?.apple_music_playlist_id) {
                  appleMusicPlaylistId = activePlaylistData.apple_music_playlist_id;
                  console.log('Found Apple Music playlist ID from active session playlist:', appleMusicPlaylistId);
                }
              }
            }
          }
        }

        if (!appleMusicPlaylistId) {
          console.log('No Apple Music ID found for playlist, attempting to create a default playlist');

          // Get the DJ's user ID for this session
          const { data: sessionData, error: sessionError } = await supabase
            .from('sessions')
            .select('dj_id')
            .eq('id', sessionId)
            .single();

          if (sessionError) {
            console.error('Error getting DJ ID from session:', sessionError);
            toast({
              title: "Playlist error",
              description: "Could not find the DJ for this session.",
              variant: "destructive"
            });
            return;
          }

          // Import the createDefaultPlaylistForDJ function
          const { createDefaultPlaylistForDJ } = await import('@/utils/appleMusicService');

          // Create a default playlist for the DJ
          const defaultPlaylistId = await createDefaultPlaylistForDJ(sessionData.dj_id);

          if (!defaultPlaylistId) {
            console.error('Failed to create default playlist');
            toast({
              title: "Playlist error",
              description: "Could not create a default playlist. Please ask the DJ to create one in their settings.",
              variant: "destructive"
            });
            return;
          }

          console.log(`Created default playlist with ID: ${defaultPlaylistId}`);
          appleMusicPlaylistId = defaultPlaylistId;
        }

        console.log(`Adding song ${song.id} to Apple Music playlist ${appleMusicPlaylistId}`);

        // We'll use the DJ's token directly instead of trying to authorize MusicKit
        // This avoids prompting the requester to log in to Apple Music
        console.log('Using DJ token for playlist operations instead of MusicKit authorization');

        // Get the DJ's user ID for this session to ensure we're using the correct token
        const { data: sessionData, error: sessionError } = await supabase
          .from('sessions')
          .select('dj_id')
          .eq('id', sessionId)
          .single();

        if (sessionError) {
          console.error('Error getting DJ ID from session:', sessionError);
          throw new Error('Could not get DJ ID from session');
        }

        // Get the DJ's Apple Music token using the RPC function
        const { data: musicTokenData, error: musicTokenError } = await supabase
          .rpc('get_valid_apple_music_token', { user_id_param: sessionData.dj_id })
          .maybeSingle();

        if (musicTokenError || !musicTokenData?.token) {
          console.error('Error getting Apple Music token:', musicTokenError);
          console.error('Token data:', musicTokenData);
          throw new Error('Could not get a valid Apple Music token for the DJ');
        }

        console.log('Using fresh Apple Music token from the database');

        // Use direct Apple Music API instead of Edge Function
        console.log('=== DETAILED DEBUGGING FOR DIRECT API CALL ===');
        console.log(`Adding song ${song.id} to playlist ${appleMusicPlaylistId} using direct API`);
        console.log(`Request ID: ${requestIdToUse || 'none'}`);
        console.log(`Session ID: ${sessionId}`);
        console.log(`DJ User ID: ${sessionData.dj_id}`);
        console.log(`Status: ${status}`);

        // Call the direct method with the DJ's user ID
        const apiResult = await addSongToPlaylistDirect(
          appleMusicPlaylistId,
          song.id,
          requestIdToUse || undefined,
          sessionData.dj_id
        );

        console.log('Direct API result:', apiResult);
        console.log('=== END DETAILED DEBUGGING ===');

        console.log('Result from addSongToPlaylist:', apiResult);

        // Always mark the song as added to the playlist in our database, even if the Apple Music API call fails
        // This prevents repeated attempts to add the same song
        let updateResult, updateError;

        // Always update all approved requests for this song in this session for consistency
        console.log(`Marking all approved requests for song ${song.id} as added to playlist`);

        // First, log how many requests we're going to update
        const { count, error: countError } = await supabase
          .from('song_requests')
          .select('id', { count: 'exact', head: true })
          .eq('apple_music_id', song.id)
          .eq('session_id', sessionId)
          .in('status', ['approved', 'auto-approved']);

        if (countError) {
          console.error('Error counting related song requests:', countError);
        } else {
          console.log(`Found ${count} related requests for song ${song.id} to mark as added to playlist`);
        }

        // Now update all related requests
        const result = await supabase
          .from('song_requests')
          .update({ added_to_playlist: true })
          .eq('apple_music_id', song.id)
          .eq('session_id', sessionId)
          .in('status', ['approved', 'auto-approved']);

        updateResult = result.data;
        updateError = result.error;

        console.log('Update result after marking as added to playlist:', updateResult, 'Error:', updateError);

        if (apiResult.success) {
          console.log('Song successfully added to Apple Music playlist');

          toast({
            title: "Added to playlist",
            description: `"${song.title}" has been added to your Apple Music playlist.`,
          });
        } else {
          console.log('Failed to add song to Apple Music playlist. Status:', apiResult.status, 'Error:', apiResult.error);

          // Check if it's a 404 error indicating playlist not found in Apple Music
          if (apiResult.status === 404 || (apiResult.errorText && apiResult.errorText.includes('Resource Not Found'))) {
            toast({
              title: "Playlist not found",
              description: "This playlist is no longer available in your Apple Music library. It might have been deleted from Apple Music.",
              variant: "destructive"
            });
          } else if (apiResult.status === 500 && apiResult.errorText && apiResult.errorText.includes('Unable to update tracks')) {
            // This is likely a temporary issue with Apple Music or the song might already be in the playlist
            toast({
              title: "Apple Music API Error",
              description: "Apple Music reported an error (500: Unable to update tracks). This could be a temporary issue with Apple Music's servers. You can try manually adding the song to your playlist in the Apple Music app.",
              variant: "warning",
              duration: 10000 // Show for 10 seconds
            });
          } else {
            toast({
              title: "Playlist error",
              description: "Song was approved but couldn't be added to the Apple Music playlist.",
              variant: "destructive"
            });
          }
        }
      } catch (error) {
        console.error('Error adding song to playlist:', error);
        toast({
          title: "Playlist error",
          description: "An error occurred while adding to your Apple Music playlist.",
          variant: "destructive"
        });
      }
    }
  };

  return {
    songStatuses,
    updateSongStatus
  };
}
