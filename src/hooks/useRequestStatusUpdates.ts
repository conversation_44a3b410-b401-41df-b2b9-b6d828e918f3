
import { useEffect, useRef } from "react";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { RealtimeChannel } from "@supabase/supabase-js";
import { SongRequest } from "@/lib/types";
import { ExtendedRealtimeChannel, asExtendedChannel } from "@/types/supabase";

// Define the request status type to match the database enum
type RequestStatus = "pending" | "approved" | "auto-approved" | "declined" | "played";

export function useRequestStatusUpdates(
  sessionId: string | null,
  requesterName: string,
  lastRequestId: string | null,
  onStatusChange?: (songId: string, status: RequestStatus) => void
) {
  const { toast } = useToast();

  // Use a ref to track the channel
  const channelRef = useRef<ExtendedRealtimeChannel | null>(null);

  // Subscribe to real-time updates for session requests using a single channel approach
  useEffect(() => {
    if (!sessionId || !requesterName) return;

    // Skip setup if we already have a channel for this session and requester
    if (channelRef.current &&
        channelRef.current.topic === `realtime:session-events-${sessionId}-${requesterName}`) {
      console.log(`Channel already exists for ${requesterName} in session ${sessionId}, skipping setup`);
      return;
    }

    console.log(`Setting up status updates for ${requesterName} in session ${sessionId}`);

    // Cleanup any existing channel
    if (channelRef.current) {
      console.log(`Removing existing channel for ${requesterName}`);
      try {
        // Check if channel exists in active channels before removing
        const activeChannel = supabase.getChannels().find(ch =>
          asExtendedChannel(ch).topic === channelRef.current?.topic
        );

        if (activeChannel) {
          supabase.removeChannel(activeChannel);
        }
      } catch (error) {
        console.error("Error removing channel:", error);
      }
      channelRef.current = null;
    }

    // Create a single channel with an optimized filter
    const channel = asExtendedChannel(supabase
      .channel(`session-events-${sessionId}-${requesterName}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'song_requests',
        filter: `session_id=eq.${sessionId}`
      }, (payload) => {
        console.log('Song request update event:', payload);

        if (payload.eventType === 'UPDATE' && payload.new) {
          const songRequest = payload.new as SongRequest;

          // Process updates:
          // 1. For this user's requests
          // 2. For all played songs (which everyone should see)
          if (songRequest.requester_name === requesterName || songRequest.status === 'played') {
            const newStatus = songRequest.status;
            const requestId = songRequest.id;
            const isLatestRequest = lastRequestId && requestId === lastRequestId;
            const isAutoApproved = songRequest.auto_approved === true;

            // Call status change callback if provided
            if (newStatus && songRequest.apple_music_id && onStatusChange) {
              console.log(`Calling onStatusChange with song ID ${songRequest.apple_music_id} and status ${newStatus}`);
              onStatusChange(songRequest.apple_music_id, newStatus as RequestStatus);
            } else if (newStatus && songRequest.id && onStatusChange) {
              // If we don't have apple_music_id directly, use the request's ID
              console.log(`No apple_music_id available for request ${songRequest.id}, using request ID for tracking`);
              onStatusChange(songRequest.id, newStatus as RequestStatus);
            }

            // Only show notifications for this user's requests
            if (songRequest.requester_name === requesterName) {
              // Show toast notifications based on status with humor for auto-approval
              if (newStatus === 'approved') {
                // Funny messages for auto-approval in real-time updates
                const funnyAutoApproveMessages = [
                  `"${songRequest.song_title}" got the VIP treatment! The DJ already loved this track! 🎸`,
                  `Musical telepathy! "${songRequest.song_title}" was auto-approved because the DJ's already a fan! 🧠`,
                  `Fast pass activated! "${songRequest.song_title}" skipped the line and got approved instantly! ⚡`,
                  `DJ mind-meld complete! "${songRequest.song_title}" got auto-approved at supersonic speed! 🚀`
                ];

                // Standard approval message
                const standardApprovalMsg = `"${songRequest.song_title}" has been approved by the DJ.`;

                // Pick a random funny message for auto-approval
                const randomMessage = funnyAutoApproveMessages[Math.floor(Math.random() * funnyAutoApproveMessages.length)];

                toast({
                  title: isAutoApproved ? "Auto-Approved! 🎉" : "Request approved!",
                  description: isAutoApproved ? randomMessage : standardApprovalMsg,
                  variant: "default",
                });
              } else if (newStatus === 'declined') {
                toast({
                  title: "Request declined",
                  description: `Your request for "${songRequest.song_title}" was not approved.`,
                  variant: "destructive",
                });
              } else if (newStatus === 'played') {
                toast({
                  title: "Your song was played!",
                  description: `The DJ just played "${songRequest.song_title}" 🎉`,
                  variant: "default",
                });
              }
            }
          }
        }
      })
      .subscribe((status) => {
        console.log(`Realtime subscription status for ${requesterName}: ${status}`);
      }));

    // Store the channel reference for cleanup
    channelRef.current = channel;

    // Fetch all played songs for the current session to populate initial state
    const fetchPlayedSongs = async () => {
      try {
        const { data, error } = await supabase
          .from('song_requests')
          .select('apple_music_id')
          .eq('session_id', sessionId)
          .eq('status', 'played');

        if (error) {
          console.error('Error fetching played songs:', error);
          return;
        }

        // Update status for all played songs
        if (data && data.length > 0 && onStatusChange) {
          console.log(`Found ${data.length} played songs in session ${sessionId}`);
          data.forEach(song => {
            if (song.apple_music_id) {
              onStatusChange(song.apple_music_id, 'played');
            }
          });
        }
      } catch (error) {
        console.error('Error in fetching played songs:', error);
      }
    };

    fetchPlayedSongs();

    // Add a flag to track if this effect instance is the most recent one
    const effectInstanceId = Date.now();
    channelRef.current.effectInstanceId = effectInstanceId;

    return () => {
      // Only cleanup if this is the most recent effect instance
      if (channelRef.current && channelRef.current.effectInstanceId === effectInstanceId) {
        console.log(`Cleaning up realtime channel for ${requesterName}`);
        try {
          // Check if channel exists in active channels before removing
          const activeChannel = supabase.getChannels().find(ch =>
            asExtendedChannel(ch).topic === channelRef.current?.topic
          );

          if (activeChannel) {
            supabase.removeChannel(activeChannel);
          }
        } catch (error) {
          console.error("Error removing channel:", error);
        }
        channelRef.current = null;
      }
    };
  }, [sessionId, requesterName, lastRequestId, toast, onStatusChange]);
}
