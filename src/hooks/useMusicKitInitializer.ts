
import { useState, useEffect, useRef } from "react";
import { initializeMusicKit as initializeMusicKitService } from "@/utils/appleMusicService";

// Global flag to track initialization across component instances
let globalMusicKitInitialized = false;

type MusicKitInitializerProps = {
  developerToken: string | null;
  userToken: string | null;
  onInitialized?: (success: boolean) => void;
  addDebugInfo?: (message: string) => void;
};

export function useMusicKitInitializer({
  developerToken,
  userToken,
  onInitialized,
  addDebugInfo
}: MusicKitInitializerProps) {
  // Provide default implementation if not provided
  function defaultDebugLogger(msg: string) {
    console.log("[MusicKit]", msg);
  }
  const logDebug = addDebugInfo || defaultDebugLogger;
  const [isInitialized, setIsInitialized] = useState(false);
  const [initError, setInitError] = useState<Error | null>(null);

  // Use a stable ref to track initialization state
  const initStateRef = useRef({
    hasInitialized: false,
    attemptedInitialization: false,
    isMounted: true
  });

  // Initialize MusicKit when the component mounts
  useEffect(() => {
    // Set isMounted to true when the effect runs
    initStateRef.current.isMounted = true;

    // Function to initialize MusicKit
    const initializeMusicKit = async () => {
      // Skip if we've already attempted initialization
      if (initStateRef.current.attemptedInitialization) {
        return;
      }

      // Mark that we've attempted initialization
      initStateRef.current.attemptedInitialization = true;

      // Skip if we don't have a developer token
      if (!developerToken) {
        logDebug("Missing developer token for MusicKit initialization");
        return;
      }

      // User token is optional
      if (!userToken) {
        logDebug("No user token provided, continuing with developer token only");
      }

      // If MusicKit is already initialized globally, just update our state
      if (globalMusicKitInitialized) {
        logDebug("MusicKit already globally initialized");
        if (initStateRef.current.isMounted) {
          setIsInitialized(true);
          initStateRef.current.hasInitialized = true;
          if (onInitialized) onInitialized(true);
        }
        return;
      }

      try {
        logDebug("Starting MusicKit initialization");

        // Initialize MusicKit using the service
        const initialized = await initializeMusicKitService();

        if (!initialized) {
          throw new Error("Failed to initialize MusicKit using appleMusicService");
        }

        // Check if MusicKit is already initialized
        try {
          const existingInstance = window.MusicKit.getInstance();
          if (existingInstance) {
            logDebug("MusicKit instance already exists, reusing");

            // Set user token if provided
            if (userToken) {
              existingInstance.musicUserToken = userToken;
              logDebug("Updated existing MusicKit instance with user token");
            }

            // Update initialization flags
            globalMusicKitInitialized = true;

            if (initStateRef.current.isMounted) {
              setIsInitialized(true);
              initStateRef.current.hasInitialized = true;
              if (onInitialized) onInitialized(true);
            }
            return;
          }
        } catch (e) {
          // No existing instance, continue with initialization
          logDebug("No existing MusicKit instance found, will create new one");
        }

        // Configure MusicKit with the developer token
        logDebug("Configuring MusicKit with DJ's tokens");

        await window.MusicKit.configure({
          developerToken: developerToken,
          app: {
            name: 'PlayBeg',
            build: '1.0',
          }
        });

        // Get the instance and set the user token if available
        const musicKit = window.MusicKit.getInstance();

        // Set user token if provided
        if (userToken) {
          musicKit.musicUserToken = userToken;
          logDebug("Set user token on new MusicKit instance");
        } else {
          logDebug("No user token available for new MusicKit instance");
        }

        if (!initStateRef.current.isMounted) return;

        logDebug("MusicKit initialized with DJ's token successfully");

        // Update initialization flags
        globalMusicKitInitialized = true;
        setIsInitialized(true);
        initStateRef.current.hasInitialized = true;

        if (onInitialized) {
          onInitialized(true);
        }
      } catch (error) {
        if (!initStateRef.current.isMounted) return;

        const err = error instanceof Error ? error : new Error(String(error));
        logDebug(`Error initializing MusicKit: ${err.message}`);
        console.error("Error initializing MusicKit with DJ token:", err);
        setInitError(err);

        if (onInitialized) {
          onInitialized(false);
        }
      }
    };

    // Start initialization
    initializeMusicKit();

    // Cleanup function
    return () => {
      initStateRef.current.isMounted = false;
    };
  }, [developerToken, userToken, onInitialized, logDebug]);

  return { isInitialized, initError };
}

// We're now using the improved loadMusicKitScript function from appleMusicService
