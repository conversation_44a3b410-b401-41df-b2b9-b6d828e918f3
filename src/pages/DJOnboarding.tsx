
import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { motion } from "framer-motion";
import { supabase } from "@/integrations/supabase/client";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { ArrowRight, Music, Check, AlertTriangle, Loader2, Upload, User, Camera, Music2, PlugZap } from "lucide-react";
import { useAppleMusicAuth } from "@/hooks/useAppleMusicAuth";
import { useFileUpload } from "@/hooks/useFileUpload";
import { MusicService } from "@/services/MusicService";
import { Progress } from "@/components/ui/progress";

const DJOnboarding = () => {
  const { user, needsOnboarding, completeOnboarding } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [step, setStep] = useState(1);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [formData, setFormData] = useState({
    displayName: user?.user_metadata?.name || "",
  });
  const [profileImage, setProfileImage] = useState<File | null>(null);
  const [profileImagePreview, setProfileImagePreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const {
    isAuthorized,
    connectionState,
    error: initError,
    connectAppleMusic: authorize,
    syncConnectionState
  } = useAppleMusicAuth();

  const { uploadProfileImage, isUploading } = useFileUpload();

  // Check if user should be on this page
  useEffect(() => {
    // If user has already completed onboarding, redirect to dashboard
    if (user && !needsOnboarding) {
      navigate("/dashboard");
    }
  }, [user, needsOnboarding, navigate]);

  const handleUpdateProfile = async () => {
    try {
      if (!user) return;

      let profileImageUrl = null;

      if (profileImage) {
        profileImageUrl = await uploadProfileImage(user.id, profileImage);
      }

      const { error } = await supabase
        .from('dj_profiles')
        .update({
          display_name: formData.displayName,
          ...(profileImageUrl && { profile_image_url: profileImageUrl }),
        })
        .eq('id', user.id);

      if (error) throw error;

      setStep(2);

      toast({
        title: "Profile updated",
        description: "Your DJ profile has been updated successfully."
      });
    } catch (error: any) {
      toast({
        title: "Error updating profile",
        description: error.message,
        variant: "destructive"
      });
      console.error("Error updating profile:", error);
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please select an image under 5MB",
        variant: "destructive"
      });
      return;
    }

    // Check file type
    const allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please select an image file (JPEG, PNG, or GIF)",
        variant: "destructive"
      });
      return;
    }

    // Check for specific allowed image types
    if (!allowedMimeTypes.includes(file.type)) {
      toast({
        title: "Unsupported image format",
        description: "Only JPEG, PNG, and GIF formats are supported",
        variant: "destructive"
      });
      return;
    }

    setProfileImage(file);

    // Create a preview URL
    const reader = new FileReader();
    reader.onload = (event) => {
      setProfileImagePreview(event.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const connectAppleMusic = async () => {
    setIsConnecting(true);

    try {
      console.log('DJOnboarding: Starting Apple Music connection...');

      // Use the connectAppleMusic function from the hook directly
      // This function already handles initialization and authorization
      console.log('DJOnboarding: Calling authorize (connectAppleMusic from hook)...');
      const success = await authorize();
      console.log('DJOnboarding: Authorization result:', success);

      if (success) {
        // Explicitly sync the connection state after authorization
        console.log('DJOnboarding: Syncing connection state...');
        await syncConnectionState();

        toast({
          title: "Apple Music connected",
          description: "Your Apple Music account has been successfully connected."
        });

        // Short delay before advancing to next step to ensure state has updated
        setTimeout(() => {
          setStep(3);
        }, 500);
      } else {
        console.error('DJOnboarding: Failed to connect to Apple Music');
        throw new Error("Failed to connect to Apple Music");
      }
    } catch (error: any) {
      console.error('DJOnboarding: Error connecting to Apple Music:', error);

      // Show a more detailed error toast with a skip option
      toast({
        title: "Connection failed",
        description: (
          <div className="space-y-2">
            <p>Unable to connect to Apple Music. This may be due to:</p>
            <ul className="list-disc pl-4 text-sm">
              <li>No Apple Music subscription</li>
              <li>Browser compatibility issues</li>
              <li>Temporary Apple Music service issues</li>
            </ul>
            <p className="text-sm mt-2">You can continue without connecting or try again later.</p>
          </div>
        ),
        variant: "destructive",
        duration: 10000, // Show for 10 seconds
        action: (
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // Skip Apple Music connection and continue to next step
              setStep(3);
            }}
          >
            Skip for now
          </Button>
        ),
      });
    } finally {
      setIsConnecting(false);
    }
  };

  const finishOnboarding = async () => {
    setIsComplete(true);

    try {
      // Save onboarding completion status to the database
      await completeOnboarding();

      // Only navigate to dashboard if the update succeeded
      setTimeout(() => {
        navigate("/dashboard", { replace: true });
      }, 1500);
    } catch (error) {
      console.error("Error completing onboarding:", error);
      setIsComplete(false); // Reset the completion state to allow retrying
      toast({
        title: "Error",
        description: "There was a problem completing your onboarding. Please try again.",
        variant: "destructive",
      });
    }
  };

  const renderProfileStep = () => (
    <Card className="bg-black/50 border-purple-500/20 backdrop-blur-md w-full max-w-lg">
      <CardHeader>
        <CardTitle className="text-2xl text-white">Set Up Your DJ Profile</CardTitle>
        <CardDescription className="text-gray-400">
          Let's personalize your DJ profile before you start hosting sessions.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex justify-center mb-4">
          <div
            className="relative group cursor-pointer w-32 h-32 rounded-full bg-gray-900/60 border-2 border-dashed border-gray-700 flex items-center justify-center overflow-hidden hover:border-purple-500 transition-all"
            onClick={triggerFileInput}
          >
            {profileImagePreview ? (
              <img
                src={profileImagePreview}
                alt="Profile preview"
                className="w-full h-full object-cover"
              />
            ) : (
              <User className="h-12 w-12 text-gray-600" />
            )}
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 flex items-center justify-center transition-opacity">
              <Camera className="h-6 w-6 text-white" />
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              className="hidden"
              onChange={handleImageChange}
            />
          </div>
        </div>

        <div className="text-center text-sm text-gray-500 mb-4">
          {profileImage ?
            `Selected: ${profileImage.name}` :
            "Click to upload a profile image"
          }
        </div>

        <div className="space-y-2">
          <Label htmlFor="display-name">DJ Name</Label>
          <Input
            id="display-name"
            placeholder="How should your audience know you?"
            value={formData.displayName}
            onChange={(e) => setFormData({...formData, displayName: e.target.value})}
            className="bg-gray-900/60 border-gray-700"
          />
        </div>
      </CardContent>
      <CardFooter>
        <Button
          onClick={handleUpdateProfile}
          disabled={isUploading || !formData.displayName.trim()}
          className="w-full bg-gradient-to-r from-purple-600 to-cyan-500 hover:from-purple-700 hover:to-cyan-600"
        >
          {isUploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Uploading...
            </>
          ) : (
            <>
              Continue
              <ArrowRight className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );

  const renderAppleMusicStep = () => (
    <Card className="bg-black/50 border-purple-500/20 backdrop-blur-md w-full max-w-lg">
      <CardHeader>
        <CardTitle className="text-2xl text-white">Connect Apple Music</CardTitle>
        <CardDescription className="text-gray-400">
          Connect your Apple Music account to enable song searches and playback.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {connectionState === 'connecting' ? (
          <div className="flex justify-center items-center py-10">
            <Loader2 className="h-8 w-8 text-purple-500 animate-spin" />
            <span className="ml-2 text-gray-400">Loading Apple Music...</span>
          </div>
        ) : (
          <>
            {initError && (
              <div className="bg-red-500/10 rounded-lg p-4 border border-red-500/20 mb-4">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
                  <div>
                    <h3 className="font-medium text-red-400">Apple Music Error</h3>
                    <p className="text-sm text-red-300 mt-1">Unable to connect to Apple Music. Please try again later.</p>
                  </div>
                </div>
              </div>
            )}

            <div className="bg-gray-900/60 rounded-lg p-4 md:p-6 border border-gray-800">
              <div className="flex flex-col md:flex-row md:items-center gap-4">
                <div className="flex items-center">
                  <div className="rounded-full bg-gray-800 p-2 mr-3">
                    <Music2 className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-medium text-white">Apple Music</h3>
                    <p className="text-sm text-gray-400">Access millions of songs for your audience</p>
                  </div>
                </div>

                {isAuthorized ? (
                  <div className="flex items-center gap-2 px-3 py-1.5 md:px-4 md:py-2 rounded-md md:rounded-lg bg-green-900/30 border border-green-500/30 text-green-400 text-xs md:text-sm font-medium md:shadow-sm md:ml-auto transition-colors duration-200">
                    <Check className="w-4 h-4 text-green-400 flex-shrink-0" />
                    <span className="md:hidden">Apple Music Connected</span>
                    <span className="hidden md:inline">Connected to Apple Music</span>
                  </div>
                ) : (
                  <div className="flex gap-2 md:gap-3 md:ml-auto">
                    <Button
                      onClick={() => setStep(3)}
                      variant="outline"
                      size="sm"
                      className="text-gray-400 hover:text-white min-h-[44px] md:hover:scale-105 md:transition-all duration-200"
                    >
                      Skip
                    </Button>
                    <button
                      onClick={connectAppleMusic}
                      disabled={isConnecting || !!initError}
                      className="flex items-center justify-center gap-2 px-4 py-2 md:px-5 md:py-2.5 rounded-md bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white text-sm md:text-base font-semibold min-h-[44px] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <PlugZap className="w-4 h-4 flex-shrink-0" />
                      {isConnecting ? (
                        <>
                          <Loader2 className="w-4 h-4 animate-spin" />
                          <span className="md:hidden">Connecting...</span>
                          <span className="hidden md:inline">Connecting to Apple Music...</span>
                        </>
                      ) : (
                        <>
                          <span className="md:hidden">Connect</span>
                          <span className="hidden md:inline">Connect to Apple Music</span>
                        </>
                      )}
                    </button>
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-start gap-3 bg-yellow-500/10 p-3 rounded-md border border-yellow-500/20">
              <AlertTriangle className="h-5 w-5 text-yellow-500 flex-shrink-0 mt-0.5" />
              <p className="text-sm text-yellow-200">
                Connect your Apple Music account to effortlessly create playlists and manage your music directly from PlayBeg. This integration empowers your audience to search for and request their favorite songs in real time. Please note that a valid Apple Music subscription is required for DJs to access these features.
              </p>
            </div>
          </>
        )}
      </CardContent>
      <CardFooter>
        <div className="w-full space-y-2">
          <Button
            onClick={() => setStep(3)}
            className="w-full bg-gradient-to-r from-purple-600 to-cyan-500 hover:from-purple-700 hover:to-cyan-600"
          >
            {isAuthorized ? "Continue" : "Continue without Apple Music"}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
          {!isAuthorized && (
            <p className="text-xs text-center text-gray-500">
              You can connect Apple Music later from your profile settings
            </p>
          )}
        </div>
      </CardFooter>
    </Card>
  );

  const renderFinalStep = () => (
    <Card className="bg-black/50 border-purple-500/20 backdrop-blur-md w-full max-w-lg">
      <CardHeader>
        <CardTitle className="text-2xl text-white">You're All Set!</CardTitle>
        <CardDescription className="text-gray-400">
          Your DJ profile is ready to start hosting music sessions and receiving requests.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 pb-6">
        <div className="bg-green-500/10 rounded-lg p-6 border border-green-500/20 flex flex-col items-center">
          <div className="rounded-full bg-green-500/20 p-4 mb-4">
            <Check className="h-8 w-8 text-green-500" />
          </div>
          <h3 className="font-medium text-green-500 text-lg">Setup Complete</h3>
          <p className="text-center text-gray-400 mt-2">
            You've successfully set up your DJ profile and connected Apple Music.
          </p>
        </div>

        <div className="space-y-4">
          <div className="bg-gray-900/40 rounded-lg p-4">
            <h4 className="text-white font-medium mb-2">What's next?</h4>
            <ul className="space-y-2 text-gray-400 text-sm">
              <li className="flex items-start gap-2">
                <div className="rounded-full bg-purple-500/20 p-1 mt-0.5 flex-shrink-0">
                  <Check className="h-3 w-3 text-purple-500" />
                </div>
                <span>Create your first DJ session</span>
              </li>
              <li className="flex items-start gap-2">
                <div className="rounded-full bg-purple-500/20 p-1 mt-0.5 flex-shrink-0">
                  <Check className="h-3 w-3 text-purple-500" />
                </div>
                <span>Create a new Playlist for the session</span>
              </li>
              <li className="flex items-start gap-2">
                <div className="rounded-full bg-purple-500/20 p-1 mt-0.5 flex-shrink-0">
                  <Check className="h-3 w-3 text-purple-500" />
                </div>
                <span>Share your QR code with your audience</span>
              </li>
              <li className="flex items-start gap-2">
                <div className="rounded-full bg-purple-500/20 p-1 mt-0.5 flex-shrink-0">
                  <Check className="h-3 w-3 text-purple-500" />
                </div>
                <span>Start receiving and playing song requests</span>
              </li>
            </ul>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button
          onClick={finishOnboarding}
          className="w-full bg-gradient-to-r from-purple-600 to-cyan-500 hover:from-purple-700 hover:to-cyan-600"
        >
          Go to Dashboard
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  );

  if (isComplete) {
    return (
      <div className="min-h-[80vh] flex items-center justify-center">
        <motion.div
          animate={{ scale: [1, 1.1, 1], opacity: [0, 1, 0] }}
          transition={{ duration: 1.5, ease: "easeInOut" }}
          className="text-center"
        >
          <div className="flex justify-center mb-4">
            <div className="rounded-full bg-gradient-to-r from-purple-600 to-cyan-500 p-6">
              <Music className="h-12 w-12 text-white" />
            </div>
          </div>
          <h2 className="text-2xl font-bold bg-gradient-to-r from-purple-500 to-cyan-400 bg-clip-text text-transparent mb-2">
            Welcome to PlayBeg!
          </h2>
          <p className="text-gray-400">Taking you to your dashboard...</p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-[80vh] flex flex-col items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
        className="w-full"
      >
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-500 to-cyan-400 bg-clip-text text-transparent">
            DJ Onboarding
          </h1>
          <p className="text-gray-400 mt-2 max-w-md mx-auto">
            Complete a few quick steps to set up your DJ profile and start receiving song requests.
          </p>
        </div>

        <div className="flex justify-center mb-8">
          <div className="flex items-center max-w-md w-full">
            <div className={`flex flex-col items-center ${step >= 1 ? 'text-purple-500' : 'text-gray-600'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${step >= 1 ? 'border-purple-500 bg-purple-500/20' : 'border-gray-700 bg-gray-800'}`}>
                1
              </div>
              <span className="text-xs mt-1">Profile</span>
            </div>
            <div className={`flex-1 h-0.5 mx-2 ${step >= 2 ? 'bg-purple-500' : 'bg-gray-700'}`}></div>
            <div className={`flex flex-col items-center ${step >= 2 ? 'text-cyan-500' : 'text-gray-600'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${step >= 2 ? 'border-cyan-500 bg-cyan-500/20' : 'border-gray-700 bg-gray-800'}`}>
                2
              </div>
              <span className="text-xs mt-1">Music</span>
            </div>
            <div className={`flex-1 h-0.5 mx-2 ${step >= 3 ? 'bg-cyan-500' : 'bg-gray-700'}`}></div>
            <div className={`flex flex-col items-center ${step >= 3 ? 'text-green-500' : 'text-gray-600'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${step >= 3 ? 'border-green-500 bg-green-500/20' : 'border-gray-700 bg-gray-800'}`}>
                3
              </div>
              <span className="text-xs mt-1">Finish</span>
            </div>
          </div>
        </div>

        <div className="flex justify-center">
          {step === 1 && renderProfileStep()}
          {step === 2 && renderAppleMusicStep()}
          {step === 3 && renderFinalStep()}
        </div>
      </motion.div>
    </div>
  );
};

export default DJOnboarding;
