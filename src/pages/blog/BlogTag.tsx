import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { getBlogPostsByTag, formatDate, BlogPost } from '@/utils/blogUtils';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Tag } from 'lucide-react';
import { motion } from 'framer-motion';
import { BlogHeader } from '@/components/blog/BlogHeader';
import { SEOHead } from '@/components/seo/SEOHead';

const BlogTag = () => {
  const { tag } = useParams<{ tag: string }>();
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPosts = async () => {
      if (tag) {
        try {
          const decodedTag = decodeURIComponent(tag);
          const tagPosts = await getBlogPostsByTag(decodedTag);
          setPosts(tagPosts);
        } catch (error) {
          console.error('Error fetching posts by tag:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchPosts();
  }, [tag]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-12 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mx-auto"></div>
        <p className="mt-4 text-gray-400">Loading posts...</p>
      </div>
    );
  }

  const decodedTag = tag ? decodeURIComponent(tag) : '';
  const tagTitle = decodedTag.charAt(0).toUpperCase() + decodedTag.slice(1);

  return (
    <div className="container mx-auto px-4 py-12">
      <SEOHead
        title={`${tagTitle} Articles | PlayBeg Blog`}
        description={`Explore all articles tagged with "${decodedTag}" on PlayBeg Blog. Discover insights about ${decodedTag} in the music and DJ industry.`}
        keywords={`${decodedTag}, DJ ${decodedTag}, music industry ${decodedTag}, PlayBeg ${decodedTag}, song request ${decodedTag}`}
        canonicalUrl={`https://playbeg.com/blog/tag/${encodeURIComponent(decodedTag)}`}
      />

      {/* Back Button */}
      <Link to="/blog" className="inline-flex items-center text-purple-400 hover:text-purple-300 mb-8 transition-colors">
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to all posts
      </Link>

      {/* Header */}
      <div className="text-center mb-12">
        <div className="flex items-center justify-center mb-4">
          <Tag className="h-8 w-8 text-purple-400 mr-3" />
          <h1 className="text-3xl md:text-4xl font-bold">
            Posts tagged with "{decodedTag}"
          </h1>
        </div>
        <p className="text-gray-400 text-lg">
          {posts.length} {posts.length === 1 ? 'article' : 'articles'} found
        </p>
      </div>

      {/* Posts Grid */}
      {posts.length === 0 ? (
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-4">No posts found</h2>
          <p className="text-gray-400 mb-8">
            There are no published articles with the tag "{decodedTag}" yet.
          </p>
          <Button asChild>
            <Link to="/blog">Browse all posts</Link>
          </Button>
        </div>
      ) : (
        <motion.div
          className="grid gap-8 md:grid-cols-2 lg:grid-cols-3"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {posts.map(post => (
            <motion.div key={post.slug} variants={itemVariants}>
              <Card className="h-full bg-gradient-to-b from-gray-900 to-black border-purple-500/20 hover:border-purple-500/40 active:border-purple-500/60 transition-all duration-300 overflow-hidden group mobile-touch-friendly blog-card-layout">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-600/5 to-transparent opacity-0 group-hover:opacity-100 group-active:opacity-100 transition-opacity duration-500 pointer-events-none"></div>

                {(post.cover_image_url || post.coverImage) && (
                  <div className="blog-image-container">
                    <BlogHeader
                      title={post.title}
                      coverImage={post.cover_image_url || post.coverImage}
                      slug={post.slug}
                      height="h-full"
                      className="rounded-t-lg"
                    />
                  </div>
                )}

                <CardContent className="p-6 relative z-10 flex-grow">
                  <div className="flex items-center mb-3">
                    <span className="text-xs text-purple-400 bg-purple-400/10 px-2 py-1 rounded-full">
                      {post.category}
                    </span>
                    <span className="text-xs text-gray-400 ml-auto">
                      {formatDate(post.date)}
                    </span>
                  </div>

                  <Link
                    to={`/blog/${post.slug}`}
                    className="block mobile-touch-friendly"
                    style={{
                      touchAction: 'manipulation',
                      WebkitTapHighlightColor: 'rgba(168, 85, 247, 0.2)'
                    }}
                  >
                    <h2 className="text-xl font-bold text-white mb-2 group-hover:text-purple-400 group-active:text-purple-400 transition-colors">
                      {post.title}
                    </h2>
                  </Link>

                  <p className="text-gray-400 text-sm mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>

                  {/* Tags */}
                  {post.tags && post.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mb-4">
                      {post.tags.slice(0, 3).map((postTag, index) => (
                        <Link
                          key={index}
                          to={`/blog/tag/${encodeURIComponent(postTag)}`}
                          className={`text-xs px-2 py-1 rounded-full transition-colors mobile-touch-friendly ${
                            postTag === decodedTag
                              ? 'bg-purple-600/30 text-purple-300 border border-purple-500/50'
                              : 'bg-gray-700/50 text-gray-400 hover:bg-gray-600/50 active:bg-gray-600/50'
                          }`}
                          style={{
                            touchAction: 'manipulation',
                            WebkitTapHighlightColor: 'rgba(168, 85, 247, 0.2)'
                          }}
                        >
                          {postTag}
                        </Link>
                      ))}
                      {post.tags.length > 3 && (
                        <span className="text-xs text-gray-500 px-2 py-1">
                          +{post.tags.length - 3} more
                        </span>
                      )}
                    </div>
                  )}

                  <Link
                    to={`/blog/${post.slug}`}
                    className="text-purple-400 hover:text-purple-300 active:text-purple-300 text-sm font-medium transition-colors mobile-touch-friendly"
                    style={{
                      touchAction: 'manipulation',
                      WebkitTapHighlightColor: 'rgba(168, 85, 247, 0.2)'
                    }}
                  >
                    Read more →
                  </Link>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      )}
    </div>
  );
};

export default BlogTag;
