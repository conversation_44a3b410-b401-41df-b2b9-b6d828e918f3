import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { getAllPosts, getAllCategories, formatDate, BlogPost } from '@/utils/blogUtils';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import { BlogHeader } from '@/components/blog/BlogHeader';
import { SEOHead } from '@/components/seo/SEOHead';

const BlogIndex = () => {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch all posts from database
        const allPosts = await getAllPosts();
        setPosts(allPosts);

        // Fetch all categories from database
        const allCategories = await getAllCategories();
        setCategories(allCategories);
      } catch (error) {
        console.error('Error fetching blog data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Filter posts by category if one is selected
  const filteredPosts = selectedCategory
    ? posts.filter(post => post.category === selectedCategory)
    : posts;

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <>
      <SEOHead
        title="PlayBeg Blog - DJ Tips, Music Industry Insights & Song Request Guides"
        description="Discover expert DJ tips, music industry insights, and comprehensive guides on song requests, Apple Music integration, and event management with PlayBeg."
        keywords="DJ blog, song request tips, Apple Music DJ, music industry insights, DJ guides, event management, PlayBeg blog, DJ software tips"
        canonicalUrl="https://playbeg.com/blog"
      />

      <div className="container mx-auto px-4 py-12">
        <div className="text-center mb-12">
        <h1 className="text-4xl md:text-5xl font-bold mb-4">
          <span className="bg-gradient-to-r from-purple-500 to-cyan-400 bg-clip-text text-transparent">
            PlayBeg Blog
          </span>
        </h1>
        <p className="text-xl text-gray-400 max-w-2xl mx-auto">
          Insights, tips, and updates for DJs and event organizers
        </p>
      </div>

      {/* Category Filter */}
      {categories.length > 0 && (
        <div className="flex flex-wrap gap-2 justify-center mb-8 relative z-50">
          <Button
            variant={selectedCategory === null ? "default" : "outline"}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('All button clicked');
              setSelectedCategory(null);
            }}
            className="rounded-full cursor-pointer"
            style={{ pointerEvents: 'auto', touchAction: 'manipulation' }}
          >
            All
          </Button>
          {categories.map(category => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('Category clicked:', category);
                setSelectedCategory(category);
              }}
              className="rounded-full cursor-pointer"
              style={{ pointerEvents: 'auto', touchAction: 'manipulation' }}
            >
              {category}
            </Button>
          ))}
        </div>
      )}

      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mx-auto"></div>
          <p className="mt-4 text-gray-400">Loading blog posts...</p>
        </div>
      ) : filteredPosts.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-xl text-gray-400">No blog posts found{selectedCategory ? ` in ${selectedCategory}` : ''}.</p>
          {selectedCategory && (
            <Button
              onClick={() => setSelectedCategory(null)}
              className="mt-4"
            >
              View all posts
            </Button>
          )}
        </div>
      ) : (
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {filteredPosts.map(post => (
            <div key={post.slug}>
              <Card className="h-full bg-gradient-to-b from-gray-900 to-black border-purple-500/20 hover:border-purple-500/40 active:border-purple-500/60 transition-all duration-300 overflow-hidden group mobile-touch-friendly blog-card-layout">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-600/5 to-transparent opacity-0 group-hover:opacity-100 group-active:opacity-100 transition-opacity duration-500 pointer-events-none"></div>

                {(post.cover_image_url || post.coverImage) && (
                  <div className="blog-image-container">
                    <BlogHeader
                      title={post.title}
                      coverImage={post.cover_image_url || post.coverImage}
                      slug={post.slug}
                      height="h-full"
                      className="rounded-t-lg"
                    />
                  </div>
                )}

                <CardContent className="p-6 relative z-10 flex-grow">
                  <div className="flex items-center mb-3">
                    <span className="text-xs text-purple-400 bg-purple-400/10 px-2 py-1 rounded-full">
                      {post.category}
                    </span>
                    <span className="text-xs text-gray-400 ml-auto">
                      {formatDate(post.date)}
                    </span>
                  </div>

                  <Link
                    to={`/blog/${post.slug}`}
                    className="block mobile-touch-friendly"
                    style={{
                      touchAction: 'manipulation',
                      WebkitTapHighlightColor: 'rgba(168, 85, 247, 0.2)'
                    }}
                  >
                    <h2 className="text-xl font-bold text-white mb-2 group-hover:text-purple-400 group-active:text-purple-400 transition-colors">
                      {post.title}
                    </h2>
                  </Link>

                  <p className="text-gray-400 text-sm mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>

                  <div className="flex items-center mt-auto">
                    <span className="text-sm text-gray-400">By {post.author}</span>
                    <Link
                      to={`/blog/${post.slug}`}
                      className="ml-auto mobile-touch-friendly"
                      style={{
                        touchAction: 'manipulation',
                        WebkitTapHighlightColor: 'rgba(168, 85, 247, 0.2)'
                      }}
                    >
                      <span className="text-purple-400 text-sm group-hover:translate-x-1 group-active:translate-x-1 transition-transform duration-300">
                        Read more →
                      </span>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      )}
      </div>
    </>
  );
};

export default BlogIndex;
