import { useState, useEffect, useRef } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import {
  getPostBySlug,
  formatDate,
  getBlogPostOGImage,
  getBlogPostSEOTitle,
  getBlogPostSEODescription,
  getBlogPostSEOKeywords,
  BlogPost as BlogPostType
} from '@/utils/blogUtils';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Calendar, User, Tag } from 'lucide-react';
import { motion } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { BlogHeader } from '@/components/blog/BlogHeader';
import { SEOHead } from '@/components/seo/SEOHead';
import { ShareButton } from '@/components/blog/ShareButton';

const BlogPost = () => {
  const { slug } = useParams<{ slug: string }>();
  const [post, setPost] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const firstParagraphRef = useRef(false);

  useEffect(() => {
    const fetchPost = async () => {
      if (slug) {
        console.log('BlogPost component received slug:', slug);

        try {
          // Get the post by slug from the database
          const blogPost = await getPostBySlug(slug);

          if (blogPost) {
            setPost(blogPost);
            // Reset the first paragraph ref when loading a new post
            firstParagraphRef.current = false;
            console.log(`Successfully loaded blog post: ${blogPost.title} with slug: ${blogPost.slug}`);
          } else {
            console.error(`Blog post with slug "${slug}" not found`);
            // If no post is found, redirect to the blog index
            navigate('/blog');
          }
        } catch (error) {
          console.error('Error fetching blog post:', error);
          navigate('/blog');
        }

        setLoading(false);
      }
    };

    fetchPost();
  }, [slug, navigate]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-12 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mx-auto"></div>
        <p className="mt-4 text-gray-400">Loading blog post...</p>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="container mx-auto px-4 py-12 text-center">
        <h1 className="text-3xl font-bold mb-4">Blog Post Not Found</h1>
        <p className="text-gray-400 mb-8">The blog post you're looking for doesn't exist or has been moved.</p>
        <Button onClick={() => navigate('/blog')} className="bg-purple-600 hover:bg-purple-700">
          Back to Blog
        </Button>
      </div>
    );
  }

  return (
    <>
      {/* Dynamic SEO Head with blog post specific Open Graph image */}
      <SEOHead
        title={getBlogPostSEOTitle(post)}
        description={getBlogPostSEODescription(post)}
        keywords={getBlogPostSEOKeywords(post)}
        image={getBlogPostOGImage(post)}
        type="article"
      />

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
        {/* Back Button */}
        <Link to="/blog" className="inline-flex items-center text-purple-400 hover:text-purple-300 mb-8 transition-colors">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to all posts
        </Link>

        {/* Cover Image */}
        {(post.cover_image_url || post.coverImage) && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="w-full mb-8 rounded-lg overflow-hidden aspect-[1.9/1] max-h-96 blog-post-header-image block clear-both"
            style={{
              display: 'block',
              width: '100%',
              position: 'relative',
              zIndex: 1,
              boxSizing: 'border-box'
            }}
          >
            <img
              src={post.cover_image_url || post.coverImage}
              alt={post.cover_image_alt || post.title}
              className="w-full h-full object-cover object-center block"
              style={{
                display: 'block',
                width: '100%',
                height: '100%',
                margin: 0,
                padding: 0,
                border: 'none',
                outline: 'none',
                boxSizing: 'border-box',
                objectFit: 'cover',
                objectPosition: 'center'
              }}
            />
          </motion.div>
        )}

        {/* Post Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
            {post.title}
          </h1>

          <div className="flex flex-wrap items-center text-sm text-gray-400 mb-8 gap-4">
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-1" />
              {formatDate(post.date)}
            </div>
            <div className="flex items-center">
              <User className="h-4 w-4 mr-1" />
              {post.author}
            </div>
            <div className="flex items-center">
              <Tag className="h-4 w-4 mr-1" />
              <Link to={`/blog/category/${post.category}`} className="text-purple-400 hover:text-purple-300">
                {post.category}
              </Link>
            </div>
          </div>
        </motion.div>

        {/* Post Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="prose prose-invert prose-lg max-w-none
                     prose-headings:text-white prose-headings:font-bold
                     prose-h1:text-3xl prose-h1:md:text-4xl prose-h1:mb-8 prose-h1:mt-2
                     prose-h2:text-2xl prose-h2:md:text-3xl prose-h2:mb-6 prose-h2:mt-10 prose-h2:text-purple-400
                     prose-h3:text-xl prose-h3:md:text-2xl prose-h3:mb-4 prose-h3:mt-8 prose-h3:text-cyan-400
                     prose-h4:text-lg prose-h4:md:text-xl prose-h4:mb-3 prose-h4:mt-6
                     prose-p:text-gray-300 prose-p:leading-relaxed prose-p:mb-6 prose-p:max-w-[600px]
                     prose-p:first-of-type:text-xl prose-p:first-of-type:font-medium prose-p:first-of-type:text-gray-200
                     prose-a:text-purple-400 prose-a:no-underline hover:prose-a:text-purple-300 hover:prose-a:underline
                     prose-strong:text-white prose-strong:font-semibold
                     prose-ul:text-gray-300 prose-ul:leading-relaxed prose-ul:my-6 prose-ul:pl-6
                     prose-li:my-3 prose-li:pl-2
                     prose-blockquote:border-purple-500 prose-blockquote:bg-purple-500/10 prose-blockquote:p-4 prose-blockquote:rounded-md
                     prose-code:text-cyan-400 prose-code:bg-cyan-400/10 prose-code:p-1 prose-code:rounded
                     prose-pre:bg-gray-900 prose-pre:border prose-pre:border-purple-500/20"
        >
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
              // Skip the first h1 heading since we already display the title from frontmatter
              h1: ({node, children, ...props}) => {
                // Check if this is the first h1 in the document by comparing content with title
                const headingText = Array.isArray(children)
                  ? children.join('')
                  : String(children || '');

                const isFirstHeading = headingText.trim() === post.title.trim();

                // Skip rendering the first h1 to avoid duplication
                return isFirstHeading ? null : <h1 className="text-3xl md:text-4xl font-bold mb-8 mt-2" {...props}>{children}</h1>;
              },
              h2: ({node, ...props}) => <h2 className="text-2xl md:text-3xl font-bold mb-6 mt-10 text-purple-400" {...props} />,
              h3: ({node, ...props}) => <h3 className="text-xl md:text-2xl font-bold mb-4 mt-8 text-cyan-400" {...props} />,
              h4: ({node, ...props}) => <h4 className="text-lg md:text-xl font-bold mb-3 mt-6" {...props} />,
              p: ({node, children, ...props}) => {
                // Use a ref to track if we've seen the first paragraph
                if (!firstParagraphRef.current) {
                  firstParagraphRef.current = true;
                  return <p className="text-xl font-medium text-gray-200 leading-relaxed mb-6 max-w-[600px]" {...props}>{children}</p>;
                }
                return <p className="text-gray-300 leading-relaxed mb-6 max-w-[600px]" {...props}>{children}</p>;
              },
              ul: ({node, ...props}) => <ul className="text-gray-300 leading-relaxed my-6 pl-6" {...props} />,
              li: ({node, ...props}) => <li className="my-3 pl-2" {...props} />,
              a: ({node, ...props}) => <a className="text-purple-400 no-underline hover:text-purple-300 hover:underline" {...props} />,
              strong: ({node, ...props}) => <strong className="text-white font-semibold" {...props} />
            }}
          >
            {post.content}
          </ReactMarkdown>
        </motion.div>

        {/* Share Button Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="mt-12 pt-8 border-t border-gray-800"
        >
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">Share this article</h3>
              <p className="text-gray-400 text-sm">Help others discover this content</p>
            </div>
            <ShareButton
              title={post.title}
              excerpt={post.excerpt}
              url={`https://playbeg.com/blog/${post.slug}`}
              coverImageUrl={getBlogPostOGImage(post)}
            />
          </div>
        </motion.div>

        {/* Tags Section */}
        {post.tags && post.tags.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mt-8 pt-6 border-t border-gray-800"
          >
            <h3 className="text-lg font-semibold text-white mb-4">Tags</h3>
            <div className="flex flex-wrap gap-2">
              {post.tags.map((tag, index) => (
                <Link
                  key={index}
                  to={`/blog/tag/${encodeURIComponent(tag)}`}
                  className="px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm border border-purple-500/30 hover:bg-purple-600/30 transition-colors cursor-pointer"
                >
                  {tag}
                </Link>
              ))}
            </div>
          </motion.div>
        )}

        {/* Navigation */}
        <div className="mt-12 pt-6 border-t border-gray-800 flex justify-between">
          <Link to="/blog" className="text-purple-400 hover:text-purple-300 transition-colors">
            ← Back to all posts
          </Link>
        </div>
        </div>
      </div>
    </>
  );
};

export default BlogPost;
