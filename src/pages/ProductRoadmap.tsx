import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ThumbsUp, Clock, Calendar, Lightbulb, CheckCircle, ChevronDown, ChevronUp, Info } from 'lucide-react';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Progress } from '@/components/ui/progress';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Helmet } from 'react-helmet-async';

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};

const stagger = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

// Feature type definition
interface Feature {
  id: string;
  title: string;
  description: string;
  category: 'coming-soon' | 'planned' | 'consideration' | 'implemented';
  votes: number;
  progress?: number; // 0-100 for progress bar
  estimatedDelivery?: string;
  tags?: string[];
}

// Category configuration
const categories = [
  {
    id: 'implemented',
    label: 'Implemented',
    icon: CheckCircle,
    description: 'Features that are live and available',
    color: 'from-emerald-500 to-green-500'
  },
  {
    id: 'coming-soon',
    label: 'Coming Soon',
    icon: Clock,
    description: 'Features currently in development',
    color: 'from-green-500 to-cyan-500'
  },
  {
    id: 'planned',
    label: 'Planned',
    icon: Calendar,
    description: 'Features on our short-term roadmap',
    color: 'from-purple-500 to-blue-500'
  },
  {
    id: 'consideration',
    label: 'Under Consideration',
    icon: Lightbulb,
    description: 'Features we\'re evaluating',
    color: 'from-amber-500 to-pink-500'
  }
];

const ProductRoadmap = () => {
  const { toast } = useToast();
  const [features, setFeatures] = useState<Feature[]>([]);
  const [loading, setLoading] = useState(true);
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({
    'implemented': true,
    'coming-soon': true,
    'planned': true,
    'consideration': true
  });
  const [activeTab, setActiveTab] = useState<string>('all');
  const [votedFeatures, setVotedFeatures] = useState<string[]>([]);

  // Load features from Supabase
  useEffect(() => {
    const fetchFeatures = async () => {
      try {
        const { data, error } = await supabase
          .from('roadmap_features')
          .select('*')
          .order('votes', { ascending: false });

        if (error) throw error;

        setFeatures(data || []);

        // Load voted features from localStorage
        const savedVotes = localStorage.getItem('playbeg_voted_features');
        if (savedVotes) {
          setVotedFeatures(JSON.parse(savedVotes));
        }
      } catch (error) {
        console.error('Error fetching roadmap features:', error);
        // Use sample data if fetch fails
        setFeatures(sampleFeatures);
      } finally {
        setLoading(false);
      }
    };

    fetchFeatures();

    // Set up realtime subscription for vote updates
    const subscription = supabase
      .channel('roadmap_votes')
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'roadmap_features'
      }, (payload) => {
        setFeatures(currentFeatures =>
          currentFeatures.map(feature =>
            feature.id === payload.new.id
              ? { ...feature, votes: payload.new.votes }
              : feature
          )
        );
      })
      .subscribe();

    return () => {
      supabase.removeChannel(subscription);
    };
  }, []);

  // Toggle category expansion
  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  // Handle voting
  const handleVote = async (featureId: string) => {
    // Check if already voted
    if (votedFeatures.includes(featureId)) {
      toast({
        title: "Already voted",
        description: "You've already voted for this feature",
        variant: "default"
      });
      return;
    }

    try {
      // Optimistic UI update
      setFeatures(currentFeatures =>
        currentFeatures.map(feature =>
          feature.id === featureId
            ? { ...feature, votes: feature.votes + 1 }
            : feature
        )
      );

      // Update vote in database
      const { error } = await supabase.rpc('increment_feature_vote', {
        feature_id: featureId
      });

      if (error) throw error;

      // Save vote to localStorage
      const updatedVotes = [...votedFeatures, featureId];
      setVotedFeatures(updatedVotes);
      localStorage.setItem('playbeg_voted_features', JSON.stringify(updatedVotes));

      toast({
        title: "Vote recorded",
        description: "Thanks for your vote!",
        variant: "default"
      });
    } catch (error) {
      console.error('Error voting for feature:', error);
      toast({
        title: "Error",
        description: "Couldn't register your vote. Please try again.",
        variant: "destructive"
      });

      // Revert optimistic update
      setFeatures(currentFeatures =>
        currentFeatures.map(feature =>
          feature.id === featureId
            ? { ...feature, votes: feature.votes - 1 }
            : feature
        )
      );
    }
  };

  // Filter features based on active tab
  const filteredFeatures = activeTab === 'all'
    ? features
    : features.filter(feature => feature.category === activeTab);

  // Group features by category
  const featuresByCategory = filteredFeatures.reduce((acc, feature) => {
    if (!acc[feature.category]) {
      acc[feature.category] = [];
    }
    acc[feature.category].push(feature);
    return acc;
  }, {} as Record<string, Feature[]>);

  return (
    <div className="container mx-auto px-3 sm:px-4 py-8 sm:py-12">
      <Helmet>
        <title>Product Roadmap | PlayBeg</title>
        <meta name="description" content="See what's coming next to PlayBeg and vote on features you'd like to see prioritized." />
        <meta name="keywords" content="PlayBeg roadmap, upcoming features, DJ software, song request system" />
        <link rel="canonical" href="https://playbeg.com/roadmap" />
      </Helmet>

      <motion.div
        initial="hidden"
        animate="visible"
        variants={fadeIn}
        className="space-y-6 sm:space-y-8"
      >
        <div className="text-center max-w-3xl mx-auto">
          <h1 className="text-3xl sm:text-4xl font-bold mb-3 sm:mb-4">
            <span className="bg-gradient-to-r from-purple-500 to-cyan-400 bg-clip-text text-transparent">
              Product Roadmap
            </span>
          </h1>
          <p className="text-gray-300 text-base sm:text-lg px-2">
            Help shape the future of PlayBeg by voting on features you'd like to see.
            Your feedback directly influences our development priorities.
          </p>
        </div>

        {/* Category Tabs */}
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="flex justify-center px-2">
            <TabsList className="flex flex-wrap w-full max-w-2xl mx-auto bg-gray-800/60 rounded-lg">
              <div className="grid grid-cols-3 sm:grid-cols-5 w-full gap-1 p-1">
                <TabsTrigger
                  value="all"
                  className="text-xs sm:text-sm h-9 data-[state=active]:bg-purple-600/20 data-[state=active]:text-white"
                >
                  All
                </TabsTrigger>
                <TabsTrigger
                  value="implemented"
                  className="text-xs sm:text-sm h-9 data-[state=active]:bg-purple-600/20 data-[state=active]:text-white"
                >
                  Live
                </TabsTrigger>
                <TabsTrigger
                  value="coming-soon"
                  className="text-xs sm:text-sm h-9 data-[state=active]:bg-purple-600/20 data-[state=active]:text-white"
                >
                  Coming Soon
                </TabsTrigger>
                <TabsTrigger
                  value="planned"
                  className="text-xs sm:text-sm h-9 data-[state=active]:bg-purple-600/20 data-[state=active]:text-white"
                >
                  Planned
                </TabsTrigger>
                <TabsTrigger
                  value="consideration"
                  className="text-xs sm:text-sm h-9 data-[state=active]:bg-purple-600/20 data-[state=active]:text-white"
                >
                  Considering
                </TabsTrigger>
              </div>
            </TabsList>
          </div>
        </Tabs>

        {/* Feature Categories */}
        <motion.div variants={stagger} className="space-y-8 sm:space-y-12">
          {loading ? (
            // Loading skeleton
            <div className="space-y-8 sm:space-y-10 pt-2 mt-16">
              {[1, 2, 3].map(i => (
                <Card key={i} className="bg-gray-800/40 border border-purple-500/20 backdrop-blur-md">
                  <CardHeader className="animate-pulse">
                    <div className="h-7 bg-gray-700 rounded-md w-1/3 mb-2"></div>
                    <div className="h-4 bg-gray-700 rounded-md w-2/3"></div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {[1, 2, 3].map(j => (
                      <div key={j} className="animate-pulse">
                        <div className="h-24 bg-gray-700/50 rounded-lg"></div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            // Actual content
            <>
              {categories.map((category, index) => {
                // Skip empty categories unless "All" is selected
                if (activeTab !== 'all' && category.id !== activeTab) return null;
                if (!featuresByCategory[category.id]?.length) return null;

                // Find the first visible category index
                const visibleCategories = categories.filter(cat =>
                  (activeTab === 'all' || cat.id === activeTab) &&
                  featuresByCategory[cat.id]?.length
                );
                const isFirstVisible = visibleCategories[0]?.id === category.id;

                return (
                  <motion.div
                    key={category.id}
                    variants={fadeIn}
                    className={`space-y-5 sm:space-y-6 mb-6 sm:mb-8 ${isFirstVisible ? 'mt-16' : ''}`}
                  >
                    <div
                      className="flex items-center justify-between cursor-pointer px-1 sm:px-0"
                      onClick={() => toggleCategory(category.id)}
                    >
                      <div className="flex items-center gap-2">
                        <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-gradient-to-r ${category.color} flex items-center justify-center shadow-lg`}>
                          <category.icon className="text-white h-4 w-4 sm:h-5 sm:w-5" />
                        </div>
                        <h2 className="text-xl sm:text-2xl font-bold text-white">{category.label}</h2>
                      </div>
                      <Button variant="ghost" size="icon" className="h-8 w-8 sm:h-10 sm:w-10">
                        {expandedCategories[category.id] ? <ChevronUp className="h-4 w-4 sm:h-5 sm:w-5" /> : <ChevronDown className="h-4 w-4 sm:h-5 sm:w-5" />}
                      </Button>
                    </div>

                    {expandedCategories[category.id] && (
                      <div className="grid gap-4 sm:gap-5 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 px-1 sm:px-0 mt-4 sm:mt-5">
                        {featuresByCategory[category.id]?.map(feature => (
                          <FeatureCard
                            key={feature.id}
                            feature={feature}
                            hasVoted={votedFeatures.includes(feature.id)}
                            onVote={handleVote}
                            categoryColor={category.color}
                          />
                        ))}
                      </div>
                    )}
                  </motion.div>
                );
              })}
            </>
          )}
        </motion.div>
      </motion.div>
    </div>
  );
};

// Feature Card Component
interface FeatureCardProps {
  feature: Feature;
  hasVoted: boolean;
  onVote: (id: string) => void;
  categoryColor: string;
}

const FeatureCard = ({ feature, hasVoted, onVote, categoryColor }: FeatureCardProps) => {
  return (
    <motion.div
      whileHover={{ y: -5 }}
      transition={{ duration: 0.2 }}
      className="relative"
    >
      <Card className="h-full bg-gray-800/40 border border-purple-500/20 backdrop-blur-md overflow-hidden group relative z-10">
        <div className={`absolute inset-0 bg-gradient-to-br ${categoryColor} opacity-0 group-hover:opacity-5 transition-opacity duration-300`}></div>

        <CardHeader className="pb-2 px-3 sm:px-6 pt-3 sm:pt-6">
          <div className="flex flex-wrap gap-1 sm:gap-2 mb-2">
            {feature.tags?.map(tag => (
              <Badge key={tag} variant="secondary" className="bg-gray-700/70 text-gray-300 text-xs">
                {tag}
              </Badge>
            ))}
          </div>
          <CardTitle className="text-lg sm:text-xl text-white">{feature.title}</CardTitle>
          <CardDescription className="text-sm sm:text-base text-gray-400">{feature.description}</CardDescription>
        </CardHeader>

        <CardContent className="pt-0 px-3 sm:px-6">
          {feature.progress !== undefined && (
            <div className="mb-3 sm:mb-4">
              <div className="flex justify-between text-xs sm:text-sm mb-1">
                <span className="text-gray-400">Development Progress</span>
                <span className="text-gray-300">{feature.progress}%</span>
              </div>
              <Progress value={feature.progress} className="h-1.5 sm:h-2" />
            </div>
          )}

          {feature.estimatedDelivery && (
            <div className="text-xs sm:text-sm text-gray-400 flex items-center gap-1 sm:gap-2 mt-2">
              <Calendar className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500" />
              <span>Estimated: {feature.estimatedDelivery}</span>
            </div>
          )}
        </CardContent>

        <CardFooter className="pt-2 border-t border-gray-700/50 px-3 sm:px-6 py-2 sm:py-3">
          <div className="flex justify-between items-center w-full">
            <div className="flex items-center gap-1">
              <ThumbsUp className="h-3 w-3 sm:h-4 sm:w-4 text-gray-400" />
              <span className="text-gray-300 text-sm sm:text-base font-medium">{feature.votes}</span>
            </div>

            {/* Separate the button from the tooltip for better click handling */}
            {hasVoted ? (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="inline-block">
                      <Button
                        disabled={true}
                        variant="outline"
                        size="sm"
                        className="h-7 sm:h-9 px-2 sm:px-3 py-0 text-xs sm:text-sm bg-gray-700/50 text-gray-400 border-gray-600/30 pointer-events-none"
                      >
                        Voted
                      </Button>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    You've already voted for this feature
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ) : (
              <div className="relative inline-block">
                {/* Direct button without tooltip interference */}
                <button
                  type="button"
                  onClick={(e) => {
                    // Stop event propagation to prevent card interactions
                    e.stopPropagation();
                    // Call the vote handler
                    onVote(feature.id);
                  }}
                  className="h-7 sm:h-9 px-2 sm:px-3 py-0 sm:py-2 rounded-md text-xs sm:text-sm font-medium bg-gradient-to-r from-purple-600 to-cyan-500 text-white hover:from-purple-700 hover:to-cyan-600 cursor-pointer transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900"
                >
                  Vote
                </button>
              </div>
            )}
          </div>
        </CardFooter>
      </Card>
    </motion.div>
  );
};

// Sample features data (fallback if API fails)
const sampleFeatures: Feature[] = [
  // Implemented Features
  {
    id: 'impl-1',
    title: 'Real-time Song Requests',
    description: 'Live song request system with instant updates and DJ management tools',
    category: 'implemented',
    votes: 150,
    progress: 100,
    tags: ['Core Feature', 'Real-time', 'DJ Tools']
  },
  {
    id: 'impl-2',
    title: 'Genre Blocking System',
    description: 'Allow DJs to block specific music genres from being requested',
    category: 'implemented',
    votes: 89,
    progress: 100,
    tags: ['DJ Control', 'Content Filtering']
  },
  {
    id: 'impl-3',
    title: 'Apple Music Integration',
    description: 'Search and request songs directly from Apple Music catalog',
    category: 'implemented',
    votes: 142,
    progress: 100,
    tags: ['Integration', 'Music Services', 'Core Feature']
  },
  {
    id: '1',
    title: 'Mobile App for DJs',
    description: 'Native mobile application for DJs to manage requests on the go',
    category: 'coming-soon',
    votes: 124,
    progress: 75,
    estimatedDelivery: 'Q2 2024',
    tags: ['Mobile', 'DJ Experience']
  },
  {
    id: '2',
    title: 'Spotify Integration',
    description: 'Connect your Spotify account to manage song requests',
    category: 'coming-soon',
    votes: 98,
    progress: 60,
    estimatedDelivery: 'Q2 2024',
    tags: ['Integration', 'Music Services']
  },
  {
    id: '3',
    title: 'Advanced Analytics Dashboard',
    description: 'Detailed insights into song request patterns and audience preferences',
    category: 'planned',
    votes: 76,
    estimatedDelivery: 'Q3 2024',
    tags: ['Analytics', 'DJ Tools']
  },
  {
    id: '4',
    title: 'Custom Branding Options',
    description: 'Personalize the request page with your own logo and colors',
    category: 'planned',
    votes: 65,
    tags: ['Customization', 'Branding']
  },
  {
    id: '5',
    title: 'Audience Chat Feature',
    description: 'Allow event attendees to chat with the DJ and each other',
    category: 'consideration',
    votes: 42,
    tags: ['Social', 'Audience Engagement']
  },
  {
    id: '6',
    title: 'Virtual Tip Jar',
    description: 'Let your audience send tips directly through the app',
    category: 'consideration',
    votes: 38,
    tags: ['Monetization', 'DJ Income']
  },
  // Coming Soon Features
  {
    id: '7',
    title: 'Multi-Platform Song Search',
    description: 'Search for songs across multiple music platforms simultaneously',
    category: 'coming-soon',
    votes: 87,
    progress: 65,
    estimatedDelivery: 'Q2 2024',
    tags: ['Search', 'Music Discovery']
  },
  {
    id: '8',
    title: 'Request Queue Management',
    description: 'Advanced tools for DJs to organize, prioritize, and filter song requests',
    category: 'coming-soon',
    votes: 92,
    progress: 70,
    estimatedDelivery: 'Q2 2024',
    tags: ['DJ Tools', 'Workflow']
  },
  {
    id: '9',
    title: 'Event Templates',
    description: 'Save and reuse settings for recurring events or similar venues',
    category: 'coming-soon',
    votes: 78,
    progress: 55,
    estimatedDelivery: 'Q3 2024',
    tags: ['DJ Tools', 'Productivity']
  },

  // Planned Features
  {
    id: '10',
    title: 'Audience Song Voting',
    description: 'Let attendees vote on which requested songs should be played next',
    category: 'planned',
    votes: 81,
    estimatedDelivery: 'Q3 2024',
    tags: ['Audience Engagement', 'Social']
  },
  {
    id: '11',
    title: 'DJ Profiles & Discovery',
    description: 'Public profiles for DJs with ratings, specialties, and booking information',
    category: 'planned',
    votes: 63,
    estimatedDelivery: 'Q4 2024',
    tags: ['DJ Marketing', 'Discovery']
  },
  {
    id: '12',
    title: 'Genre Preferences',
    description: 'Set genre preferences for events to guide audience song requests',
    category: 'planned',
    votes: 72,
    estimatedDelivery: 'Q3 2024',
    tags: ['Music Curation', 'DJ Tools']
  },
  {
    id: '13',
    title: 'Automated Playlist Generation',
    description: 'AI-powered playlist suggestions based on audience demographics and preferences',
    category: 'planned',
    votes: 68,
    tags: ['AI', 'Music Curation']
  },
  {
    id: '14',
    title: 'Event Photo Sharing',
    description: 'Allow attendees to share and view photos from the event',
    category: 'planned',
    votes: 59,
    tags: ['Social', 'Audience Engagement']
  },
  {
    id: '15',
    title: 'Venue Integration',
    description: 'Partner with venues to offer seamless PlayBeg integration for all events',
    category: 'planned',
    votes: 54,
    tags: ['Partnerships', 'Venues']
  },

  // Under Consideration Features
  {
    id: '16',
    title: 'Song Request Comments',
    description: 'Allow requesters to add notes or dedications with their song requests',
    category: 'consideration',
    votes: 47,
    tags: ['Audience Engagement', 'Personalization']
  },
  {
    id: '17',
    title: 'DJ Collaboration Tools',
    description: 'Features for multiple DJs to collaborate on events and share request queues',
    category: 'consideration',
    votes: 41,
    tags: ['Collaboration', 'DJ Tools']
  },
  {
    id: '18',
    title: 'Offline Mode',
    description: 'Continue using core features when internet connection is limited or unavailable',
    category: 'consideration',
    votes: 53,
    tags: ['Reliability', 'DJ Experience']
  },
  {
    id: '19',
    title: 'Song Request Limits by User',
    description: 'Set custom limits on how many songs each attendee can request',
    category: 'consideration',
    votes: 39,
    tags: ['DJ Control', 'Event Management']
  },
  {
    id: '20',
    title: 'Mood-Based Requests',
    description: 'Allow audience to request songs based on mood rather than specific tracks',
    category: 'consideration',
    votes: 36,
    tags: ['Music Discovery', 'Audience Experience']
  },
  {
    id: '21',
    title: 'Event Recap & Stats',
    description: 'Post-event analytics and shareable summaries of the most popular songs',
    category: 'consideration',
    votes: 44,
    tags: ['Analytics', 'Social Sharing']
  },
  {
    id: '22',
    title: 'YouTube Music Integration',
    description: 'Connect with YouTube Music to expand song availability and discovery',
    category: 'consideration',
    votes: 49,
    tags: ['Integration', 'Music Services']
  },
  {
    id: '23',
    title: 'Live Song Announcements',
    description: 'Display currently playing song information on a dedicated screen for venues',
    category: 'consideration',
    votes: 37,
    tags: ['Venue Experience', 'Audience Engagement']
  },
  {
    id: '24',
    title: 'Request Notifications',
    description: 'Real-time notifications when your requested song is coming up or playing',
    category: 'consideration',
    votes: 51,
    tags: ['Audience Experience', 'Notifications']
  },
  {
    id: '25',
    title: 'DJ Equipment Integration',
    description: 'Direct integration with popular DJ hardware and software platforms',
    category: 'consideration',
    votes: 46,
    tags: ['Integration', 'DJ Hardware']
  },
  {
    id: '26',
    title: 'Multi-Language Support',
    description: 'Translate the PlayBeg interface into multiple languages for international events',
    category: 'consideration',
    votes: 33,
    tags: ['Accessibility', 'Global Reach']
  }
];

export default ProductRoadmap;
