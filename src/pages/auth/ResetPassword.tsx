import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { supabase } from "@/integrations/supabase/client";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Loader2, CheckCircle2, Music } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { SEOHead } from "@/components/seo/SEOHead";

const resetSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
});

type ResetFormValues = z.infer<typeof resetSchema>;

const ResetPassword = () => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const form = useForm<ResetFormValues>({
    resolver: zodResolver(resetSchema),
    defaultValues: {
      email: "",
    },
  });



  const onSubmit = async (data: ResetFormValues) => {
    setIsSubmitting(true);

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(data.email, {
        redirectTo: `${window.location.origin}/auth/update-password`,
      });

      if (error) {
        throw error;
      }

      setIsSubmitted(true);
      toast({
        title: "Password reset email sent",
        description: "Please check your email for a link to reset your password",
      });
    } catch (error: any) {
      toast({
        title: "Password reset failed",
        description: error.message || "An error occurred. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="md:mx-auto md:max-w-md md:p-8 md:rounded-2xl md:bg-gray-900/50 md:border md:border-purple-500/20 md:backdrop-blur-md md:shadow-xl">
      <SEOHead
        title="Reset Password | PlayBeg"
        description="Reset your PlayBeg account password. Recover access to your DJ song request platform account."
        noindex={true}
      />
      {/* PlayBeg Branding Element - Desktop Only */}
      <div className="hidden md:flex md:items-center md:justify-center md:mb-8">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center">
            <Music className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-xl font-bold text-white">Welcome to PlayBeg</h2>
          <p className="text-gray-400 text-sm">The DJ's song request platform</p>
        </div>
      </div>

      <div className="space-y-6 md:space-y-8">
        <div className="space-y-3 text-center">
          <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-500 to-cyan-400 bg-clip-text text-transparent leading-tight">Reset Password</h1>
          <p className="text-gray-400 md:text-muted-foreground text-sm md:text-base leading-relaxed">
            Recover access to your account
          </p>
        </div>

        {isSubmitted ? (
          <div className="mt-6 md:mt-8 text-center space-y-6 bg-black/40 border border-purple-500/20 rounded-lg p-6 md:p-8 shadow-md">
            <CheckCircle2 className="h-12 w-12 text-green-500 mx-auto" />
            <div className="space-y-4">
              <p className="text-gray-300 text-sm md:text-base leading-6">
                If an account exists with this email, you will receive a password reset link shortly.
              </p>
              <p className="text-gray-400 text-xs md:text-sm leading-5">
                Please check your inbox and spam folder.
              </p>
            </div>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 md:space-y-6">
              <div className="space-y-5 md:space-y-6">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <FormLabel className="block text-sm md:text-base font-medium text-white leading-5">Email Address</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                          className="w-full h-11 md:h-12 px-4 py-3 rounded-lg bg-gray-800 border border-gray-700 text-white text-sm md:text-base leading-5 focus:ring-2 focus:ring-purple-500 focus:border-purple-500/50 focus:outline-none transition-colors"
                          required
                          aria-required="true"
                        />
                      </FormControl>
                      <FormMessage className="text-xs md:text-sm leading-4" />
                    </FormItem>
                  )}
                />
              </div>

              <Button
                type="submit"
                className="w-full h-10 md:h-12 mt-4 md:mt-6 text-base md:text-lg font-semibold tracking-wide rounded-lg md:rounded-xl bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white md:hover:scale-105 md:transition-transform duration-200 flex items-center justify-center"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Sending reset link...
                  </>
                ) : (
                  <span>Send Reset Link</span>
                )}
              </Button>
            </form>
          </Form>
        )}

        <div className="flex flex-col space-y-3 md:flex-row md:justify-between md:space-y-0 mt-6 md:mt-8 text-sm md:text-base">
          <Link to="/login" className="text-purple-400 hover:text-purple-300 transition-colors leading-5 text-center md:text-left">
            Remember your password? Sign in
          </Link>
          <Link to="/signup" className="text-purple-400 hover:text-purple-300 transition-colors leading-5 text-center md:text-right">
            Don't have an account? Sign up
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;
