
import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useAuth } from "@/context/AuthContext";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Loader2, Music } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

type LoginFormValues = z.infer<typeof loginSchema>;

const Login = () => {
  const { signIn, isLoading } = useAuth();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });



  const onSubmit = async (data: LoginFormValues) => {
    setIsSubmitting(true);

    try {
      await signIn(data.email, data.password);
    } catch (error: any) {
      toast({
        title: "Login failed",
        description: error.message || "Invalid email or password",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="md:mx-auto md:max-w-md md:p-8 md:rounded-2xl md:bg-gray-900/50 md:border md:border-purple-500/20 md:backdrop-blur-md md:shadow-xl">
      {/* PlayBeg Branding Element - Desktop Only */}
      <div className="hidden md:flex md:items-center md:justify-center md:mb-8">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center">
            <Music className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-xl font-bold text-white">Welcome to PlayBeg</h2>
          <p className="text-gray-400 text-sm">The DJ's song request platform</p>
        </div>
      </div>

      <div className="space-y-6 md:space-y-8">
        <div className="space-y-2 text-center">
          <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-500 to-cyan-400 bg-clip-text text-transparent">Sign In to PlayBeg</h1>
          <p className="text-gray-400 md:text-muted-foreground text-base mt-2">
            Access your DJ dashboard
          </p>
        </div>


        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 md:space-y-6">
            <div className="space-y-4 md:space-y-6">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="block text-sm md:text-base font-medium text-white mb-2">Email Address</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        autoComplete="email"
                        {...field}
                        className="w-full h-10 md:h-12 px-3 rounded-lg bg-gray-800 border border-gray-700 text-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500/50 focus:outline-none transition-colors"
                        required
                        aria-required="true"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="block text-sm md:text-base font-medium text-white mb-2">Password</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="••••••••"
                        autoComplete="current-password"
                        {...field}
                        className="w-full h-10 md:h-12 px-3 rounded-lg bg-gray-800 border border-gray-700 text-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500/50 focus:outline-none transition-colors"
                        required
                        aria-required="true"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Button
              type="submit"
              className="w-full h-10 md:h-12 mt-4 md:mt-6 text-base md:text-lg font-semibold tracking-wide rounded-lg md:rounded-xl bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white md:hover:scale-105 md:transition-transform duration-200 flex items-center justify-center"
              disabled={isSubmitting || isLoading}
            >
              {(isSubmitting || isLoading) ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Signing in...
                </>
              ) : <span>Sign In</span>}
            </Button>
          </form>
        </Form>

        <div className="flex flex-col space-y-2 md:flex-row md:justify-between md:space-y-0 mt-4 md:mt-6 text-sm">
          <Link to="/auth/reset-password" className="text-purple-400 hover:text-purple-300 transition-colors">
            Forgot password?
          </Link>
          <Link to="/signup" className="text-purple-400 hover:text-purple-300 transition-colors">
            Don't have an account? Sign up
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Login;
