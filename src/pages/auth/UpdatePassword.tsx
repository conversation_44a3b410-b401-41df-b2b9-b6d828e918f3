import { useState, useEffect } from "react";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { supabase } from "@/integrations/supabase/client";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Loader2, CheckCircle2, Music, AlertCircle } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { SEOHead } from "@/components/seo/SEOHead";

const updatePasswordSchema = z.object({
  password: z.string().min(6, "Password must be at least 6 characters"),
  confirmPassword: z.string().min(6, "Password must be at least 6 characters")
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"]
});

type UpdatePasswordFormValues = z.infer<typeof updatePasswordSchema>;

const UpdatePassword = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isValidating, setIsValidating] = useState(true);
  const [isValidCode, setIsValidCode] = useState(false);

  const form = useForm<UpdatePasswordFormValues>({
    resolver: zodResolver(updatePasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  // Validate the reset code when component mounts
  useEffect(() => {
    const validateResetCode = async () => {
      console.log('Validating reset code...');
      console.log('Current URL:', window.location.href);
      console.log('Search params:', Object.fromEntries(searchParams.entries()));
      console.log('Hash:', window.location.hash);

      // Check for hash parameters as well (Supabase sometimes uses hash-based auth)
      const hashParams = new URLSearchParams(window.location.hash.substring(1));
      console.log('Hash params:', Object.fromEntries(hashParams.entries()));

      try {
        // Give Supabase a moment to process any URL parameters with detectSessionInUrl
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Get the current session to see if Supabase has already processed the reset link
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

        console.log('Current session:', sessionData);
        console.log('Session error:', sessionError);

        if (sessionError) {
          console.error('Error getting session:', sessionError);
          toast({
            title: "Session error",
            description: "There was an error validating your session. Please try again.",
            variant: "destructive"
          });
          setIsValidCode(false);
          setIsValidating(false);
          return;
        }

        // Check if we have a valid session and if it's a recovery session
        if (sessionData.session && sessionData.session.user) {
          console.log('Valid session found:', sessionData.session.user.email);
          setIsValidCode(true);
        } else {
          // Check for URL parameters that might indicate a reset flow
          const error = searchParams.get('error');
          const errorDescription = searchParams.get('error_description');

          if (error) {
            console.error('Auth error from URL:', error, errorDescription);
            toast({
              title: "Authentication error",
              description: errorDescription || "There was an error with the password reset link.",
              variant: "destructive"
            });
            setIsValidCode(false);
          } else {
            // No session and no error - this might be an invalid or expired link
            console.log('No valid session found and no error parameters');
            toast({
              title: "Invalid or expired reset link",
              description: "This password reset link is invalid or has expired. Please request a new one.",
              variant: "destructive"
            });
            setIsValidCode(false);
          }
        }
      } catch (error) {
        console.error('Error validating reset code:', error);
        toast({
          title: "Error validating reset link",
          description: "There was an error validating your reset link. Please try again.",
          variant: "destructive"
        });
        setIsValidCode(false);
      } finally {
        setIsValidating(false);
      }
    };

    validateResetCode();

    // Also listen for auth state changes in case Supabase processes the reset in the background
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('Auth state change:', event, session?.user?.email);

      if (event === 'PASSWORD_RECOVERY' || (event === 'SIGNED_IN' && session)) {
        console.log('Password recovery session detected');
        setIsValidCode(true);
        setIsValidating(false);
      } else if (event === 'SIGNED_OUT') {
        console.log('User signed out');
        setIsValidCode(false);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [searchParams, toast]);

  const onSubmit = async (data: UpdatePasswordFormValues) => {
    if (!isValidCode) {
      toast({
        title: "Invalid session",
        description: "Your password reset session is invalid. Please request a new reset link.",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // First, let's get detailed information about the current session
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      console.log('Current session before password update:', sessionData);
      console.log('Session error:', sessionError);

      if (!sessionData.session || !sessionData.session.user) {
        throw new Error("No valid session found. Please try the password reset process again.");
      }

      const currentUser = sessionData.session.user;
      console.log('Current user:', {
        id: currentUser.id,
        email: currentUser.email,
        aud: currentUser.aud,
        role: currentUser.role,
        created_at: currentUser.created_at,
        updated_at: currentUser.updated_at
      });

      // Check if the user exists in the auth.users table by trying to get user info
      const { data: userData, error: userError } = await supabase.auth.getUser();
      console.log('User data from getUser():', userData);
      console.log('User error from getUser():', userError);

      if (userError) {
        console.error('Error getting user:', userError);
        throw new Error(`User validation failed: ${userError.message}`);
      }

      if (!userData.user) {
        throw new Error("User record not found. The account may have been deleted.");
      }

      // Now attempt to update the password
      console.log('Attempting to update password for user:', userData.user.id);
      const { error } = await supabase.auth.updateUser({
        password: data.password
      });

      if (error) {
        console.error('Password update error:', error);
        throw error;
      }

      console.log('Password updated successfully');
      setIsSuccess(true);
      toast({
        title: "Password updated successfully",
        description: "Your password has been updated. You can now sign in with your new password.",
      });

      // Sign out the user to clear the recovery session
      await supabase.auth.signOut();

      // Redirect to login after a short delay
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (error: any) {
      console.error('Error updating password:', error);

      let errorMessage = error.message || "An error occurred while updating your password. Please try again.";

      // Provide more specific error messages for common issues
      if (error.message?.includes("User from sub claim in JWT does not exist")) {
        errorMessage = "Your account session is invalid. This may happen if your account was recently modified. Please request a new password reset link from the login page.";

        // Log additional information for debugging
        console.error('JWT user mismatch error. This suggests the user ID in the JWT token does not exist in the auth.users table.');
        console.error('Possible causes: 1) User account was deleted, 2) Database inconsistency, 3) Invalid recovery token');
        console.error('Recommended action: User should request a new password reset link');

      } else if (error.message?.includes("Invalid JWT")) {
        errorMessage = "Your password reset session has expired. Please request a new password reset link.";
      } else if (error.message?.includes("User not found")) {
        errorMessage = "Your account could not be found. Please contact support if this issue persists.";
      }

      toast({
        title: "Password update failed",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading state while validating
  if (isValidating) {
    return (
      <div className="md:mx-auto md:max-w-md md:p-8 md:rounded-2xl md:bg-gray-900/50 md:border md:border-purple-500/20 md:backdrop-blur-md md:shadow-xl">
        <SEOHead
          title="Update Password | PlayBeg"
          description="Update your PlayBeg account password."
          noindex={true}
        />
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-purple-500" />
          <p className="text-gray-400">Validating reset link...</p>
        </div>
      </div>
    );
  }

  // Show error state for invalid codes
  if (!isValidCode) {
    return (
      <div className="md:mx-auto md:max-w-md md:p-8 md:rounded-2xl md:bg-gray-900/50 md:border md:border-purple-500/20 md:backdrop-blur-md md:shadow-xl">
        <SEOHead
          title="Invalid Reset Link | PlayBeg"
          description="Invalid password reset link."
          noindex={true}
        />
        <div className="text-center space-y-6">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
          <div className="space-y-2">
            <h1 className="text-xl font-bold text-white">Invalid Reset Link</h1>
            <p className="text-gray-400 text-sm">
              This password reset link is invalid or has expired.
            </p>
          </div>
          <Link
            to="/auth/reset-password"
            className="inline-block bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
          >
            Request New Reset Link
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="md:mx-auto md:max-w-md md:p-8 md:rounded-2xl md:bg-gray-900/50 md:border md:border-purple-500/20 md:backdrop-blur-md md:shadow-xl">
      <SEOHead
        title="Update Password | PlayBeg"
        description="Update your PlayBeg account password."
        noindex={true}
      />
      {/* PlayBeg Branding Element - Desktop Only */}
      <div className="hidden md:flex md:items-center md:justify-center md:mb-8">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center">
            <Music className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-xl font-bold text-white">Welcome to PlayBeg</h2>
          <p className="text-gray-400 text-sm">The DJ's song request platform</p>
        </div>
      </div>

      <div className="space-y-6 md:space-y-8">
        <div className="space-y-3 text-center">
          <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-500 to-cyan-400 bg-clip-text text-transparent leading-tight">
            Update Password
          </h1>
          <p className="text-gray-400 md:text-muted-foreground text-sm md:text-base leading-relaxed">
            Enter your new password below
          </p>
        </div>

        {isSuccess ? (
          <div className="mt-6 md:mt-8 text-center space-y-6 bg-black/40 border border-purple-500/20 rounded-lg p-6 md:p-8 shadow-md">
            <CheckCircle2 className="h-12 w-12 text-green-500 mx-auto" />
            <div className="space-y-4">
              <p className="text-gray-300 text-sm md:text-base leading-6">
                Your password has been updated successfully!
              </p>
              <p className="text-gray-400 text-xs md:text-sm leading-5">
                Redirecting you to the login page...
              </p>
            </div>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 md:space-y-6">
              <div className="space-y-5 md:space-y-6">
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <FormLabel className="block text-sm md:text-base font-medium text-white leading-5">
                        New Password
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="Enter your new password"
                          {...field}
                          className="w-full h-11 md:h-12 px-4 py-3 rounded-lg bg-gray-800 border border-gray-700 text-white text-sm md:text-base leading-5 focus:ring-2 focus:ring-purple-500 focus:border-purple-500/50 focus:outline-none transition-colors"
                          required
                          aria-required="true"
                        />
                      </FormControl>
                      <FormMessage className="text-xs md:text-sm leading-4" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <FormLabel className="block text-sm md:text-base font-medium text-white leading-5">
                        Confirm New Password
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="Confirm your new password"
                          {...field}
                          className="w-full h-11 md:h-12 px-4 py-3 rounded-lg bg-gray-800 border border-gray-700 text-white text-sm md:text-base leading-5 focus:ring-2 focus:ring-purple-500 focus:border-purple-500/50 focus:outline-none transition-colors"
                          required
                          aria-required="true"
                        />
                      </FormControl>
                      <FormMessage className="text-xs md:text-sm leading-4" />
                    </FormItem>
                  )}
                />
              </div>

              <Button
                type="submit"
                className="w-full h-10 md:h-12 mt-4 md:mt-6 text-base md:text-lg font-semibold tracking-wide rounded-lg md:rounded-xl bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white md:hover:scale-105 md:transition-transform duration-200 flex items-center justify-center"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating password...
                  </>
                ) : (
                  <span>Update Password</span>
                )}
              </Button>
            </form>
          </Form>
        )}

        <div className="flex justify-center mt-6 md:mt-8 text-sm md:text-base">
          <Link to="/login" className="text-purple-400 hover:text-purple-300 transition-colors leading-5">
            Back to Sign In
          </Link>
        </div>
      </div>
    </div>
  );
};

export default UpdatePassword;
