import { SessionNotFound } from "@/components/song-request/SessionNotFound";
import { useSessionData } from "@/hooks/useSessionData";
import { SessionDetailProps } from "@/types/session";
import SongRequestsView from "./SongRequestsView";

const SessionDetail = ({ sessionId, onBack }: SessionDetailProps) => {
  const {
    session,
    sessionNotFound,
    isLoadingSession
  } = useSessionData(sessionId);

  if (sessionNotFound) {
    return <SessionNotFound onBack={onBack} />;
  }

  if (isLoadingSession) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
        <span className="ml-3 text-gray-400">Loading session...</span>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  // SessionDetail now only handles the focused Song Requests view
  // All administrative components have been moved to SessionsList
  return (
    <SongRequestsView
      sessionId={sessionId}
      sessionName={session.name || 'Untitled Session'}
      onBack={onBack}
    />
  );
};

export default SessionDetail;
