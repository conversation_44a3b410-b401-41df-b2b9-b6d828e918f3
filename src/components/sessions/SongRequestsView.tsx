import { useState, useMemo } from "react";
import { useToast } from "@/components/ui/use-toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { supabase } from "@/integrations/supabase/client";
import { Play, Clock, Music, X, Check, CheckCircle, XCircle, List, ChevronDown, ChevronUp, ArrowLeft, Loader2 } from "lucide-react";
import { useSongStatuses } from "@/hooks/useSongStatuses";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useSessionData } from "@/hooks/useSessionData";
import { useSessionSubscriptions } from "@/hooks/useSessionSubscriptions";
import { RequestStatus, GroupedRequest } from "@/types/session";
import { Tables } from "@/integrations/supabase/types";

// Define a type for song requests using the Tables utility
type SongRequest = Tables<"song_requests">;

// Helper function to get status color classes
const getStatusColor = (status: RequestStatus) => {
  switch (status) {
    case "pending":
      return "bg-purple-500/20 text-purple-300 border border-purple-500/30";
    case "approved":
      return "bg-green-500/20 text-green-300 border border-green-500/30";
    case "auto-approved":
      return "bg-green-500/20 text-green-300 border border-green-500/30";
    case "played":
      return "bg-blue-500/20 text-blue-300 border border-blue-500/30";
    case "declined":
      return "bg-red-500/20 text-red-300 border border-red-500/30";
    default:
      console.warn('Unknown status:', status);
      return "bg-gray-500/20 text-gray-300 border border-gray-500/30";
  }
};

interface SongRequestsViewProps {
  sessionId: string;
  sessionName: string;
  onBack: () => void;
}

const SongRequestsView = ({ sessionId, sessionName, onBack }: SongRequestsViewProps) => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState<"pending" | "approved" | "played" | "declined" | "all">("pending");
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});

  // Loading states for buttons - tracks which group+action is currently processing
  const [loadingStates, setLoadingStates] = useState<Record<string, {
    action: 'approve' | 'auto' | 'decline' | 'played';
    stage: 'processing' | 'success';
  }>>({});

  const {
    requests,
    isLoadingRequests,
    refetchRequests
  } = useSessionData(sessionId);

  const { updateSongStatus } = useSongStatuses(sessionId, null, null);

  useSessionSubscriptions(sessionId, refetchRequests, () => {});

  // Helper functions for managing button loading states
  const setButtonLoading = (groupKey: string, action: 'approve' | 'auto' | 'decline' | 'played') => {
    setLoadingStates(prev => ({
      ...prev,
      [groupKey]: { action, stage: 'processing' }
    }));
  };

  const setButtonSuccess = (groupKey: string, action: 'approve' | 'auto' | 'decline' | 'played') => {
    setLoadingStates(prev => ({
      ...prev,
      [groupKey]: { action, stage: 'success' }
    }));

    // Clear success state after 1 second
    setTimeout(() => {
      setLoadingStates(prev => {
        const newState = { ...prev };
        delete newState[groupKey];
        return newState;
      });
    }, 1000);
  };

  const clearButtonLoading = (groupKey: string) => {
    setLoadingStates(prev => {
      const newState = { ...prev };
      delete newState[groupKey];
      return newState;
    });
  };

  const getButtonState = (groupKey: string, action: 'approve' | 'auto' | 'decline' | 'played') => {
    const state = loadingStates[groupKey];
    if (!state || state.action !== action) return 'idle';
    return state.stage;
  };

  // Group the requests by song (title + artist)
  const groupedRequests = useMemo(() => {
    console.log('Grouping requests:', requests);
    const groups: Record<string, GroupedRequest> = {};
    requests.forEach(request => {
      const key = `${request.song_title}:${request.artist_name}`.toLowerCase();
      if (!groups[key]) {
        console.log('Creating new group for song:', request.song_title, 'with initial status:', request.status);
        groups[key] = {
          key,
          songTitle: request.song_title,
          artistName: request.artist_name,
          appleMusicId: request.apple_music_id,
          albumArtwork: request.album_artwork,
          status: request.status as RequestStatus,
          count: 0,
          requests: [],
          createdAt: request.created_at,
          lastRequestAt: request.created_at
        };
      }
      groups[key].count++;
      groups[key].requests.push(request);

      if (new Date(request.created_at) < new Date(groups[key].createdAt)) {
        groups[key].createdAt = request.created_at;
      }
      if (new Date(request.created_at) > new Date(groups[key].lastRequestAt)) {
        groups[key].lastRequestAt = request.created_at;
      }

      const requestStatus = request.status as RequestStatus;
      const oldStatus = groups[key].status;

      if (requestStatus === 'pending') {
        groups[key].status = 'pending';
        console.log(`Updated group status for ${request.song_title} from ${oldStatus} to pending`);
      } else if (requestStatus === 'approved' || requestStatus === 'auto-approved') {
        if (groups[key].status !== 'pending') {
          groups[key].status = requestStatus;
          console.log(`Updated group status for ${request.song_title} from ${oldStatus} to ${requestStatus}`);
        }
      } else if (requestStatus === 'played') {
        if (groups[key].status !== 'pending' &&
            groups[key].status !== 'approved' &&
            groups[key].status !== 'auto-approved') {
          groups[key].status = 'played';
          console.log(`Updated group status for ${request.song_title} from ${oldStatus} to played`);
        }
      }
    });

    const sortedGroups = Object.values(groups).sort((a, b) => {
      if (a.status === 'pending' && b.status !== 'pending') return -1;
      if (a.status !== 'pending' && b.status === 'pending') return 1;
      if (a.count !== b.count) return b.count - a.count;
      return new Date(b.lastRequestAt).getTime() - new Date(a.lastRequestAt).getTime();
    });

    // Handle auto-approved songs properly
    Object.values(groups).forEach(group => {
      const hasAutoApproved = group.requests.some(req => req.status === 'auto-approved');
      const hasPending = group.requests.some(req => req.status === 'pending');

      if (hasAutoApproved && !hasPending && group.status !== 'auto-approved') {
        console.log(`Fixing group status for ${group.songTitle} from ${group.status} to auto-approved`);
        group.status = 'auto-approved';
      }
    });

    console.log('Grouped requests:', sortedGroups);
    return sortedGroups;
  }, [requests]);

  const autoApproveRelatedRequests = async (songTitle: string, artistName: string) => {
    const groupKey = `${songTitle}:${artistName}`.toLowerCase();

    try {
      // Set immediate loading state
      setButtonLoading(groupKey, 'auto');

      const { data: requestData, error: fetchError } = await supabase
        .from('song_requests')
        .select('*')
        .eq('session_id', sessionId)
        .eq('song_title', songTitle)
        .eq('artist_name', artistName)
        .eq('status', 'pending')
        .limit(1)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') throw fetchError;

      const { error } = await supabase
        .from('song_requests')
        .update({ status: 'approved' })
        .eq('session_id', sessionId)
        .eq('song_title', songTitle)
        .eq('artist_name', artistName)
        .eq('status', 'pending');

      if (error) throw error;

      if (requestData && requestData.apple_music_id) {
        const song = {
          id: requestData.apple_music_id,
          title: requestData.song_title,
          artist: requestData.artist_name,
          artwork: requestData.album_artwork || ''
        };

        await updateSongStatus(song, 'approved', requestData.id);
      }

      // Show success state briefly
      setButtonSuccess(groupKey, 'auto');

      await refetchRequests();
      toast({
        title: "Success",
        description: "All related requests have been approved",
      });
    } catch (error) {
      console.error("Error auto-approving requests:", error);
      clearButtonLoading(groupKey);
      toast({
        title: "Error",
        description: "Failed to approve requests",
        variant: "destructive",
      });
    }
  };

  const updateAllRequestsInGroup = async (group: GroupedRequest, status: RequestStatus) => {
    const action = status === 'approved' ? 'approve' : status === 'declined' ? 'decline' : 'played';

    try {
      // Set immediate loading state
      setButtonLoading(group.key, action);

      const { error } = await supabase
        .from('song_requests')
        .update({ status })
        .eq('session_id', sessionId)
        .eq('song_title', group.songTitle)
        .eq('artist_name', group.artistName)
        .eq('status', group.status);

      if (error) throw error;

      if (status === 'approved' && group.appleMusicId) {
        const song = {
          id: group.appleMusicId,
          title: group.songTitle,
          artist: group.artistName,
          artwork: group.albumArtwork || ''
        };

        const requestId = group.requests.length > 0 ? group.requests[0].id : undefined;

        try {
          await updateSongStatus(song, status, requestId);
        } catch (playlistError) {
          console.error('Error adding song to Apple Music playlist:', playlistError);
        }
      }

      // Show success state briefly
      setButtonSuccess(group.key, action);

      await refetchRequests();
      toast({
        title: "Success",
        description: `All requests for "${group.songTitle}" have been ${status}`,
      });
    } catch (error) {
      console.error("Error updating group status:", error);
      clearButtonLoading(group.key);
      toast({
        title: "Error",
        description: "Failed to update request status",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="w-full h-full flex flex-col">
      {/* Sticky Header */}
      <div className="flex-shrink-0 bg-gray-900/95 backdrop-blur-md border-b border-purple-500/20">
        <div className="flex items-center p-3 xs:p-4">
          <Button
            variant="ghost"
            onClick={onBack}
            className="text-white hover:bg-purple-500/20 transition-colors duration-200 flex-shrink-0"
          >
            <ArrowLeft className="h-4 w-4 mr-1 xs:mr-2" />
            <span className="hidden xs:inline">Back to Sessions</span>
            <span className="xs:hidden">Back</span>
          </Button>
          <div className="text-center flex-1 px-2 min-w-0">
            <h1 className="text-sm xs:text-base sm:text-lg font-semibold text-white truncate">
              {sessionName}
            </h1>
            <p className="text-xs text-gray-400">Song Requests</p>
          </div>
          <div className="w-[80px] xs:w-[120px] flex-shrink-0"></div> {/* Spacer for centering */}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto p-4">
        <Card className="!bg-gray-900/70 border border-purple-500/20 backdrop-blur-md">
          <CardContent className="pt-6">
            {/* Filter Tabs */}
            <div className="mb-6">
              {/* Mobile: Horizontal scrollable tabs */}
              <div className="w-full overflow-x-auto pb-2 sm:hidden" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
                <div className="flex gap-2 min-w-max">
                {[
                  { label: 'Pending', value: 'pending', icon: Clock, activeClass: 'bg-purple-500/20 text-purple-300 border-purple-500/30', badgeClass: 'bg-purple-500/30 text-purple-200' },
                  { label: 'Approved', value: 'approved', icon: CheckCircle, activeClass: 'bg-green-500/20 text-green-300 border-green-500/30', badgeClass: 'bg-green-500/30 text-green-200' },
                  { label: 'Played', value: 'played', icon: Play, activeClass: 'bg-blue-500/20 text-blue-300 border-blue-500/30', badgeClass: 'bg-blue-500/30 text-blue-200' },
                  { label: 'Declined', value: 'declined', icon: XCircle, activeClass: 'bg-red-500/20 text-red-300 border-red-500/30', badgeClass: 'bg-red-500/30 text-red-200' },
                  { label: 'All', value: 'all', icon: List, activeClass: 'bg-gray-500/20 text-gray-300 border-gray-500/30', badgeClass: 'bg-gray-500/30 text-gray-200' },
                ].map((tab) => {
                  const count = tab.value === 'all' ? null : (() => {
                    const filteredGroups = groupedRequests.filter(group => {
                      if (tab.value === "approved") return group.status === "approved" || group.status === "auto-approved";
                      return group.status === tab.value;
                    });
                    return filteredGroups.length;
                  })();

                  const isActive = activeTab === tab.value;

                  return (
                    <Button
                      key={tab.value}
                      onClick={() => setActiveTab(tab.value as any)}
                      variant="ghost"
                      aria-pressed={isActive}
                      aria-label={`${tab.label} requests${count !== null ? `, ${count} items` : ''}`}
                      className={`
                        flex-shrink-0 min-w-[90px] w-[90px] h-[60px] flex flex-col items-center justify-center
                        text-xs px-3 py-2 rounded-lg border transition-all duration-200
                        focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-2 focus:ring-offset-gray-900
                        ${isActive
                          ? tab.activeClass + ' shadow-sm transform scale-[0.98]'
                          : 'text-gray-400 border-gray-700/30 hover:text-white hover:bg-gray-700/40 hover:border-gray-600/40 hover:scale-[1.02]'
                        }
                      `}
                    >
                      <div className="flex items-center gap-1 mb-1">
                        <tab.icon className={`w-3.5 h-3.5 transition-transform duration-200 ${isActive ? 'scale-110' : ''}`} />
                        <span className="font-medium leading-tight text-xs">{tab.label}</span>
                      </div>
                      {count !== null && (
                        <Badge
                          variant="secondary"
                          className={`
                            text-xs px-1.5 py-0.5 min-w-[18px] h-4 flex items-center justify-center
                            transition-colors duration-200 border-0
                            ${isActive
                              ? tab.badgeClass + ' font-semibold'
                              : 'bg-gray-700/50 text-gray-300 font-medium'
                            }
                          `}
                        >
                          {count}
                        </Badge>
                      )}
                    </Button>
                  );
                })}
                </div>
              </div>

              {/* Desktop: Grid layout */}
              <div className="hidden sm:grid sm:grid-cols-5 gap-2">
                {[
                  { label: 'Pending', value: 'pending', icon: Clock, activeClass: 'bg-purple-500/20 text-purple-300 border-purple-500/30', badgeClass: 'bg-purple-500/30 text-purple-200' },
                  { label: 'Approved', value: 'approved', icon: CheckCircle, activeClass: 'bg-green-500/20 text-green-300 border-green-500/30', badgeClass: 'bg-green-500/30 text-green-200' },
                  { label: 'Played', value: 'played', icon: Play, activeClass: 'bg-blue-500/20 text-blue-300 border-blue-500/30', badgeClass: 'bg-blue-500/30 text-blue-200' },
                  { label: 'Declined', value: 'declined', icon: XCircle, activeClass: 'bg-red-500/20 text-red-300 border-red-500/30', badgeClass: 'bg-red-500/30 text-red-200' },
                  { label: 'All', value: 'all', icon: List, activeClass: 'bg-gray-500/20 text-gray-300 border-gray-500/30', badgeClass: 'bg-gray-500/30 text-gray-200' },
                ].map((tab) => {
                  const count = tab.value === 'all' ? null : (() => {
                    const filteredGroups = groupedRequests.filter(group => {
                      if (tab.value === "approved") return group.status === "approved" || group.status === "auto-approved";
                      return group.status === tab.value;
                    });
                    return filteredGroups.length;
                  })();

                  const isActive = activeTab === tab.value;

                  return (
                    <Button
                      key={tab.value}
                      onClick={() => setActiveTab(tab.value as any)}
                      variant="ghost"
                      aria-pressed={isActive}
                      aria-label={`${tab.label} requests${count !== null ? `, ${count} items` : ''}`}
                      className={`
                        w-full min-h-[64px] flex flex-col items-center justify-center
                        text-sm px-3 py-3 rounded-lg border transition-all duration-200
                        focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-2 focus:ring-offset-gray-900
                        ${isActive
                          ? tab.activeClass + ' shadow-sm transform scale-[0.98]'
                          : 'text-gray-400 border-gray-700/30 hover:text-white hover:bg-gray-700/40 hover:border-gray-600/40 hover:scale-[1.02]'
                        }
                      `}
                    >
                      <div className="flex items-center gap-1.5 mb-1.5">
                        <tab.icon className={`w-4 h-4 transition-transform duration-200 ${isActive ? 'scale-110' : ''}`} />
                        <span className="font-medium leading-tight">{tab.label}</span>
                      </div>
                      {count !== null && (
                        <Badge
                          variant="secondary"
                          className={`
                            text-xs px-2 py-0.5 min-w-[22px] h-5 flex items-center justify-center
                            transition-colors duration-200 border-0
                            ${isActive
                              ? tab.badgeClass + ' font-semibold'
                              : 'bg-gray-700/50 text-gray-300 font-medium'
                            }
                          `}
                        >
                          {count}
                        </Badge>
                      )}
                    </Button>
                  );
                })}
              </div>
            </div>

            {/* Requests List */}
            <div className="space-y-3">
              {isLoadingRequests ? (
                <div className="flex justify-center items-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
                  <span className="ml-3 text-gray-400">Loading requests...</span>
                </div>
              ) : (() => {
                const filteredGroups = groupedRequests.filter(group => {
                  if (activeTab === "all") return true;
                  if (activeTab === "approved") return group.status === "approved" || group.status === "auto-approved";
                  return group.status === activeTab;
                });
                return filteredGroups.length === 0;
              })() ? (
                <div className="text-center py-12 border border-dashed border-gray-700 rounded-lg">
                  <div className="mx-auto w-12 h-12 rounded-full bg-gray-800/80 flex items-center justify-center mb-3">
                    <Music className="h-6 w-6 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-300">No {activeTab !== "all" ? activeTab : ""} requests yet</h3>
                  <p className="text-gray-500 mt-1">
                    {activeTab === "pending" ? "Share your session QR code to get song requests" :
                     activeTab === "approved" ? "Approve some pending requests to see them here" :
                     activeTab === "played" ? "Mark approved songs as played to see them here" :
                     activeTab === "declined" ? "No requests have been declined" :
                     "Share your session QR code to get song requests"}
                  </p>
                </div>
              ) : (
                (() => {
                  const filteredGroups = groupedRequests.filter(group => {
                    if (activeTab === "all") return true;
                    if (activeTab === "approved") return group.status === "approved" || group.status === "auto-approved";
                    return group.status === activeTab;
                  });
                  return filteredGroups;
                })().map((group) => {
                  const isExpanded = expandedGroups[group.key] || false;

                  return (
                    <div key={group.key} className="bg-gray-800/50 p-4 rounded-lg border border-gray-700/30">
                      <div className="flex items-start gap-3">
                        {/* Album Artwork */}
                        {group.albumArtwork ? (
                          <Avatar className="h-12 w-12 rounded-md shadow-lg shadow-black/20 ring-1 ring-white/10">
                            <AvatarImage src={group.albumArtwork} alt={`${group.songTitle} album art`} />
                            <AvatarFallback className="bg-gray-800 text-purple-300 rounded-md">{group.songTitle[0]}</AvatarFallback>
                          </Avatar>
                        ) : (
                          <Avatar className="h-12 w-12 rounded-md bg-gray-800">
                            <AvatarFallback className="bg-gray-800 text-purple-300 rounded-md">{group.songTitle[0]}</AvatarFallback>
                          </Avatar>
                        )}

                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-white text-sm line-clamp-1">{group.songTitle}</h4>
                          <p className="text-xs text-gray-400 line-clamp-1">{group.artistName}</p>

                          {/* Genre display */}
                          {group.requests[0]?.genre && (
                            <p className="text-xs text-purple-400 line-clamp-1 mt-0.5">{group.requests[0].genre}</p>
                          )}

                          <div className="flex items-center gap-2 mt-1">
                            <span className={`px-2 py-0.5 text-xs rounded-full ${getStatusColor(group.status)}`}>
                              {group.status}
                            </span>
                            <span className="text-xs text-gray-500">
                              {group.count} {group.count === 1 ? 'request' : 'requests'}
                            </span>
                          </div>

                          <div className="text-xs text-gray-500 mt-1">
                            First: {new Date(group.createdAt).toLocaleString()}
                          </div>
                        </div>
                      </div>

                      {/* Collapsible Requester Information */}
                      {group.count > 1 && (
                        <Collapsible open={isExpanded} onOpenChange={(open) =>
                          setExpandedGroups(prev => ({ ...prev, [group.key]: open }))
                        }>
                          <CollapsibleTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="w-full mt-2 text-xs text-gray-400 hover:text-white hover:bg-gray-700/50 transition-colors duration-200"
                            >
                              <div className="flex items-center justify-center gap-1">
                                <span>View all requesters</span>
                                {isExpanded ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
                              </div>
                            </Button>
                          </CollapsibleTrigger>
                          <CollapsibleContent className="mt-2">
                            <div className="bg-gray-900/50 rounded-lg p-2 border border-gray-700/30">
                              <ScrollArea className="max-h-[120px] pr-2">
                                <div className="space-y-1">
                                  {group.requests.map((request) => (
                                    <div
                                      key={request.id}
                                      className="flex items-center justify-between p-1.5 bg-gray-800/50 rounded-md border border-gray-700/30"
                                    >
                                      <div className="flex items-center space-x-2">
                                        <Avatar className="h-5 w-5 ring-1 ring-purple-500/20">
                                          <AvatarFallback className="bg-gray-700 text-gray-300 text-xs">{request.requester_name?.[0] || 'U'}</AvatarFallback>
                                        </Avatar>
                                        <span className="text-gray-300 text-xs">{request.requester_name || 'Anonymous'}</span>
                                      </div>
                                      <span className="text-xs text-gray-500">
                                        {new Date(request.created_at).toLocaleTimeString()}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              </ScrollArea>
                            </div>
                          </CollapsibleContent>
                        </Collapsible>
                      )}

                      {/* Action Buttons */}
                      {group.status === 'pending' && (
                        <div className="grid grid-cols-3 gap-2 mt-3">
                          <Button
                            variant="outline"
                            onClick={() => updateAllRequestsInGroup(group, 'declined')}
                            disabled={getButtonState(group.key, 'decline') !== 'idle'}
                            className={`h-12 px-3 flex items-center justify-center transition-all duration-200 text-sm font-medium ${
                              getButtonState(group.key, 'decline') === 'processing'
                                ? 'border border-red-500/60 bg-red-500/20 text-red-300 cursor-wait'
                                : getButtonState(group.key, 'decline') === 'success'
                                ? 'border border-red-500/80 bg-red-500/30 text-red-200 scale-[0.98]'
                                : 'border border-red-500/40 text-red-400 hover:bg-red-500/15 hover:border-red-500/60 active:scale-[0.98]'
                            }`}
                          >
                            {getButtonState(group.key, 'decline') === 'processing' ? (
                              <>
                                <Loader2 className="h-4 w-4 mr-1.5 animate-spin" />
                                Declining...
                              </>
                            ) : getButtonState(group.key, 'decline') === 'success' ? (
                              <>
                                <Check className="h-4 w-4 mr-1.5" />
                                Declined!
                              </>
                            ) : (
                              <>
                                <X className="h-4 w-4 mr-1.5" />
                                Decline
                              </>
                            )}
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => autoApproveRelatedRequests(group.songTitle, group.artistName)}
                            disabled={getButtonState(group.key, 'auto') !== 'idle'}
                            className={`h-12 px-3 flex items-center justify-center transition-all duration-200 text-sm font-medium ${
                              getButtonState(group.key, 'auto') === 'processing'
                                ? 'border border-purple-500/60 bg-purple-500/20 text-purple-300 cursor-wait'
                                : getButtonState(group.key, 'auto') === 'success'
                                ? 'border border-purple-500/80 bg-purple-500/30 text-purple-200 scale-[0.98]'
                                : 'border border-purple-500/40 text-purple-400 hover:bg-purple-500/15 hover:border-purple-500/60 active:scale-[0.98]'
                            }`}
                          >
                            {getButtonState(group.key, 'auto') === 'processing' ? (
                              <>
                                <Loader2 className="h-4 w-4 mr-1.5 animate-spin" />
                                Adding...
                              </>
                            ) : getButtonState(group.key, 'auto') === 'success' ? (
                              <>
                                <Check className="h-4 w-4 mr-1.5" />
                                Added!
                              </>
                            ) : (
                              'Auto'
                            )}
                          </Button>
                          <Button
                            onClick={() => updateAllRequestsInGroup(group, 'approved')}
                            disabled={getButtonState(group.key, 'approve') !== 'idle'}
                            className={`h-12 px-3 flex items-center justify-center transition-all duration-200 text-sm font-medium ${
                              getButtonState(group.key, 'approve') === 'processing'
                                ? 'bg-green-700 border border-green-700 text-green-100 cursor-wait'
                                : getButtonState(group.key, 'approve') === 'success'
                                ? 'bg-green-500 border border-green-500 text-white scale-[0.98] shadow-lg shadow-green-500/25'
                                : 'bg-green-600 hover:bg-green-700 border border-green-600 hover:border-green-700 text-white active:scale-[0.98]'
                            }`}
                          >
                            {getButtonState(group.key, 'approve') === 'processing' ? (
                              <>
                                <Loader2 className="h-4 w-4 mr-1.5 animate-spin" />
                                Adding to Playlist...
                              </>
                            ) : getButtonState(group.key, 'approve') === 'success' ? (
                              <>
                                <Check className="h-4 w-4 mr-1.5" />
                                Added to Playlist!
                              </>
                            ) : (
                              <>
                                <Check className="h-4 w-4 mr-1.5" />
                                Approve
                              </>
                            )}
                          </Button>
                        </div>
                      )}

                      {(group.status === 'approved' || group.status === 'auto-approved') && (
                        <div className="mt-3">
                          <Button
                            onClick={() => updateAllRequestsInGroup(group, 'played')}
                            disabled={getButtonState(group.key, 'played') !== 'idle'}
                            className={`w-full h-12 px-3 flex items-center justify-center transition-all duration-200 text-sm font-medium ${
                              getButtonState(group.key, 'played') === 'processing'
                                ? 'bg-blue-700 border border-blue-700 text-blue-100 cursor-wait'
                                : getButtonState(group.key, 'played') === 'success'
                                ? 'bg-blue-500 border border-blue-500 text-white scale-[0.98] shadow-lg shadow-blue-500/25'
                                : 'bg-blue-600 hover:bg-blue-700 border border-blue-600 hover:border-blue-700 text-white active:scale-[0.98]'
                            }`}
                          >
                            {getButtonState(group.key, 'played') === 'processing' ? (
                              <>
                                <Loader2 className="h-4 w-4 mr-1.5 animate-spin" />
                                Marking as Played...
                              </>
                            ) : getButtonState(group.key, 'played') === 'success' ? (
                              <>
                                <Check className="h-4 w-4 mr-1.5" />
                                Marked as Played!
                              </>
                            ) : (
                              <>
                                <Play className="h-4 w-4 mr-1.5" />
                                Mark as Played
                              </>
                            )}
                          </Button>
                        </div>
                      )}
                    </div>
                  );
                })
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SongRequestsView;
