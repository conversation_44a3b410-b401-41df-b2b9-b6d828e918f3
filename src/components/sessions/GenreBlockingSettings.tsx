import React, { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { X, Music, Shield, ChevronDown, ChevronRight, Search } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import { Tables } from '@/integrations/supabase/types';
import { GENRE_TAXONOMY, GenreTaxonomyUtils, GenreGroup } from '@/data/genreTaxonomy';

type Session = Tables<"sessions">;

interface GenreBlockingSettingsProps {
  session: Session;
  onUpdate: (updatedSession: Session) => void;
}

export default function GenreBlockingSettings({ session, onUpdate }: GenreBlockingSettingsProps) {
  const [blockedGenres, setBlockedGenres] = useState<string[]>(session.blocked_genres || []);
  const [isUpdating, setIsUpdating] = useState(false);
  const [showGenreSelector, setShowGenreSelector] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());

  useEffect(() => {
    setBlockedGenres(session.blocked_genres || []);
  }, [session.blocked_genres]);

  // Filtered groups based on search query
  const filteredGroups = useMemo(() => {
    if (!searchQuery.trim()) {
      return GENRE_TAXONOMY.groups;
    }

    const searchResults = GenreTaxonomyUtils.searchGenres(searchQuery);
    return searchResults.map(result => ({
      ...result.group,
      genres: result.matchingGenres
    }));
  }, [searchQuery]);

  // Auto-expand groups when searching
  useEffect(() => {
    if (searchQuery.trim()) {
      const matchingGroupIds = filteredGroups.map(group => group.id);
      setExpandedGroups(new Set(matchingGroupIds));
    }
  }, [searchQuery, filteredGroups]);

  const handleGenreToggle = (genre: string) => {
    setBlockedGenres(prev => {
      if (prev.includes(genre)) {
        return prev.filter(g => g !== genre);
      } else {
        return [...prev, genre];
      }
    });
  };

  const handleGroupToggle = (groupId: string) => {
    const group = GenreTaxonomyUtils.getGroupById(groupId);
    if (!group) return;

    const selectionState = GenreTaxonomyUtils.getGroupSelectionState(groupId, blockedGenres);

    setBlockedGenres(prev => {
      if (selectionState === 'full') {
        // Deselect all genres in this group
        return prev.filter(genre => !group.genres.includes(genre));
      } else {
        // Select all genres in this group
        const newGenres = group.genres.filter(genre => !prev.includes(genre));
        return [...prev, ...newGenres];
      }
    });
  };

  const handleRemoveGenre = (genre: string) => {
    setBlockedGenres(prev => prev.filter(g => g !== genre));
  };

  const toggleGroupExpansion = (groupId: string) => {
    setExpandedGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(groupId)) {
        newSet.delete(groupId);
      } else {
        newSet.add(groupId);
      }
      return newSet;
    });
  };

  const handleSaveChanges = async () => {
    setIsUpdating(true);
    try {
      const { data, error } = await supabase
        .from('sessions')
        .update({ blocked_genres: blockedGenres })
        .eq('id', session.id)
        .select()
        .single();

      if (error) throw error;

      onUpdate(data);
      toast({
        title: "Genre blocking updated",
        description: `${blockedGenres.length} genres are now blocked from requests.`,
      });
      setShowGenreSelector(false);
    } catch (error) {
      console.error('Error updating blocked genres:', error);
      toast({
        title: "Error",
        description: "Failed to update genre blocking settings.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const hasChanges = JSON.stringify(blockedGenres.sort()) !== JSON.stringify((session.blocked_genres || []).sort());

  return (
    <Card className="bg-gray-900/70 border-purple-500/20 backdrop-blur-md">
      <CardHeader>
        <div className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-purple-400" />
          <CardTitle className="text-white">Genre Blocking</CardTitle>
        </div>
        <CardDescription className="text-gray-400">
          Block specific music genres from being requested in this session
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Currently Blocked Genres */}
        {blockedGenres.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-white mb-2">Blocked Genres ({blockedGenres.length})</h4>
            <div className="flex flex-wrap gap-2">
              {blockedGenres.map((genre) => (
                <Badge
                  key={genre}
                  variant="destructive"
                  className="bg-red-600/20 text-red-400 border-red-500/30 hover:bg-red-600/30"
                >
                  <Music className="h-3 w-3 mr-1" />
                  {genre}
                  <button
                    onClick={() => handleRemoveGenre(genre)}
                    className="ml-1 hover:text-red-200"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Genre Selector */}
        {showGenreSelector && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-white">Select Genres to Block</h4>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search genres..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-64 bg-gray-800/50 border-gray-600 text-white placeholder-gray-400"
                />
              </div>
            </div>

            <div className="max-h-96 overflow-y-auto space-y-2">
              {filteredGroups.map((group) => {
                const selectionState = GenreTaxonomyUtils.getGroupSelectionState(group.id, blockedGenres);
                const isExpanded = expandedGroups.has(group.id);

                return (
                  <Collapsible
                    key={group.id}
                    open={isExpanded}
                    onOpenChange={() => toggleGroupExpansion(group.id)}
                  >
                    <div className="border border-gray-700/50 rounded-lg bg-gray-800/30">
                      {/* Group Header */}
                      <CollapsibleTrigger className="w-full p-3 flex items-center justify-between hover:bg-gray-700/30 transition-colors">
                        <div className="flex items-center space-x-3">
                          <Checkbox
                            checked={selectionState === 'full'}
                            ref={(el) => {
                              if (el) {
                                el.indeterminate = selectionState === 'partial';
                              }
                            }}
                            onCheckedChange={() => handleGroupToggle(group.id)}
                            onClick={(e) => e.stopPropagation()}
                            className="border-purple-500/30 data-[state=checked]:bg-purple-600 data-[state=checked]:border-purple-600"
                          />
                          <div className="flex items-center space-x-2">
                            <span className="text-lg">{group.icon}</span>
                            <div className="text-left">
                              <div className="font-medium text-white">{group.name}</div>
                              {group.description && (
                                <div className="text-xs text-gray-400">{group.description}</div>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="text-xs bg-gray-700/50 text-gray-300 border-gray-600">
                            {group.genres.filter(genre => blockedGenres.includes(genre)).length}/{group.genres.length}
                          </Badge>
                          {isExpanded ? (
                            <ChevronDown className="h-4 w-4 text-gray-400" />
                          ) : (
                            <ChevronRight className="h-4 w-4 text-gray-400" />
                          )}
                        </div>
                      </CollapsibleTrigger>

                      {/* Group Content */}
                      <CollapsibleContent>
                        <div className="px-3 pb-3 border-t border-gray-700/30">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-3">
                            {group.genres.map((genre) => (
                              <div key={genre} className="flex items-center space-x-2 py-1">
                                <Checkbox
                                  id={`${group.id}-${genre}`}
                                  checked={blockedGenres.includes(genre)}
                                  onCheckedChange={() => handleGenreToggle(genre)}
                                  className="border-purple-500/30 data-[state=checked]:bg-purple-600 data-[state=checked]:border-purple-600"
                                />
                                <label
                                  htmlFor={`${group.id}-${genre}`}
                                  className="text-sm text-gray-300 cursor-pointer hover:text-white flex-1"
                                >
                                  {genre}
                                </label>
                              </div>
                            ))}
                          </div>
                        </div>
                      </CollapsibleContent>
                    </div>
                  </Collapsible>
                );
              })}
            </div>

            {filteredGroups.length === 0 && searchQuery && (
              <div className="text-center py-8 text-gray-400">
                <Music className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No genres found matching "{searchQuery}"</p>
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          {!showGenreSelector ? (
            <Button
              onClick={() => setShowGenreSelector(true)}
              variant="outline"
              className="border-purple-500/30 text-purple-400 hover:bg-purple-600/20"
            >
              <Music className="h-4 w-4 mr-2" />
              {blockedGenres.length > 0 ? 'Modify Blocked Genres' : 'Block Genres'}
            </Button>
          ) : (
            <>
              <Button
                onClick={handleSaveChanges}
                disabled={!hasChanges || isUpdating}
                className="bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700"
              >
                {isUpdating ? 'Saving...' : 'Save Changes'}
              </Button>
              <Button
                onClick={() => {
                  setShowGenreSelector(false);
                  setBlockedGenres(session.blocked_genres || []);
                  setSearchQuery('');
                  setExpandedGroups(new Set());
                }}
                variant="outline"
                className="border-gray-600 text-gray-400 hover:bg-gray-800"
              >
                Cancel
              </Button>
            </>
          )}
        </div>

        {blockedGenres.length === 0 && (
          <p className="text-xs text-gray-500">
            No genres are currently blocked. All music genres can be requested.
          </p>
        )}
      </CardContent>
    </Card>
  );
}
