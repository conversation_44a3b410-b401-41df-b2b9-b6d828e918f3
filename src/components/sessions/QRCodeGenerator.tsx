
import { useRef, useState, useEffect } from "react";
import { QRCodeSVG } from "qrcode.react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Share2, Monitor, Printer, Copy } from "lucide-react";
import { Tables } from "@/integrations/supabase/types";
import { motion } from "framer-motion";
import { toast } from "@/components/ui/use-toast";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { supabase } from "@/integrations/supabase/client";
// Import the display components
import { displayQRCode as displayQRCodeNew } from "./display/DisplayQRCodeNew";
import { displayQRCodeResponsive } from "./display/DisplayQRCodeResponsive";
import { printQRCode } from "./print/PrintQRCode";

type Session = Tables<"sessions"> & {
  sponsor_header?: string;
  sponsor_message?: string;
  sponsor_logo_url?: string;
};

interface QRCodeGeneratorProps {
  session: Session;
}

const QRCodeGenerator = ({ session }: QRCodeGeneratorProps) => {
  const [copied, setCopied] = useState(false);
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [djProfile, setDjProfile] = useState<{ display_name: string, profile_image_url: string | null } | null>(null);
  const qrRef = useRef<HTMLDivElement>(null);

  // Generate a request URL for this session
  const requestUrl = `${window.location.origin}/request?session=${session.id}`;

  // Fetch DJ profile when component mounts
  useEffect(() => {
    const fetchDjProfile = async () => {
      try {
        const { data, error } = await supabase
          .from('dj_profiles')
          .select('display_name, profile_image_url')
          .eq('id', session.dj_id)
          .single();

        if (error) {
          console.error("Error fetching DJ profile:", error);
          return;
        }

        if (data) {
          setDjProfile(data);
        }
      } catch (err) {
        console.error("Failed to fetch DJ profile:", err);
      }
    };

    fetchDjProfile();
  }, [session.dj_id]);

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(requestUrl);
      setCopied(true);
      toast({
        title: "Link copied",
        description: "Request link copied to clipboard",
      });
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy:", err);
      toast({
        title: "Copy failed",
        description: "Unable to copy link to clipboard",
        variant: "destructive",
      });
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `PlayBeg Song Request - ${session.name}`,
          text: "Scan this QR code or use this link to request songs!",
          url: requestUrl
        });
        toast({
          title: "Shared successfully",
          description: "Your session link has been shared",
        });
      } catch (error) {
        console.error("Error sharing:", error);
        // Fallback to copy if sharing fails
        handleCopyLink();
      }
    } else {
      // If Web Share API is not available, show the share dialog
      setShowShareDialog(true);
    }
  };

  const handleDisplayQR = () => {
    // Use the responsive display component
    displayQRCodeResponsive({
      session,
      requestUrl,
      djProfile
    });
  };

  const handlePrintQR = () => {
    // Generate QR code SVG as string
    const qrCodeSvg = document.getElementById('qr-code-svg');
    const svgData = qrCodeSvg ? qrCodeSvg.outerHTML : '';

    printQRCode({
      session,
      requestUrl,
      svgData
    });
  };

  return (
    <>
      <Card className="bg-black/50 border-purple-500/20 backdrop-blur-md overflow-hidden">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-white">Share Your Session</CardTitle>
          <CardDescription className="text-sm text-muted-foreground md:hidden">
            Audience requests songs here!
          </CardDescription>
        </CardHeader>
        <CardContent className="p-4 sm:p-6">
          <div className="flex flex-col md:grid md:grid-cols-2 md:gap-8">
            {/* Left Column - QR Code */}
            <div className="flex flex-col items-center md:justify-center mb-4 md:mb-0">
              <div
                ref={qrRef}
                className="bg-white p-3 rounded-lg shadow-lg max-w-[240px] md:max-w-[260px] mx-auto"
              >
                <QRCodeSVG
                  id="qr-code-svg"
                  value={requestUrl}
                  size={250}
                  level="H"
                  className="w-full h-auto"
                />
              </div>
              <p className="text-sm text-muted-foreground text-center mt-2">
                Scan to request songs
              </p>
            </div>

            {/* Right Column - Controls */}
            <div className="space-y-4">
              <h3 className="text-xl font-bold hidden md:block">Share Session</h3>

              {/* Primary Actions Row - Mobile-first responsive */}
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                <motion.div whileTap={{ scale: 0.95 }} className="flex-1">
                  <Button
                    className="w-full min-h-[44px] bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white font-semibold transition-all duration-200 active:scale-[0.98] touch-manipulation"
                    onClick={handleShare}
                  >
                    <Share2 className="mr-2 h-4 w-4" />
                    Share
                  </Button>
                </motion.div>
                <motion.div whileTap={{ scale: 0.95 }} className="flex-1">
                  <Button
                    className="w-full min-h-[44px] bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white font-semibold transition-all duration-200 active:scale-[0.98] touch-manipulation"
                    onClick={handleCopyLink}
                  >
                    <Copy className="mr-2 h-4 w-4" />
                    {copied ? "Copied!" : "Copy Link"}
                  </Button>
                </motion.div>
              </div>

              {/* Secondary Actions Row - Enhanced for mobile */}
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                <motion.div whileTap={{ scale: 0.95 }} className="flex-1">
                  <Button
                    variant="secondary"
                    className="w-full min-h-[44px] bg-gray-800/80 hover:bg-gray-700/80 border-purple-500/20 text-white transition-all duration-200 active:scale-[0.98] touch-manipulation backdrop-blur-sm"
                    onClick={handleDisplayQR}
                  >
                    <Monitor className="mr-2 h-4 w-4" />
                    Show on Display
                  </Button>
                </motion.div>
                <motion.div whileTap={{ scale: 0.95 }} className="flex-1">
                  <Button
                    variant="secondary"
                    className="w-full min-h-[44px] bg-gray-800/80 hover:bg-gray-700/80 border-purple-500/20 text-white transition-all duration-200 active:scale-[0.98] touch-manipulation backdrop-blur-sm"
                    onClick={handlePrintQR}
                  >
                    <Printer className="mr-2 h-4 w-4" />
                    Print QR Code
                  </Button>
                </motion.div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Share Dialog for browsers without Web Share API */}
      <Dialog open={showShareDialog} onOpenChange={setShowShareDialog}>
        <DialogContent className="sm:max-w-md bg-gray-900 border-purple-500/20">
          <DialogHeader>
            <DialogTitle className="text-white">Share Session</DialogTitle>
            <DialogDescription className="text-gray-400">
              Share this link with your audience to request songs
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col space-y-4 p-2">
            <div className="flex items-center justify-between p-3 bg-gray-800/80 rounded-lg">
              <div className="text-sm text-gray-300 truncate flex-1 p-2">
                {requestUrl}
              </div>
            </div>
            <div className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:justify-end md:gap-2">
              <Button
                variant="outline"
                className="w-full md:w-auto min-h-[44px] border-gray-700 hover:bg-gray-800"
                onClick={() => setShowShareDialog(false)}
              >
                Close
              </Button>
              <Button
                className="w-full md:w-auto min-h-[44px] bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white font-semibold transition-colors duration-200"
                onClick={handleCopyLink}
              >
                <span>Copy Link</span>
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default QRCodeGenerator;
