import { useState, useEffect, useRef } from "react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
// Card components removed as we're using Dialog instead
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Tables } from "@/integrations/supabase/types";
import { Loader2, Settings, HelpCircle, Trash2, Lock, Link } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Textarea } from "@/components/ui/textarea";
import { useFileUpload } from "@/hooks/useFileUpload";
import { useAuth } from "@/context/AuthContext";
import { useSubscription } from "@/hooks/useSubscription";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import { SessionWithSettings } from "@/types/session";
import GenreBlockingSettings from "./GenreBlockingSettings";
import WeddingSettings from "./display/wedding/WeddingSettings";
import SponsorSettings from "./display/sponsor/SponsorSettings";

// Extend the Session type to include the new fields
type BaseSession = Tables<"sessions">;

interface SessionSettingsProps {
  session: SessionWithSettings;
  onSettingsUpdated: () => void;
}

export const SessionSettings = ({
  session,
  onSettingsUpdated
}: SessionSettingsProps) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [isFreeTier, setIsFreeTier] = useState(false);
  const { uploadSponsorImage, deleteSponsorImage, isUploading, isDeleting } = useFileUpload();
  const { currentPlan, hasActiveSubscription } = useSubscription();

  // Basic Request Settings
  const [autoApproval, setAutoApproval] = useState(isFreeTier ? true : (session.auto_approval || false));
  const [acceptRequests, setAcceptRequests] = useState(session.accept_requests !== false); // Default to true if undefined

  // User Quota Settings
  const [maxRequestsPerUser, setMaxRequestsPerUser] = useState(session.max_requests_per_user || 3);

  // Rate Limiting Settings
  const [maxRequestsPerTimeframe, setMaxRequestsPerTimeframe] = useState(session.max_requests_per_timeframe || 2);
  const [timeframeMinutes, setTimeframeMinutes] = useState(session.timeframe_minutes || 60); // Default to 60 minutes instead of 1

  // IP-based rate limiting toggle
  const [enableIpLimiting, setEnableIpLimiting] = useState(session.enable_ip_limiting || false);

  // Display mode states
  const [weddingData, setWeddingData] = useState<any>(null);
  const [sponsorData, setSponsorData] = useState<any>(null);
  const [weddingModeEnabled, setWeddingModeEnabled] = useState(session.wedding_mode_enabled || false);
  const [sponsorModeEnabled, setSponsorModeEnabled] = useState(
    Boolean(session.sponsor_header || session.sponsor_message || session.sponsor_logo_url)
  );

  // Sponsor Information - Initialize with session data
  const [sponsorHeader, setSponsorHeader] = useState(session.sponsor_header || '');
  const [sponsorMessage, setSponsorMessage] = useState(session.sponsor_message || '');
  const [sponsorLogoUrl, setSponsorLogoUrl] = useState(session.sponsor_logo_url || '');
  const [sponsorUrl, setSponsorUrl] = useState(session.sponsor_url || '');

  // Create refs for the sponsor inputs to avoid re-renders during typing
  const sponsorHeaderRef = useRef<HTMLInputElement>(null);
  const sponsorMessageRef = useRef<HTMLTextAreaElement>(null);
  const sponsorUrlRef = useRef<HTMLInputElement>(null);

  // Check free tier status and handle subscription changes
  useEffect(() => {
    if (!user?.id) return;

    const checkSubscription = async () => {
      try {
        const { data, error } = await supabase.rpc(
          'get_user_subscription_status',
          { user_id: user.id }
        );

        if (error) throw error;

        if (data && data.length > 0) {
          // Check if this is the Free plan - make it read-only only for the Free plan
          const wasFreeTier = isFreeTier; // Store previous state for comparison
          const isFreePlan = data[0].plan_name === 'Free' && !data[0].has_paid_plan;
          setIsFreeTier(isFreePlan);

          // If it's a free plan, force auto-approval to be ON
          if (isFreePlan) {
            setAutoApproval(true);
          }

          // If user just upgraded from free to paid tier, show a notification and unlock settings
          if (wasFreeTier && !isFreePlan) {
            toast({
              title: "Settings Unlocked! 🎉",
              description: "Your subscription has been upgraded. All session settings are now available!",
              variant: "default",
            });
          }

          console.log("Is Free plan:", isFreePlan);
        }
      } catch (err) {
        console.error("Error checking subscription:", err);
      }
    };

    // Initial check
    checkSubscription();

    // Set up a subscription to monitor subscription changes
    const subscriptionChannel = supabase
      .channel('settings-subscription-changes')
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'dj_subscriptions',
        filter: `dj_id=eq.${user.id}`
      }, () => {
        // When subscription changes, recheck free tier status
        console.log('Subscription status changed, rechecking free tier status');
        checkSubscription();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(subscriptionChannel);
    };
  }, [user?.id, isFreeTier, toast]);

  // Set initial values in refs
  useEffect(() => {
    if (sponsorHeaderRef.current) {
      sponsorHeaderRef.current.value = session.sponsor_header || '';
    }
    if (sponsorMessageRef.current) {
      sponsorMessageRef.current.value = session.sponsor_message || '';
    }
    if (sponsorUrlRef.current) {
      sponsorUrlRef.current.value = session.sponsor_url || '';
    }
  }, [session]);

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files || event.target.files.length === 0 || !user?.id) {
      return;
    }

    const file = event.target.files[0];
    try {
      setIsLoading(true);

      // Validate file type
      const allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
      if (!allowedMimeTypes.includes(file.type)) {
        toast({
          title: "Invalid file type",
          description: "Only JPG, PNG, and GIF images are allowed",
          variant: "destructive"
        });
        return;
      }

      // Validate file size (max 1MB)
      if (file.size > 1 * 1024 * 1024) {
        toast({
          title: "File too large",
          description: "Please select an image under 1MB",
          variant: "destructive"
        });
        return;
      }

      // Use user.id instead of session.id for file upload
      const imageUrl = await uploadSponsorImage(user.id, file);

      if (imageUrl) {
        // Create a new Image object to preload the image
        const img = new Image();
        img.onload = () => {
          // Image loaded successfully, update the state
          setSponsorLogoUrl(imageUrl);
          toast({
            title: "Logo uploaded",
            description: "Your sponsor logo has been uploaded successfully."
          });
        };
        img.onerror = () => {
          // Image failed to load
          console.error("Failed to load the uploaded image:", imageUrl);
          toast({
            title: "Upload issue",
            description: "Logo was uploaded but may not display correctly. Please try a different image.",
            variant: "destructive"
          });
          setSponsorLogoUrl(imageUrl); // Still set the URL in case it works later
        };
        img.src = imageUrl;
      }
    } catch (error: any) {
      toast({
        title: "Upload failed",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogoDelete = async () => {
    if (!sponsorLogoUrl || !user?.id) return;

    try {
      setIsLoading(true);
      // Use user.id instead of session.id for file deletion
      const success = await deleteSponsorImage(user.id, sponsorLogoUrl);

      if (success) {
        setSponsorLogoUrl('');
      }
    } catch (error: any) {
      toast({
        title: "Deletion failed",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setIsLoading(true);

      // Get values directly from refs for better performance
      const currentSponsorHeader = sponsorHeaderRef.current?.value || '';
      const currentSponsorMessage = sponsorMessageRef.current?.value || '';
      const currentSponsorUrl = sponsorUrlRef.current?.value || '';

      const updateData: any = {
        accept_requests: acceptRequests
      };

      // Auto-approval feature has been removed
      // Apply all editable settings
      updateData.auto_approval = false; // Always set to false (auto-approval removed)
      updateData.max_requests_per_user = maxRequestsPerUser;
      updateData.max_requests_per_timeframe = maxRequestsPerTimeframe;
      updateData.timeframe_minutes = timeframeMinutes;
      updateData.enable_ip_limiting = enableIpLimiting;
      updateData.sponsor_header = currentSponsorHeader;
      updateData.sponsor_message = currentSponsorMessage;
      updateData.sponsor_logo_url = sponsorLogoUrl;
      updateData.sponsor_url = currentSponsorUrl;

      // Display mode settings - ensure mutual exclusivity
      if (weddingData && weddingData.wedding_mode_enabled) {
        // Wedding mode is enabled, disable sponsor mode
        Object.assign(updateData, weddingData);
        updateData.sponsor_header = '';
        updateData.sponsor_message = '';
        updateData.sponsor_logo_url = '';
        updateData.sponsor_url = '';
      } else if (sponsorData && sponsorData.sponsor_mode_enabled) {
        // Sponsor mode is enabled, disable wedding mode
        Object.assign(updateData, sponsorData);
        updateData.wedding_mode_enabled = false;
        updateData.wedding_couple_name_1 = '';
        updateData.wedding_couple_name_2 = '';
        updateData.wedding_date = '';
        updateData.wedding_hashtag = '';
        updateData.wedding_template = 'classic-elegance';
        updateData.wedding_primary_color = '#D4AF37';
        updateData.wedding_secondary_color = '#F5F5DC';
        updateData.wedding_custom_message = '';
        updateData.wedding_show_icons = true;
        updateData.wedding_border_style = 'elegant-frame';
        updateData.wedding_background_pattern = 'none';
      } else {
        // Neither mode is enabled, clear both
        updateData.sponsor_header = currentSponsorHeader;
        updateData.sponsor_message = currentSponsorMessage;
        updateData.sponsor_logo_url = sponsorLogoUrl;
        updateData.sponsor_url = currentSponsorUrl;

        if (weddingData) {
          Object.assign(updateData, weddingData);
        }
      }

      const { error } = await supabase.from('sessions').update(updateData).eq('id', session.id);

      if (error) throw error;

      // Update state after successful save to keep UI in sync
      setSponsorHeader(currentSponsorHeader);
      setSponsorMessage(currentSponsorMessage);
      setSponsorUrl(currentSponsorUrl);

      toast({
        title: "Settings updated",
        description: "Your session settings have been saved successfully."
      });

      onSettingsUpdated();
    } catch (error: any) {
      toast({
        title: "Error saving settings",
        description: error.message,
        variant: "destructive"
      });
      console.error("Error saving session settings:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Navigate to Plans tab
  const navigateToPlans = () => {
    navigate('/dashboard?tab=subscription');
    // Close the settings dialog when navigating away
    if (typeof onSettingsUpdated === 'function') {
      onSettingsUpdated();
    }
  };

  // Display mode handlers
  const handleWeddingSave = async (newWeddingData: any) => {
    setWeddingData(newWeddingData);
    setWeddingModeEnabled(newWeddingData.wedding_mode_enabled);

    // Auto-save wedding settings when they're updated
    try {
      setIsLoading(true);

      const { error } = await supabase
        .from('sessions')
        .update(newWeddingData)
        .eq('id', session.id);

      if (error) throw error;

      toast({
        title: "Wedding settings saved",
        description: "Your wedding display settings have been updated successfully."
      });

      onSettingsUpdated();
    } catch (error: any) {
      toast({
        title: "Error saving wedding settings",
        description: error.message,
        variant: "destructive"
      });
      console.error("Error saving wedding settings:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSponsorSave = async (newSponsorData: any) => {
    setSponsorData(newSponsorData);
    setSponsorModeEnabled(newSponsorData.sponsor_mode_enabled);

    // Auto-save sponsor settings when they're updated
    try {
      setIsLoading(true);

      const { error } = await supabase
        .from('sessions')
        .update(newSponsorData)
        .eq('id', session.id);

      if (error) throw error;

      toast({
        title: "Sponsor settings saved",
        description: "Your sponsor display settings have been updated successfully."
      });

      onSettingsUpdated();
    } catch (error: any) {
      toast({
        title: "Error saving sponsor settings",
        description: error.message,
        variant: "destructive"
      });
      console.error("Error saving sponsor settings:", error);
    } finally {
      setIsLoading(false);
    }
  };



  // Helper to determine if control should be disabled for free tier
  const isDisabledForFreeTier = (settingType: string) => {
    if (!isFreeTier) return false;

    // Allow turning requests on/off for all tiers
    if (settingType === 'acceptRequests') return false;

    // Disable everything else for free tier
    return true;
  };

  // Free tier control wrapper
  const FreeTierControl = ({ children, settingName, tooltip }: { children: React.ReactNode, settingName: string, tooltip: string }) => {
    if (!isFreeTier) return <>{children}</>;

    return (
      <div className="relative">
        {children}
        <div className="absolute inset-0 bg-black/10 backdrop-blur-[1px] rounded flex items-center justify-end cursor-not-allowed">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="mr-2">
                  <Badge variant="outline" className="bg-gray-800/80 text-gray-300 border-gray-700 gap-1">
                    <Lock className="h-3 w-3" />
                    <span>PRO</span>
                  </Badge>
                </div>
              </TooltipTrigger>
              <TooltipContent side="left" className="bg-gray-900 border-gray-700">
                <p className="max-w-[200px]">{tooltip}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
    );
  };

  return (
    <div className="text-white">
      <div className="text-gray-400 mb-6">
        Configure how your session handles song requests
        {isFreeTier && (
          <div className="mt-2 flex items-center gap-2">
            <Badge variant="outline" className="bg-gray-800 text-gray-300 border-gray-700">Free tier</Badge>
            <span className="text-sm text-gray-400">
              Some settings require <Button
                variant="link"
                className="p-0 h-auto font-normal text-cyan-400 hover:text-cyan-300 hover:underline"
                onClick={navigateToPlans}
                type="button"
                role="link"
              >
                at least paid 24 Hours Access
              </Button>
            </span>
          </div>
        )}
      </div>
      <div className="space-y-6">
        {/* Basic Request Settings */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-white">Basic Request Settings</h3>

          {/* Session Duration removed */}

          {/* Auto-approval feature has been removed */}

          <div className="flex items-center justify-between space-x-2">
            <div className="flex items-center gap-1">
              <Label htmlFor="acceptRequests" className="flex flex-col space-y-1">
                <span>Accept requests</span>
                <span className="font-normal text-xs text-gray-400">
                  Allow new song requests for this session
                </span>
              </Label>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="cursor-help">
                      <HelpCircle className="h-4 w-4 text-gray-500" />
                    </span>
                  </TooltipTrigger>
                  <TooltipContent className="max-w-xs bg-gray-900 text-gray-200 border-gray-700">
                    <p>
                      Controls whether your session is accepting new song requests.
                      <br /><br />
                      <strong>Example:</strong> Toggle this off at the end of your event
                      or when your playlist is full. Users will see a message that requests
                      are currently closed.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <Switch id="acceptRequests" checked={acceptRequests} onCheckedChange={setAcceptRequests} />
          </div>
        </div>

        {/* User Quota Settings */}
        <div className="space-y-4 pt-2 border-t border-gray-800">
          <h3 className="text-sm font-medium text-white">User Quota Settings</h3>
          <div className="text-xs text-gray-400 bg-gray-800/50 p-2 rounded border border-gray-700/50">
            <strong>Note:</strong> All sessions have a system-wide limit of 100 song requests total, regardless of individual user quotas.
          </div>

          <FreeTierControl
            settingName="maxRequestsPerUser"
            tooltip="Customizing user quota settings is a Pro feature. Upgrade your plan to set custom limits for song requests."
          >
            <div className="flex items-center justify-between space-x-2">
              <div className="flex items-center gap-1">
                <Label htmlFor="maxRequestsPerUser" className="flex flex-col space-y-1">
                  <span>Max requests per user</span>
                  <span className="font-normal text-xs text-gray-400">
                    Maximum number of songs each user can request
                  </span>
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="cursor-help">
                        <HelpCircle className="h-4 w-4 text-gray-500" />
                      </span>
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs bg-gray-900 text-gray-200 border-gray-700">
                      <p>
                        Limits how many song requests each person can submit in total.
                        <br /><br />
                        <strong>Example:</strong> Setting this to 3 means each user can request
                        up to 3 songs for your entire session, ensuring fair distribution of
                        song choices among all attendees.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Input
                id="maxRequestsPerUser"
                type="number"
                min={1}
                max={10} /* Reduced from 20 to 10 */
                value={maxRequestsPerUser}
                onChange={e => setMaxRequestsPerUser(parseInt(e.target.value) || 3)}
                className="w-20 bg-gray-900/60 border-gray-700"
                disabled={isDisabledForFreeTier('maxRequestsPerUser')}
              />
            </div>
          </FreeTierControl>


        </div>

        {/* Rate Limiting Settings */}
        <div className="space-y-4 pt-2 border-t border-gray-800">
          <h3 className="text-sm font-medium text-white">Rate Limiting Settings</h3>

          <FreeTierControl
            settingName="rateLimiting"
            tooltip="Advanced rate limiting features are available in the Pro plan. Upgrade to customize these settings."
          >
            <div className="flex items-center justify-between space-x-2">
              <div className="flex items-center gap-1">
                <Label htmlFor="maxRequestsPerTimeframe" className="flex flex-col space-y-1">
                  <span>Max requests per timeframe</span>
                  <span className="font-normal text-xs text-gray-400">
                    Maximum requests a user can send within a time window
                  </span>
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="cursor-help">
                        <HelpCircle className="h-4 w-4 text-gray-500" />
                      </span>
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs bg-gray-900 text-gray-200 border-gray-700">
                      <p>
                        Limits how quickly a user can submit multiple requests, preventing spam.
                        <br /><br />
                        <strong>Example:</strong> If set to 2 requests per 1 minute, a user
                        could request two songs right away, but would need to wait a minute
                        before submitting more, even if they haven't reached their total quota yet.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Input
                id="maxRequestsPerTimeframe"
                type="number"
                min={1}
                max={10}
                value={maxRequestsPerTimeframe}
                onChange={e => setMaxRequestsPerTimeframe(parseInt(e.target.value) || 2)}
                className="w-20 bg-gray-900/60 border-gray-700"
                disabled={isDisabledForFreeTier('maxRequestsPerTimeframe')}
              />
            </div>

            <div className="flex items-center justify-between space-x-2">
              <div className="flex items-center gap-1">
                <Label htmlFor="timeframeMinutes" className="flex flex-col space-y-1">
                  <span>Timeframe (minutes)</span>
                  <span className="font-normal text-xs text-gray-400">
                    Length of time for the rate limiting window
                  </span>
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="cursor-help">
                        <HelpCircle className="h-4 w-4 text-gray-500" />
                      </span>
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs bg-gray-900 text-gray-200 border-gray-700">
                      <p>
                        Sets the duration of the time window for rate limiting.
                        <br /><br />
                        <strong>Example:</strong> If set to 60 minutes (default) with a max of 2 requests,
                        users can submit 2 songs within any 60-minute period. Setting this to
                        a lower value like 5 minutes would make users wait less time between
                        submitting batches of requests.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Input
                id="timeframeMinutes"
                type="number"
                min={1}
                max={120} /* Reduced from 1440 (24 hours) to 120 (2 hours) */
                value={timeframeMinutes}
                onChange={e => setTimeframeMinutes(parseInt(e.target.value) || 60)}
                className="w-20 bg-gray-900/60 border-gray-700"
                disabled={isDisabledForFreeTier('timeframeMinutes')}
              />
            </div>

            <div className="flex items-center justify-between space-x-2 mt-3">
              <div className="flex items-center gap-1">
                <Label htmlFor="enableIpLimiting" className="flex flex-col space-y-1">
                  <span>IP address-based rate limiting</span>
                  <span className="font-normal text-xs text-gray-400">
                    Restrict requests based on IP address for better security
                  </span>
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="cursor-help">
                        <HelpCircle className="h-4 w-4 text-gray-500" />
                      </span>
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs bg-gray-900 text-gray-200 border-gray-700 space-y-2">
                      <p>
                        <strong>What it does:</strong> Tracks and limits requests coming from the same IP address,
                        adding an extra layer of security against spam.
                      </p>
                      <p>
                        <strong>Great for:</strong> Events where most attendees are using mobile data, as
                        each phone typically has a unique IP address.
                      </p>
                      <p>
                        <strong>Not recommended for:</strong> Venues with shared Wi-Fi where multiple
                        legitimate users would appear to have the same IP address (like coffee shops,
                        hotels, or conference centers).
                      </p>
                      <p>
                        <strong>Example scenario:</strong> If enabled, two friends using the same
                        hotspot might be identified as the same user for rate limiting purposes.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Switch
                id="enableIpLimiting"
                checked={enableIpLimiting}
                onCheckedChange={setEnableIpLimiting}
                disabled={isDisabledForFreeTier('enableIpLimiting')}
              />
            </div>
            <div className="mt-1 text-xs text-amber-400/80 bg-amber-800/20 p-2 rounded border border-amber-600/20">
              <strong>Note:</strong> IP-based limiting adds extra security but isn't recommended for events where
              attendees share the same network (like venue Wi-Fi or hotspots). In these cases, legitimate requests
              from different people might be blocked. Works best when attendees use mobile data connections, which
              typically have unique IP addresses.
            </div>
          </FreeTierControl>
        </div>

        {/* Genre Blocking Settings */}
        <div className="pt-2 border-t border-gray-800">
          <GenreBlockingSettings
            session={session}
            onUpdate={(updatedSession) => {
              // Update the session data and trigger a refresh
              onSettingsUpdated();
            }}
          />
        </div>

        {/* Sponsor Display Mode */}
        <FreeTierControl
          settingName="sponsorMode"
          tooltip="Sponsor display mode is a Pro feature. Upgrade your plan to add sponsor branding to your sessions."
        >
          <div className="pt-2 border-t border-gray-800">
            <SponsorSettings
              session={session}
              onSave={handleSponsorSave}
              isLoading={isLoading}
              weddingModeEnabled={weddingModeEnabled}
              onWeddingModeChange={setWeddingModeEnabled}
              sponsorLogoUrl={sponsorLogoUrl}
              onLogoUpload={handleLogoUpload}
              onLogoDelete={handleLogoDelete}
              isUploading={isUploading}
              isDeleting={isDeleting}
            />
          </div>
        </FreeTierControl>

        {/* Wedding Display Mode */}
        <FreeTierControl
          settingName="weddingMode"
          tooltip="Wedding display mode is a Pro feature. Upgrade your plan to create beautiful wedding-themed displays for your events."
        >
          <div className="pt-2 border-t border-gray-800">
            <WeddingSettings
              session={session}
              onSave={handleWeddingSave}
              isLoading={isLoading}
              sponsorModeEnabled={sponsorModeEnabled}
              onSponsorModeChange={setSponsorModeEnabled}
            />
          </div>
        </FreeTierControl>

        {isFreeTier ? (
          <div className="relative">
            <Button
              onClick={handleSave}
              disabled={true}
              variant="locked"
              className="w-full"
            >
              Save Settings
            </Button>
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className="flex items-center gap-2">
                <Lock className="h-4 w-4 text-gray-400" />
                <span className="text-gray-300">Pro feature</span>
              </div>
            </div>
          </div>
        ) : (
          <Button
            onClick={handleSave}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white font-semibold transition-colors duration-200"
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Save Settings
          </Button>
        )}

        {isFreeTier && (
          <div className="text-center text-sm text-gray-400 pt-2">
            <a href="/dashboard?tab=subscription" className="text-cyan-400 hover:underline">
              Upgrade to Pro
            </a> to unlock all session settings
          </div>
        )}
      </div>
    </div>
  );
};

export default SessionSettings;
