
import { useState, useEffect, useCallback } from "react";
import { PlusCircle, Calendar, Users, Music, Clock, ArrowRight, AlertTriangle, Lock, Trash2 } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { deleteAppleMusicPlaylist } from "@/utils/appleMusicService";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tables } from "@/integrations/supabase/types";
import { useQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useSubscription } from "@/hooks/useSubscription";
import { useSubscriptionExpiration } from "@/hooks/useSubscriptionExpiration";
import { useSessionDuration } from "@/hooks/useSessionDuration";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from "@/components/ui/alert-dialog";

type Session = Tables<"sessions">;

interface SessionsManagerProps {
  onSelectSession: (sessionId: string) => void;
  isAppleMusicAuthorized: boolean;
}

const SessionsManager = ({ onSelectSession, isAppleMusicAuthorized }: SessionsManagerProps) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [newSessionName, setNewSessionName] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const [hasActiveSession, setHasActiveSession] = useState(false);
  const [sessionToDelete, setSessionToDelete] = useState<Session | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const { currentPlan, subscription, isLoading: isSubscriptionLoading } = useSubscription();

  // Set up a subscription to monitor subscription changes
  useEffect(() => {
    if (!user?.id) return;

    const subscriptionChannel = supabase
      .channel('manager-subscription-changes')
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'dj_subscriptions',
        filter: `dj_id=eq.${user.id}`
      }, async () => {
        // When subscription changes, refetch sessions and show a notification
        console.log('Subscription status changed, refetching sessions');

        // Check if we need to update session durations and reactivate expired sessions (if user upgraded from free tier)
        try {
          console.log('Checking if we need to update session durations and reactivate expired sessions...');
          const { data: accessToken } = await supabase.auth.getSession();

          // Call the update-session-durations edge function
          const { data, error } = await supabase.functions.invoke('update-session-durations', {
            headers: {
              Authorization: `Bearer ${accessToken.session?.access_token}`
            }
          });

          if (error) {
            console.error('Error updating session durations:', error);
          } else if (data) {
            console.log('Session update results:', data);

            if (data.updatedCount > 0) {
              console.log(`Successfully updated ${data.updatedCount} session durations`);
            }

            if (data.reactivatedCount > 0) {
              console.log(`Successfully reactivated ${data.reactivatedCount} sessions`);

              // Show a notification about the reactivated sessions
              toast({
                title: "Sessions Updated! 🎉",
                description: `${data.reactivatedCount} of your previously inactive sessions have been reactivated with your paid tier duration.`,
                variant: "default",
              });
            } else {
              console.log('No sessions were reactivated');
            }
          }
        } catch (error) {
          console.error('Error in update-session-durations:', error);
        }

        // Refetch sessions to show the updated list
        refetch();

        // Show a notification about the upgrade if we didn't already show a reactivation notification
        if (!data || data.reactivatedCount === 0) {
          toast({
            title: "Subscription Updated! 🎉",
            description: "Your subscription has been upgraded. Your sessions now have increased limits!",
            variant: "default",
          });
        }
      })
      .subscribe();

    return () => {
      supabase.removeChannel(subscriptionChannel);
    };
  }, [user?.id, refetch, toast]);

  // Fetch sessions data with automatic refetch when subscription changes
  const { data: sessions, isLoading, refetch } = useQuery({
    queryKey: ['sessions', user?.id],
    queryFn: async () => {
      if (!user?.id) {
        console.log('🔍 REACT QUERY: No user ID, returning empty array');
        return [];
      }

      console.log('🔍 REACT QUERY: Starting sessions fetch for user:', user.id, 'at', new Date().toISOString());
      console.log('🔍 REACT QUERY: Window focus state:', document.hasFocus());
      console.log('🔍 REACT QUERY: Document visibility:', document.visibilityState);

      const { data, error } = await supabase
        .from('sessions')
        .select('*')
        .eq('dj_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error("🔍 REACT QUERY: Error fetching sessions:", error);
        throw error;
      }

      console.log('🔍 REACT QUERY: Successfully fetched', data?.length || 0, 'sessions at', new Date().toISOString());
      return data as Session[];
    },
    enabled: !!user?.id,
    // Refresh sessions data every minute to update remaining time displays
    refetchInterval: 60000,
    // Add explicit logging for refetch triggers
    onSuccess: (data) => {
      console.log('🔍 REACT QUERY: Query success with', data?.length || 0, 'sessions');
    },
    onError: (error) => {
      console.error('🔍 REACT QUERY: Query error:', error);
    },
  });

  // Check if user has an active session
  useEffect(() => {
    if (sessions && sessions.length > 0) {
      const activeSessionExists = sessions.some(session => session.active);
      setHasActiveSession(activeSessionExists);
    } else {
      setHasActiveSession(false);
    }
  }, [sessions]);

  // Use the session expiration hook - make sure we pass valid userId
  useSubscriptionExpiration(user?.id, refetch);

  // Get latest active session with duration for time calculation
  const latestSessionWithDuration = sessions?.find(session =>
    session.duration_minutes && session.duration_minutes > 0
  ) || null;

  // Use the session duration hook to calculate remaining time from existing subscription
  const { remainingTime, calculateRemainingTime } = useSessionDuration(latestSessionWithDuration);

  // Function to calculate remaining minutes from an active subscription
  const calculateRemainingSubscriptionMinutes = useCallback(() => {
    if (!latestSessionWithDuration) return null;

    // Convert latestSessionWithDuration to compatible format for calculateRemainingTime
    const remainingMinutes = calculateRemainingTime();
    if (typeof remainingMinutes === 'number') {
      return remainingMinutes;
    }

    return null;
  }, [latestSessionWithDuration, calculateRemainingTime]);

  // Set up a subscription listener to update sessions when subscription changes
  useEffect(() => {
    if (!user?.id) return;

    console.log('🔍 SUPABASE: Setting up realtime subscriptions for user:', user.id, 'at', new Date().toISOString());

    // Set up a subscription to listen for changes to the dj_subscriptions table
    const subscriptionChannel = supabase
      .channel('dj_subscription_changes')
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'dj_subscriptions',
        filter: `dj_id=eq.${user.id}`,
      }, (payload) => {
        console.log('🔍 SUPABASE: Subscription changed, refreshing sessions at', new Date().toISOString());
        console.log('🔍 SUPABASE: Subscription payload:', payload);
        console.log('🔍 SUPABASE: Window focus when subscription fired:', document.hasFocus());
        refetch();
      })
      .subscribe((status) => {
        console.log('🔍 SUPABASE: Subscription channel status:', status, 'at', new Date().toISOString());
      });

    // Also set up a listener for changes to the sessions table
    const sessionsChannel = supabase
      .channel('sessions_changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'sessions',
        filter: `dj_id=eq.${user.id}`,
      }, (payload) => {
        console.log('🔍 SUPABASE: Sessions changed, refreshing data at', new Date().toISOString());
        console.log('🔍 SUPABASE: Sessions payload:', payload);
        console.log('🔍 SUPABASE: Window focus when sessions changed:', document.hasFocus());
        refetch();
      })
      .subscribe((status) => {
        console.log('🔍 SUPABASE: Sessions channel status:', status, 'at', new Date().toISOString());
      });

    return () => {
      console.log('🔍 SUPABASE: Cleaning up realtime subscriptions at', new Date().toISOString());
      supabase.removeChannel(subscriptionChannel);
      supabase.removeChannel(sessionsChannel);
    };
  }, [user?.id, refetch]);

  const handleCreateSession = async () => {
    if (!isAppleMusicAuthorized) {
      toast({
        title: "Apple Music required",
        description: "Please connect your Apple Music account before creating a session.",
        variant: "destructive",
      });
      return;
    }

    if (hasActiveSession) {
      toast({
        title: "Active session exists",
        description: "You already have an active session. Please deactivate it before creating a new one.",
        variant: "destructive",
      });
      return;
    }

    if (!newSessionName.trim()) {
      toast({
        title: "Session name required",
        description: "Please enter a name for your session.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsCreating(true);

      // First check if there are any existing sessions with remaining time
      const remainingMinutes = calculateRemainingSubscriptionMinutes();

      if (remainingMinutes && remainingMinutes > 0) {
        console.log(`Using remaining time from existing session: ${remainingMinutes} minutes`);

        // Create session with the remaining time from existing sessions
        const { data, error } = await supabase
          .from('sessions')
          .insert({
            name: newSessionName,
            dj_id: user?.id as string,
            active: true,
            duration_minutes: remainingMinutes
          })
          .select()
          .maybeSingle();

        if (error) throw error;

        toast({
          title: "Session created!",
          description: `Your session "${newSessionName}" is now active with ${remainingMinutes} minutes remaining from your subscription.`,
        });

        setNewSessionName("");
        refetch();
        return;
      }

      // If no existing session with remaining time, fetch user's subscription status
      const { data: subscriptionData, error: subscriptionError } = await supabase.rpc(
        'get_user_subscription_status',
        { user_id: user?.id }
      );

      if (subscriptionError) throw subscriptionError;

      console.log("Subscription data:", subscriptionData);

      // Get duration from the subscription plan (convert hours to minutes)
      let durationMinutes = 0;

      // For paid plans, use the remaining time from the subscription instead of the full plan duration
      if (!isFreePlan && subscription?.current_period_end) {
        const endDate = new Date(subscription.current_period_end);
        const now = new Date();

        // If subscription is still active
        if (endDate > now) {
          // Calculate remaining minutes from subscription end date
          const diffMs = endDate.getTime() - now.getTime();
          durationMinutes = Math.floor(diffMs / (1000 * 60));
          console.log(`Using remaining subscription time: ${durationMinutes} minutes (${Math.floor(durationMinutes / 60)} hours and ${durationMinutes % 60} minutes)`);
        } else if (subscriptionData && subscriptionData.length > 0) {
          // Fallback to full plan duration if subscription has expired (shouldn't happen)
          durationMinutes = Math.round(subscriptionData[0].plan_duration_hours * 60);
          console.log(`Subscription expired, using full plan duration: ${subscriptionData[0].plan_duration_hours} hours (${durationMinutes} minutes)`);
        }
      } else if (subscriptionData && subscriptionData.length > 0) {
        // Fallback to full plan duration if no end date is available
        durationMinutes = Math.round(subscriptionData[0].plan_duration_hours * 60);
        console.log(`Converting ${subscriptionData[0].plan_duration_hours} hours to ${durationMinutes} minutes`);
      }

      // If user is on Free plan, set duration to 20 minutes and show a toast
      console.log('Current plan:', currentPlan);
      const isFreePlan = currentPlan && currentPlan.name === 'Free';
      console.log('Is free plan detected:', isFreePlan);

      // Debug log for duration minutes
      console.log(`Current duration minutes: ${durationMinutes}`);

      if (isFreePlan) {
        // Explicitly set to 20 minutes for free tier, regardless of what the RPC returns
        durationMinutes = 20;

        console.log('Free tier user - enforcing 20 minute session duration');

        toast({
          title: "Free Plan Limitation",
          description: `As a Free tier user, your session duration is limited to ${formatDuration(durationMinutes)}.`,
          variant: "default",
        });
      }

      // Create session data with auto_approval always set to false (feature removed)
      const sessionData = {
        name: newSessionName,
        dj_id: user?.id as string,
        active: true,
        duration_minutes: durationMinutes,
        // Auto-approval feature has been removed
        auto_approval: false
      };

      console.log('Session data to be inserted:', sessionData);

      const { data, error } = await supabase
        .from('sessions')
        .insert(sessionData)
        .select()
        .maybeSingle();

      if (error) throw error;

      console.log('Session created successfully:', data);
      console.log('Checking if duration_minutes was set correctly:', data?.duration_minutes);

      toast({
        title: "Session created!",
        description: `Your session "${newSessionName}" is now active with a duration of ${data?.duration_minutes || 'unknown'} minutes.`,
      });

      setNewSessionName("");
      refetch();
    } catch (error: any) {
      toast({
        title: "Error creating session",
        description: error.message,
        variant: "destructive",
      });
      console.error("Error creating session:", error);
    } finally {
      setIsCreating(false);
    }
  };

  const toggleSessionStatus = async (session: Session) => {
    try {
      const newStatus = !session.active;

      // If trying to activate a session while another is active
      if (newStatus && hasActiveSession && sessions) {
        const otherActiveSessions = sessions.filter(s => s.active && s.id !== session.id);
        if (otherActiveSessions.length > 0) {
          toast({
            title: "Cannot activate multiple sessions",
            description: "Please deactivate your current active session first.",
            variant: "destructive",
          });
          return;
        }
      }

      const { error } = await supabase
        .from('sessions')
        .update({ active: newStatus })
        .eq('id', session.id);

      if (error) throw error;

      toast({
        title: newStatus ? "Session activated" : "Session deactivated",
        description: `"${session.name}" is now ${newStatus ? "active" : "inactive"}.`,
      });

      refetch();
    } catch (error: any) {
      toast({
        title: "Error updating session",
        description: error.message,
        variant: "destructive",
      });
      console.error("Error updating session:", error);
    }
  };

  // Modified handleDeleteSession function to properly handle session deletion
  const handleDeleteSession = async (session: Session) => {
    try {
      setIsDeleting(true);

      console.log("Deleting session:", session.id);

      // Try to delete the associated Apple Music playlist if it exists
      if (session.apple_music_playlist_id && session.dj_id) {
        console.log(`Attempting to delete Apple Music playlist: ${session.apple_music_playlist_id}`);

        const playlistDeletionResult = await deleteAppleMusicPlaylist(
          session.apple_music_playlist_id,
          session.dj_id
        );

        if (playlistDeletionResult.success) {
          console.log('Successfully deleted Apple Music playlist');
        } else {
          console.warn('Failed to delete Apple Music playlist:', playlistDeletionResult.error);
          // Don't fail the entire operation if playlist deletion fails
        }
      }

      // Delete the session from the database
      const { error } = await supabase
        .from('sessions')
        .delete()
        .eq('id', session.id);

      if (error) throw error;

      toast({
        title: "Session deleted",
        description: `"${session.name}" has been permanently deleted.`,
      });

      setSessionToDelete(null);
      refetch();
    } catch (error: any) {
      toast({
        title: "Error deleting session",
        description: error.message,
        variant: "destructive",
      });
      console.error("Error deleting session:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  // Determine if Create Session button should be disabled
  const isCreateDisabled = !isAppleMusicAuthorized || hasActiveSession || isCreating;

  // Generate the tooltip message based on conditions
  const getTooltipMessage = () => {
    if (!isAppleMusicAuthorized) {
      return "Please connect your Apple Music account before creating a session";
    }
    if (hasActiveSession) {
      return "You can only have one active session at a time";
    }
    return "";
  };

  // Calculate remaining time function - memoized to prevent recalculations
  const calculateSessionRemainingTime = useCallback((session: Session): number | null => {
    if (!session.created_at || !session.duration_minutes) return null;

    const startTime = new Date(session.created_at).getTime();
    const durationMs = session.duration_minutes * 60 * 1000;
    const endTime = startTime + durationMs;
    const now = new Date().getTime();

    const remainingMs = endTime - now;
    return Math.max(0, Math.floor(remainingMs / (60 * 1000))); // Convert to minutes and ensure non-negative
  }, []);

  // Calculate expiry date and time
  const calculateExpiryDateTime = useCallback((session: Session): Date | null => {
    if (!session.created_at || !session.duration_minutes) return null;

    const startTime = new Date(session.created_at).getTime();
    const durationMs = session.duration_minutes * 60 * 1000;
    const endTime = startTime + durationMs;

    return new Date(endTime);
  }, []);

  // Modified format duration function to remove the detailed timestamp
  const formatDuration = useCallback((minutes: number | null | undefined, isActive: boolean = false, createdAt?: string | null) => {
    if (!minutes) return "No limit";

    if (isActive && createdAt) {
      const remainingMinutes = calculateSessionRemainingTime({
        created_at: createdAt,
        duration_minutes: minutes
      } as Session);

      // Get expiry date but don't format it for display
      const expiryDate = calculateExpiryDateTime({
        created_at: createdAt,
        duration_minutes: minutes
      } as Session);

      if (remainingMinutes === 0) {
        return "Expired";
      }

      if (remainingMinutes && remainingMinutes < 60) {
        return `${remainingMinutes}m remaining`;
      } else if (remainingMinutes) {
        const hours = Math.floor(remainingMinutes / 60);

        // For durations longer than 24 hours, show in days and hours
        if (hours >= 24) {
          const days = Math.floor(hours / 24);
          const remainingHours = hours % 24;

          if (remainingHours === 0) {
            return `${days}d remaining`;
          } else {
            return `${days}d ${remainingHours}h`;
          }
        } else {
          // Less than a day - show hours and minutes
          const mins = remainingMinutes % 60;
          return `${hours}h ${mins}m`;
        }
      }
    }

    // For non-active sessions or when showing plan duration
    if (minutes < 60) {
      return `${minutes} min`;
    } else {
      const hours = Math.floor(minutes / 60);

      // For durations longer than 24 hours, show in days and hours
      if (hours >= 24) {
        const days = Math.floor(hours / 24);
        const remainingHours = hours % 24;

        if (remainingHours === 0) {
          return `${days} day${days !== 1 ? 's' : ''}`;
        } else {
          return `${days}d ${remainingHours}h`;
        }
      } else {
        // Less than a day
        const remainingMinutes = minutes % 60;

        if (remainingMinutes === 0) {
          return `${hours} hour${hours !== 1 ? 's' : ''}`;
        } else {
          return `${hours}h ${remainingMinutes}m`;
        }
      }
    }
  }, [calculateSessionRemainingTime, calculateExpiryDateTime]);

  // Add an effect to update remaining time
  const [, setTimeUpdate] = useState(0);
  useEffect(() => {
    if (!sessions?.some(s => s.active)) return;

    const timer = setInterval(() => {
      setTimeUpdate(prev => prev + 1); // Force re-render every minute
    }, 30000); // Update every 30 seconds for more responsive display

    return () => clearInterval(timer);
  }, [sessions]);

  // Determine if user is on a free plan
  const isFreePlan = currentPlan && currentPlan.name === 'Free';

  return (
    <div className="space-y-6">
      <Card className="bg-black/50 border-purple-500/20 backdrop-blur-md">
        <CardHeader>
          <CardTitle className="text-white">Create New Session</CardTitle>
          <CardDescription className="text-gray-400">
            Start a new DJ session for taking song requests
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="session-name">Session Name</Label>
              <Input
                id="session-name"
                placeholder="Saturday Night Mix, Birthday Party, etc."
                value={newSessionName}
                onChange={(e) => setNewSessionName(e.target.value)}
                className="bg-gray-900/60 border-gray-700"
                disabled={isCreateDisabled}
              />
            </div>

            {!isAppleMusicAuthorized && (
              <div className="flex items-start gap-2 p-3 bg-amber-950/30 border border-amber-800/30 rounded-md">
                <AlertTriangle className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
                <p className="text-sm text-amber-200/90">
                  Apple Music connection is required to create a session
                </p>
              </div>
            )}

            {hasActiveSession && (
              <div className="flex items-start gap-2 p-3 bg-blue-950/30 border border-blue-800/30 rounded-md">
                <AlertTriangle className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                <p className="text-sm text-blue-200/90">
                  You already have an active session. Deactivate it to create a new one.
                </p>
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="w-full">
                  <Button
                    onClick={handleCreateSession}
                    disabled={isCreateDisabled}
                    className="w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white font-semibold transition-colors duration-200"
                  >
                    <PlusCircle className="mr-2 h-4 w-4" />
                    {isCreating ? "Creating..." : "Create New Session"}
                  </Button>
                </div>
              </TooltipTrigger>
              {isCreateDisabled && (
                <TooltipContent className="bg-gray-900 border border-gray-700">
                  <p>{getTooltipMessage()}</p>
                </TooltipContent>
              )}
            </Tooltip>
          </TooltipProvider>
        </CardFooter>
      </Card>

      <div className="space-y-4">
        <h2 className="text-xl font-bold text-white">Your Sessions</h2>

        {isLoading ? (
          <div className="flex justify-center p-8">
            <motion.div
              animate={{
                rotate: 360,
                borderRadius: ["50% 50% 50% 50%", "30% 70% 70% 30%", "50% 50% 50% 50%"]
              }}
              transition={{
                repeat: Infinity,
                duration: 2,
                ease: "easeInOut"
              }}
              className="h-12 w-12 border-4 border-transparent border-t-purple-500 border-r-cyan-500"
            />
          </div>
        ) : sessions && sessions.length > 0 ? (
          <div className="grid gap-4 md:grid-cols-2">
            {sessions.map((session) => (
              <motion.div
                key={session.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card className={`bg-gray-900/30 backdrop-blur-sm border-l-4 ${session.active ? 'border-l-green-500' : 'border-l-gray-500'}`}>
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <span className="truncate">{session.name}</span>
                      {session.active && (
                        <span className="ml-2 flex-shrink-0 h-2 w-2 rounded-full bg-green-500 shadow-[0_0_8px_2px_rgba(0,255,102,0.6)]"></span>
                      )}
                    </CardTitle>
                    <CardDescription className="text-gray-400 flex items-center gap-2">
                      <Calendar className="h-3 w-3" />
                      <span>
                        {new Date(session.created_at).toLocaleDateString()}
                      </span>
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pb-0">

                  </CardContent>
                  <CardFooter className="pt-4">
                    <div className="flex space-x-2 w-full">
                      <Button
                        variant="outline"
                        className="flex-1 border-gray-700 hover:bg-gray-800"
                        onClick={() => toggleSessionStatus(session)}
                      >
                        {session.active ? "Deactivate" : "Activate"}
                      </Button>
                      <Button
                        className="flex-1 bg-gradient-to-r from-purple-600 to-cyan-500 hover:from-purple-700 hover:to-cyan-600"
                        onClick={() => onSelectSession(session.id)}
                      >
                        <span>Manage</span>
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>

                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="outline"
                            className="border-red-900/30 bg-red-950/20 hover:bg-red-950/40 text-red-400"
                            size="icon"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent className="bg-gray-900 border-gray-700">
                          <AlertDialogHeader>
                            <AlertDialogTitle className="text-white">Delete Session</AlertDialogTitle>
                            <AlertDialogDescription className="text-gray-400">
                              Are you sure you want to delete "{session.name}"? This action cannot be undone.
                              All song requests will be permanently deleted from PlayBeg, and the associated Apple Music playlist will also be deleted from your Apple Music library.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel className="bg-gray-800 text-white hover:bg-gray-700 border-gray-700">
                              Cancel
                            </AlertDialogCancel>
                            <AlertDialogAction
                              className="bg-red-600 text-white hover:bg-red-700"
                              onClick={() => handleDeleteSession(session)}
                              disabled={isDeleting}
                            >
                              {isDeleting && sessionToDelete?.id === session.id ? "Deleting..." : "Delete Session"}
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </CardFooter>
                </Card>
              </motion.div>
            ))}
          </div>
        ) : (
          <Card className="bg-gray-900/30 border-gray-800">
            <CardContent className="flex flex-col items-center justify-center p-6 text-center">
              <div className="rounded-full bg-gray-800/80 p-3 mb-3">
                <Music className="h-6 w-6 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-300">No sessions yet</h3>
              <p className="text-gray-500 mt-1">
                Create your first session to start receiving song requests
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default SessionsManager;
