import { useState, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Loader2, CheckCircle2 } from 'lucide-react';
import { toast } from 'sonner';

// Define the form schema with Zod
const formSchema = z.object({
  companyName: z.string().min(2, { message: 'Company name must be at least 2 characters' }),
  contactName: z.string().min(2, { message: 'Contact name must be at least 2 characters' }),
  contactEmail: z.string().email({ message: 'Please enter a valid email address' }),
  contactPhone: z.string().optional(),
  website: z.string().url({ message: 'Please enter a valid URL' }).optional().or(z.literal('')),
  partnerType: z.enum(['venue', 'dj_agency', 'tech', 'music_industry', 'other'], {
    required_error: 'Please select a partner type',
  }),
  companySize: z.enum(['1-10', '11-50', '51-200', '201-1000', '1000+'], {
    required_error: 'Please select a company size',
  }),
  message: z.string().min(10, { message: 'Message must be at least 10 characters' }),
  howHeard: z.string().optional(),
});

// Define the form data type
type FormData = z.infer<typeof formSchema>;

// Obfuscate email display
const ObfuscatedEmail = ({ email }: { email: string }) => {
  // This component will render the email in a way that's harder for bots to scrape
  return (
    <span className="email-protected" data-email={email.split('').map(char => char.charCodeAt(0)).join(',')}>
      {/* The actual email is not rendered directly in the HTML */}
      Contact Us
    </span>
  );
};

const PartnerApplicationForm = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Initialize the form
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      companyName: '',
      contactName: '',
      contactEmail: '',
      contactPhone: '',
      website: '',
      partnerType: undefined,
      companySize: undefined,
      message: '',
      howHeard: '',
    },
  });



  // Handle form submission
  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);

    try {

      // Insert the form data into Supabase
      const { error } = await supabase
        .from('partner_applications')
        .insert([
          {
            company_name: data.companyName,
            contact_name: data.contactName,
            contact_email: data.contactEmail,
            contact_phone: data.contactPhone || null,
            website: data.website || null,
            partner_type: data.partnerType,
            company_size: data.companySize,
            message: data.message,
            how_heard: data.howHeard || null,
            status: 'new'
          },
        ]);

      if (error) {
        throw error;
      }

      // Show success message
      toast.success('Your application has been submitted successfully!');
      setIsSubmitted(true);
    } catch (error) {
      console.error('Error submitting partner application:', error);
      toast.error('There was an error submitting your application. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="bg-gray-900 rounded-lg p-8 text-center">
        <div className="flex justify-center mb-6">
          <CheckCircle2 className="h-16 w-16 text-green-500" />
        </div>
        <h3 className="text-2xl font-bold text-white mb-4">Application Submitted!</h3>
        <p className="text-gray-300 mb-6">
          Thank you for your interest in partnering with PlayBeg. Our team will review your application and contact you within 3 business days.
        </p>
        <Button
          onClick={() => setIsSubmitted(false)}
          variant="outline"
          className="border-purple-500 text-purple-400 hover:bg-purple-500/10"
        >
          Submit Another Application
        </Button>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="companyName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company Name *</FormLabel>
                <FormControl>
                  <Input className="form-input" placeholder="Your company name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="contactName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Contact Name *</FormLabel>
                <FormControl>
                  <Input className="form-input" placeholder="Your full name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="contactEmail"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Contact Email *</FormLabel>
                <FormControl>
                  <Input className="form-input" type="email" placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="contactPhone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Contact Phone</FormLabel>
                <FormControl>
                  <Input className="form-input" placeholder="Optional phone number" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="website"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Website</FormLabel>
                <FormControl>
                  <Input className="form-input" placeholder="https://yourcompany.com" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="partnerType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Partner Type *</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="select-trigger">
                      <SelectValue placeholder="Select partner type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="select-content">
                    <SelectItem className="select-item" value="venue">Venue Owner / Event Space</SelectItem>
                    <SelectItem className="select-item" value="dj_agency">DJ Collective / Agency</SelectItem>
                    <SelectItem className="select-item" value="tech">Technology Partner</SelectItem>
                    <SelectItem className="select-item" value="music_industry">Music Industry Professional</SelectItem>
                    <SelectItem className="select-item" value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="companySize"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company Size *</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="select-trigger">
                      <SelectValue placeholder="Select company size" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="select-content">
                    <SelectItem className="select-item" value="1-10">1-10 employees</SelectItem>
                    <SelectItem className="select-item" value="11-50">11-50 employees</SelectItem>
                    <SelectItem className="select-item" value="51-200">51-200 employees</SelectItem>
                    <SelectItem className="select-item" value="201-1000">201-1000 employees</SelectItem>
                    <SelectItem className="select-item" value="1000+">1000+ employees</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="howHeard"
            render={({ field }) => (
              <FormItem>
                <FormLabel>How did you hear about us?</FormLabel>
                <FormControl>
                  <Input className="form-input" placeholder="Optional" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="message"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Message *</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Tell us about your interest in partnering with PlayBeg"
                  className="min-h-[120px] form-textarea"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="text-sm text-gray-400 mt-4">
          <p>* Required fields</p>
        </div>



        <Button
          type="submit"
          className="w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Submitting...
            </>
          ) : (
            'Submit Application'
          )}
        </Button>
      </form>
    </Form>
  );
};

export default PartnerApplicationForm;
