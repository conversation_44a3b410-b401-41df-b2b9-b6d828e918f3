
import { useState, useEffect } from 'react';
import { Clock, Check, X, Play, Music, Users } from 'lucide-react';
import { Badge } from "@/components/ui/badge";
import { cn } from '@/lib/utils';
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";

type RequestStatus = "pending" | "approved" | "auto-approved" | "declined" | "played" | "archived";

interface RequestStatusIndicatorProps {
  sessionId: string;
  requestId: string;
  status: RequestStatus;
  songTitle?: string;
  artistName?: string;
  artworkUrl?: string;
  duplicateCount?: number;
  isSecondary?: boolean;
}

export function RequestStatusIndicator({
  sessionId,
  requestId,
  status,
  songTitle = '',
  artistName = '',
  artworkUrl = '',
  duplicateCount = 0,
  isSecondary = false
}: RequestStatusIndicatorProps) {
  const [statusClass, setStatusClass] = useState('');
  const [statusIcon, setStatusIcon] = useState<React.ReactNode>(null);
  const [statusText, setStatusText] = useState('');

  useEffect(() => {
    // Set appropriate styles and text based on status
    switch (status) {
      case 'pending':
        setStatusClass('bg-yellow-500/10 text-yellow-300 border-yellow-500/20');
        setStatusIcon(<Clock className={cn("mr-1.5", isSecondary ? "h-3 w-3" : "h-4 w-4")} />);
        setStatusText('Pending');
        break;
      case 'approved':
        setStatusClass('bg-green-500/10 text-green-300 border-green-500/20');
        setStatusIcon(<Check className={cn("mr-1.5", isSecondary ? "h-3 w-3" : "h-4 w-4")} />);
        setStatusText('Approved');
        break;
      case 'auto-approved':
        setStatusClass('bg-blue-500/10 text-blue-300 border-blue-500/20');
        setStatusIcon(<Check className={cn("mr-1.5", isSecondary ? "h-3 w-3" : "h-4 w-4")} />);
        setStatusText('Auto-Approved');
        break;
      case 'declined':
        setStatusClass('bg-red-500/10 text-red-300 border-red-500/20');
        setStatusIcon(<X className={cn("mr-1.5", isSecondary ? "h-3 w-3" : "h-4 w-4")} />);
        setStatusText('Declined');
        break;
      case 'played':
        setStatusClass('bg-purple-500/10 text-purple-300 border-purple-500/20');
        setStatusIcon(<Play className={cn("mr-1.5", isSecondary ? "h-3 w-3" : "h-4 w-4")} />);
        setStatusText('Played');
        break;
      case 'archived':
        setStatusClass('bg-gray-500/10 text-gray-400 border-gray-500/20');
        setStatusIcon(<Music className={cn("mr-1.5", isSecondary ? "h-3 w-3" : "h-4 w-4")} />);
        setStatusText('Archived');
        break;
      default:
        setStatusClass('bg-gray-500/10 text-gray-300 border-gray-500/20');
        setStatusIcon(<Music className={cn("mr-1.5", isSecondary ? "h-3 w-3" : "h-4 w-4")} />);
        setStatusText('Unknown');
    }
  }, [status, isSecondary]);

  // Generate the time display
  const timeDisplay = (
    <span className="text-xs text-gray-500">
      {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
    </span>
  );

  if (isSecondary) {
    // Simplified view for secondary indicators
    return (
      <div className={`px-3 py-2 flex items-center justify-between ${statusClass} rounded-lg shadow-sm`}>
        <div className="flex flex-col w-full">
          <div className="flex items-center text-xs">
            {statusIcon}
            <span>{statusText}</span>
          </div>
          {(songTitle || artistName) && (
            <div className="flex items-center gap-2 mt-1">
              {artworkUrl ? (
                <Avatar className="h-7 w-7 rounded-md shadow-sm ring-1 ring-white/5">
                  <AvatarImage src={artworkUrl} alt={songTitle || 'Album artwork'} />
                  <AvatarFallback className="rounded-md bg-gradient-to-br from-purple-900 to-purple-950 text-purple-200 text-[8px]">
                    <Music className="h-3 w-3" />
                  </AvatarFallback>
                </Avatar>
              ) : (
                <Avatar className="h-7 w-7 rounded-md shadow-sm">
                  <AvatarFallback className="rounded-md bg-gradient-to-br from-purple-900 to-purple-950 text-purple-200 text-[8px]">
                    <Music className="h-3 w-3" />
                  </AvatarFallback>
                </Avatar>
              )}
              <div className="ml-0 text-xs flex-1 min-w-0">
                <div className="font-medium text-gray-200 truncate">{songTitle}</div>
                <div className="text-gray-400 truncate">{artistName}</div>
              </div>
            </div>
          )}
        </div>
        <div className="text-xs text-gray-500 bg-black/20 px-1.5 py-0.5 rounded-full text-[10px]">
          {timeDisplay}
        </div>
      </div>
    );
  }

  return (
    <div className={`p-3 ${statusClass} rounded-xl shadow-md`}>
      <div className="flex justify-between items-start">
        <div className="flex flex-col w-full">
          <div className="flex items-center">
            {statusIcon}
            <span className="font-medium">{statusText}</span>

            {duplicateCount > 0 && (
              <Badge
                variant="outline"
                className="ml-2 bg-blue-500/20 text-blue-300 border-blue-500/30 text-xs px-2 py-0.5 rounded-full"
              >
                <Users className="h-3 w-3 mr-1" />
                +{duplicateCount} {duplicateCount === 1 ? 'other' : 'others'}
              </Badge>
            )}
          </div>

          {/* Display song information with artwork */}
          {(songTitle || artistName) && (
            <div className="flex items-center gap-3 ml-5 mt-2">
              {artworkUrl ? (
                <Avatar className="h-12 w-12 rounded-lg shadow-md ring-1 ring-white/10">
                  <AvatarImage src={artworkUrl} alt={songTitle || 'Album artwork'} />
                  <AvatarFallback className="rounded-lg bg-gradient-to-br from-purple-900 to-purple-950 text-purple-200">
                    <Music className="h-5 w-5" />
                  </AvatarFallback>
                </Avatar>
              ) : (
                <Avatar className="h-12 w-12 rounded-lg shadow-md">
                  <AvatarFallback className="rounded-lg bg-gradient-to-br from-purple-900 to-purple-950 text-purple-200">
                    <Music className="h-5 w-5" />
                  </AvatarFallback>
                </Avatar>
              )}
              <div className="flex-1 min-w-0">
                <div className="font-medium text-gray-200 truncate">{songTitle}</div>
                <div className="text-sm text-gray-400 truncate">{artistName}</div>
              </div>
            </div>
          )}
        </div>
        <div className="text-xs text-gray-500 bg-black/20 px-2 py-1 rounded-full">
          {timeDisplay}
        </div>
      </div>
    </div>
  );
}
