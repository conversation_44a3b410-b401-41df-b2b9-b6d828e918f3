import { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Search, Loader2, MusicIcon, AlertTriangle, PlusCircle, Check, Timer, Music, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { SongSearchResult, searchSongs } from '@/utils/songSearchService';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useDebounce } from '@/hooks/useDebounce';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { supabase } from '@/integrations/supabase/client';
import { cn } from '@/lib/utils';

interface SongSearchProps {
  onSongSelect: (SongSearchResult) => void;
  selectedSong: SongSearchResult | null;
  isAuthorizedOverride?: boolean;
  songStatuses?: Record<string, string>;
  requesterName?: string;
  directSubmit?: boolean;
  onCooldown?: boolean;
  cooldownTimeLeft?: number;
  sessionId?: string;
}

export function SongSearch({
  onSongSelect,
  selectedSong,
  isAuthorizedOverride = false,
  songStatuses = {},
  requesterName = '',
  directSubmit = true,
  onCooldown = false,
  cooldownTimeLeft = 0,
  sessionId
}: SongSearchProps) {
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [results, setResults] = useState<SongSearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [songToConfirm, setSongToConfirm] = useState<SongSearchResult | null>(null);
  const [localSongStatuses, setLocalSongStatuses] = useState<Record<string, string>>(songStatuses);
  const debouncedSearchTerm = useDebounce(searchQuery, 500);
  const { toast } = useToast();
  const inputRef = useRef<HTMLInputElement>(null);

  // Update local song statuses when prop changes
  useEffect(() => {
    setLocalSongStatuses(songStatuses);
  }, [songStatuses]);

  useEffect(() => {
    if (!debouncedSearchTerm || debouncedSearchTerm.length < 2 || selectedSong) {
      setResults([]);
      return;
    }

    if (!isAuthorizedOverride) {
      setSearchError('Authorization required to search');
      return;
    }

    const performSearch = async () => {
      try {
        setIsSearching(true);
        setSearchError(null);
        // Use sessionId if available for secure proxy search
        const searchResults = await searchSongs(debouncedSearchTerm, sessionId);
        setResults(searchResults);
      } catch (error) {
        console.error('Error searching for songs:', error);
        setSearchError('Failed to search for songs');
        toast({
          title: 'Search Error',
          description: 'Could not retrieve song results',
          variant: 'destructive',
        });
      } finally {
        setIsSearching(false);
      }
    };

    performSearch();
  }, [debouncedSearchTerm, selectedSong, toast, isAuthorizedOverride]);

  const handleSongSelect = (song: SongSearchResult) => {
    // If song is already requested, show a toast and don't proceed
    if (localSongStatuses[song.id]) {
      const status = localSongStatuses[song.id];
      toast({
        title: "Song already requested",
        description: `This song is currently ${status}`,
        variant: "default",
      });
      return;
    }

    // Ensure the song has all necessary properties, including artwork, before selection
    const enhancedSong = {
      ...song,
      // Make sure artwork is properly formatted with the URL template replaced
      artwork: song.artwork ? song.artwork.replace('{w}', '300').replace('{h}', '300') : null
    };

    if (directSubmit) {
      // Skip confirmation dialog and directly submit
      onSongSelect(enhancedSong);
      setSearchQuery('');
      setResults([]);
    } else {
      // Show confirmation dialog (original behavior)
      setSongToConfirm(enhancedSong);
      setShowConfirmDialog(true);
    }
  };

  const confirmSongSelection = () => {
    if (!songToConfirm) return;

    onSongSelect(songToConfirm);
    setSearchQuery('');
    setResults([]);
    setSongToConfirm(null);
    setShowConfirmDialog(false);

    // Show success toast
    toast({
      title: "Song selected",
      description: "Ready to submit your request",
      variant: "default",
    });
  };

  const getStatusBadge = (songId: string) => {
    const status = localSongStatuses[songId];
    if (!status) return null;

    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-500/10 text-yellow-400 border-yellow-500/30">Pending</Badge>;
      case 'approved':
        return <Badge variant="outline" className="bg-green-500/10 text-green-400 border-green-500/30">Approved</Badge>;
      case 'played':
        return <Badge variant="outline" className="bg-purple-500/10 text-purple-400 border-purple-500/30"><Music className="w-3 h-3 mr-1" />Played</Badge>;
      case 'declined':
        return <Badge variant="outline" className="bg-red-500/10 text-red-400 border-red-500/30">Declined</Badge>;
      default:
        return null;
    }
  };

  // Get a random funny placeholder from our list
  const getRandomPlaceholder = () => {
    const funnyPlaceholders = [
      "Search for that song stuck in your head...",
      "Find the song the DJ pretends to know...",
      "Type here, music appears like magic!",
      "What earworm are we hunting today?",
      "Find your guilty pleasure song...",
      "The DJ awaits your musical wisdom...",
      "Discover music the DJ hasn't heard yet...",
      "Your song suggestion goes here...",
      "What should everyone dance to next?",
      "Your musical destiny awaits..."
    ];
    return funnyPlaceholders[Math.floor(Math.random() * funnyPlaceholders.length)];
  };

  const clearSearch = () => {
    setSearchQuery('');
    setResults([]);
    // Focus the input after clearing
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Subscribe to real-time updates for song request statuses
  useEffect(() => {
    if (!requesterName) return;

    console.log('Setting up song status listener for requester:', requesterName);

    const channel = supabase
      .channel(`song-status-updates-${requesterName}`)
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'song_requests',
        filter: `requester_name=eq.${requesterName}`
      }, async (payload) => {
        console.log('Song request status updated:', payload);

        if (payload.new && payload.new.apple_music_id && payload.new.status) {
          // Update the local song statuses directly
          setLocalSongStatuses(prev => ({
            ...prev,
            [payload.new.apple_music_id]: payload.new.status
          }));

          console.log(`Updated status for song ${payload.new.apple_music_id} to ${payload.new.status}`);
        } else if (payload.new && payload.new.status) {
          // If we don't have the apple_music_id directly in the payload, try to fetch it
          try {
            const { data: songData } = await supabase
              .from('songs')
              .select('apple_music_id')
              .eq('id', payload.new.song_id)
              .single();

            if (songData?.apple_music_id) {
              // Update the local song statuses with the fetched apple_music_id
              setLocalSongStatuses(prev => ({
                ...prev,
                [songData.apple_music_id]: payload.new.status
              }));

              console.log(`Updated status for song ${songData.apple_music_id} to ${payload.new.status}`);
            }
          } catch (error) {
            console.error('Error fetching song data for status update:', error);
          }
        }
      })
      .subscribe((status) => {
        console.log(`Song status updates subscription status: ${status}`);
      });

    // Add subscription for played songs updates regardless of requester
    const playedSongsChannel = supabase
      .channel(`all-played-songs-updates`)
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'song_requests',
        filter: `status=eq.played`
      }, (payload) => {
        if (payload.new && payload.new.apple_music_id) {
          console.log(`Global notification: Song ${payload.new.apple_music_id} was played`);

          // Update local status for this song
          setLocalSongStatuses(prev => ({
            ...prev,
            [payload.new.apple_music_id]: 'played'
          }));
        }
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
      supabase.removeChannel(playedSongsChannel);
    };
  }, [requesterName]);

  // Initial load of all played songs to show their status in search results
  useEffect(() => {
    const fetchPlayedSongs = async () => {
      try {
        const { data, error } = await supabase
          .from('song_requests')
          .select('apple_music_id')
          .eq('status', 'played');

        if (error) {
          console.error('Error fetching played songs:', error);
          return;
        }

        if (data && data.length > 0) {
          console.log(`Found ${data.length} played songs in database`);

          // Create a map of apple_music_id -> 'played' for all played songs
          const playedSongsMap = data.reduce((acc, song) => {
            if (song.apple_music_id) {
              acc[song.apple_music_id] = 'played';
            }
            return acc;
          }, {} as Record<string, string>);

          // Merge with existing statuses
          setLocalSongStatuses(prev => ({
            ...prev,
            ...playedSongsMap
          }));
        }
      } catch (error) {
        console.error('Error in fetching played songs:', error);
      }
    };

    fetchPlayedSongs();
  }, []);

  // Function to format cooldown time in minutes with a humorous message
  const formatCooldownTime = (seconds: number) => {
    const minutes = Math.ceil(seconds / 60);
    const timeUnit = minutes === 1 ? 'minute' : 'minutes';

    // Array of humorous cooldown messages
    const cooldownMessages = [

      `Take a ${minutes}-${timeUnit} break!`
    ];

    // Select a random message from the array
    const randomIndex = Math.floor(Math.random() * cooldownMessages.length);
    return cooldownMessages[randomIndex];
  };

  return (
    <div className="space-y-2 w-full">
      <div className="relative w-full">
        <Input
          ref={inputRef}
          placeholder={
            onCooldown
              ? formatCooldownTime(cooldownTimeLeft)
              : getRandomPlaceholder()
          }
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className={cn(
            "w-full pl-10 pr-12 py-3 bg-gray-950/50 border-gray-800 rounded-xl text-white shadow-md",
            "focus:ring-2 focus:ring-purple-500/40 transition-all",
            "text-base md:text-sm", // Larger text on mobile
            searchQuery && "pr-12" // Extra padding when we show the X button
          )}
          disabled={!isAuthorizedOverride || !!selectedSong || onCooldown}
          autoFocus
        />
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          {isSearching ? (
            <Loader2 className="h-5 w-5 text-gray-400 animate-spin" />
          ) : onCooldown ? (
            <Timer className="h-5 w-5 text-amber-400" />
          ) : (
            <Search className="h-5 w-5 text-purple-400" />
          )}
        </div>

        {/* X button to clear search */}
        {searchQuery && (
          <Button
            type="button"
            variant="ghost"
            size="icon"
            onClick={clearSearch}
            className="absolute inset-y-0 right-0 pr-3 flex items-center h-full"
          >
            <X className="h-5 w-5 text-gray-400 hover:text-white transition-colors" />
            <span className="sr-only">Clear search</span>
          </Button>
        )}
      </div>

      {searchError && (
        <Alert variant="default" className="bg-amber-950/50 border-amber-800">
          <AlertTriangle className="h-4 w-4 text-amber-400" />
          <AlertDescription className="text-amber-300">
            {searchError}
          </AlertDescription>
        </Alert>
      )}

      <AnimatePresence>
        {results.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="mt-2 bg-gray-900/90 backdrop-blur-sm border border-gray-800 rounded-xl overflow-hidden shadow-xl max-h-72 overflow-y-auto"
          >
            {results.map((result) => {
              const hasStatus = !!localSongStatuses[result.id];
              const isPlayed = localSongStatuses[result.id] === 'played';
              const isRequestable = !hasStatus || isPlayed;

              return (
                <motion.div
                  key={result.id}
                  className="flex items-center justify-between py-4 px-3 border-b border-gray-800/50 last:border-0 hover:bg-white/5 group"
                  whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.05)' }}
                >
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className="h-12 w-12 rounded-lg bg-gray-800 flex-shrink-0 overflow-hidden shadow-md">
                      {result.artwork ? (
                        <img
                          src={result.artwork}
                          alt={result.title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <MusicIcon className="h-6 w-6 text-gray-600" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <p className="text-white font-medium truncate">{result.title}</p>
                        {getStatusBadge(result.id)}
                      </div>
                      <p className="text-sm text-gray-400 truncate">{result.artist}</p>
                      {result.genre && (
                        <p className="text-xs text-purple-400 truncate mt-0.5">{result.genre}</p>
                      )}
                    </div>
                  </div>
                  <Button
                    onClick={() => handleSongSelect(result)}
                    variant={isRequestable ? "default" : "ghost"}
                    size="sm"
                    className={`ml-4 ${isRequestable ?
                      'bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-500 hover:to-cyan-500 hover:scale-105 text-white shadow-md h-10 px-5 rounded-full' :
                      'text-gray-500 cursor-not-allowed'} opacity-0 group-hover:opacity-100 transition-all duration-200`}
                    disabled={!isRequestable}
                  >
                    {isRequestable ? (
                      <>
                        <PlusCircle className="h-5 w-5 mr-1" />
                        <span className="sr-only sm:not-sr-only sm:inline-block">
                          {isPlayed ? 'Request Again' : (directSubmit ? 'Request' : 'Select')}
                        </span>
                      </>
                    ) : (
                      <>
                        <Check className="h-5 w-5 mr-1" />
                        <span className="sr-only sm:not-sr-only sm:inline-block">Requested</span>
                      </>
                    )}
                  </Button>
                </motion.div>
              );
            })}
          </motion.div>
        )}
      </AnimatePresence>

      {searchQuery && !isSearching && results.length === 0 && !searchError && (
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-sm text-gray-400 mt-2 text-center"
        >
          No songs found. Try another search.
        </motion.p>
      )}

      {/* Confirmation Dialog - only shown when directSubmit is false */}
      {!directSubmit && (
        <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
          <DialogContent className="bg-gray-900 border border-purple-500/30">
            <DialogHeader>
              <DialogTitle className="text-white">Confirm Your Request</DialogTitle>
              <DialogDescription className="text-gray-400">
                You're about to request this song
              </DialogDescription>
            </DialogHeader>

            {songToConfirm && (
              <div className="p-4 bg-gray-800/50 rounded-md flex items-center gap-3 my-2">
                <div className="h-14 w-14 rounded-md bg-gray-800 flex-shrink-0 overflow-hidden">
                  {songToConfirm.artwork ? (
                    <img
                      src={songToConfirm.artwork}
                      alt={songToConfirm.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <MusicIcon className="h-7 w-7 text-gray-600" />
                    </div>
                  )}
                </div>
                <div>
                  <p className="font-medium text-white">{songToConfirm.title}</p>
                  <p className="text-sm text-gray-400">{songToConfirm.artist}</p>
                  {songToConfirm.genre && (
                    <p className="text-xs text-purple-400 mt-0.5">{songToConfirm.genre}</p>
                  )}
                  {requesterName && (
                    <p className="text-xs text-gray-500 mt-1">
                      Requested by: {requesterName}
                    </p>
                  )}
                </div>
              </div>
            )}

            <div className="flex gap-3 justify-end mt-4">
              <Button
                variant="outline"
                onClick={() => setShowConfirmDialog(false)}
                className="border-gray-700 text-gray-300 hover:bg-gray-800"
              >
                Cancel
              </Button>
              <Button
                onClick={confirmSongSelection}
                className="bg-gradient-to-r from-purple-600 to-cyan-500 hover:from-purple-700 hover:to-cyan-600"
              >
                Confirm Request
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}

// Function to format cooldown time in minutes with a humorous message
const formatCooldownTime = (seconds: number) => {
  const minutes = Math.ceil(seconds / 60);
  const timeUnit = minutes === 1 ? 'minute' : 'minutes';

  // Array of humorous cooldown messages
  const cooldownMessages = [
    `Take a ${minutes}-${timeUnit} break!`,
    `DJ needs ${minutes} ${timeUnit} to recover...`,
    `${minutes} ${timeUnit} until your next music wisdom`,
    `Cooldown active: ${minutes} ${timeUnit} to go`,
    `Music powers recharge in ${minutes} ${timeUnit}...`
  ];

  // Select a random message from the array
  const randomIndex = Math.floor(Math.random() * cooldownMessages.length);
  return cooldownMessages[randomIndex];
};
