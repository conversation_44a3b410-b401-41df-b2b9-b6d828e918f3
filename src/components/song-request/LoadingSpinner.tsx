
import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Music, Disc, Headphones, Sparkles } from "lucide-react";
import { DJLoadingAnimation } from "./DJLoadingAnimation";

interface LoadingSpinnerProps {
  message?: string;
  showFunnyMessage?: boolean;
  minDisplayTime?: number; // Add prop for minimum display time in milliseconds
}

const funnyMessages = [
  "Convincing the DJ to play your song...",
  "Bribing the DJ with compliments...",
  "Polishing the vinyl records...",
  "Untangling the headphone cables...",
  "Teaching the DJ how to pronounce this artist...",
  "Finding the perfect moment for your track...",
  "Checking if the DJ actually likes this song...",
  "Warming up the speakers...",
  "Making room on the dance floor...",
  "Calculating the perfect BPM...",
  "Waiting for the DJ to finish their coffee...",
  "Syncing to the cosmic rhythm...",
  "Checking how many others requested this song...",
  "Joining your song to the VIP queue...",
  "Measuring the dance-ability score...",
  "Finding other fans of this track in the crowd...",
];

export function LoadingSpinner({ 
  message = "Loading...", 
  showFunnyMessage = true,
  minDisplayTime = 3000 // Default to 3 seconds (3000ms)
}: LoadingSpinnerProps) {
  const [funnyMessage, setFunnyMessage] = useState<string>("");
  const [shouldShow, setShouldShow] = useState<boolean>(true);
  const [startTime] = useState<number>(Date.now());
  
  useEffect(() => {
    if (showFunnyMessage) {
      // Select a random funny message
      const randomIndex = Math.floor(Math.random() * funnyMessages.length);
      setFunnyMessage(funnyMessages[randomIndex]);
      
      // Change message every 5 seconds (increased from 3 seconds)
      const interval = setInterval(() => {
        const newIndex = Math.floor(Math.random() * funnyMessages.length);
        setFunnyMessage(funnyMessages[newIndex]);
      }, 5000); // Increased from 3000 to 5000 milliseconds
      
      return () => clearInterval(interval);
    }
  }, [showFunnyMessage]);

  // Ensure loading spinner is shown for at least minDisplayTime milliseconds
  useEffect(() => {
    const timer = setTimeout(() => {
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minDisplayTime - elapsedTime);
      
      // Only hide after minDisplayTime has elapsed
      setTimeout(() => {
        setShouldShow(false);
      }, remainingTime);
    }, 0);
    
    return () => clearTimeout(timer);
  }, [minDisplayTime, startTime]);

  // If shouldShow is false, signal to parent component loading is done
  if (!shouldShow) return null;

  return (
    <div className="flex flex-col justify-center items-center min-h-[60vh] relative px-4">
      {/* Tiny dancing figures in corners */}
      <motion.div 
        className="absolute top-0 left-0"
        animate={{ 
          y: [0, -10, 0],
          rotate: [0, 5, 0, -5, 0],
        }}
        transition={{ 
          repeat: Infinity, 
          duration: 2,
          ease: "easeInOut" 
        }}
      >
        <Headphones className="h-8 w-8 text-purple-500/70" />
      </motion.div>
      
      <motion.div 
        className="absolute top-0 right-0"
        animate={{ 
          y: [0, -10, 0],
          rotate: [0, -5, 0, 5, 0],
        }}
        transition={{ 
          repeat: Infinity, 
          duration: 1.7, 
          ease: "easeInOut",
          delay: 0.3
        }}
      >
        <Music className="h-8 w-8 text-cyan-500/70" />
      </motion.div>
      
      <motion.div 
        className="absolute bottom-0 left-0"
        animate={{ 
          y: [0, 10, 0],
          rotate: [0, 10, 0, -10, 0],
        }}
        transition={{ 
          repeat: Infinity, 
          duration: 2.2,
          ease: "easeInOut",
          delay: 0.5
        }}
      >
        <Sparkles className="h-8 w-8 text-amber-500/70" />
      </motion.div>
      
      <motion.div 
        className="absolute bottom-0 right-0"
        animate={{ 
          y: [0, 10, 0],
          rotate: [0, -10, 0, 10, 0],
        }}
        transition={{ 
          repeat: Infinity, 
          duration: 1.8,
          ease: "easeInOut",
          delay: 0.7
        }}
      >
        <Disc className="h-8 w-8 text-pink-500/70" />
      </motion.div>
      
      {/* Main DJ animation */}
      <DJLoadingAnimation />
      
      {/* Messages */}
      <div className="mt-8 text-center">
        {showFunnyMessage ? (
          <motion.p
            key={funnyMessage}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="text-lg sm:text-xl text-purple-300 font-medium"
          >
            {funnyMessage}
          </motion.p>
        ) : (
          <p className="text-lg text-gray-300">{message}</p>
        )}
        
        <p className="mt-2 text-sm text-gray-400">{message}</p>
      </div>
    </div>
  );
}
