import React, { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Music, Search, X, Shield } from 'lucide-react';
import { SongSearchResult, searchSongs } from '@/utils/songSearchService';
import { useDebounce } from '@/hooks/useDebounce';
import { SelectedSong } from './SelectedSong';
import { DebugPanel } from './DebugPanel';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { cn } from '@/lib/utils';
import { useIsMobile } from "@/hooks/use-mobile";

// This type definition ensures we use the correct RequestStatus values
type RequestStatus = "pending" | "approved" | "auto-approved" | "declined" | "played";

// Type for the song request payload from Supabase
interface SongRequestPayload {
  id: string;
  session_id: string;
  song_title: string;
  artist_name: string;
  requester_name: string;
  status: RequestStatus;
  apple_music_id?: string;
  [key: string]: any; // For other properties that might be present
}

interface SongSearchCardProps {
  sessionId: string | null;
  sessionName: string | null;
  isSessionActive: boolean;
  selectedSong: SongSearchResult | null;
  onSongSelect: (song: SongSearchResult) => void;
  onSongClear: () => void;
  isAppleMusicAuthorized: boolean;
  songStatuses: Record<string, string>;
  requesterName: string;
  isSubmitting: boolean;
  onCooldown: boolean;
  cooldownTimeLeft?: number;
  remainingQuota: number;
  onRequestSubmit: () => void;
  lastRequestId: string | null;
  debugInfo: string[];
  isMusicKitInitialized?: boolean;
  isFreeTier?: boolean;
  remainingSessionRequests?: number | null;
}

export function SongSearchCard({
  sessionId,
  sessionName,
  isSessionActive,
  selectedSong,
  onSongSelect,
  onSongClear,
  isAppleMusicAuthorized,
  songStatuses,
  requesterName,
  isSubmitting,
  onCooldown,
  cooldownTimeLeft,
  remainingQuota,
  onRequestSubmit,
  lastRequestId,
  debugInfo,
  isMusicKitInitialized = false,
  isFreeTier = false,
  remainingSessionRequests = null
}: SongSearchCardProps) {
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [results, setResults] = useState<SongSearchResult[]>([]);
  const [error, setError] = useState<string | null>(null);
  const debouncedSearchQuery = useDebounce<string>(searchQuery, 400);
  const inputRef = useRef<HTMLInputElement>(null);
  const isMobile = useIsMobile();

  // Track songs that have already been played in this session
  const [playedSongs, setPlayedSongs] = useState<Set<string>>(new Set());

  // Store our own copy of song statuses for more reliable access
  const [localSongStatuses, setLocalSongStatuses] = useState<Record<string, string>>({});

  // Track which songs the current user has already requested
  const [userRequestedSongs, setUserRequestedSongs] = useState<Set<string>>(new Set());

  // Track blocked genres for this session
  const [blockedGenres, setBlockedGenres] = useState<string[]>([]);

  // Track when genre blocking updates are received for user feedback
  const [genreBlockingUpdated, setGenreBlockingUpdated] = useState(false);

  // Update local song statuses when prop changes
  useEffect(() => {
    if (Object.keys(songStatuses).length > 0) {
      console.log("Received updated song statuses from parent:", songStatuses);
      setLocalSongStatuses(songStatuses);
    }
  }, [songStatuses]);

  // Get a random funny placeholder from our list
  const getRandomPlaceholder = () => {
    const funnyPlaceholders = [
      "Search for that song stuck in your head...",
      "Find the song the DJ pretends to know...",
      "Type here, music appears like magic!",
      "What earworm are we hunting today?",
      "Find your guilty pleasure song...",
      "The DJ awaits your musical wisdom...",
      "Discover music the DJ hasn't heard yet...",
      "Your song suggestion goes here...",
      "What should everyone dance to next?",
      "Your musical destiny awaits..."
    ];
    return funnyPlaceholders[Math.floor(Math.random() * funnyPlaceholders.length)];
  };

  // Fetch blocked genres for this session and set up real-time updates
  useEffect(() => {
    if (!sessionId) return;

    const fetchBlockedGenres = async () => {
      try {
        const { data, error } = await supabase
          .from('sessions')
          .select('blocked_genres')
          .eq('id', sessionId)
          .single();

        if (error) {
          console.error('Error fetching blocked genres:', error);
          return;
        }

        console.log('Initial blocked genres loaded:', data?.blocked_genres);
        setBlockedGenres(data?.blocked_genres || []);
      } catch (err) {
        console.error('Error in fetchBlockedGenres:', err);
      }
    };

    fetchBlockedGenres();

    // Set up real-time subscription for session blocked_genres changes
    const sessionChannel = supabase
      .channel(`session-genre-blocking-${sessionId}`)
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'sessions',
        filter: `id=eq.${sessionId}`
      }, (payload) => {
        console.log('Session updated, checking for blocked genres changes:', payload);

        // Type guard to ensure payload.new has the expected structure
        const newData = payload.new as { blocked_genres?: string[] } | null;

        if (newData && 'blocked_genres' in newData) {
          const newBlockedGenres = newData.blocked_genres || [];
          console.log('Blocked genres updated in real-time:', newBlockedGenres);
          setBlockedGenres(newBlockedGenres);

          // Show brief visual feedback that genre blocking was updated
          setGenreBlockingUpdated(true);
          setTimeout(() => setGenreBlockingUpdated(false), 3000);
        }
      })
      .subscribe();

    return () => {
      supabase.removeChannel(sessionChannel);
    };
  }, [sessionId]);

  // When the component mounts, fetch all the played songs for this session directly
  useEffect(() => {
    if (!sessionId) return;

    const fetchSongStatuses = async () => {
      try {
        // Query all song requests in this session to get their statuses
        const { data, error } = await supabase
          .from('song_requests')
          .select('apple_music_id, status, requester_name')
          .eq('session_id', sessionId);

        if (error) {
          console.error('Error fetching song statuses:', error);
          return;
        }

        // Create a map of apple_music_id -> status for all songs
        const statusMap: Record<string, string> = {};
        const playedIds = new Set<string>();
        const userRequested = new Set<string>();

        if (data) {
          data.forEach(item => {
            if (item && item.apple_music_id) {
              statusMap[item.apple_music_id] = item.status;

              // Add to played songs set if status is 'played'
              if (item.status === 'played') {
                playedIds.add(item.apple_music_id);
              }

              // Add to user requested set if requester is current user
              if (item.requester_name === requesterName) {
                userRequested.add(item.apple_music_id);
              }
            }
          });
        }

        console.log(`Loaded ${Object.keys(statusMap).length} songs with statuses, ${playedIds.size} played`);
        setLocalSongStatuses(statusMap);
        setPlayedSongs(playedIds);
        setUserRequestedSongs(userRequested);

      } catch (err) {
        console.error('Error in fetchSongStatuses:', err);
      }
    };

    fetchSongStatuses();

    // Subscribe to song request status updates
    const channel = supabase
      .channel(`song-status-updates-${sessionId}`)
      .on('postgres_changes', {
        event: '*', // Listen for all events (INSERT, UPDATE, DELETE)
        schema: 'public',
        table: 'song_requests',
        filter: `session_id=eq.${sessionId}`
      }, (payload) => {
        console.log('Song request changed, updating local statuses:', payload);

        // Type guard to ensure payload.new is a valid object with the required properties
        const newData = payload.new as SongRequestPayload | null;

        if (newData && typeof newData === 'object' && 'apple_music_id' in newData && newData.apple_music_id) {
          // Update local song statuses
          setLocalSongStatuses(prev => ({
            ...prev,
            [newData.apple_music_id]: newData.status
          }));

          // If it's a played status, also update the played songs set
          if (newData.status === 'played') {
            setPlayedSongs(prev => new Set([...prev, newData.apple_music_id]));
          }

          // If it's a request by the current user, update the user requested set
          if (newData.requester_name === requesterName) {
            setUserRequestedSongs(prev => new Set([...prev, newData.apple_music_id]));
          }
        }
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [sessionId, requesterName]);

  // Handle search
  useEffect(() => {
    if (!debouncedSearchQuery || debouncedSearchQuery.length < 2) {
      setResults([]);
      return;
    }

    if (!isAppleMusicAuthorized) {
      setError('Apple Music connection unavailable');
      return;
    }

    // Check if MusicKit is initialized
    if (!isMusicKitInitialized) {
      console.log('MusicKit is not yet initialized, but will attempt search anyway');
      // We'll continue with the search and rely on the retry logic
    }

    const performSearch = async () => {
      setIsSearching(true);
      setError(null);

      try {
        // Add retry logic for MusicKit initialization issues
        let retryCount = 0;
        const maxRetries = 3;
        let searchResults: SongSearchResult[] = [];

        while (retryCount < maxRetries) {
          try {
            // Use the sessionId to search via the secure proxy endpoint
            searchResults = await searchSongs(debouncedSearchQuery, sessionId);
            break; // If successful, exit the retry loop
          } catch (searchError) {
            console.error(`Search attempt ${retryCount + 1} failed:`, searchError);
            retryCount++;

            if (retryCount >= maxRetries) {
              throw searchError; // Re-throw if we've exhausted retries
            }

            // Wait before retrying with exponential backoff
            const backoffTime = 500 * Math.pow(2, retryCount);
            console.log(`Retrying in ${backoffTime}ms...`);
            await new Promise(resolve => setTimeout(resolve, backoffTime));
          }
        }

        setResults(searchResults);
        console.log("Search results:", searchResults);
        console.log("Current song statuses:", localSongStatuses);
      } catch (error) {
        console.error('Search error after retries:', error);
        setError('Failed to search Apple Music');
      } finally {
        setIsSearching(false);
      }
    };

    performSearch();
  }, [debouncedSearchQuery, isAppleMusicAuthorized, localSongStatuses]);

  // Clear search function
  const clearSearch = () => {
    setSearchQuery('');
    setResults([]);
    // Focus the input after clearing
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Check if a song has already been requested by current user
  const getSongStatus = (songId: string): RequestStatus | null => {
    const status = localSongStatuses[songId];
    console.log(`Getting status for song ${songId}: ${status}`);
    // Ensure the returned value is of type RequestStatus or null
    if (status && (status === "pending" || status === "approved" || status === "auto-approved" || status === "declined" || status === "played")) {
      return status as RequestStatus;
    }
    return null;
  };

  // Check if a song has been played in any request
  const isSongPlayed = (songId: string): boolean => {
    return playedSongs.has(songId) || getSongStatus(songId) === "played";
  };

  // Check if the current user has already requested this song
  const isRequestedByCurrentUser = (songId: string): boolean => {
    return userRequestedSongs.has(songId);
  };

  // Check if a song's genre is blocked
  const isGenreBlocked = (song: SongSearchResult): boolean => {
    if (!song.genre || blockedGenres.length === 0) return false;
    return blockedGenres.some(blockedGenre =>
      blockedGenre.toLowerCase() === song.genre?.toLowerCase()
    );
  };

  // Get the appropriate badge for a song based on its status
  const getSongStatusBadge = (songId: string) => {
    const status = getSongStatus(songId);

    if (isSongPlayed(songId)) {
      return (
        <Badge variant="outline" className="text-xs flex items-center gap-1 bg-purple-900/30 text-purple-300 border-purple-700/30">
          <Music className="h-3 w-3" /> Played
        </Badge>
      );
    }

    if (status === "declined") {
      return (
        <Badge variant="outline" className="text-xs bg-red-900/30 text-red-300 border-red-700/30">
          Declined by DJ
        </Badge>
      );
    }

    if (isRequestedByCurrentUser(songId) && status === "pending") {
      return (
        <Badge variant="outline" className="text-xs bg-yellow-900/30 text-yellow-300 border-yellow-700/30">
          Your Request Pending
        </Badge>
      );
    }

    if (isRequestedByCurrentUser(songId) && (status === "approved" || status === "auto-approved")) {
      return (
        <Badge variant="outline" className="text-xs bg-green-900/30 text-green-300 border-green-700/30">
          Your Request Approved
        </Badge>
      );
    }

    if (isRequestedByCurrentUser(songId)) {
      return (
        <Badge variant="outline" className="text-xs bg-red-900/30 text-red-300 border-red-700/30">
          Your Request Declined
        </Badge>
      );
    }

    // For songs with status but not requested by current user
    if (status === "pending") {
      return (
        <Badge variant="outline" className="text-xs bg-blue-900/30 text-blue-300 border-blue-700/30">
          Others Requested
        </Badge>
      );
    }

    if (status === "approved" || status === "auto-approved") {
      return (
        <Badge variant="outline" className="text-xs bg-green-900/30 text-green-300 border-green-700/30">
          Others' Request Approved
        </Badge>
      );
    }

    return null;
  };


  return (
    <div className="bg-black/60 backdrop-blur-md border border-purple-500/20 rounded-xl px-4 py-3 shadow-md mb-4 w-full">
      {!selectedSong ? (

        <div>
          <div className="mb-4">
            <label htmlFor="songSearch" className="block text-base font-medium text-gray-300 mb-2 flex items-center gap-2">
              <Music className="h-5 w-5 text-cyan-400" />
              Search for a track, artist, or vibe
            </label>
            <div className="relative">
              <Input
                ref={inputRef}
                id="songSearch"
                placeholder={isFreeTier && remainingSessionRequests === 0 ?
                  "Free tier session limit reached" :
                  getRandomPlaceholder()}
                className={cn(
                  "text-base bg-black/40 text-white border border-cyan-400/20 rounded-lg px-4 py-3 w-full",
                  "pl-10 pr-12 h-12 shadow-inner",
                  "focus:ring-2 focus:ring-cyan-500/30 focus:border-cyan-500/50",
                  isFreeTier && remainingSessionRequests === 0 ? "opacity-50" : ""
                )}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                disabled={!isAppleMusicAuthorized || (isFreeTier && remainingSessionRequests === 0)}
              />
              <div className="absolute left-3 top-1/2 -translate-y-1/2 pointer-events-none">
                {isSearching ? (
                  <div className="animate-spin h-5 w-5 border-t-2 border-b-2 border-cyan-400 rounded-full" />
                ) : (
                  <Search className="h-5 w-5 text-cyan-400" />
                )}
              </div>

              {/* X button to clear search input */}
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="icon"
                  type="button"
                  onClick={clearSearch}
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 rounded-full hover:bg-gray-800/50"
                >
                  <X className="h-4 w-4 text-gray-400 hover:text-white transition-colors" />
                  <span className="sr-only">Clear search</span>
                </Button>
              )}
            </div>
          </div>

          {error && (
            <div className="text-red-400 text-sm mb-4 p-3 bg-red-900/30 border border-red-900/40 rounded-md shadow-md">
              Error: {error}
            </div>
          )}

          {/* Genre blocking update notification */}
          {genreBlockingUpdated && (
            <div className="text-cyan-400 text-sm mb-4 p-3 bg-cyan-900/30 border border-cyan-500/40 rounded-md shadow-md animate-pulse">
              🎵 Genre blocking settings updated by DJ
            </div>
          )}

          <div className="space-y-2.5 max-h-[60vh] overflow-y-auto rounded-md pb-1 pr-1">
            {results.map((song) => {
              const status = getSongStatus(song.id);
              const statusBadge = getSongStatusBadge(song.id);
              const genreBlocked = isGenreBlocked(song);
              // Update the isRequestable logic to include genre blocking
              const isRequestable = !isRequestedByCurrentUser(song.id) && status !== "played" && status !== "declined" && !genreBlocked;

              return (
                <div
                  key={song.id}
                  className={`flex items-center py-4 px-3 rounded-xl ${isRequestedByCurrentUser(song.id) || status === "declined" || genreBlocked ?
                    'bg-gray-800/50 border border-gray-700/50' :
                    'hover:bg-white/5 hover:bg-gradient-to-r hover:from-purple-900/20 hover:to-cyan-900/20 hover:border hover:border-purple-500/20'}
                    transition-all duration-200 mb-2`}
                >

                  <div className="w-14 h-14 bg-gray-800 rounded-lg flex-shrink-0 mr-3 flex items-center justify-center overflow-hidden shadow-md">
                    {song.artwork ? (
                      <img src={song.artwork} alt={song.title} className="w-full h-full object-cover" />
                    ) : (
                      <Music className="h-6 w-6 text-gray-400" />
                    )}
                  </div>

                  <div className="flex-1 min-w-0">
                    <p className="text-base font-medium text-white truncate">{song.title}</p>
                    <p className="text-sm text-gray-400 truncate">{song.artist}</p>
                    {song.genre && (
                      <p className="text-xs text-purple-400 truncate mt-0.5">{song.genre}</p>
                    )}
                  </div>

                  <div className="ml-4 flex items-center">
                    {/* Show genre blocked badge first, then status badge */}
                    {genreBlocked && (
                      <div className="mr-3">
                        <Badge variant="destructive" className="text-xs flex items-center gap-1 bg-red-600/20 text-red-400 border-red-500/30">
                          <Shield className="h-3 w-3" />
                          Genre Blocked
                        </Badge>
                      </div>
                    )}
                    {statusBadge && !genreBlocked && (
                      <div className="mr-3">{statusBadge}</div>
                    )}
                    <Button
                      size="sm"
                      variant={isRequestable ? "default" : "ghost"}
                      className={isRequestable ?
                        "bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-500 hover:to-cyan-500 hover:scale-105 text-white shadow-md h-10 px-5 rounded-full transition-all duration-200" :
                        "text-purple-400 hover:text-purple-300 hover:bg-purple-900/40 h-10 px-4 rounded-md"}
                      onClick={() => onSongSelect(song)}
                      disabled={remainingQuota <= 0 || onCooldown || !requesterName || isRequestedByCurrentUser(song.id) || isSongPlayed(song.id) || status === "declined" || genreBlocked}
                      title={genreBlocked ? `${song.genre} genre is blocked by the DJ` : undefined}
                    >
                      {isRequestedByCurrentUser(song.id) ? "Requested" :
                       isSongPlayed(song.id) ? "Played" :
                       status === "declined" ? "Not Available" : "Request"}
                    </Button>
                  </div>
                </div>
              );
            })}

            {/* Free tier limit reached message - show based on either remainingSessionRequests or localStorage */}
            {/* Only show if this is actually a free tier session AND either the remaining requests is 0 or the flag is set */}
            {(isFreeTier && (remainingSessionRequests === 0 || localStorage.getItem(`session_${sessionId}_free_limit_notified`))) && (
              <div className="text-center py-6 bg-amber-950/30 border border-amber-900/30 rounded-md my-4">
                <div className="text-amber-300 font-medium mb-1">Free Tier Session Limit Reached</div>
                <p className="text-amber-200/80 text-sm px-4">
                  This session has reached its limit of 3 total requests. The DJ would need to upgrade to a paid plan for more requests.
                </p>
                <p className="text-amber-200/60 text-xs px-4 mt-2">
                  The page will automatically refresh if the DJ upgrades to a paid plan.
                </p>
              </div>
            )}

            {/* Paid tier limit reached message */}
            {!isFreeTier && localStorage.getItem(`session_${sessionId}_paid_limit_notified`) && (
              <div className="text-center py-6 bg-red-950/30 border border-red-900/30 rounded-md my-4">
                <div className="text-red-300 font-medium mb-1">Session Request Limit Reached</div>
                <p className="text-red-200/80 text-sm px-4">
                  This session has reached its limit of 100 total requests. No additional requests can be submitted.
                </p>
              </div>
            )}

            {/* Paid tier approaching limit warning */}
            {!isFreeTier && localStorage.getItem(`session_${sessionId}_paid_limit_warning`) &&
             !localStorage.getItem(`session_${sessionId}_paid_limit_notified`) && (
              <div className="text-center py-4 bg-amber-950/20 border border-amber-900/20 rounded-md my-3">
                <div className="text-amber-300 text-sm px-4">
                  This session is approaching its limit of 100 requests.
                </div>
              </div>
            )}

            {debouncedSearchQuery && results.length === 0 && !isSearching && !isFreeTier && (
              <div className="text-center py-6 text-gray-400">
                No songs found. Try a different search term.
              </div>
            )}

            {isSearching && (
              <div className="text-center py-6 text-gray-400 flex items-center justify-center">
                <div className="animate-spin mr-3 h-6 w-6 border-t-2 border-b-2 border-purple-400 rounded-full"></div>
                Searching...
              </div>
            )}
          </div>
        </div>
      ) : (

        <div>
          <div className="mb-4">
            <label className="block text-base font-medium text-gray-300 mb-2">
              Selected Song
            </label>
            <SelectedSong song={selectedSong} onClear={onSongClear} />
          </div>

          <Button
            className="w-full mt-3 bg-gradient-to-r from-purple-600 to-cyan-500 hover:from-purple-700 hover:to-cyan-600 h-12 text-base font-medium shadow-md rounded-md"
            disabled={isSubmitting || !requesterName || remainingQuota <= 0 || onCooldown || isSongPlayed(selectedSong.id) || isRequestedByCurrentUser(selectedSong.id) || getSongStatus(selectedSong.id) === "declined"}
            onClick={onRequestSubmit}
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin mr-2 h-5 w-5 border-t-2 border-b-2 border-white rounded-full"></div>
                Submitting...
              </>
            ) : isSongPlayed(selectedSong.id) ? (
              'Already Played'
            ) : isRequestedByCurrentUser(selectedSong.id) ? (
              'Already Requested'
            ) : getSongStatus(selectedSong.id) === "declined" ? (
              'Not Available'
            ) : onCooldown ? (
              `On Cooldown (${cooldownTimeLeft}s)`
            ) : remainingQuota <= 0 ? (
              'No Requests Left'
            ) : !requesterName ? (
              'Enter Your Name First'
            ) : (
              'Submit Request'
            )}
          </Button>
        </div>
      )}

      {/* Debug panel for development */}
      <DebugPanel
        sessionId={sessionId}
        isSessionActive={isSessionActive}
        sessionName={sessionName}
        isAppleMusicAuthorized={isAppleMusicAuthorized}
        debugInfo={debugInfo}
      />
    </div>
  );
}
