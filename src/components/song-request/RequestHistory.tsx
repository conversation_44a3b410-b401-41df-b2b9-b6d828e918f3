import { useState, useEffect } from 'react';
import { Info, ChevronDown, ChevronUp } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { RequestStatusIndicator } from './RequestStatusIndicator';
import { motion, AnimatePresence } from 'framer-motion';
import { SongRequestPayload } from '@/utils/realtimeService';
import { ScrollArea } from "@/components/ui/scroll-area";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { supabase } from "@/integrations/supabase/client";
import { SongRequest } from "@/lib/types";
import { useRequestStatusUpdates } from "@/hooks/useRequestStatusUpdates";

type RequestHistoryProps = {
  sessionId: string | null;
  requesterName: string;
  userRequests?: SongRequestPayload[]; // Add this prop to receive requests from parent
};

export function RequestHistory({ sessionId, requesterName, userRequests = [] }: RequestHistoryProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});
  const [localUserRequests, setLocalUserRequests] = useState<SongRequest[]>(userRequests as SongRequest[]);
  const isMobile = useIsMobile();

  // Toggle expansion state for a specific group
  const toggleGroupExpansion = (key: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Use the consolidated useRequestStatusUpdates hook to handle status updates
  useRequestStatusUpdates(
    sessionId,
    requesterName,
    null, // We don't need to track a specific request ID here
    // Status change callback not needed for this component
  );

  // Initial loading of user requests
  useEffect(() => {
    if (!sessionId || !requesterName) return;

    setIsLoading(true);

    const fetchUserRequests = async () => {
      try {
        const { data, error } = await supabase
          .from('song_requests')
          .select('*')
          .eq('session_id', sessionId)
          .eq('requester_name', requesterName)
          .order('created_at', { ascending: false });

        if (error) throw error;

        if (data) {
          setLocalUserRequests(data as unknown as SongRequest[]);
        }
      } catch (error) {
        console.error("Error fetching user requests:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserRequests();

    // No need for additional channel setup since we're using the consolidated hook

    return () => {
      // No cleanup needed here as the hook handles its own cleanup
    };
  }, [sessionId, requesterName]);

  // Update local requests if props change
  useEffect(() => {
    if (userRequests && userRequests.length > 0) {
      setLocalUserRequests(userRequests as unknown as SongRequest[]);
    }
  }, [userRequests]);

  // Group requests by song title and artist to count duplicates
  const groupedRequests = localUserRequests.reduce((acc, request) => {
    const key = `${request.song_title}:${request.artist_name}`.toLowerCase();
    if (!acc[key]) {
      acc[key] = {
        requests: [],
        count: 0,
        mostRecent: request,
        key
      };
    }
    acc[key].requests.push(request);
    acc[key].count += 1;

    // Update most recent request if this one is newer
    if (new Date(request.created_at || '') > new Date(acc[key].mostRecent.created_at || '')) {
      acc[key].mostRecent = request;
    }

    return acc;
  }, {} as Record<string, { requests: SongRequest[], count: number, mostRecent: SongRequest, key: string }>);

  // Convert to array for rendering
  const groupedRequestsArray = Object.values(groupedRequests);

  if (!requesterName) {
    return null;
  }

  return (
    <Card className={`bg-black/60 backdrop-blur-md border border-purple-500/20 rounded-xl px-4 py-3 shadow-md mt-6 ${isMobile ? 'mx-[-1rem] rounded-none w-screen max-w-[100vw]' : ''}`}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <span className="bg-gradient-to-r from-purple-500 to-cyan-500 bg-clip-text text-transparent">Your Requests</span>
        </CardTitle>
        <CardDescription>
          Track the status of your song requests
        </CardDescription>
      </CardHeader>
      <CardContent className={isMobile ? 'px-3' : ''}>
        {isLoading ? (
          <div className="animate-pulse space-y-2">
            <div className="h-12 bg-gray-800/50 rounded-xl"></div>
            <div className="h-12 bg-gray-800/50 rounded-xl"></div>
          </div>
        ) : groupedRequestsArray.length > 0 ? (
          <div className="space-y-3">
            <AnimatePresence>
              {groupedRequestsArray.map((group) => (
                <motion.div
                  key={group.key}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.3 }}
                  className="mb-3"
                >
                  <div className="mb-1">
                    <RequestStatusIndicator
                      sessionId={sessionId || ''}
                      requestId={group.mostRecent.id}
                      status={group.mostRecent.status}
                      songTitle={group.mostRecent.song_title}
                      artistName={group.mostRecent.artist_name}
                      artworkUrl={group.mostRecent.album_artwork}
                      duplicateCount={group.count - 1}
                    />
                  </div>

                  {/* Collapsible list of additional requests */}
                  {group.count > 1 && (
                    <Collapsible
                      className="ml-4 pl-2 border-l border-gray-700"
                      open={!!expandedGroups[group.key]}
                      onOpenChange={() => toggleGroupExpansion(group.key)}
                    >
                      <div className="flex items-center gap-1.5 text-xs text-gray-400">
                        <CollapsibleTrigger asChild>
                          <Button variant="ghost" size="sm" className="p-0 h-5 flex items-center gap-1 text-gray-400 hover:text-gray-300">
                            {expandedGroups[group.key] ?
                              <ChevronUp className="h-3 w-3" /> :
                              <ChevronDown className="h-3 w-3" />
                            }
                            <span>{expandedGroups[group.key] ? "Hide" : "Show"} {group.count - 1} similar {(group.count - 1) === 1 ? "request" : "requests"}</span>
                          </Button>
                        </CollapsibleTrigger>
                      </div>

                      <CollapsibleContent>
                        <ScrollArea className={`mt-1 ${group.count > 3 ? "max-h-32" : ""}`}>
                          <div className="space-y-1 pr-2">
                            {group.requests
                              .filter(req => req.id !== group.mostRecent.id) // Skip the most recent which is already displayed
                              .map(request => (
                                <div key={request.id} className="text-xs">
                                  <RequestStatusIndicator
                                    sessionId={sessionId || ''}
                                    requestId={request.id}
                                    status={request.status}
                                    songTitle={request.song_title}
                                    artistName={request.artist_name}
                                    artworkUrl={request.album_artwork}
                                    isSecondary
                                  />
                                </div>
                              ))
                            }
                          </div>
                        </ScrollArea>
                      </CollapsibleContent>
                    </Collapsible>
                  )}
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
            className="flex flex-col items-center justify-center py-6 px-4 text-center"
          >
            <div className="w-16 h-16 mb-3 rounded-full bg-purple-900/20 flex items-center justify-center">
              <Info className="h-6 w-6 text-purple-400" />
            </div>
            <h4 className="text-white font-medium mb-1">No requests yet</h4>
            <p className="text-sm text-gray-400 max-w-xs">
              Find a track you love and make your first request!
            </p>
          </motion.div>
        )}
      </CardContent>
    </Card>
  );
}
