
import { useState } from "react";
import { useFileUpload } from "@/hooks/useFileUpload";
import { useToast } from "@/hooks/use-toast";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Upload } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { DJProfile } from "@/lib/types";

interface ProfilePictureProps {
  userId: string;
  profile: DJProfile | null;
  setProfile: React.Dispatch<React.SetStateAction<DJProfile | null>>;
}

const ProfilePicture = ({ userId, profile, setProfile }: ProfilePictureProps) => {
  const { uploadProfileImage, isUploading } = useFileUpload();
  const { toast } = useToast();

  const handleProfileImageChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file size and type before uploading
    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please select an image under 5MB",
        variant: "destructive"
      });
      return;
    }

    // Check file type
    const allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please select an image file (JPEG, PNG, or GIF)",
        variant: "destructive"
      });
      return;
    }

    // Check for specific allowed image types
    if (!allowedMimeTypes.includes(file.type)) {
      toast({
        title: "Unsupported image format",
        description: "Only JPEG, PNG, and GIF formats are supported",
        variant: "destructive"
      });
      return;
    }

    try {
      const imageUrl = await uploadProfileImage(userId, file);

      if (imageUrl) {
        // Update profile with new image URL
        const { error } = await supabase
          .from("dj_profiles")
          .update({ profile_image_url: imageUrl })
          .eq("id", userId);

        if (error) throw error;

        // Update local state
        setProfile(prev => prev ? {...prev, profile_image_url: imageUrl} : null);

        toast({
          title: "Profile picture updated",
          description: "Your profile picture has been updated successfully.",
        });
      }
    } catch (error: any) {
      console.error("Error updating profile image:", error.message);
      toast({
        title: "Error updating profile image",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="bg-black/50 border-purple-500/20 backdrop-blur-md">
      <CardHeader>
        <CardTitle className="text-white">Profile Picture</CardTitle>
        <CardDescription>
          Upload or change your profile image
        </CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col items-center gap-4">
        <div className="relative mb-6">
          <Avatar className="h-24 w-24 border-2 border-purple-500/30">
            {profile?.profile_image_url ? (
              <AvatarImage src={profile.profile_image_url} alt="Profile" />
            ) : (
              <AvatarFallback className="bg-gradient-to-br from-purple-500/20 to-cyan-500/20 text-white text-xl">
                {profile?.display_name ? profile.display_name.charAt(0).toUpperCase() : "DJ"}
              </AvatarFallback>
            )}
          </Avatar>

          <Button
            variant="outline"
            size="sm"
            className="absolute bottom-[-32px] left-1/2 transform -translate-x-1/2 border-purple-500/50 text-purple-400 bg-black/80 hover:bg-black/60 hover:text-purple-300"
            disabled={isUploading}
          >
            {isUploading ? (
              <>Uploading...</>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Change Picture
              </>
            )}
            <input
              type="file"
              id="profile-image-upload"
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              accept="image/*"
              onChange={handleProfileImageChange}
              disabled={isUploading}
            />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfilePicture;
