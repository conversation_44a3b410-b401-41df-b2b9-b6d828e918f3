# PlayBeg Convex Migration - Branching Strategy

## Overview

This document outlines the Git workflow and branching strategy for the PlayBeg Supabase to Convex migration project. The strategy is designed to ensure safe, organized development while maintaining the ability to rollback if needed.

## Branch Structure

### Main Branches

#### `main`
- **Purpose**: Production-ready Supabase version
- **Protection**: Protected branch, requires PR reviews
- **Deployment**: Current production environment
- **Status**: Stable, existing PlayBeg application

#### `feature/convex-migration`
- **Purpose**: Primary migration development branch
- **Created**: ✅ Active migration branch
- **Contains**: All Convex migration work
- **Merges to**: `staging` → `main` (when migration complete)

### Supporting Branches

#### `feature/convex-migration-*`
- **Purpose**: Specific feature branches for migration components
- **Naming Convention**: 
  - `feature/convex-migration-schema`
  - `feature/convex-migration-auth`
  - `feature/convex-migration-realtime`
  - `feature/convex-migration-payments`
- **Base Branch**: `feature/convex-migration`
- **Merge Target**: `feature/convex-migration`

#### `staging`
- **Purpose**: Pre-production testing environment
- **Source**: Merges from `feature/convex-migration`
- **Deployment**: Staging environment
- **Testing**: Full integration testing

#### `hotfix/convex-migration-*`
- **Purpose**: Critical fixes during migration
- **Base Branch**: `feature/convex-migration`
- **Merge Target**: `feature/convex-migration`

## Workflow Process

### Phase-Based Development

#### Phase 1: Foundation Setup (Current)
```bash
# Current branch
feature/convex-migration

# Work completed:
✅ Project setup and environment configuration
✅ Convex project initialization
✅ Environment management setup
🔄 Version control and branching strategy (in progress)
```

#### Phase 2-4: Feature Development
```bash
# Create feature branches for each major component
git checkout feature/convex-migration
git checkout -b feature/convex-migration-schema
git checkout -b feature/convex-migration-auth
git checkout -b feature/convex-migration-realtime
```

#### Phase 5: Integration and Deployment
```bash
# Merge to staging for testing
git checkout staging
git merge feature/convex-migration

# After successful testing, merge to main
git checkout main
git merge staging
```

## Commit Message Convention

### Format
```
<type>(<scope>): <description>

<body>

<footer>
```

### Types
- **feat**: New feature implementation
- **fix**: Bug fixes
- **docs**: Documentation updates
- **refactor**: Code refactoring
- **test**: Test additions or modifications
- **chore**: Maintenance tasks
- **migration**: Migration-specific changes

### Scopes
- **convex**: Convex backend changes
- **schema**: Database schema modifications
- **auth**: Authentication system changes
- **realtime**: Real-time functionality
- **payments**: Payment/subscription system
- **frontend**: Frontend integration changes
- **deploy**: Deployment and infrastructure

### Examples
```bash
feat(convex): implement song request schema and mutations
fix(auth): resolve token refresh issue in Convex Auth
docs(migration): update deployment guide for staging environment
migration(schema): migrate user profiles from Supabase to Convex
```

## Branch Protection Rules

### `main` Branch
- ✅ Require pull request reviews (2 reviewers)
- ✅ Require status checks to pass
- ✅ Require branches to be up to date
- ✅ Restrict pushes to admins only
- ✅ Require linear history

### `feature/convex-migration` Branch
- ✅ Require pull request reviews (1 reviewer)
- ✅ Require status checks to pass
- ✅ Allow force pushes (for development flexibility)

### `staging` Branch
- ✅ Require pull request reviews (1 reviewer)
- ✅ Require status checks to pass
- ✅ Automatic deployment to staging environment

## CI/CD Integration

### GitHub Actions Workflows

#### Development Workflow
```yaml
# .github/workflows/convex-migration-dev.yml
name: Convex Migration Development
on:
  push:
    branches: [ feature/convex-migration* ]
  pull_request:
    branches: [ feature/convex-migration ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: cd playbeg-convex && npm ci
      - name: Run tests
        run: cd playbeg-convex && npm test
      - name: Lint code
        run: cd playbeg-convex && npm run lint
```

#### Staging Deployment
```yaml
# .github/workflows/convex-staging.yml
name: Deploy to Staging
on:
  push:
    branches: [ staging ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to Convex Staging
        run: cd playbeg-convex && npm run deploy:staging
```

## Migration-Specific Workflow

### 1. Feature Development
```bash
# Start new migration feature
git checkout feature/convex-migration
git pull origin feature/convex-migration
git checkout -b feature/convex-migration-[component]

# Develop and commit
git add .
git commit -m "feat(convex): implement [component]"

# Push and create PR
git push origin feature/convex-migration-[component]
# Create PR to feature/convex-migration
```

### 2. Integration Testing
```bash
# Merge completed features
git checkout feature/convex-migration
git merge feature/convex-migration-[component]

# Test integration
cd playbeg-convex
npm run dev
npm test
```

### 3. Staging Deployment
```bash
# Deploy to staging for testing
git checkout staging
git merge feature/convex-migration
git push origin staging
# Automatic deployment via CI/CD
```

### 4. Production Deployment
```bash
# After successful staging validation
git checkout main
git merge staging
git push origin main
# Manual production deployment
```

## Rollback Strategy

### Emergency Rollback
```bash
# If migration fails, rollback to previous main
git checkout main
git revert [migration-commit-hash]
git push origin main
```

### Partial Rollback
```bash
# Rollback specific features
git checkout feature/convex-migration
git revert [feature-commit-hash]
git push origin feature/convex-migration
```

## Team Collaboration

### Code Review Process
1. **Feature PRs**: Require 1 reviewer (technical validation)
2. **Integration PRs**: Require 2 reviewers (architecture + technical)
3. **Production PRs**: Require all team leads approval

### Communication
- **Daily standups**: Progress updates on migration tasks
- **Weekly reviews**: Architecture and integration discussions
- **Phase gates**: Formal review before moving to next phase

## Current Status

### ✅ Completed
- Created `feature/convex-migration` branch
- Committed initial migration setup
- Established branching strategy documentation
- Configured Git user settings

### 🔄 In Progress
- Setting up CI/CD workflows
- Configuring branch protection rules

### 📋 Next Steps
1. Push migration branch to remote
2. Set up GitHub Actions workflows
3. Configure branch protection rules
4. Create feature branches for Phase 2 tasks

## Repository Structure

```
PlayBeg/
├── src/                          # Original Supabase application
├── playbeg-convex/              # New Convex application
├── PLAYBEG_PROJECT_ANALYSIS.md  # Project analysis
├── SUPABASE_TO_CONVEX_MIGRATION_PLAN.md
├── MIGRATION_TASK_BREAKDOWN.md
├── TASK_IMPLEMENTATION_GUIDE.md
└── CONVEX_MIGRATION_BRANCHING_STRATEGY.md
```

This branching strategy ensures safe, organized development while maintaining the ability to rollback and continue parallel development on both Supabase and Convex versions during the migration period.
