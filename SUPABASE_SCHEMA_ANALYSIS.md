# PlayBeg Supabase Database Schema Analysis

## Overview

This document provides a comprehensive analysis of the current PlayBeg Supabase PostgreSQL database schema. This analysis serves as the foundation for designing the equivalent Convex document-based schema during the migration process.

## Database Structure Summary

### Core Tables (9 tables)
1. **dj_profiles** - DJ user profiles and settings
2. **sessions** - DJ event sessions with configuration
3. **song_requests** - Individual song requests from audience
4. **dj_subscriptions** - Subscription management
5. **subscription_plans** - Available pricing tiers
6. **apple_music_tokens** - Secure token storage
7. **blog_posts** - Content management system
8. **playlists** - Apple Music playlist management
9. **recently_played_tracks** - Track history
10. **songs** - Song catalog

### Database Functions (4 functions)
- `get_session_users` - Session user management
- `get_user_subscription_status` - Subscription status checking
- `insert_playlist_with_apple_id` - Playlist creation
- `remove_session_user` - User removal

### Enums (1 enum)
- `request_status` - Song request status values

## Detailed Table Analysis

### 1. dj_profiles
**Purpose**: DJ user profiles and onboarding status

**Schema**:
```sql
CREATE TABLE dj_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  display_name TEXT,
  completed_onboarding BOOLEAN DEFAULT FALSE,
  profile_image_url TEXT,
  last_login_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Key Characteristics**:
- **Primary Key**: UUID (id)
- **Foreign Keys**: None (linked via Supabase Auth user_id)
- **Indexes**: `idx_dj_profiles_last_login_at`
- **Constraints**: None
- **Nullable Fields**: display_name, profile_image_url, last_login_at

**Migration Considerations**:
- Need to establish relationship with Convex Auth users
- Profile images will need file storage migration
- Last login tracking for analytics

### 2. sessions
**Purpose**: DJ event sessions with comprehensive configuration

**Schema**:
```sql
CREATE TABLE sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  dj_id UUID NOT NULL REFERENCES dj_profiles(id),
  name TEXT NOT NULL,
  active BOOLEAN DEFAULT FALSE,
  accept_requests BOOLEAN DEFAULT TRUE,
  auto_approval BOOLEAN DEFAULT FALSE,
  blocked_genres TEXT[],
  duration_minutes INTEGER,
  max_requests_per_timeframe INTEGER,
  max_requests_per_user INTEGER,
  enable_ip_limiting BOOLEAN DEFAULT FALSE,
  allow_quota_requests BOOLEAN DEFAULT TRUE,
  sponsor_header TEXT,
  sponsor_logo_url TEXT,
  sponsor_message TEXT,
  sponsor_url TEXT,
  timeframe_minutes INTEGER DEFAULT 60,
  -- Wedding Mode Fields
  wedding_mode_enabled BOOLEAN DEFAULT FALSE,
  wedding_couple_name_1 TEXT,
  wedding_couple_name_2 TEXT,
  wedding_date TEXT,
  wedding_hashtag TEXT,
  wedding_template TEXT DEFAULT 'classic-elegance',
  wedding_primary_color TEXT DEFAULT '#D4AF37',
  wedding_secondary_color TEXT DEFAULT '#F5F5DC',
  wedding_custom_message TEXT,
  wedding_show_icons BOOLEAN DEFAULT TRUE,
  wedding_border_style TEXT DEFAULT 'elegant-frame',
  wedding_background_pattern TEXT DEFAULT 'none',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Key Characteristics**:
- **Primary Key**: UUID (id)
- **Foreign Keys**: dj_id → dj_profiles(id)
- **Indexes**: `idx_sessions_wedding_mode`
- **Constraints**: 
  - Wedding template validation
  - Border style validation
  - Background pattern validation
- **Arrays**: blocked_genres (TEXT[])

**Migration Considerations**:
- Complex wedding mode configuration needs careful mapping
- Array fields (blocked_genres) map well to Convex arrays
- Sponsor logo URLs need file storage migration
- Multiple boolean configuration flags

### 3. song_requests
**Purpose**: Individual song requests from audience members

**Schema**:
```sql
CREATE TABLE song_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID NOT NULL REFERENCES sessions(id),
  song_title TEXT NOT NULL,
  artist_name TEXT NOT NULL,
  requester_name TEXT NOT NULL,
  requester_ip TEXT,
  apple_music_id TEXT,
  album_artwork TEXT,
  playlist_id TEXT,
  apple_music_playlist_id TEXT,
  genre TEXT,
  status request_status DEFAULT 'pending',
  added_to_playlist BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Key Characteristics**:
- **Primary Key**: UUID (id)
- **Foreign Keys**: session_id → sessions(id)
- **Indexes**: None explicitly defined (needs optimization)
- **Enums**: status (request_status)
- **Nullable Fields**: requester_ip, apple_music_id, album_artwork, playlist_id, apple_music_playlist_id, genre

**Migration Considerations**:
- High-volume table requiring efficient indexing
- Real-time updates critical for user experience
- Status enum maps to Convex union types
- Apple Music integration data

### 4. dj_subscriptions
**Purpose**: Subscription management and billing

**Schema**:
```sql
CREATE TABLE dj_subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  dj_id UUID NOT NULL REFERENCES dj_profiles(id) UNIQUE,
  plan_id UUID REFERENCES subscription_plans(id),
  status TEXT DEFAULT 'active',
  current_period_start TIMESTAMPTZ,
  current_period_end TIMESTAMPTZ,
  cancel_at_period_end BOOLEAN DEFAULT FALSE,
  stripe_customer_id TEXT,
  stripe_subscription_id TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Key Characteristics**:
- **Primary Key**: UUID (id)
- **Foreign Keys**: 
  - dj_id → dj_profiles(id) (one-to-one)
  - plan_id → subscription_plans(id)
- **Unique Constraints**: dj_id (one subscription per DJ)
- **Stripe Integration**: customer_id, subscription_id

**Migration Considerations**:
- Critical for billing and feature access
- Stripe webhook integration required
- One-to-one relationship with DJ profiles
- Period tracking for subscription management

### 5. subscription_plans
**Purpose**: Available pricing tiers and features

**Schema**:
```sql
CREATE TABLE subscription_plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  price_amount INTEGER NOT NULL,
  price_currency TEXT DEFAULT 'usd',
  duration_hours INTEGER NOT NULL,
  stripe_price_id TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Key Characteristics**:
- **Primary Key**: UUID (id)
- **Foreign Keys**: None
- **Stripe Integration**: stripe_price_id
- **Pricing**: price_amount (cents), duration_hours

**Migration Considerations**:
- Reference data table
- Stripe price ID integration
- Duration-based pricing model

### 6. apple_music_tokens
**Purpose**: Secure Apple Music API token storage

**Schema**:
```sql
CREATE TABLE apple_music_tokens (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  apple_music_token TEXT NOT NULL,
  expires_at TIMESTAMPTZ NOT NULL,
  is_valid BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Key Characteristics**:
- **Primary Key**: UUID (id)
- **Foreign Keys**: user_id (references auth.users)
- **Security**: Encrypted token storage
- **Expiration**: expires_at tracking

**Migration Considerations**:
- Sensitive data requiring secure storage
- Token refresh logic needed
- User relationship via auth system

### 7. blog_posts
**Purpose**: Content management system for blog

**Schema**:
```sql
CREATE TABLE blog_posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  published_date DATE NOT NULL,
  author TEXT NOT NULL,
  category TEXT NOT NULL,
  excerpt TEXT NOT NULL,
  cover_image_url TEXT NOT NULL,
  cover_image_alt TEXT,
  tags TEXT[] DEFAULT '{}',
  content TEXT NOT NULL,
  status TEXT DEFAULT 'published' CHECK (status IN ('published', 'draft', 'archived')),
  meta_description TEXT,
  og_image_url TEXT,
  view_count INTEGER DEFAULT 0,
  featured BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Key Characteristics**:
- **Primary Key**: UUID (id)
- **Unique Constraints**: slug
- **Arrays**: tags (TEXT[])
- **Check Constraints**: status validation
- **SEO Fields**: meta_description, og_image_url

**Migration Considerations**:
- Content management functionality
- SEO optimization fields
- Image storage for covers and OG images
- Tag array handling

### 8. playlists
**Purpose**: Apple Music playlist management

**Schema**:
```sql
CREATE TABLE playlists (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  apple_music_playlist_id TEXT,
  active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Migration Considerations**:
- Apple Music integration
- User-owned playlists
- Active/inactive status

### 9. recently_played_tracks
**Purpose**: Track history and analytics

**Schema**:
```sql
CREATE TABLE recently_played_tracks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID NOT NULL REFERENCES sessions(id),
  song_title TEXT NOT NULL,
  artist_name TEXT NOT NULL,
  album_artwork TEXT,
  apple_music_id TEXT,
  playlist_id TEXT REFERENCES playlists(id),
  played_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Migration Considerations**:
- Analytics and history tracking
- Session-based organization
- Apple Music metadata

### 10. songs
**Purpose**: Song catalog and metadata

**Schema**:
```sql
CREATE TABLE songs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  artist TEXT NOT NULL,
  album TEXT,
  apple_music_id TEXT,
  artwork_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Migration Considerations**:
- Song metadata storage
- Apple Music integration
- Artwork URL handling

## Database Functions Analysis

### 1. get_user_subscription_status(user_id UUID)
**Purpose**: Determine user's current subscription status and plan details

**Returns**:
```sql
TABLE (
  plan_name TEXT,
  has_paid_plan BOOLEAN,
  plan_duration_hours NUMERIC
)
```

**Logic**:
- Checks for active subscription
- Handles subscription expiration
- Falls back to free plan
- Considers subscription status and period end

**Migration Considerations**:
- Business logic needs to be implemented in Convex functions
- Critical for feature access control
- Subscription expiration handling

### 2. get_session_users(p_session_id UUID)
**Purpose**: Session user management

**Migration Considerations**:
- Session participant tracking
- User management within sessions

### 3. insert_playlist_with_apple_id
**Purpose**: Playlist creation with Apple Music integration

**Migration Considerations**:
- Apple Music API integration
- Playlist management workflow

### 4. remove_session_user
**Purpose**: User removal from sessions

**Migration Considerations**:
- Session management functionality
- User access control

## Enums Analysis

### request_status
**Values**: "pending" | "approved" | "auto-approved" | "declined" | "played"

**Migration Considerations**:
- Maps directly to Convex union types
- Critical for request workflow
- Status transitions need validation

## Relationships and Constraints

### Primary Relationships
1. **dj_profiles** ← **sessions** (one-to-many)
2. **sessions** ← **song_requests** (one-to-many)
3. **dj_profiles** ← **dj_subscriptions** (one-to-one)
4. **subscription_plans** ← **dj_subscriptions** (one-to-many)
5. **sessions** ← **recently_played_tracks** (one-to-many)
6. **playlists** ← **recently_played_tracks** (one-to-many)

### Indexes Required for Performance
- `sessions.dj_id` (foreign key)
- `song_requests.session_id` (foreign key)
- `song_requests.status` (filtering)
- `song_requests.created_at` (ordering)
- `dj_subscriptions.dj_id` (foreign key)
- `blog_posts.slug` (unique lookup)
- `blog_posts.status` (filtering)
- `blog_posts.category` (filtering)

## Migration Challenges

### 1. Data Type Transformations
- **UUIDs** → Convex Id<T>
- **TIMESTAMPTZ** → Unix timestamps (number)
- **TEXT[]** → JavaScript arrays
- **JSONB** → JavaScript objects
- **ENUMs** → TypeScript union types

### 2. Relationship Mapping
- Foreign key constraints → Convex Id references
- One-to-one relationships → Unique indexes
- Complex queries → Convex query patterns

### 3. Business Logic Migration
- Database functions → Convex actions/mutations
- Check constraints → Validation in functions
- Triggers → Convex function logic

### 4. Performance Considerations
- Complex queries with joins → Denormalization strategies
- Real-time updates → Convex subscriptions
- Indexing strategy → Convex index definitions

### 5. Security and Access Control
- Row Level Security → Convex function-level security
- User authentication → Convex Auth integration
- Data isolation → Function-based access control

## Next Steps

1. **Design Convex Schema** based on this analysis
2. **Map Data Transformations** for each table
3. **Implement Business Logic** in Convex functions
4. **Plan Index Strategy** for optimal performance
5. **Design Migration Scripts** for data transfer

This analysis provides the foundation for creating an equivalent Convex schema that maintains all functionality while leveraging Convex's document-based architecture and real-time capabilities.
