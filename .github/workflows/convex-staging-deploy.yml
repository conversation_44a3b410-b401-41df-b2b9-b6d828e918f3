name: Deploy Convex to Staging

on:
  push:
    branches: 
      - staging
    paths:
      - 'playbeg-convex/**'
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if tests fail'
        required: false
        default: false
        type: boolean

jobs:
  pre-deployment-checks:
    name: Pre-deployment Checks
    runs-on: ubuntu-latest
    
    defaults:
      run:
        working-directory: ./playbeg-convex
    
    outputs:
      should_deploy: ${{ steps.check.outputs.should_deploy }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: './playbeg-convex/package-lock.json'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run tests
        id: tests
        run: |
          npm run lint
          npm run build
          # npm test (when tests are implemented)
          echo "tests_passed=true" >> $GITHUB_OUTPUT
        continue-on-error: true
        
      - name: Check deployment readiness
        id: check
        run: |
          if [ "${{ steps.tests.outputs.tests_passed }}" == "true" ] || [ "${{ github.event.inputs.force_deploy }}" == "true" ]; then
            echo "should_deploy=true" >> $GITHUB_OUTPUT
            echo "✅ Ready for staging deployment"
          else
            echo "should_deploy=false" >> $GITHUB_OUTPUT
            echo "❌ Tests failed - deployment blocked"
          fi

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: pre-deployment-checks
    if: needs.pre-deployment-checks.outputs.should_deploy == 'true'
    
    defaults:
      run:
        working-directory: ./playbeg-convex
    
    environment:
      name: staging
      url: ${{ steps.deploy.outputs.deployment_url }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: './playbeg-convex/package-lock.json'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Install Convex CLI
        run: npm install -g convex
        
      - name: Configure Convex for staging
        run: |
          echo "CONVEX_DEPLOYMENT=${{ secrets.CONVEX_STAGING_DEPLOYMENT }}" > .env.staging
          echo "NODE_ENV=staging" >> .env.staging
        env:
          CONVEX_STAGING_DEPLOYMENT: ${{ secrets.CONVEX_STAGING_DEPLOYMENT }}
          
      - name: Deploy to Convex staging
        id: deploy
        run: |
          # Load staging environment
          export $(cat .env.staging | grep -v '^#' | xargs)
          
          # Deploy Convex functions
          npx convex deploy --prod
          
          # Get deployment URL
          DEPLOYMENT_URL="https://dashboard.convex.dev/d/${{ secrets.CONVEX_STAGING_DEPLOYMENT }}"
          echo "deployment_url=$DEPLOYMENT_URL" >> $GITHUB_OUTPUT
          
          echo "✅ Staging deployment completed"
          echo "🌐 Dashboard: $DEPLOYMENT_URL"
        env:
          CONVEX_DEPLOY_KEY: ${{ secrets.CONVEX_DEPLOY_KEY }}
          
      - name: Run post-deployment tests
        run: |
          # Basic health check
          echo "Running post-deployment health checks..."
          
          # Test Convex functions are accessible
          # npx convex run myFunctions:get || echo "Warning: Basic function test failed"
          
          echo "✅ Post-deployment checks completed"
          
      - name: Update deployment status
        run: |
          echo "📊 Staging Deployment Summary:"
          echo "- Environment: Staging"
          echo "- Deployment: ${{ secrets.CONVEX_STAGING_DEPLOYMENT }}"
          echo "- Status: Success"
          echo "- Dashboard: https://dashboard.convex.dev/d/${{ secrets.CONVEX_STAGING_DEPLOYMENT }}"

  notify-deployment:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [pre-deployment-checks, deploy-staging]
    if: always()
    
    steps:
      - name: Deployment notification
        run: |
          if [ "${{ needs.deploy-staging.result }}" == "success" ]; then
            echo "🚀 Staging deployment successful!"
            echo "🔗 Dashboard: https://dashboard.convex.dev/d/${{ secrets.CONVEX_STAGING_DEPLOYMENT }}"
            echo "🧪 Ready for staging testing"
          elif [ "${{ needs.pre-deployment-checks.outputs.should_deploy }}" == "false" ]; then
            echo "⏸️ Deployment skipped due to failed checks"
          else
            echo "❌ Staging deployment failed"
            exit 1
          fi

  # Future: Add integration tests against staging environment
  staging-integration-tests:
    name: Staging Integration Tests
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: needs.deploy-staging.result == 'success'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          
      - name: Run integration tests
        run: |
          echo "🧪 Running integration tests against staging..."
          echo "Note: Integration tests will be implemented as migration progresses"
          
          # Future tests:
          # - API endpoint health checks
          # - Database connectivity tests
          # - Authentication flow tests
          # - Real-time functionality tests
          
          echo "✅ Integration tests placeholder completed"
