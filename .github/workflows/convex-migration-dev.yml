name: Convex Migration Development

on:
  push:
    branches: 
      - feature/convex-migration*
    paths:
      - 'playbeg-convex/**'
      - '.github/workflows/convex-migration-dev.yml'
  pull_request:
    branches: 
      - feature/convex-migration
    paths:
      - 'playbeg-convex/**'

jobs:
  test:
    name: Test Convex Migration
    runs-on: ubuntu-latest
    
    defaults:
      run:
        working-directory: ./playbeg-convex
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: './playbeg-convex/package-lock.json'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run ESLint
        run: npm run lint
        
      - name: Build project
        run: npm run build
        
      - name: Type check
        run: npx tsc --noEmit
        
      # Note: Convex tests will be added as we implement functions
      # - name: Run tests
      #   run: npm test
        
  convex-deploy-check:
    name: Convex Deployment Check
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    defaults:
      run:
        working-directory: ./playbeg-convex
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: './playbeg-convex/package-lock.json'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Install Convex CLI
        run: npm install -g convex
        
      - name: Validate Convex functions
        run: npx convex codegen
        env:
          CONVEX_DEPLOYMENT: ${{ secrets.CONVEX_DEV_DEPLOYMENT }}
          
      # Note: This will validate functions without deploying
      - name: Check Convex schema
        run: npx convex dev --once --dry-run || echo "Schema validation check"

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    defaults:
      run:
        working-directory: ./playbeg-convex
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: './playbeg-convex/package-lock.json'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run npm audit
        run: npm audit --audit-level=moderate
        
      - name: Check for sensitive files
        run: |
          if find . -name "*.env*" -not -path "./node_modules/*" | grep -v ".env.example" | grep -q .; then
            echo "Error: Environment files found in repository"
            exit 1
          fi
          
  documentation-check:
    name: Documentation Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Check migration documentation
        run: |
          required_files=(
            "PLAYBEG_PROJECT_ANALYSIS.md"
            "SUPABASE_TO_CONVEX_MIGRATION_PLAN.md"
            "MIGRATION_TASK_BREAKDOWN.md"
            "TASK_IMPLEMENTATION_GUIDE.md"
            "CONVEX_MIGRATION_BRANCHING_STRATEGY.md"
            "playbeg-convex/ENVIRONMENT_SETUP.md"
          )
          
          for file in "${required_files[@]}"; do
            if [ ! -f "$file" ]; then
              echo "Error: Required documentation file missing: $file"
              exit 1
            fi
          done
          
          echo "✅ All required documentation files present"
          
      - name: Check README updates
        run: |
          if [ -f "playbeg-convex/README.md" ]; then
            echo "✅ Convex project README exists"
          else
            echo "Warning: Consider adding a comprehensive README for the Convex project"
          fi

  notify-status:
    name: Notify Status
    runs-on: ubuntu-latest
    needs: [test, convex-deploy-check, security-scan, documentation-check]
    if: always()
    
    steps:
      - name: Check overall status
        run: |
          if [ "${{ needs.test.result }}" == "success" ] && \
             [ "${{ needs.security-scan.result }}" == "success" ] && \
             [ "${{ needs.documentation-check.result }}" == "success" ]; then
            echo "✅ All checks passed - Migration development ready"
          else
            echo "❌ Some checks failed - Review required"
            exit 1
          fi
