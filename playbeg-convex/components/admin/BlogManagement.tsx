"use client";

import React, { useState } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { Button } from '../ui/button';
import { Card } from '../ui/card';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Switch } from '../ui/switch';
import { Badge } from '../ui/badge';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff,
  Save,
  X,
  FileText,
  Calendar,
  User,
  Tag,
  BarChart3
} from 'lucide-react';

interface BlogPost {
  _id: Id<"blogPosts">;
  title: string;
  slug: string;
  excerpt: string;
  author: string;
  category: string;
  tags: string[];
  status: "draft" | "published" | "archived";
  featured: boolean;
  viewCount: number;
  publishedAt?: number;
  createdAt: number;
  updatedAt: number;
}

export default function BlogManagement() {
  const [activeTab, setActiveTab] = useState<'posts' | 'create' | 'categories'>('posts');
  const [selectedPost, setSelectedPost] = useState<Id<"blogPosts"> | null>(null);
  const [showEditor, setShowEditor] = useState(false);
  const [statusFilter, setStatusFilter] = useState<'all' | 'draft' | 'published' | 'archived'>('all');

  // Form state for creating/editing posts
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    content: '',
    excerpt: '',
    author: '',
    category: '',
    tags: [] as string[],
    coverImageUrl: '',
    coverImageAlt: '',
    metaDescription: '',
    status: 'draft' as 'draft' | 'published',
    featured: false,
  });

  // Queries
  const allPosts = useQuery(api.blog.getAllPostsAdmin, {
    status: statusFilter === 'all' ? undefined : statusFilter,
    limit: 50,
  });
  const selectedPostData = useQuery(
    api.blog.getPostById,
    selectedPost ? { postId: selectedPost } : "skip"
  );
  const categories = useQuery(api.blog.getCategories);

  // Mutations
  const createPost = useMutation(api.blog.createPost);
  const updatePost = useMutation(api.blog.updatePost);
  const deletePost = useMutation(api.blog.deletePost);
  const upsertCategory = useMutation(api.blog.upsertCategory);

  const resetForm = () => {
    setFormData({
      title: '',
      slug: '',
      content: '',
      excerpt: '',
      author: '',
      category: '',
      tags: [],
      coverImageUrl: '',
      coverImageAlt: '',
      metaDescription: '',
      status: 'draft',
      featured: false,
    });
    setSelectedPost(null);
    setShowEditor(false);
  };

  const handleCreatePost = async () => {
    if (!formData.title.trim() || !formData.content.trim()) return;

    try {
      await createPost({
        ...formData,
        slug: formData.slug || generateSlug(formData.title),
      });
      resetForm();
      setActiveTab('posts');
    } catch (error: any) {
      console.error('Failed to create post:', error);
    }
  };

  const handleUpdatePost = async () => {
    if (!selectedPost || !formData.title.trim()) return;

    try {
      await updatePost({
        postId: selectedPost,
        ...formData,
      });
      resetForm();
    } catch (error: any) {
      console.error('Failed to update post:', error);
    }
  };

  const handleDeletePost = async (postId: Id<"blogPosts">) => {
    if (!confirm('Are you sure you want to delete this post?')) return;

    try {
      await deletePost({ postId });
    } catch (error: any) {
      console.error('Failed to delete post:', error);
    }
  };

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getStatusBadge = (status: string) => {
    const colors = {
      draft: 'bg-yellow-600',
      published: 'bg-green-600',
      archived: 'bg-gray-600',
    };
    return (
      <Badge className={`${colors[status as keyof typeof colors]} text-white`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  // Load post data when editing
  React.useEffect(() => {
    if (selectedPostData && showEditor) {
      setFormData({
        title: selectedPostData.title,
        slug: selectedPostData.slug,
        content: selectedPostData.content,
        excerpt: selectedPostData.excerpt,
        author: selectedPostData.author,
        category: selectedPostData.category,
        tags: selectedPostData.tags,
        coverImageUrl: selectedPostData.coverImageUrl || '',
        coverImageAlt: selectedPostData.coverImageAlt || '',
        metaDescription: selectedPostData.metaDescription || '',
        status: selectedPostData.status,
        featured: selectedPostData.featured,
      });
    }
  }, [selectedPostData, showEditor]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Blog Management</h2>
        <Button
          onClick={() => {
            resetForm();
            setActiveTab('create');
            setShowEditor(true);
          }}
          className="bg-purple-600 hover:bg-purple-700 text-white"
        >
          <Plus className="w-4 h-4 mr-2" />
          New Post
        </Button>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-white/10 rounded-lg p-1">
        {[
          { id: 'posts', label: 'All Posts', icon: FileText },
          { id: 'create', label: 'Create', icon: Plus },
          { id: 'categories', label: 'Categories', icon: Tag },
        ].map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-purple-600 text-white'
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
            >
              <Icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          );
        })}
      </div>

      {/* Posts Tab */}
      {activeTab === 'posts' && !showEditor && (
        <div className="space-y-4">
          {/* Filters */}
          <div className="flex items-center space-x-4">
            <Label className="text-white">Filter by status:</Label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="bg-gray-800 border-gray-600 text-white rounded px-3 py-1"
            >
              <option value="all">All Posts</option>
              <option value="draft">Drafts</option>
              <option value="published">Published</option>
              <option value="archived">Archived</option>
            </select>
          </div>

          {/* Posts List */}
          <div className="grid gap-4">
            {allPosts?.map((post) => (
              <Card key={post._id} className="bg-white/10 backdrop-blur-md border-white/20 p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-white">{post.title}</h3>
                      {getStatusBadge(post.status)}
                      {post.featured && (
                        <Badge className="bg-yellow-600 text-white">Featured</Badge>
                      )}
                    </div>
                    <p className="text-white/70 text-sm mb-3">{post.excerpt}</p>
                    <div className="flex items-center space-x-4 text-xs text-white/60">
                      <span className="flex items-center">
                        <User className="w-3 h-3 mr-1" />
                        {post.author}
                      </span>
                      <span className="flex items-center">
                        <Calendar className="w-3 h-3 mr-1" />
                        {formatDate(post.createdAt)}
                      </span>
                      <span className="flex items-center">
                        <BarChart3 className="w-3 h-3 mr-1" />
                        {post.viewCount} views
                      </span>
                      <span className="flex items-center">
                        <Tag className="w-3 h-3 mr-1" />
                        {post.category}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        setSelectedPost(post._id);
                        setShowEditor(true);
                      }}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleDeletePost(post._id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
            {(!allPosts || allPosts.length === 0) && (
              <div className="text-center py-8 text-white/60">
                No posts found. Create your first blog post to get started!
              </div>
            )}
          </div>
        </div>
      )}

      {/* Create/Edit Post Form */}
      {(activeTab === 'create' || showEditor) && (
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-white">
              {selectedPost ? 'Edit Post' : 'Create New Post'}
            </h3>
            <Button
              variant="outline"
              onClick={resetForm}
              className="text-white border-white/30"
            >
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-4">
              <div>
                <Label htmlFor="title" className="text-white">Title</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => {
                    setFormData(prev => ({
                      ...prev,
                      title: e.target.value,
                      slug: prev.slug || generateSlug(e.target.value)
                    }));
                  }}
                  className="bg-gray-800 border-gray-600 text-white"
                  placeholder="Enter post title..."
                />
              </div>

              <div>
                <Label htmlFor="slug" className="text-white">Slug</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                  className="bg-gray-800 border-gray-600 text-white"
                  placeholder="post-url-slug"
                />
              </div>

              <div>
                <Label htmlFor="excerpt" className="text-white">Excerpt</Label>
                <Textarea
                  id="excerpt"
                  value={formData.excerpt}
                  onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
                  className="bg-gray-800 border-gray-600 text-white"
                  placeholder="Brief description of the post..."
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="author" className="text-white">Author</Label>
                  <Input
                    id="author"
                    value={formData.author}
                    onChange={(e) => setFormData(prev => ({ ...prev, author: e.target.value }))}
                    className="bg-gray-800 border-gray-600 text-white"
                    placeholder="Author name"
                  />
                </div>
                <div>
                  <Label htmlFor="category" className="text-white">Category</Label>
                  <Input
                    id="category"
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    className="bg-gray-800 border-gray-600 text-white"
                    placeholder="Post category"
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="featured"
                    checked={formData.featured}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, featured: checked }))}
                  />
                  <Label htmlFor="featured" className="text-white">Featured Post</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Label htmlFor="status" className="text-white">Status:</Label>
                  <select
                    value={formData.status}
                    onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
                    className="bg-gray-800 border-gray-600 text-white rounded px-3 py-1"
                  >
                    <option value="draft">Draft</option>
                    <option value="published">Published</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              <div>
                <Label htmlFor="content" className="text-white">Content (Markdown)</Label>
                <Textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  className="bg-gray-800 border-gray-600 text-white"
                  placeholder="Write your blog post content in Markdown..."
                  rows={12}
                />
              </div>

              <div>
                <Label htmlFor="coverImageUrl" className="text-white">Cover Image URL</Label>
                <Input
                  id="coverImageUrl"
                  value={formData.coverImageUrl}
                  onChange={(e) => setFormData(prev => ({ ...prev, coverImageUrl: e.target.value }))}
                  className="bg-gray-800 border-gray-600 text-white"
                  placeholder="https://example.com/image.jpg"
                />
              </div>

              <div>
                <Label htmlFor="metaDescription" className="text-white">Meta Description</Label>
                <Textarea
                  id="metaDescription"
                  value={formData.metaDescription}
                  onChange={(e) => setFormData(prev => ({ ...prev, metaDescription: e.target.value }))}
                  className="bg-gray-800 border-gray-600 text-white"
                  placeholder="SEO meta description..."
                  rows={2}
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <Button
              onClick={selectedPost ? handleUpdatePost : handleCreatePost}
              disabled={!formData.title.trim() || !formData.content.trim()}
              className="bg-purple-600 hover:bg-purple-700 text-white"
            >
              <Save className="w-4 h-4 mr-2" />
              {selectedPost ? 'Update Post' : 'Create Post'}
            </Button>
          </div>
        </Card>
      )}

      {/* Categories Tab */}
      {activeTab === 'categories' && (
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
          <h3 className="text-xl font-bold text-white mb-4">Blog Categories</h3>
          <div className="grid gap-3">
            {categories?.map((category) => (
              <div key={category._id} className="flex items-center justify-between bg-white/10 rounded-lg p-3">
                <div>
                  <div className="font-semibold text-white">{category.name}</div>
                  <div className="text-sm text-white/60">{category.postCount} posts</div>
                </div>
                <Badge variant="outline" className="text-white border-white/30">
                  {category.slug}
                </Badge>
              </div>
            ))}
            {(!categories || categories.length === 0) && (
              <div className="text-center py-4 text-white/60">
                No categories yet. Categories will be created automatically when you create posts.
              </div>
            )}
          </div>
        </Card>
      )}
    </div>
  );
}
