"use client";

import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../convex/_generated/api';
import { Id } from '../convex/_generated/dataModel';

interface SessionAnalyticsProps {
  sessionId?: Id<"sessions">;
}

export default function SessionAnalyticsDashboard({ sessionId }: SessionAnalyticsProps) {
  const [timeRange, setTimeRange] = useState<'last_week' | 'last_month' | 'last_3_months' | 'last_year' | 'all_time'>('last_month');
  const [selectedMetric, setSelectedMetric] = useState<'sessions' | 'requests' | 'engagement'>('sessions');

  // Queries
  const analytics = useQuery(api.sessionAnalytics.getDJSessionAnalytics, {
    timeRange,
    includeScheduled: true,
  });

  const retentionData = sessionId ? useQuery(api.sessionAnalytics.getAudienceRetentionAnalytics, {
    sessionId,
  }) : null;

  if (!analytics) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const formatPercentage = (num: number) => {
    return `${num.toFixed(1)}%`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Session Analytics</h2>
          <p className="text-gray-600">Comprehensive insights into your DJ performance</p>
        </div>
        <div className="flex gap-2">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="last_week">Last Week</option>
            <option value="last_month">Last Month</option>
            <option value="last_3_months">Last 3 Months</option>
            <option value="last_year">Last Year</option>
            <option value="all_time">All Time</option>
          </select>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Sessions"
          value={formatNumber(analytics.totalSessions)}
          subtitle={`${analytics.activeSessions} active, ${analytics.completedSessions} completed`}
          icon="🎵"
          trend={analytics.monthlyStats.length > 1 ? 
            analytics.monthlyStats[analytics.monthlyStats.length - 1].sessionCount - 
            analytics.monthlyStats[analytics.monthlyStats.length - 2].sessionCount : 0}
        />
        <MetricCard
          title="Song Requests"
          value={formatNumber(analytics.totalRequests)}
          subtitle={`${formatPercentage(analytics.approvalRate)} approval rate`}
          icon="🎤"
          trend={analytics.monthlyStats.length > 1 ? 
            analytics.monthlyStats[analytics.monthlyStats.length - 1].totalRequests - 
            analytics.monthlyStats[analytics.monthlyStats.length - 2].totalRequests : 0}
        />
        <MetricCard
          title="Unique Listeners"
          value={formatNumber(analytics.uniqueUsers)}
          subtitle={`Peak: ${analytics.peakListeners} listeners`}
          icon="👥"
          trend={analytics.monthlyStats.length > 1 ? 
            analytics.monthlyStats[analytics.monthlyStats.length - 1].uniqueListeners - 
            analytics.monthlyStats[analytics.monthlyStats.length - 2].uniqueListeners : 0}
        />
        <MetricCard
          title="Avg Session Duration"
          value={formatDuration(analytics.averageSessionDuration)}
          subtitle={`${formatNumber(analytics.totalEvents)} total events`}
          icon="⏱️"
          trend={0}
        />
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Trends */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Monthly Trends</h3>
          <div className="space-y-4">
            {analytics.monthlyStats.slice(-6).map((month, index) => (
              <div key={month.month} className="flex justify-between items-center">
                <div className="flex-1">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-900">
                      {new Date(month.month + '-01').toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}
                    </span>
                    <span className="text-sm text-gray-600">
                      {month.sessionCount} sessions
                    </span>
                  </div>
                  <div className="mt-1 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{
                        width: `${Math.min(100, (month.sessionCount / Math.max(...analytics.monthlyStats.map(m => m.sessionCount))) * 100)}%`
                      }}
                    />
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>{month.totalRequests} requests</span>
                    <span>{month.uniqueListeners} listeners</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Content */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Popular Content</h3>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Top Genres</h4>
              <div className="space-y-2">
                {analytics.topGenres.slice(0, 5).map((genre, index) => (
                  <div key={genre.genre} className="flex justify-between items-center">
                    <span className="text-sm text-gray-700">{genre.genre}</span>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{
                            width: `${(genre.count / analytics.topGenres[0].count) * 100}%`
                          }}
                        />
                      </div>
                      <span className="text-sm text-gray-600 w-8 text-right">{genre.count}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="border-t pt-4">
              <h4 className="font-medium text-gray-900 mb-2">Most Requested Songs</h4>
              <div className="space-y-2">
                {analytics.topSongs.slice(0, 5).map((song, index) => (
                  <div key={song.song} className="flex justify-between items-center">
                    <span className="text-sm text-gray-700 truncate flex-1 mr-2">{song.song}</span>
                    <span className="text-sm text-gray-600">{song.count}x</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Request Management</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Total Requests</span>
              <span className="font-medium">{formatNumber(analytics.totalRequests)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Approved</span>
              <span className="font-medium text-green-600">{formatNumber(analytics.approvedRequests)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Played</span>
              <span className="font-medium text-blue-600">{formatNumber(analytics.playedRequests)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Approval Rate</span>
              <span className="font-medium">{formatPercentage(analytics.approvalRate)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Play Rate</span>
              <span className="font-medium">{formatPercentage(analytics.playRate)}</span>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Audience Engagement</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Total Events</span>
              <span className="font-medium">{formatNumber(analytics.totalEvents)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Unique Users</span>
              <span className="font-medium">{formatNumber(analytics.uniqueUsers)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Events per Session</span>
              <span className="font-medium">{analytics.averageEventsPerSession.toFixed(1)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Peak Listeners</span>
              <span className="font-medium text-purple-600">{formatNumber(analytics.peakListeners)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Peak Requests</span>
              <span className="font-medium text-orange-600">{formatNumber(analytics.peakRequests)}</span>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Session Stats</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Total Sessions</span>
              <span className="font-medium">{formatNumber(analytics.totalSessions)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Active</span>
              <span className="font-medium text-green-600">{formatNumber(analytics.activeSessions)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Completed</span>
              <span className="font-medium text-gray-600">{formatNumber(analytics.completedSessions)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Scheduled</span>
              <span className="font-medium text-blue-600">{formatNumber(analytics.scheduledSessions)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Avg Duration</span>
              <span className="font-medium">{formatDuration(analytics.averageSessionDuration)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Audience Retention (if viewing specific session) */}
      {retentionData && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Audience Retention - {retentionData.sessionName}
          </h3>
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{retentionData.peakAudience}</div>
              <div className="text-sm text-gray-600">Peak Audience</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{retentionData.averageAudience}</div>
              <div className="text-sm text-gray-600">Average Audience</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{formatDuration(retentionData.sessionDuration)}</div>
              <div className="text-sm text-gray-600">Session Duration</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{retentionData.totalIntervals}</div>
              <div className="text-sm text-gray-600">5-min Intervals</div>
            </div>
          </div>
          
          <div className="space-y-2">
            {retentionData.retentionData.slice(0, 20).map((interval, index) => (
              <div key={interval.interval} className="flex items-center gap-3">
                <div className="w-16 text-sm text-gray-600">
                  {Math.floor(interval.interval * 5)}m
                </div>
                <div className="flex-1 bg-gray-200 rounded-full h-4 relative">
                  <div
                    className="bg-blue-600 h-4 rounded-full"
                    style={{
                      width: `${Math.min(100, (interval.activeUsers / retentionData.peakAudience) * 100)}%`
                    }}
                  />
                  <div className="absolute inset-0 flex items-center justify-center text-xs text-white font-medium">
                    {interval.activeUsers}
                  </div>
                </div>
                <div className="w-16 text-sm text-gray-600 text-right">
                  {interval.events}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Last Updated */}
      <div className="text-center text-sm text-gray-500">
        Last updated: {new Date(analytics.lastUpdated).toLocaleString()}
      </div>
    </div>
  );
}

interface MetricCardProps {
  title: string;
  value: string;
  subtitle: string;
  icon: string;
  trend: number;
}

function MetricCard({ title, value, subtitle, icon, trend }: MetricCardProps) {
  const getTrendIcon = () => {
    if (trend > 0) return '↗️';
    if (trend < 0) return '↘️';
    return '➡️';
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between mb-2">
        <span className="text-2xl">{icon}</span>
        {trend !== 0 && (
          <div className="flex items-center gap-1">
            <span className="text-sm">{getTrendIcon()}</span>
            <span className={`text-sm font-medium ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
              {Math.abs(trend)}
            </span>
          </div>
        )}
      </div>
      <div className="text-2xl font-bold text-gray-900 mb-1">{value}</div>
      <div className="text-sm font-medium text-gray-700">{title}</div>
      <div className="text-xs text-gray-500 mt-1">{subtitle}</div>
    </div>
  );
}
