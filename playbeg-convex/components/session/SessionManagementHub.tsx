"use client";

import React, { useState } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { Button } from '../ui/button';
import { Card } from '../ui/card';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Switch } from '../ui/switch';
import { Textarea } from '../ui/textarea';
import { Badge } from '../ui/badge';
import { 
  Play, 
  Pause, 
  Square, 
  Settings, 
  Users, 
  Music, 
  Calendar,
  Plus,
  Edit,
  Trash2,
  Share,
  QrCode,
  BarChart3
} from 'lucide-react';

interface SessionManagementHubProps {
  onSessionSelect?: (sessionId: Id<"sessions">) => void;
}

export default function SessionManagementHub({ onSessionSelect }: SessionManagementHubProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'create' | 'manage' | 'analytics'>('overview');
  const [selectedSessionId, setSelectedSessionId] = useState<Id<"sessions"> | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);

  // Session creation form state
  const [newSessionName, setNewSessionName] = useState('');
  const [newSessionDescription, setNewSessionDescription] = useState('');
  const [acceptRequests, setAcceptRequests] = useState(true);
  const [autoApproval, setAutoApproval] = useState(false);
  const [maxRequestsPerUser, setMaxRequestsPerUser] = useState(5);
  const [enableWeddingMode, setEnableWeddingMode] = useState(false);

  // Get current user
  const currentUser = useQuery(api.users.getCurrentUser);

  // Queries
  const userSessions = useQuery(
    api.sessions.getUserSessions,
    currentUser ? { userId: currentUser._id } : "skip"
  );
  const selectedSession = useQuery(
    api.sessions.getSessionById,
    selectedSessionId ? { sessionId: selectedSessionId } : "skip"
  );
  const sessionAnalytics = useQuery(
    api.analytics.getSessionAnalytics,
    selectedSessionId ? { sessionId: selectedSessionId } : "skip"
  );

  // Mutations
  const createSession = useMutation(api.sessions.createSession);
  const updateSession = useMutation(api.sessions.updateSession);
  const activateSession = useMutation(api.sessions.activateSession);
  const deactivateSession = useMutation(api.sessions.deactivateSession);
  const deleteSession = useMutation(api.sessions.deleteSession);

  const handleCreateSession = async () => {
    if (!newSessionName.trim()) return;

    try {
      const sessionId = await createSession({
        name: newSessionName.trim(),
        acceptRequests,
        autoApproval,
        maxRequestsPerUser,
        timeframeMinutes: 60,
        weddingModeEnabled: enableWeddingMode,
      });

      setNewSessionName('');
      setNewSessionDescription('');
      setShowCreateForm(false);
      setSelectedSessionId(sessionId);
      onSessionSelect?.(sessionId);
    } catch (error: any) {
      console.error('Failed to create session:', error);
    }
  };

  const handleSessionAction = async (sessionId: Id<"sessions">, action: 'activate' | 'deactivate' | 'delete') => {
    try {
      switch (action) {
        case 'activate':
          await activateSession({ sessionId });
          break;
        case 'deactivate':
          await deactivateSession({ sessionId });
          break;
        case 'delete':
          await deleteSession({ sessionId });
          if (selectedSessionId === sessionId) {
            setSelectedSessionId(null);
          }
          break;
      }
    } catch (error: any) {
      console.error(`Failed to ${action} session:`, error);
    }
  };

  const getSessionStatusBadge = (session: any) => {
    if (session.active) {
      return <Badge className="bg-green-600 text-white">Active</Badge>;
    }
    return <Badge variant="secondary">Inactive</Badge>;
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Session Management</h2>
        <Button
          onClick={() => setShowCreateForm(true)}
          className="bg-purple-600 hover:bg-purple-700 text-white"
        >
          <Plus className="w-4 h-4 mr-2" />
          New Session
        </Button>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-white/10 rounded-lg p-1">
        {[
          { id: 'overview', label: 'Overview', icon: BarChart3 },
          { id: 'create', label: 'Create', icon: Plus },
          { id: 'manage', label: 'Manage', icon: Settings },
          { id: 'analytics', label: 'Analytics', icon: BarChart3 },
        ].map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-purple-600 text-white'
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
            >
              <Icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          );
        })}
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Sessions List */}
          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
            <h3 className="text-xl font-bold text-white mb-4">Your Sessions</h3>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {userSessions?.map((session) => (
                <div
                  key={session._id}
                  className={`bg-white/10 rounded-lg p-4 cursor-pointer transition-colors ${
                    selectedSessionId === session._id ? 'ring-2 ring-purple-500' : 'hover:bg-white/20'
                  }`}
                  onClick={() => setSelectedSessionId(session._id)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-white">{session.name}</h4>
                    {getSessionStatusBadge(session)}
                  </div>
                  <div className="text-sm text-white/70">
                    Created: {formatDate(session.createdAt)}
                  </div>
                  <div className="flex items-center space-x-4 mt-2">
                    <Button
                      size="sm"
                      variant={session.active ? "destructive" : "default"}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSessionAction(session._id, session.active ? 'deactivate' : 'activate');
                      }}
                      className="text-xs"
                    >
                      {session.active ? <Pause className="w-3 h-3" /> : <Play className="w-3 h-3" />}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        onSessionSelect?.(session._id);
                      }}
                      className="text-xs"
                    >
                      <Share className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              ))}
              {(!userSessions || userSessions.length === 0) && (
                <div className="text-center py-8 text-white/60">
                  No sessions yet. Create your first session to get started!
                </div>
              )}
            </div>
          </Card>

          {/* Session Details */}
          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
            <h3 className="text-xl font-bold text-white mb-4">Session Details</h3>
            {selectedSession ? (
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-white">{selectedSession.name}</h4>
                  <div className="flex items-center space-x-2 mt-1">
                    {getSessionStatusBadge(selectedSession)}
                    <Badge variant="outline" className="text-white border-white/30">
                      {selectedSession.acceptRequests ? 'Accepting Requests' : 'Requests Closed'}
                    </Badge>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="text-white/60">Max Requests/User</div>
                    <div className="text-white font-medium">{selectedSession.maxRequestsPerUser || 'Unlimited'}</div>
                  </div>
                  <div>
                    <div className="text-white/60">Auto Approval</div>
                    <div className="text-white font-medium">{selectedSession.autoApproval ? 'On' : 'Off'}</div>
                  </div>
                  <div>
                    <div className="text-white/60">Wedding Mode</div>
                    <div className="text-white font-medium">{selectedSession.weddingModeEnabled ? 'On' : 'Off'}</div>
                  </div>
                  <div>
                    <div className="text-white/60">Created</div>
                    <div className="text-white font-medium">{formatDate(selectedSession.createdAt)}</div>
                  </div>
                </div>

                {sessionAnalytics && (
                  <div className="border-t border-white/20 pt-4">
                    <h5 className="font-semibold text-white mb-2">Quick Stats</h5>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-white/60">Total Requests</div>
                        <div className="text-white font-medium">{sessionAnalytics.totalRequests}</div>
                      </div>
                      <div>
                        <div className="text-white/60">Approved</div>
                        <div className="text-white font-medium">{sessionAnalytics.approvedRequests}</div>
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex space-x-2 pt-4">
                  <Button
                    size="sm"
                    variant={selectedSession.active ? "destructive" : "default"}
                    onClick={() => handleSessionAction(selectedSession._id, selectedSession.active ? 'deactivate' : 'activate')}
                    className="flex-1"
                  >
                    {selectedSession.active ? (
                      <>
                        <Pause className="w-4 h-4 mr-2" />
                        Deactivate
                      </>
                    ) : (
                      <>
                        <Play className="w-4 h-4 mr-2" />
                        Activate
                      </>
                    )}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onSessionSelect?.(selectedSession._id)}
                    className="flex-1"
                  >
                    <QrCode className="w-4 h-4 mr-2" />
                    Share
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-white/60">
                Select a session to view details
              </div>
            )}
          </Card>
        </div>
      )}

      {/* Create Session Form Modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="bg-gray-900 border-gray-700 p-6 w-full max-w-md mx-4">
            <h3 className="text-xl font-bold text-white mb-4">Create New Session</h3>
            <div className="space-y-4">
              <div>
                <Label htmlFor="session-name" className="text-white">Session Name</Label>
                <Input
                  id="session-name"
                  value={newSessionName}
                  onChange={(e) => setNewSessionName(e.target.value)}
                  placeholder="Saturday Night Mix, Birthday Party, etc."
                  className="bg-gray-800 border-gray-600 text-white"
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="accept-requests" className="text-white">Accept Requests</Label>
                <Switch
                  id="accept-requests"
                  checked={acceptRequests}
                  onCheckedChange={setAcceptRequests}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="auto-approval" className="text-white">Auto Approval</Label>
                <Switch
                  id="auto-approval"
                  checked={autoApproval}
                  onCheckedChange={setAutoApproval}
                />
              </div>

              <div>
                <Label htmlFor="max-requests" className="text-white">Max Requests per User</Label>
                <Input
                  id="max-requests"
                  type="number"
                  value={maxRequestsPerUser}
                  onChange={(e) => setMaxRequestsPerUser(parseInt(e.target.value) || 5)}
                  className="bg-gray-800 border-gray-600 text-white"
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="wedding-mode" className="text-white">Wedding Mode</Label>
                <Switch
                  id="wedding-mode"
                  checked={enableWeddingMode}
                  onCheckedChange={setEnableWeddingMode}
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowCreateForm(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateSession}
                  disabled={!newSessionName.trim()}
                  className="flex-1 bg-purple-600 hover:bg-purple-700"
                >
                  Create Session
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}
