"use client";

import React, { useState, useEffect } from 'react';
import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Button } from '../ui/button';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { 
  Smartphone, 
  Download, 
  Wifi, 
  WifiOff,
  Bell,
  BellOff,
  Share,
  Home,
  RefreshCw,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface PWAManagerProps {
  sessionId?: string;
}

export default function PWAManager({ sessionId }: PWAManagerProps) {
  const [isOnline, setIsOnline] = useState(true);
  const [isStandalone, setIsStandalone] = useState(false);
  const [canInstall, setCanInstall] = useState(false);
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null);

  const trackMobileUsage = useMutation(api.mobile.trackMobileUsage);

  useEffect(() => {
    // Check if running as PWA
    const isStandaloneMode = window.matchMedia('(display-mode: standalone)').matches ||
                            (window.navigator as any).standalone ||
                            document.referrer.includes('android-app://');
    setIsStandalone(isStandaloneMode);

    // Check online status
    setIsOnline(navigator.onLine);

    // Check notification permission
    if ('Notification' in window) {
      setNotificationsEnabled(Notification.permission === 'granted');
    }

    // Listen for install prompt
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e);
      setCanInstall(true);
    };

    // Listen for online/offline events
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Track PWA usage
    trackMobileUsage({
      event: 'pwa_loaded',
      sessionId,
      deviceInfo: {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        screenSize: `${screen.width}x${screen.height}`,
        isStandalone: isStandaloneMode,
      },
    });

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [sessionId, trackMobileUsage]);

  const handleInstallApp = async () => {
    if (!deferredPrompt) return;

    deferredPrompt.prompt();
    const { outcome } = await deferredPrompt.userChoice;
    
    if (outcome === 'accepted') {
      trackMobileUsage({
        event: 'pwa_installed',
        sessionId,
      });
    }

    setDeferredPrompt(null);
    setCanInstall(false);
  };

  const handleEnableNotifications = async () => {
    if (!('Notification' in window)) {
      alert('This browser does not support notifications');
      return;
    }

    const permission = await Notification.requestPermission();
    setNotificationsEnabled(permission === 'granted');

    if (permission === 'granted') {
      trackMobileUsage({
        event: 'notifications_enabled',
        sessionId,
      });

      // Register for push notifications if service worker is available
      if ('serviceWorker' in navigator) {
        const registration = await navigator.serviceWorker.ready;
        // Here you would typically subscribe to push notifications
        console.log('Service Worker ready for push notifications');
      }
    }
  };

  const handleShareApp = async () => {
    const shareData = {
      title: 'PlayBeg - DJ Song Requests',
      text: 'Request songs from your favorite DJs in real-time!',
      url: window.location.origin,
    };

    if (navigator.share) {
      try {
        await navigator.share(shareData);
        trackMobileUsage({
          event: 'app_shared',
          sessionId,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback to clipboard
      try {
        await navigator.clipboard.writeText(shareData.url);
        alert('Link copied to clipboard!');
      } catch (error) {
        console.log('Error copying to clipboard:', error);
      }
    }
  };

  const handleAddToHomeScreen = () => {
    // For iOS Safari
    if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
      alert('To add PlayBeg to your home screen:\n1. Tap the Share button\n2. Tap "Add to Home Screen"');
    } else {
      handleInstallApp();
    }
  };

  return (
    <div className="space-y-4">
      {/* Connection Status */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {isOnline ? (
              <Wifi className="w-5 h-5 text-green-400" />
            ) : (
              <WifiOff className="w-5 h-5 text-red-400" />
            )}
            <div>
              <h3 className="font-semibold text-white">
                {isOnline ? 'Online' : 'Offline'}
              </h3>
              <p className="text-white/60 text-sm">
                {isOnline 
                  ? 'Connected to PlayBeg servers' 
                  : 'Some features may be limited'
                }
              </p>
            </div>
          </div>
          <Badge className={isOnline ? 'bg-green-600' : 'bg-red-600'}>
            {isOnline ? 'Connected' : 'Disconnected'}
          </Badge>
        </div>
      </Card>

      {/* PWA Status */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Smartphone className="w-5 h-5 text-purple-400" />
            <div>
              <h3 className="font-semibold text-white">
                {isStandalone ? 'App Mode' : 'Browser Mode'}
              </h3>
              <p className="text-white/60 text-sm">
                {isStandalone 
                  ? 'Running as installed app' 
                  : 'Running in web browser'
                }
              </p>
            </div>
          </div>
          <Badge className={isStandalone ? 'bg-purple-600' : 'bg-gray-600'}>
            {isStandalone ? 'PWA' : 'Web'}
          </Badge>
        </div>
      </Card>

      {/* Install App */}
      {canInstall && !isStandalone && (
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Download className="w-5 h-5 text-blue-400" />
              <div>
                <h3 className="font-semibold text-white">Install PlayBeg</h3>
                <p className="text-white/60 text-sm">
                  Get the full app experience with offline support
                </p>
              </div>
            </div>
            <Button
              onClick={handleInstallApp}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Install
            </Button>
          </div>
        </Card>
      )}

      {/* Add to Home Screen (iOS) */}
      {!isStandalone && /iPad|iPhone|iPod/.test(navigator.userAgent) && (
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Home className="w-5 h-5 text-green-400" />
              <div>
                <h3 className="font-semibold text-white">Add to Home Screen</h3>
                <p className="text-white/60 text-sm">
                  Quick access from your home screen
                </p>
              </div>
            </div>
            <Button
              onClick={handleAddToHomeScreen}
              variant="outline"
              className="text-white border-white/30"
            >
              Add
            </Button>
          </div>
        </Card>
      )}

      {/* Notifications */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {notificationsEnabled ? (
              <Bell className="w-5 h-5 text-yellow-400" />
            ) : (
              <BellOff className="w-5 h-5 text-gray-400" />
            )}
            <div>
              <h3 className="font-semibold text-white">
                {notificationsEnabled ? 'Notifications On' : 'Enable Notifications'}
              </h3>
              <p className="text-white/60 text-sm">
                {notificationsEnabled 
                  ? 'Get notified of new song requests' 
                  : 'Stay updated with real-time alerts'
                }
              </p>
            </div>
          </div>
          {!notificationsEnabled && (
            <Button
              onClick={handleEnableNotifications}
              className="bg-yellow-600 hover:bg-yellow-700 text-white"
            >
              Enable
            </Button>
          )}
          {notificationsEnabled && (
            <CheckCircle className="w-5 h-5 text-green-400" />
          )}
        </div>
      </Card>

      {/* Share App */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Share className="w-5 h-5 text-pink-400" />
            <div>
              <h3 className="font-semibold text-white">Share PlayBeg</h3>
              <p className="text-white/60 text-sm">
                Tell others about this awesome DJ platform
              </p>
            </div>
          </div>
          <Button
            onClick={handleShareApp}
            variant="outline"
            className="text-white border-white/30"
          >
            Share
          </Button>
        </div>
      </Card>

      {/* Offline Features */}
      {!isOnline && (
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
          <div className="flex items-start space-x-3">
            <AlertCircle className="w-5 h-5 text-orange-400 mt-0.5" />
            <div>
              <h3 className="font-semibold text-white mb-2">Offline Mode</h3>
              <div className="space-y-2 text-sm text-white/60">
                <p>• View cached session data</p>
                <p>• Browse previously loaded content</p>
                <p>• Song requests will sync when online</p>
                <p>• Limited functionality until reconnected</p>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* PWA Features */}
      {isStandalone && (
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
          <div className="flex items-start space-x-3">
            <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
            <div>
              <h3 className="font-semibold text-white mb-2">App Features Active</h3>
              <div className="space-y-2 text-sm text-white/60">
                <p>• Offline support enabled</p>
                <p>• Background sync available</p>
                <p>• Push notifications ready</p>
                <p>• Native app experience</p>
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}
