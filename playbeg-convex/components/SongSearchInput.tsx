"use client";

import React, { useState, useEffect, useRef } from 'react';
import { useAction } from "convex/react";
import { api } from "../convex/_generated/api";
import { Input } from './ui/input';
import { Button } from './ui/button';
import { Card } from './ui/card';

interface SpotifyTrack {
  id: string;
  name: string;
  artists: string[];
  album: string;
  albumArt: string | null;
  previewUrl: string | null;
  duration: number;
  popularity: number;
  explicit: boolean;
  uri: string;
}

interface SongSearchInputProps {
  onSongSelect: (song: { title: string; artist: string; spotifyId?: string; albumArt?: string }) => void;
  placeholder?: string;
  className?: string;
  initialTitle?: string;
  initialArtist?: string;
}

export default function SongSearchInput({ 
  onSongSelect, 
  placeholder = "Search for a song...", 
  className = "",
  initialTitle = "",
  initialArtist = ""
}: SongSearchInputProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SpotifyTrack[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [manualEntry, setManualEntry] = useState(false);
  const [manualTitle, setManualTitle] = useState(initialTitle);
  const [manualArtist, setManualArtist] = useState(initialArtist);

  const searchRef = useRef<HTMLDivElement>(null);
  const searchTracks = useAction(api.spotify.searchTracks);

  // Debounced search
  useEffect(() => {
    if (searchQuery.length < 2) {
      setSearchResults([]);
      setShowResults(false);
      return;
    }

    const timeoutId = setTimeout(async () => {
      setIsSearching(true);
      try {
        const result = await searchTracks({ query: searchQuery, limit: 8 });
        if (result.success) {
          setSearchResults(result.tracks);
          setShowResults(true);
        } else {
          setSearchResults([]);
          setShowResults(false);
        }
      } catch (error) {
        console.error('Search error:', error);
        setSearchResults([]);
        setShowResults(false);
      } finally {
        setIsSearching(false);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, searchTracks]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!showResults) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < searchResults.length - 1 ? prev + 1 : prev
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
          break;
        case 'Enter':
          e.preventDefault();
          if (selectedIndex >= 0 && selectedIndex < searchResults.length) {
            handleSongSelect(searchResults[selectedIndex]);
          }
          break;
        case 'Escape':
          setShowResults(false);
          setSelectedIndex(-1);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [showResults, selectedIndex, searchResults]);

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSongSelect = (track: SpotifyTrack) => {
    onSongSelect({
      title: track.name,
      artist: track.artists[0],
      spotifyId: track.id,
      albumArt: track.albumArt || undefined,
    });
    setSearchQuery(`${track.name} - ${track.artists[0]}`);
    setShowResults(false);
    setSelectedIndex(-1);
  };

  const handleManualSubmit = () => {
    if (manualTitle.trim() && manualArtist.trim()) {
      onSongSelect({
        title: manualTitle.trim(),
        artist: manualArtist.trim(),
      });
      setManualEntry(false);
    }
  };

  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (manualEntry) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-white">Manual Entry</h3>
          <Button
            onClick={() => setManualEntry(false)}
            variant="outline"
            size="sm"
            className="text-white border-white/30 hover:bg-white/10"
          >
            Back to Search
          </Button>
        </div>
        
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Song Title *
            </label>
            <Input
              type="text"
              value={manualTitle}
              onChange={(e) => setManualTitle(e.target.value)}
              placeholder="Enter song title"
              className="w-full bg-white/20 border-white/30 text-white placeholder-white/60"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Artist Name *
            </label>
            <Input
              type="text"
              value={manualArtist}
              onChange={(e) => setManualArtist(e.target.value)}
              placeholder="Enter artist name"
              className="w-full bg-white/20 border-white/30 text-white placeholder-white/60"
            />
          </div>
          
          <Button
            onClick={handleManualSubmit}
            disabled={!manualTitle.trim() || !manualArtist.trim()}
            className="w-full bg-purple-600 hover:bg-purple-700 text-white"
          >
            Use This Song
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      <div className="space-y-3">
        <div>
          <label className="block text-sm font-medium text-white mb-2">
            Search for a Song
          </label>
          <Input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder={placeholder}
            className="w-full bg-white/20 border-white/30 text-white placeholder-white/60"
          />
          {isSearching && (
            <div className="text-white/60 text-sm mt-1">Searching...</div>
          )}
        </div>
        
        <Button
          onClick={() => setManualEntry(true)}
          variant="outline"
          size="sm"
          className="text-white border-white/30 hover:bg-white/10"
        >
          Enter Song Manually
        </Button>
      </div>

      {/* Search Results Dropdown */}
      {showResults && searchResults.length > 0 && (
        <Card className="absolute top-full left-0 right-0 mt-2 bg-white/10 backdrop-blur-md border-white/20 max-h-96 overflow-y-auto z-50">
          <div className="p-2">
            <div className="text-white/80 text-sm mb-2 px-2">
              Found {searchResults.length} results
            </div>
            {searchResults.map((track, index) => (
              <div
                key={track.id}
                onClick={() => handleSongSelect(track)}
                className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors ${
                  index === selectedIndex 
                    ? 'bg-purple-600/30' 
                    : 'hover:bg-white/10'
                }`}
              >
                {track.albumArt && (
                  <img
                    src={track.albumArt}
                    alt={`${track.album} cover`}
                    className="w-12 h-12 rounded object-cover"
                  />
                )}
                <div className="flex-1 min-w-0">
                  <div className="font-semibold text-white truncate">
                    {track.name}
                    {track.explicit && (
                      <span className="ml-2 text-xs bg-red-600 text-white px-1 rounded">E</span>
                    )}
                  </div>
                  <div className="text-white/80 text-sm truncate">
                    {track.artists.join(', ')}
                  </div>
                  <div className="text-white/60 text-xs">
                    {track.album} • {formatDuration(track.duration)}
                  </div>
                </div>
                <div className="text-white/60 text-xs">
                  <div className="text-center">
                    <div className="text-green-400">♪</div>
                    <div>{track.popularity}%</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* No Results */}
      {showResults && searchResults.length === 0 && !isSearching && searchQuery.length >= 2 && (
        <Card className="absolute top-full left-0 right-0 mt-2 bg-white/10 backdrop-blur-md border-white/20 p-4 z-50">
          <div className="text-center">
            <div className="text-white/80 mb-2">No songs found</div>
            <div className="text-white/60 text-sm mb-3">
              Try a different search or enter the song manually
            </div>
            <Button
              onClick={() => setManualEntry(true)}
              size="sm"
              className="bg-purple-600 hover:bg-purple-700 text-white"
            >
              Enter Manually
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
}
