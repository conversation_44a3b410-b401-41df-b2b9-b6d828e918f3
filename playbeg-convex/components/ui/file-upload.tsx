"use client";

import React, { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Upload, X, Image, FileText, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface FileUploadProps {
  purpose: "profile_picture" | "sponsor_logo" | "session_artwork" | "other";
  onUploadComplete?: (fileId: string, url: string) => void;
  onUploadError?: (error: string) => void;
  maxSize?: number; // in bytes
  accept?: Record<string, string[]>;
  className?: string;
  disabled?: boolean;
}

interface UploadingFile {
  file: File;
  progress: number;
  status: "uploading" | "processing" | "complete" | "error";
  error?: string;
  url?: string;
  fileId?: string;
}

export function FileUpload({
  purpose,
  onUploadComplete,
  onUploadError,
  maxSize = 10 * 1024 * 1024, // 10MB default
  accept = {
    "image/*": [".jpeg", ".jpg", ".png", ".webp", ".gif"]
  },
  className,
  disabled = false,
}: FileUploadProps) {
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
  
  const generateUploadUrl = useMutation(api.fileStorage.generateUploadUrl);
  const storeFileMetadata = useMutation(api.fileStorage.storeFileMetadata);

  const uploadFile = useCallback(async (file: File) => {
    const uploadingFile: UploadingFile = {
      file,
      progress: 0,
      status: "uploading",
    };

    setUploadingFiles(prev => [...prev, uploadingFile]);

    try {
      // Generate upload URL
      const uploadUrl = await generateUploadUrl();
      
      // Upload file with progress tracking
      const xhr = new XMLHttpRequest();
      
      return new Promise<void>((resolve, reject) => {
        xhr.upload.addEventListener("progress", (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            setUploadingFiles(prev => 
              prev.map(f => 
                f.file === file 
                  ? { ...f, progress }
                  : f
              )
            );
          }
        });

        xhr.addEventListener("load", async () => {
          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText);
              const storageId = response.storageId;

              // Update status to processing
              setUploadingFiles(prev => 
                prev.map(f => 
                  f.file === file 
                    ? { ...f, status: "processing", progress: 100 }
                    : f
                )
              );

              // Store file metadata
              const fileId = await storeFileMetadata({
                storageId,
                fileName: file.name,
                fileType: file.type,
                fileSize: file.size,
                purpose,
                description: `${purpose} uploaded by user`,
              });

              // Get file URL (in a real app, you'd get this from the storage)
              const fileUrl = URL.createObjectURL(file); // Temporary for demo

              // Update status to complete
              setUploadingFiles(prev => 
                prev.map(f => 
                  f.file === file 
                    ? { ...f, status: "complete", url: fileUrl, fileId }
                    : f
                )
              );

              onUploadComplete?.(fileId, fileUrl);
              resolve();
            } catch (error) {
              throw new Error("Failed to store file metadata");
            }
          } else {
            throw new Error("Upload failed");
          }
        });

        xhr.addEventListener("error", () => {
          reject(new Error("Upload failed"));
        });

        const formData = new FormData();
        formData.append("file", file);
        
        xhr.open("POST", uploadUrl);
        xhr.send(formData);
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Upload failed";
      
      setUploadingFiles(prev => 
        prev.map(f => 
          f.file === file 
            ? { ...f, status: "error", error: errorMessage }
            : f
        )
      );

      onUploadError?.(errorMessage);
    }
  }, [generateUploadUrl, storeFileMetadata, purpose, onUploadComplete, onUploadError]);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    acceptedFiles.forEach(uploadFile);
  }, [uploadFile]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept,
    maxSize,
    disabled,
    multiple: false, // Single file upload for profile pictures
  });

  const removeFile = (file: File) => {
    setUploadingFiles(prev => prev.filter(f => f.file !== file));
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith("image/")) {
      return <Image className="w-8 h-8" />;
    }
    return <FileText className="w-8 h-8" />;
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Drop Zone */}
      <Card className={cn(
        "border-2 border-dashed transition-colors",
        isDragActive ? "border-purple-500 bg-purple-50/10" : "border-gray-600",
        disabled && "opacity-50 cursor-not-allowed"
      )}>
        <CardContent className="p-6">
          <div
            {...getRootProps()}
            className={cn(
              "flex flex-col items-center justify-center space-y-4 text-center cursor-pointer",
              disabled && "cursor-not-allowed"
            )}
          >
            <input {...getInputProps()} />
            <Upload className={cn(
              "w-12 h-12",
              isDragActive ? "text-purple-500" : "text-gray-400"
            )} />
            <div>
              <p className="text-lg font-medium text-white">
                {isDragActive ? "Drop the file here" : "Drag & drop a file here"}
              </p>
              <p className="text-sm text-gray-400 mt-1">
                or click to select a file
              </p>
              <p className="text-xs text-gray-500 mt-2">
                Max size: {Math.round(maxSize / (1024 * 1024))}MB
              </p>
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
              disabled={disabled}
            >
              Choose File
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Upload Progress */}
      {uploadingFiles.length > 0 && (
        <div className="space-y-3">
          {uploadingFiles.map((uploadingFile, index) => (
            <Card key={index} className="bg-gray-800/50 border-gray-600">
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <div className="text-gray-400">
                    {getFileIcon(uploadingFile.file.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-white truncate">
                      {uploadingFile.file.name}
                    </p>
                    <p className="text-xs text-gray-400">
                      {Math.round(uploadingFile.file.size / 1024)} KB
                    </p>
                    
                    {uploadingFile.status === "uploading" && (
                      <div className="mt-2">
                        <Progress value={uploadingFile.progress} className="h-2" />
                        <p className="text-xs text-gray-400 mt-1">
                          Uploading... {uploadingFile.progress}%
                        </p>
                      </div>
                    )}
                    
                    {uploadingFile.status === "processing" && (
                      <p className="text-xs text-blue-400 mt-1">Processing...</p>
                    )}
                    
                    {uploadingFile.status === "complete" && (
                      <p className="text-xs text-green-400 mt-1">Upload complete!</p>
                    )}
                    
                    {uploadingFile.status === "error" && (
                      <div className="flex items-center mt-1">
                        <AlertCircle className="w-3 h-3 text-red-400 mr-1" />
                        <p className="text-xs text-red-400">
                          {uploadingFile.error || "Upload failed"}
                        </p>
                      </div>
                    )}
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFile(uploadingFile.file)}
                    className="text-gray-400 hover:text-white"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
