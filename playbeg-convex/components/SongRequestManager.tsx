"use client";

import React, { useState } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "../convex/_generated/api";
import { Id } from "../convex/_generated/dataModel";
import { Button } from './ui/button';
import { Card } from './ui/card';
import { Input } from './ui/input';

interface SongRequestManagerProps {
  sessionId: Id<"sessions">;
}

export default function SongRequestManager({ sessionId }: SongRequestManagerProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'pending' | 'approved' | 'declined' | 'played'>('all');
  const [selectedRequests, setSelectedRequests] = useState<Set<Id<"songRequests">>>(new Set());

  // Queries
  const pendingRequests = useQuery(api.songRequests.getPendingRequests, { sessionId });
  const allRequests = useQuery(api.songRequests.getDjSessionRequests, { 
    sessionId,
    status: selectedStatus === 'all' ? undefined : selectedStatus,
    limit: 100
  });
  const requestStats = useQuery(api.songRequests.getSessionRequestStats, { sessionId });

  // Mutations
  const updateRequestStatus = useMutation(api.songRequests.updateRequestStatus);
  const bulkUpdateStatus = useMutation(api.songRequests.bulkUpdateRequestStatus);

  const handleStatusUpdate = async (requestId: Id<"songRequests">, status: 'approved' | 'declined' | 'played') => {
    try {
      await updateRequestStatus({ requestId, status });
    } catch (error) {
      console.error('Failed to update request status:', error);
    }
  };

  const handleBulkAction = async (status: 'approved' | 'declined' | 'played') => {
    if (selectedRequests.size === 0) return;
    
    try {
      await bulkUpdateStatus({
        requestIds: Array.from(selectedRequests),
        status
      });
      setSelectedRequests(new Set());
    } catch (error) {
      console.error('Failed to bulk update requests:', error);
    }
  };

  const toggleRequestSelection = (requestId: Id<"songRequests">) => {
    const newSelection = new Set(selectedRequests);
    if (newSelection.has(requestId)) {
      newSelection.delete(requestId);
    } else {
      newSelection.add(requestId);
    }
    setSelectedRequests(newSelection);
  };

  const selectAllVisible = () => {
    if (!allRequests) return;
    const newSelection = new Set(allRequests.map(r => r._id));
    setSelectedRequests(newSelection);
  };

  const clearSelection = () => {
    setSelectedRequests(new Set());
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-600/20 text-yellow-200 border-yellow-500/30';
      case 'approved': return 'bg-green-600/20 text-green-200 border-green-500/30';
      case 'auto-approved': return 'bg-blue-600/20 text-blue-200 border-blue-500/30';
      case 'declined': return 'bg-red-600/20 text-red-200 border-red-500/30';
      case 'played': return 'bg-purple-600/20 text-purple-200 border-purple-500/30';
      default: return 'bg-gray-600/20 text-gray-200 border-gray-500/30';
    }
  };

  const formatTimeAgo = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      {requestStats && (
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4 text-center">
            <div className="text-2xl font-bold text-white">{requestStats.stats.total}</div>
            <div className="text-white/80 text-sm">Total</div>
          </Card>
          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4 text-center">
            <div className="text-2xl font-bold text-yellow-300">{requestStats.stats.pending}</div>
            <div className="text-white/80 text-sm">Pending</div>
          </Card>
          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4 text-center">
            <div className="text-2xl font-bold text-green-300">{requestStats.stats.approved + requestStats.stats.autoApproved}</div>
            <div className="text-white/80 text-sm">Approved</div>
          </Card>
          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4 text-center">
            <div className="text-2xl font-bold text-red-300">{requestStats.stats.declined}</div>
            <div className="text-white/80 text-sm">Declined</div>
          </Card>
          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4 text-center">
            <div className="text-2xl font-bold text-purple-300">{requestStats.stats.played}</div>
            <div className="text-white/80 text-sm">Played</div>
          </Card>
        </div>
      )}

      {/* Pending Requests Alert */}
      {pendingRequests && pendingRequests.length > 0 && (
        <Card className="bg-yellow-600/20 border-yellow-500/30 p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-yellow-200">
                {pendingRequests.length} Pending Request{pendingRequests.length !== 1 ? 's' : ''}
              </h3>
              <p className="text-yellow-300/80">New requests waiting for your approval</p>
            </div>
            <Button
              onClick={() => setSelectedStatus('pending')}
              className="bg-yellow-600 hover:bg-yellow-700 text-white"
            >
              Review Now
            </Button>
          </div>
        </Card>
      )}

      {/* Controls */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
        <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
          <div className="flex flex-col md:flex-row gap-4 items-center">
            <Input
              type="text"
              placeholder="Search requests..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="bg-white/20 border-white/30 text-white placeholder-white/60"
            />
            
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value as any)}
              className="bg-white/20 border border-white/30 rounded-md px-3 py-2 text-white"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="declined">Declined</option>
              <option value="played">Played</option>
            </select>
          </div>

          {selectedRequests.size > 0 && (
            <div className="flex gap-2">
              <span className="text-white/80 text-sm self-center">
                {selectedRequests.size} selected
              </span>
              <Button
                onClick={() => handleBulkAction('approved')}
                size="sm"
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                Approve
              </Button>
              <Button
                onClick={() => handleBulkAction('declined')}
                size="sm"
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                Decline
              </Button>
              <Button
                onClick={() => handleBulkAction('played')}
                size="sm"
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                Mark Played
              </Button>
              <Button
                onClick={clearSelection}
                size="sm"
                variant="outline"
                className="text-white border-white/30 hover:bg-white/10"
              >
                Clear
              </Button>
            </div>
          )}
        </div>

        {allRequests && allRequests.length > 0 && (
          <div className="mt-4 flex gap-2">
            <Button
              onClick={selectAllVisible}
              size="sm"
              variant="outline"
              className="text-white border-white/30 hover:bg-white/10"
            >
              Select All Visible
            </Button>
          </div>
        )}
      </Card>

      {/* Requests List */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <h2 className="text-xl font-bold text-white mb-4">Song Requests</h2>
        
        {allRequests && allRequests.length > 0 ? (
          <div className="space-y-3">
            {allRequests
              .filter(request => 
                searchTerm === '' || 
                request.songTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
                request.artistName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                request.requesterName.toLowerCase().includes(searchTerm.toLowerCase())
              )
              .map((request) => (
                <div key={request._id} className="bg-white/10 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={selectedRequests.has(request._id)}
                        onChange={() => toggleRequestSelection(request._id)}
                        className="rounded"
                      />
                      <div>
                        <div className="font-semibold text-white">{request.songTitle}</div>
                        <div className="text-white/80 text-sm">by {request.artistName}</div>
                        <div className="text-white/60 text-xs">
                          Requested by {request.requesterName} • {formatTimeAgo(request.createdAt)}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <span className={`px-2 py-1 rounded text-xs border ${getStatusColor(request.status)}`}>
                        {request.status.replace('-', ' ')}
                      </span>
                      
                      {request.status === 'pending' && (
                        <div className="flex space-x-2">
                          <Button
                            onClick={() => handleStatusUpdate(request._id, 'approved')}
                            size="sm"
                            className="bg-green-600 hover:bg-green-700 text-white"
                          >
                            Approve
                          </Button>
                          <Button
                            onClick={() => handleStatusUpdate(request._id, 'declined')}
                            size="sm"
                            className="bg-red-600 hover:bg-red-700 text-white"
                          >
                            Decline
                          </Button>
                        </div>
                      )}
                      
                      {(request.status === 'approved' || request.status === 'auto-approved') && (
                        <Button
                          onClick={() => handleStatusUpdate(request._id, 'played')}
                          size="sm"
                          className="bg-purple-600 hover:bg-purple-700 text-white"
                        >
                          Mark Played
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <div className="text-4xl mb-4">🎵</div>
            <p className="text-white/80">
              {selectedStatus === 'all' ? 'No requests yet' : `No ${selectedStatus} requests`}
            </p>
          </div>
        )}
      </Card>
    </div>
  );
}
