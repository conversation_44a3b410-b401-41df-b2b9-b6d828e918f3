"use client";

import React, { useState } from 'react';
import { useConvexAuth, useQuery, useMutation, useAction } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useAuthActions } from "@convex-dev/auth/react";
import { Button } from '../ui/button';
import { Card } from '../ui/card';
import { Input } from '../ui/input';

interface TestResult {
  step: string;
  status: 'pending' | 'running' | 'success' | 'error';
  message: string;
  data?: any;
}

export default function IntegrationTestSuite() {
  const { isAuthenticated, isLoading } = useConvexAuth();
  const { signIn, signOut } = useAuthActions();
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [testEmail, setTestEmail] = useState('<EMAIL>');
  const [testPassword, setTestPassword] = useState('testpass123');

  // Queries and mutations
  const currentUser = useQuery(api.users.getCurrentUser);
  const userWithProfile = useQuery(api.users.getCurrentUserWithProfile);
  const createDjProfile = useMutation(api.djProfiles.createDjProfile);
  const completeOnboarding = useMutation(api.djProfiles.completeOnboarding);
  const createSession = useMutation(api.sessions.createSession);
  const createSongRequest = useMutation(api.songRequests.createSongRequest);
  const activateSession = useMutation(api.sessions.activateSession);

  const updateTestResult = (step: string, status: TestResult['status'], message: string, data?: any) => {
    setTestResults(prev => {
      const existing = prev.find(r => r.step === step);
      if (existing) {
        return prev.map(r => r.step === step ? { ...r, status, message, data } : r);
      } else {
        return [...prev, { step, status, message, data }];
      }
    });
  };

  const runIntegrationTest = async () => {
    setIsRunning(true);
    setTestResults([]);

    try {
      // Step 1: Test Authentication
      updateTestResult('1. Authentication', 'running', 'Testing user authentication...');
      
      if (!isAuthenticated) {
        updateTestResult('1. Authentication', 'error', 'User not authenticated. Please sign in first.');
        return;
      }
      
      updateTestResult('1. Authentication', 'success', 'User authenticated successfully', { user: currentUser });

      // Step 2: Test User Profile Retrieval
      updateTestResult('2. User Profile', 'running', 'Retrieving user profile...');
      
      if (!currentUser) {
        updateTestResult('2. User Profile', 'error', 'Failed to retrieve current user');
        return;
      }
      
      updateTestResult('2. User Profile', 'success', 'User profile retrieved', { 
        userId: currentUser._id, 
        email: currentUser.email 
      });

      // Step 3: Test DJ Profile Creation/Retrieval
      updateTestResult('3. DJ Profile', 'running', 'Checking DJ profile...');
      
      let djProfile = userWithProfile?.djProfile;
      
      if (!djProfile) {
        try {
          const profileId = await createDjProfile({
            displayName: `Test DJ ${Date.now()}`,
          });
          updateTestResult('3. DJ Profile', 'success', 'DJ profile created', { profileId });
        } catch (error: any) {
          if (error.message.includes('already exists')) {
            updateTestResult('3. DJ Profile', 'success', 'DJ profile already exists');
          } else {
            updateTestResult('3. DJ Profile', 'error', `Failed to create DJ profile: ${error.message}`);
            return;
          }
        }
      } else {
        updateTestResult('3. DJ Profile', 'success', 'DJ profile exists', { 
          profileId: djProfile._id,
          displayName: djProfile.displayName,
          onboarding: djProfile.completedOnboarding 
        });
      }

      // Step 4: Test Onboarding Completion
      updateTestResult('4. Onboarding', 'running', 'Checking onboarding status...');
      
      if (!userWithProfile?.djProfile?.completedOnboarding) {
        try {
          await completeOnboarding({
            displayName: `Test DJ ${Date.now()}`,
          });
          updateTestResult('4. Onboarding', 'success', 'Onboarding completed');
        } catch (error: any) {
          if (error.message.includes('already completed')) {
            updateTestResult('4. Onboarding', 'success', 'Onboarding already completed');
          } else {
            updateTestResult('4. Onboarding', 'error', `Failed to complete onboarding: ${error.message}`);
            return;
          }
        }
      } else {
        updateTestResult('4. Onboarding', 'success', 'Onboarding already completed');
      }

      // Step 5: Test Session Creation
      updateTestResult('5. Session Creation', 'running', 'Creating test session...');
      
      try {
        const sessionId = await createSession({
          name: `Integration Test Session ${Date.now()}`,
          acceptRequests: true,
          autoApproval: true,
          maxRequestsPerUser: 5,
          timeframeMinutes: 60,
        });
        
        updateTestResult('5. Session Creation', 'success', 'Session created successfully', { sessionId });

        // Step 6: Test Session Activation
        updateTestResult('6. Session Activation', 'running', 'Activating session...');
        
        try {
          await activateSession({ sessionId });
          updateTestResult('6. Session Activation', 'success', 'Session activated successfully');

          // Step 7: Test Song Request Creation
          updateTestResult('7. Song Request', 'running', 'Creating test song request...');
          
          try {
            const requestResult = await createSongRequest({
              sessionId,
              songTitle: 'Test Song',
              artistName: 'Test Artist',
              requesterName: 'Integration Test User',
              requesterIp: '127.0.0.1',
            });
            
            updateTestResult('7. Song Request', 'success', 'Song request created successfully', requestResult);
            
            // All tests passed
            updateTestResult('8. Integration Complete', 'success', 'All integration tests passed! 🎉');
            
          } catch (error: any) {
            updateTestResult('7. Song Request', 'error', `Failed to create song request: ${error.message}`);
          }
        } catch (error: any) {
          updateTestResult('6. Session Activation', 'error', `Failed to activate session: ${error.message}`);
        }
      } catch (error: any) {
        updateTestResult('5. Session Creation', 'error', `Failed to create session: ${error.message}`);
      }

    } catch (error: any) {
      updateTestResult('Test Error', 'error', `Unexpected error: ${error.message}`);
    } finally {
      setIsRunning(false);
    }
  };

  const handleSignIn = async () => {
    try {
      const formData = new FormData();
      formData.set("email", testEmail);
      formData.set("password", testPassword);
      formData.set("flow", "signIn");
      
      await signIn("password", formData);
    } catch (error: any) {
      updateTestResult('Sign In', 'error', `Sign in failed: ${error.message}`);
    }
  };

  const handleSignOut = async () => {
    await signOut();
    setTestResults([]);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending': return '⏳';
      case 'running': return '🔄';
      case 'success': return '✅';
      case 'error': return '❌';
      default: return '⏳';
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pending': return 'text-gray-400';
      case 'running': return 'text-blue-400';
      case 'success': return 'text-green-400';
      case 'error': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className="space-y-6">
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <h2 className="text-2xl font-bold text-white mb-4">🧪 Integration Test Suite</h2>
        <p className="text-white/80 mb-6">
          Test the complete user workflow from authentication to song requests.
        </p>

        {!isAuthenticated ? (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-white mb-2">Test Email</label>
                <Input
                  type="email"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                  className="bg-white/20 border-white/30 text-white"
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-white mb-2">Test Password</label>
                <Input
                  type="password"
                  value={testPassword}
                  onChange={(e) => setTestPassword(e.target.value)}
                  className="bg-white/20 border-white/30 text-white"
                  placeholder="testpass123"
                />
              </div>
            </div>
            <Button
              onClick={handleSignIn}
              disabled={isLoading}
              className="bg-purple-600 hover:bg-purple-700 text-white"
            >
              {isLoading ? 'Signing In...' : 'Sign In for Testing'}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="text-white">
                Authenticated as: <span className="font-semibold">{currentUser?.email}</span>
              </div>
              <Button
                onClick={handleSignOut}
                variant="outline"
                className="text-white border-white/30 hover:bg-white/10"
              >
                Sign Out
              </Button>
            </div>
            
            <Button
              onClick={runIntegrationTest}
              disabled={isRunning}
              className="w-full bg-green-600 hover:bg-green-700 text-white"
            >
              {isRunning ? 'Running Tests...' : 'Run Integration Test'}
            </Button>
          </div>
        )}
      </Card>

      {testResults.length > 0 && (
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
          <h3 className="text-xl font-bold text-white mb-4">Test Results</h3>
          <div className="space-y-3">
            {testResults.map((result, index) => (
              <div key={index} className="bg-white/10 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{getStatusIcon(result.status)}</span>
                  <div className="flex-1">
                    <div className="font-semibold text-white">{result.step}</div>
                    <div className={`text-sm ${getStatusColor(result.status)}`}>
                      {result.message}
                    </div>
                    {result.data && (
                      <details className="mt-2">
                        <summary className="text-xs text-white/60 cursor-pointer">View Data</summary>
                        <pre className="text-xs text-white/80 mt-1 bg-black/20 p-2 rounded overflow-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
}
