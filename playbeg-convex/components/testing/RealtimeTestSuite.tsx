"use client";

import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Button } from '../ui/button';
import { Card } from '../ui/card';
import { Input } from '../ui/input';
import { Id } from "../../convex/_generated/dataModel";
import { useAuthActions } from "@convex-dev/auth/react";

interface RealtimeTestResult {
  test: string;
  status: 'pending' | 'running' | 'success' | 'error';
  message: string;
  timestamp: number;
}

export default function RealtimeTestSuite() {
  const [testResults, setTestResults] = useState<RealtimeTestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [testSessionId, setTestSessionId] = useState<Id<"sessions"> | null>(null);
  const [requestCount, setRequestCount] = useState(0);

  // Get current user
  const currentUser = useQuery(api.users.getCurrentUser);

  // Real-time queries to test subscriptions
  const sessions = useQuery(
    api.sessions.getUserSessions,
    currentUser ? { userId: currentUser._id } : "skip"
  );
  const sessionDetails = useQuery(
    api.sessions.getSessionById,
    testSessionId ? { sessionId: testSessionId } : "skip"
  );
  const sessionRequests = useQuery(
    api.songRequests.getSessionRequests,
    testSessionId ? { sessionId: testSessionId } : "skip"
  );
  const sessionAnalytics = useQuery(
    api.analytics.getSessionAnalytics,
    testSessionId ? { sessionId: testSessionId } : "skip"
  );

  // Mutations for testing
  const createSession = useMutation(api.sessions.createSession);
  const activateSession = useMutation(api.sessions.activateSession);
  const deactivateSession = useMutation(api.sessions.deactivateSession);
  const updateSession = useMutation(api.sessions.updateSession);
  const createSongRequest = useMutation(api.songRequests.createSongRequest);
  const updateRequestStatus = useMutation(api.songRequests.updateRequestStatus);

  const addTestResult = (test: string, status: RealtimeTestResult['status'], message: string) => {
    setTestResults(prev => [...prev, {
      test,
      status,
      message,
      timestamp: Date.now()
    }]);
  };

  const runRealtimeTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    setRequestCount(0);

    try {
      // Test 1: Session Creation and Real-time Updates
      addTestResult('Session Creation', 'running', 'Creating test session...');
      
      const sessionId = await createSession({
        name: `Realtime Test Session ${Date.now()}`,
        acceptRequests: true,
        autoApproval: false,
        maxRequestsPerUser: 10,
      });
      
      setTestSessionId(sessionId);
      addTestResult('Session Creation', 'success', 'Session created, testing real-time updates...');

      // Wait for real-time update
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Test 2: Session Activation
      addTestResult('Session Activation', 'running', 'Activating session...');
      
      await activateSession({ sessionId });
      addTestResult('Session Activation', 'success', 'Session activated, checking real-time status...');

      // Wait for real-time update
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Test 3: Multiple Song Requests (Real-time stress test)
      addTestResult('Song Request Batch', 'running', 'Creating multiple song requests...');
      
      const requestPromises = [];
      for (let i = 1; i <= 5; i++) {
        requestPromises.push(
          createSongRequest({
            sessionId,
            songTitle: `Test Song ${i}`,
            artistName: `Test Artist ${i}`,
            requesterName: `Test User ${i}`,
            requesterIp: '127.0.0.1',
          })
        );
      }
      
      await Promise.all(requestPromises);
      setRequestCount(5);
      addTestResult('Song Request Batch', 'success', 'Created 5 song requests, monitoring real-time updates...');

      // Wait for real-time updates
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Test 4: Request Status Updates
      addTestResult('Request Status Updates', 'running', 'Testing request status changes...');
      
      if (sessionRequests && sessionRequests.length > 0) {
        const firstRequest = sessionRequests[0];
        await updateRequestStatus({
          requestId: firstRequest._id,
          status: 'approved',
        });
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        await updateRequestStatus({
          requestId: firstRequest._id,
          status: 'played',
        });
        
        addTestResult('Request Status Updates', 'success', 'Request status updated, checking real-time sync...');
      } else {
        addTestResult('Request Status Updates', 'error', 'No requests found to update');
      }

      // Test 5: Session Settings Update
      addTestResult('Session Settings Update', 'running', 'Updating session settings...');
      
      await updateSession({
        sessionId,
        name: `Updated Realtime Test Session ${Date.now()}`,
        maxRequestsPerUser: 15,
        autoApproval: true,
      });
      
      addTestResult('Session Settings Update', 'success', 'Session settings updated, checking real-time sync...');

      // Wait for final updates
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Test 6: Session Deactivation
      addTestResult('Session Deactivation', 'running', 'Deactivating session...');
      
      await deactivateSession({ sessionId });
      addTestResult('Session Deactivation', 'success', 'Session deactivated, checking real-time status...');

      // Final validation
      await new Promise(resolve => setTimeout(resolve, 1000));
      addTestResult('Real-time Validation Complete', 'success', 'All real-time features validated successfully! 🎉');

    } catch (error: any) {
      addTestResult('Test Error', 'error', `Unexpected error: ${error.message}`);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: RealtimeTestResult['status']) => {
    switch (status) {
      case 'pending': return '⏳';
      case 'running': return '🔄';
      case 'success': return '✅';
      case 'error': return '❌';
      default: return '⏳';
    }
  };

  const getStatusColor = (status: RealtimeTestResult['status']) => {
    switch (status) {
      case 'pending': return 'text-gray-400';
      case 'running': return 'text-blue-400';
      case 'success': return 'text-green-400';
      case 'error': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className="space-y-6">
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <h2 className="text-2xl font-bold text-white mb-4">⚡ Real-time Feature Validation</h2>
        <p className="text-white/80 mb-6">
          Test real-time subscriptions, live updates, and data synchronization across the application.
        </p>

        <Button
          onClick={runRealtimeTests}
          disabled={isRunning}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white mb-6"
        >
          {isRunning ? 'Running Real-time Tests...' : 'Run Real-time Validation'}
        </Button>

        {/* Real-time Data Display */}
        {testSessionId && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <Card className="bg-white/5 border-white/10 p-4">
              <h4 className="font-semibold text-white mb-2">Session Status (Live)</h4>
              <div className="text-sm text-white/80">
                <div>Active: {sessionDetails?.active ? '✅ Yes' : '❌ No'}</div>
                <div>Name: {sessionDetails?.name || 'Loading...'}</div>
                <div>Requests: {sessionDetails?.acceptRequests ? '✅ Accepting' : '❌ Closed'}</div>
                <div>Auto-approval: {sessionDetails?.autoApproval ? '✅ On' : '❌ Off'}</div>
              </div>
            </Card>

            <Card className="bg-white/5 border-white/10 p-4">
              <h4 className="font-semibold text-white mb-2">Song Requests (Live)</h4>
              <div className="text-sm text-white/80">
                <div>Total Requests: {sessionRequests?.length || 0}</div>
                <div>Expected: {requestCount}</div>
                <div>Pending: {sessionRequests?.filter(r => r.status === 'pending').length || 0}</div>
                <div>Approved: {sessionRequests?.filter(r => r.status === 'approved').length || 0}</div>
                <div>Played: {sessionRequests?.filter(r => r.status === 'played').length || 0}</div>
              </div>
            </Card>
          </div>
        )}
      </Card>

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
          <h3 className="text-xl font-bold text-white mb-4">Real-time Test Results</h3>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {testResults.map((result, index) => (
              <div key={index} className="bg-white/10 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{getStatusIcon(result.status)}</span>
                  <div className="flex-1">
                    <div className="font-semibold text-white">{result.test}</div>
                    <div className={`text-sm ${getStatusColor(result.status)}`}>
                      {result.message}
                    </div>
                    <div className="text-xs text-white/40 mt-1">
                      {new Date(result.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Real-time Analytics Display */}
      {sessionAnalytics && (
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
          <h3 className="text-xl font-bold text-white mb-4">📊 Live Session Analytics</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">{sessionAnalytics.totalRequests}</div>
              <div className="text-sm text-white/60">Total Requests</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">{sessionAnalytics.approvedRequests}</div>
              <div className="text-sm text-white/60">Approved</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">{sessionAnalytics.playedRequests}</div>
              <div className="text-sm text-white/60">Played</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-400">{sessionAnalytics.uniqueRequesters}</div>
              <div className="text-sm text-white/60">Unique Users</div>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}
