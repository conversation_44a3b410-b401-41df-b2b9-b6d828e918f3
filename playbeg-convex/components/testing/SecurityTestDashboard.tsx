"use client";

import React, { useState } from 'react';
import { useQuery, useAction } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Play,
  Clock,
  Bug,
  Lock,
  Eye,
  RefreshCw,
  Target,
  Zap
} from 'lucide-react';

export default function SecurityTestDashboard() {
  const [isRunning, setIsRunning] = useState(false);
  const [selectedScanType, setSelectedScanType] = useState<string>('comprehensive');

  const scanResults = useQuery(api.securityTesting.getSecurityScanResults, { limit: 10 });
  const runSecurityScan = useAction(api.securityTesting.runSecurityScan);
  const runPenetrationTest = useAction(api.securityTesting.runPenetrationTest);

  const scanTypes = [
    { value: 'comprehensive', label: 'Comprehensive Scan', description: 'Full security assessment' },
    { value: 'input_validation', label: 'Input Validation', description: 'Test input sanitization' },
    { value: 'authentication', label: 'Authentication', description: 'Test auth mechanisms' },
    { value: 'authorization', label: 'Authorization', description: 'Test access controls' },
    { value: 'rate_limiting', label: 'Rate Limiting', description: 'Test rate limits' },
    { value: 'data_exposure', label: 'Data Exposure', description: 'Test data leakage' },
  ];

  const penetrationTests = [
    { value: 'comprehensive', label: 'Comprehensive Test', description: 'Full penetration test' },
    { value: 'brute_force', label: 'Brute Force', description: 'Test brute force attacks' },
    { value: 'session_hijacking', label: 'Session Hijacking', description: 'Test session security' },
    { value: 'csrf', label: 'CSRF', description: 'Test CSRF protection' },
    { value: 'injection', label: 'Injection', description: 'Test injection attacks' },
  ];

  const handleRunSecurityScan = async () => {
    setIsRunning(true);
    try {
      const result = await runSecurityScan({
        scanType: selectedScanType as any,
      });
      console.log('Security scan completed:', result);
    } catch (error) {
      console.error('Security scan failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const handleRunPenetrationTest = async (testType: string) => {
    setIsRunning(true);
    try {
      const result = await runPenetrationTest({
        testType: testType as any,
      });
      console.log('Penetration test completed:', result);
    } catch (error) {
      console.error('Penetration test failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-600';
      case 'high': return 'bg-orange-600';
      case 'medium': return 'bg-yellow-600';
      case 'low': return 'bg-blue-600';
      default: return 'bg-gray-600';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <XCircle className="w-4 h-4" />;
      case 'high': return <AlertTriangle className="w-4 h-4" />;
      case 'medium': return <Clock className="w-4 h-4" />;
      case 'low': return <CheckCircle className="w-4 h-4" />;
      default: return <Bug className="w-4 h-4" />;
    }
  };

  const getRiskLevel = (score: number) => {
    if (score >= 70) return { level: 'Critical', color: 'bg-red-600' };
    if (score >= 40) return { level: 'High', color: 'bg-orange-600' };
    if (score >= 20) return { level: 'Medium', color: 'bg-yellow-600' };
    if (score > 0) return { level: 'Low', color: 'bg-blue-600' };
    return { level: 'Secure', color: 'bg-green-600' };
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white flex items-center">
          <Shield className="w-6 h-6 mr-2" />
          Security Testing Dashboard
        </h2>
        <Button
          variant="outline"
          size="sm"
          onClick={() => window.location.reload()}
          className="text-white border-white/30"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Security Overview */}
      {scanResults && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Total Scans</p>
                <p className="text-2xl font-bold text-white">{scanResults.metrics.totalScans}</p>
              </div>
              <Target className="w-8 h-8 text-purple-400" />
            </div>
          </Card>

          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Pass Rate</p>
                <p className="text-2xl font-bold text-white">
                  {scanResults.metrics.passRate?.toFixed(1) || 0}%
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-400" />
            </div>
          </Card>

          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Avg Risk Score</p>
                <p className="text-2xl font-bold text-white">
                  {scanResults.metrics.avgRiskScore?.toFixed(1) || 0}
                </p>
              </div>
              <AlertTriangle className="w-8 h-8 text-yellow-400" />
            </div>
          </Card>

          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Vulnerabilities</p>
                <p className="text-2xl font-bold text-white">{scanResults.metrics.totalVulnerabilities}</p>
              </div>
              <Bug className="w-8 h-8 text-red-400" />
            </div>
          </Card>
        </div>
      )}

      {/* Security Scan Controls */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <h3 className="text-xl font-bold text-white mb-4 flex items-center">
          <Shield className="w-5 h-5 mr-2" />
          Vulnerability Scanning
        </h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Scan Type Selection */}
          <div>
            <h4 className="text-white font-semibold mb-3">Select Scan Type</h4>
            <div className="space-y-2">
              {scanTypes.map((type) => (
                <div
                  key={type.value}
                  className={`p-3 rounded-lg cursor-pointer transition-colors ${
                    selectedScanType === type.value
                      ? 'bg-purple-600/30 border border-purple-500'
                      : 'bg-white/5 hover:bg-white/10'
                  }`}
                  onClick={() => setSelectedScanType(type.value)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h5 className="text-white font-medium">{type.label}</h5>
                      <p className="text-white/60 text-sm">{type.description}</p>
                    </div>
                    {selectedScanType === type.value && (
                      <CheckCircle className="w-5 h-5 text-purple-400" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Scan Controls */}
          <div>
            <h4 className="text-white font-semibold mb-3">Run Security Scan</h4>
            <div className="space-y-4">
              <Button
                onClick={handleRunSecurityScan}
                disabled={isRunning}
                className="w-full bg-purple-600 hover:bg-purple-700 text-white"
              >
                {isRunning ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Scanning...
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    Start Vulnerability Scan
                  </>
                )}
              </Button>

              <div className="bg-white/5 rounded-lg p-4">
                <h5 className="text-white font-medium mb-2">Scan Details</h5>
                <div className="space-y-1 text-sm text-white/60">
                  <p>• Input validation testing</p>
                  <p>• Authentication bypass attempts</p>
                  <p>• Authorization checks</p>
                  <p>• Rate limiting validation</p>
                  <p>• Data exposure detection</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Penetration Testing */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <h3 className="text-xl font-bold text-white mb-4 flex items-center">
          <Target className="w-5 h-5 mr-2" />
          Penetration Testing
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {penetrationTests.map((test) => (
            <div key={test.value} className="bg-white/5 rounded-lg p-4">
              <h4 className="text-white font-semibold mb-2">{test.label}</h4>
              <p className="text-white/60 text-sm mb-3">{test.description}</p>
              <Button
                onClick={() => handleRunPenetrationTest(test.value)}
                disabled={isRunning}
                variant="outline"
                size="sm"
                className="w-full text-white border-white/30"
              >
                <Zap className="w-4 h-4 mr-2" />
                Run Test
              </Button>
            </div>
          ))}
        </div>
      </Card>

      {/* Recent Scan Results */}
      {scanResults && scanResults.results.length > 0 && (
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
          <h3 className="text-xl font-bold text-white mb-4">Recent Scan Results</h3>
          
          <div className="space-y-4">
            {scanResults.results.map((result, index) => {
              const riskLevel = getRiskLevel(result.riskScore);
              
              return (
                <div key={index} className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded ${riskLevel.color}`}>
                        <Shield className="w-4 h-4 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-white">{result.scanType}</h4>
                        <p className="text-white/60 text-sm">
                          {new Date(result.timestamp).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Badge className={`${riskLevel.color} text-white`}>
                        {riskLevel.level}
                      </Badge>
                      <Badge variant="outline" className="text-white border-white/30">
                        {result.status}
                      </Badge>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div>
                      <p className="text-white/60 text-sm">Duration</p>
                      <p className="text-white font-semibold">{formatDuration(result.duration)}</p>
                    </div>
                    <div>
                      <p className="text-white/60 text-sm">Vulnerabilities</p>
                      <p className="text-white font-semibold">{result.vulnerabilitiesFound}</p>
                    </div>
                    <div>
                      <p className="text-white/60 text-sm">Risk Score</p>
                      <p className="text-white font-semibold">{result.riskScore}</p>
                    </div>
                    <div>
                      <p className="text-white/60 text-sm">Scan ID</p>
                      <p className="text-white font-semibold text-xs">{result.scanId.slice(-8)}</p>
                    </div>
                  </div>

                  {/* Vulnerabilities */}
                  {result.vulnerabilities.length > 0 && (
                    <div>
                      <h5 className="text-white font-medium mb-2">Vulnerabilities Found</h5>
                      <div className="space-y-2">
                        {result.vulnerabilities.map((vuln: any, vulnIndex: number) => (
                          <div key={vulnIndex} className="flex items-start space-x-3 bg-white/5 rounded p-3">
                            <div className={`p-1 rounded ${getSeverityColor(vuln.severity)}`}>
                              {getSeverityIcon(vuln.severity)}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-1">
                                <span className="text-white font-medium">{vuln.type}</span>
                                <Badge className={`${getSeverityColor(vuln.severity)} text-white text-xs`}>
                                  {vuln.severity}
                                </Badge>
                              </div>
                              <p className="text-white/70 text-sm">{vuln.description}</p>
                              {vuln.recommendation && (
                                <p className="text-blue-400 text-sm mt-1">
                                  💡 {vuln.recommendation}
                                </p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </Card>
      )}

      {/* Security Recommendations */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <h3 className="text-xl font-bold text-white mb-4 flex items-center">
          <Lock className="w-5 h-5 mr-2" />
          Security Best Practices
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Regular Security Scans</h4>
                <p className="text-white/60 text-sm">
                  Run comprehensive security scans weekly to identify new vulnerabilities.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Input Validation</h4>
                <p className="text-white/60 text-sm">
                  Validate and sanitize all user inputs to prevent injection attacks.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Authentication Security</h4>
                <p className="text-white/60 text-sm">
                  Use strong authentication mechanisms and session management.
                </p>
              </div>
            </div>
          </div>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Rate Limiting</h4>
                <p className="text-white/60 text-sm">
                  Implement rate limiting to prevent abuse and DoS attacks.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Data Protection</h4>
                <p className="text-white/60 text-sm">
                  Encrypt sensitive data and avoid exposing unnecessary information.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Security Monitoring</h4>
                <p className="text-white/60 text-sm">
                  Monitor security events and maintain audit logs for compliance.
                </p>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
