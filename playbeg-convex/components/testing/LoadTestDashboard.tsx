"use client";

import React, { useState } from 'react';
import { useQuery, useMutation, useAction } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { 
  Play, 
  Square, 
  BarChart3, 
  Clock, 
  Users, 
  Zap,
  CheckCircle,
  XCircle,
  RefreshCw,
  Trash2,
  Activity,
  TrendingUp,
  AlertTriangle
} from 'lucide-react';

export default function LoadTestDashboard() {
  const [isRunning, setIsRunning] = useState(false);
  const [testConfig, setTestConfig] = useState({
    scenarioName: 'Standard Load Test',
    concurrentUsers: 10,
    duration: 60, // seconds
    operations: [
      { type: 'query_sessions', weight: 40, params: {} },
      { type: 'submit_request', weight: 30, params: {} },
      { type: 'query_requests', weight: 20, params: {} },
      { type: 'create_session', weight: 10, params: {} },
    ],
  });

  const testResults = useQuery(api.testing.getLoadTestResults, { limit: 20 });
  const runLoadTest = useAction(api.testing.runLoadTestScenario);
  const createTestData = useMutation(api.testing.createTestData);
  const cleanupTestData = useMutation(api.testing.cleanupTestData);

  const handleRunLoadTest = async () => {
    setIsRunning(true);
    try {
      const result = await runLoadTest(testConfig);
      console.log('Load test completed:', result);
    } catch (error) {
      console.error('Load test failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const handleCreateTestData = async (type: string, count: number) => {
    try {
      await createTestData({
        testType: type,
        dataCount: count,
        testSessionId: `manual-test-${Date.now()}`,
      });
    } catch (error) {
      console.error('Failed to create test data:', error);
    }
  };

  const handleCleanup = async () => {
    try {
      await cleanupTestData({
        olderThan: Date.now() - (60 * 60 * 1000), // 1 hour ago
      });
    } catch (error) {
      console.error('Cleanup failed:', error);
    }
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const formatThroughput = (throughput: number) => {
    return `${throughput.toFixed(2)} ops/sec`;
  };

  const getPerformanceStatus = (successRate: number, avgDuration: number) => {
    if (successRate >= 95 && avgDuration < 1000) return { status: 'excellent', color: 'bg-green-600' };
    if (successRate >= 90 && avgDuration < 2000) return { status: 'good', color: 'bg-blue-600' };
    if (successRate >= 80 && avgDuration < 5000) return { status: 'warning', color: 'bg-yellow-600' };
    return { status: 'critical', color: 'bg-red-600' };
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white flex items-center">
          <Activity className="w-6 h-6 mr-2" />
          Load Testing Dashboard
        </h2>
        <div className="flex items-center space-x-3">
          <Button
            onClick={handleCleanup}
            variant="outline"
            size="sm"
            className="text-white border-white/30"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Cleanup Test Data
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
            className="text-white border-white/30"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Test Configuration */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <h3 className="text-xl font-bold text-white mb-4 flex items-center">
          <Play className="w-5 h-5 mr-2" />
          Load Test Configuration
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div>
            <Label htmlFor="scenarioName" className="text-white">Scenario Name</Label>
            <Input
              id="scenarioName"
              value={testConfig.scenarioName}
              onChange={(e) => setTestConfig(prev => ({ ...prev, scenarioName: e.target.value }))}
              className="bg-gray-800 border-gray-600 text-white"
            />
          </div>
          <div>
            <Label htmlFor="concurrentUsers" className="text-white">Concurrent Users</Label>
            <Input
              id="concurrentUsers"
              type="number"
              value={testConfig.concurrentUsers}
              onChange={(e) => setTestConfig(prev => ({ ...prev, concurrentUsers: parseInt(e.target.value) }))}
              className="bg-gray-800 border-gray-600 text-white"
            />
          </div>
          <div>
            <Label htmlFor="duration" className="text-white">Duration (seconds)</Label>
            <Input
              id="duration"
              type="number"
              value={testConfig.duration}
              onChange={(e) => setTestConfig(prev => ({ ...prev, duration: parseInt(e.target.value) }))}
              className="bg-gray-800 border-gray-600 text-white"
            />
          </div>
          <div className="flex items-end">
            <Button
              onClick={handleRunLoadTest}
              disabled={isRunning}
              className="w-full bg-purple-600 hover:bg-purple-700 text-white"
            >
              {isRunning ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Running...
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-2" />
                  Start Load Test
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Operation Mix */}
        <div>
          <h4 className="text-white font-semibold mb-3">Operation Mix</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {testConfig.operations.map((op, index) => (
              <div key={index} className="bg-white/5 rounded-lg p-3">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-white text-sm font-medium">{op.type}</span>
                  <Badge variant="outline" className="text-xs">
                    {op.weight}%
                  </Badge>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-purple-500 h-2 rounded-full"
                    style={{ width: `${op.weight}%` }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </Card>

      {/* Quick Test Data Creation */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <h3 className="text-xl font-bold text-white mb-4">Quick Test Data Creation</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button
            onClick={() => handleCreateTestData('sessions', 10)}
            variant="outline"
            className="text-white border-white/30"
          >
            Create 10 Test Sessions
          </Button>
          <Button
            onClick={() => handleCreateTestData('song_requests', 50)}
            variant="outline"
            className="text-white border-white/30"
          >
            Create 50 Test Requests
          </Button>
          <Button
            onClick={() => handleCreateTestData('connections', 25)}
            variant="outline"
            className="text-white border-white/30"
          >
            Create 25 Test Connections
          </Button>
        </div>
      </Card>

      {/* Test Results Overview */}
      {testResults && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Total Tests</p>
                <p className="text-2xl font-bold text-white">{testResults.metrics.totalTests}</p>
              </div>
              <BarChart3 className="w-8 h-8 text-purple-400" />
            </div>
          </Card>

          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Success Rate</p>
                <p className="text-2xl font-bold text-white">
                  {testResults.metrics.successRate?.toFixed(1) || 0}%
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-400" />
            </div>
          </Card>

          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Avg Duration</p>
                <p className="text-2xl font-bold text-white">
                  {formatDuration(testResults.metrics.avgDuration || 0)}
                </p>
              </div>
              <Clock className="w-8 h-8 text-blue-400" />
            </div>
          </Card>

          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Avg Throughput</p>
                <p className="text-2xl font-bold text-white">
                  {formatThroughput(testResults.metrics.avgThroughput || 0)}
                </p>
              </div>
              <Zap className="w-8 h-8 text-yellow-400" />
            </div>
          </Card>
        </div>
      )}

      {/* Performance by Test Type */}
      {testResults && Object.keys(testResults.metrics.byType).length > 0 && (
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
          <h3 className="text-xl font-bold text-white mb-4 flex items-center">
            <TrendingUp className="w-5 h-5 mr-2" />
            Performance by Test Type
          </h3>
          
          <div className="space-y-4">
            {Object.entries(testResults.metrics.byType).map(([type, metrics]: [string, any]) => {
              const { status, color } = getPerformanceStatus(metrics.successRate, metrics.avgDuration);
              
              return (
                <div key={type} className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <h4 className="font-semibold text-white">{type}</h4>
                      <Badge className={`${color} text-white text-xs`}>
                        {status}
                      </Badge>
                    </div>
                    <div className="text-right">
                      <p className="text-white font-semibold">{metrics.count} tests</p>
                      <p className="text-white/60 text-xs">{metrics.successRate.toFixed(1)}% success</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <p className="text-white/60">Avg Duration</p>
                      <p className="text-white font-semibold">{formatDuration(metrics.avgDuration)}</p>
                    </div>
                    <div>
                      <p className="text-white/60">Avg Throughput</p>
                      <p className="text-white font-semibold">{formatThroughput(metrics.avgThroughput)}</p>
                    </div>
                    <div>
                      <p className="text-white/60">Success Rate</p>
                      <p className="text-white font-semibold">{metrics.successRate.toFixed(1)}%</p>
                    </div>
                  </div>

                  {/* Performance bar */}
                  <div className="mt-3">
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${color}`}
                        style={{ width: `${Math.min(100, metrics.successRate)}%` }}
                      />
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </Card>
      )}

      {/* Recent Test Results */}
      {testResults && (
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
          <h3 className="text-xl font-bold text-white mb-4">Recent Test Results</h3>
          
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {testResults.results.map((result, index) => (
              <div key={index} className="flex items-center justify-between bg-white/5 rounded-lg p-3">
                <div className="flex items-center space-x-3">
                  {result.success ? (
                    <CheckCircle className="w-5 h-5 text-green-400" />
                  ) : (
                    <XCircle className="w-5 h-5 text-red-400" />
                  )}
                  <div>
                    <h4 className="font-semibold text-white">{result.testType}</h4>
                    <p className="text-white/60 text-sm">{result.operation}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center space-x-4 text-sm">
                    <span className="text-white">{formatDuration(result.duration)}</span>
                    <span className="text-white/60">{formatThroughput(result.throughput)}</span>
                    <span className="text-white/60">{result.dataCount} items</span>
                  </div>
                  <p className="text-white/60 text-xs">
                    {new Date(result.timestamp).toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))}

            {testResults.results.length === 0 && (
              <div className="text-center py-8 text-white/60">
                <Activity className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No test results yet. Run a load test to see results here.</p>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* Performance Recommendations */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <h3 className="text-xl font-bold text-white mb-4 flex items-center">
          <AlertTriangle className="w-5 h-5 mr-2" />
          Performance Recommendations
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Optimal Load Testing</h4>
                <p className="text-white/60 text-sm">
                  Start with 10-20 concurrent users and gradually increase to find your system's limits.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Monitor Key Metrics</h4>
                <p className="text-white/60 text-sm">
                  Focus on response time, throughput, and error rates during load testing.
                </p>
              </div>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Realistic Scenarios</h4>
                <p className="text-white/60 text-sm">
                  Use operation mixes that reflect real user behavior patterns.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Regular Testing</h4>
                <p className="text-white/60 text-sm">
                  Run load tests regularly to catch performance regressions early.
                </p>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
