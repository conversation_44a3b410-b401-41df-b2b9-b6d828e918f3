"use client";

import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../convex/_generated/api';
import { Id } from '../convex/_generated/dataModel';

interface SessionTemplate {
  _id: Id<"sessionTemplates">;
  name: string;
  description?: string;
  templateConfig: any;
  isPublic: boolean;
  tags: string[];
  usageCount: number;
  createdAt: number;
  creator?: {
    name: string;
    djName: string;
  } | null;
}

interface SessionTemplatesProps {
  onTemplateSelect?: (template: SessionTemplate) => void;
  showCreateButton?: boolean;
}

export default function SessionTemplates({ onTemplateSelect, showCreateButton = true }: SessionTemplatesProps) {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [includePublic, setIncludePublic] = useState(true);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  // Queries
  const templates = useQuery(api.sessionTemplates.getUserSessionTemplates, {
    includePublic,
    tags: selectedTags.length > 0 ? selectedTags : undefined,
    limit: 50,
  });

  const popularTemplates = useQuery(api.sessionTemplates.getPopularTemplates, {
    limit: 10,
  });

  // Mutations
  const createTemplate = useMutation(api.sessionTemplates.createSessionTemplate);
  const deleteTemplate = useMutation(api.sessionTemplates.deleteSessionTemplate);
  const createSessionFromTemplate = useMutation(api.sessionTemplates.createSessionFromTemplate);

  // Filter templates by search query
  const filteredTemplates = templates?.filter(template =>
    template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    template.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  ) || [];

  const handleCreateTemplate = async (templateData: any) => {
    try {
      await createTemplate(templateData);
      setShowCreateForm(false);
    } catch (error) {
      console.error('Failed to create template:', error);
    }
  };

  const handleDeleteTemplate = async (templateId: Id<"sessionTemplates">) => {
    if (confirm('Are you sure you want to delete this template?')) {
      try {
        await deleteTemplate({ templateId });
      } catch (error) {
        console.error('Failed to delete template:', error);
      }
    }
  };

  const handleUseTemplate = async (template: SessionTemplate) => {
    if (onTemplateSelect) {
      onTemplateSelect(template);
    } else {
      const sessionName = prompt('Enter session name:', `${template.name} Session`);
      if (sessionName) {
        try {
          const sessionId = await createSessionFromTemplate({
            templateId: template._id,
            sessionName,
          });
          console.log('Session created:', sessionId);
        } catch (error) {
          console.error('Failed to create session from template:', error);
        }
      }
    }
  };

  const allTags = Array.from(new Set(templates?.flatMap(t => t.tags) || []));

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Session Templates</h2>
          <p className="text-gray-600">Create, save, and reuse session configurations</p>
        </div>
        {showCreateButton && (
          <button
            onClick={() => setShowCreateForm(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Create Template
          </button>
        )}
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border space-y-4">
        <div className="flex flex-wrap gap-4">
          <div className="flex-1 min-w-64">
            <input
              type="text"
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={includePublic}
              onChange={(e) => setIncludePublic(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700">Include public templates</span>
          </label>
        </div>

        {/* Tag filters */}
        {allTags.length > 0 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Filter by tags:</label>
            <div className="flex flex-wrap gap-2">
              {allTags.map(tag => (
                <button
                  key={tag}
                  onClick={() => {
                    setSelectedTags(prev =>
                      prev.includes(tag)
                        ? prev.filter(t => t !== tag)
                        : [...prev, tag]
                    );
                  }}
                  className={`px-3 py-1 rounded-full text-sm transition-colors ${
                    selectedTags.includes(tag)
                      ? 'bg-blue-100 text-blue-800 border border-blue-300'
                      : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
                  }`}
                >
                  {tag}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Popular Templates */}
      {popularTemplates && popularTemplates.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Popular Templates</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {popularTemplates.slice(0, 6).map(template => (
              <TemplateCard
                key={template._id}
                template={template}
                onUse={() => handleUseTemplate(template)}
                onDelete={template.creator ? undefined : () => handleDeleteTemplate(template._id)}
                isPublic={true}
              />
            ))}
          </div>
        </div>
      )}

      {/* User Templates */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-3">
          Your Templates ({filteredTemplates.length})
        </h3>
        {filteredTemplates.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>No templates found.</p>
            {showCreateButton && (
              <button
                onClick={() => setShowCreateForm(true)}
                className="mt-2 text-blue-600 hover:text-blue-700"
              >
                Create your first template
              </button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTemplates.map(template => (
              <TemplateCard
                key={template._id}
                template={template}
                onUse={() => handleUseTemplate(template)}
                onDelete={template.creator ? undefined : () => handleDeleteTemplate(template._id)}
                isPublic={template.isPublic}
              />
            ))}
          </div>
        )}
      </div>

      {/* Create Template Modal */}
      {showCreateForm && (
        <CreateTemplateModal
          onClose={() => setShowCreateForm(false)}
          onSubmit={handleCreateTemplate}
        />
      )}
    </div>
  );
}

interface TemplateCardProps {
  template: SessionTemplate;
  onUse: () => void;
  onDelete?: () => void;
  isPublic: boolean;
}

function TemplateCard({ template, onUse, onDelete, isPublic }: TemplateCardProps) {
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start mb-3">
        <div className="flex-1">
          <h4 className="font-semibold text-gray-900 truncate">{template.name}</h4>
          {template.description && (
            <p className="text-sm text-gray-600 mt-1 line-clamp-2">{template.description}</p>
          )}
        </div>
        {isPublic && (
          <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full ml-2">
            Public
          </span>
        )}
      </div>

      {/* Tags */}
      {template.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-3">
          {template.tags.slice(0, 3).map(tag => (
            <span
              key={tag}
              className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded"
            >
              {tag}
            </span>
          ))}
          {template.tags.length > 3 && (
            <span className="text-xs text-gray-500">+{template.tags.length - 3} more</span>
          )}
        </div>
      )}

      {/* Stats */}
      <div className="flex justify-between items-center text-sm text-gray-500 mb-3">
        <span>Used {template.usageCount} times</span>
        {template.creator && (
          <span>by {template.creator.djName}</span>
        )}
      </div>

      {/* Actions */}
      <div className="flex gap-2">
        <button
          onClick={onUse}
          className="flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700 transition-colors"
        >
          Use Template
        </button>
        {onDelete && (
          <button
            onClick={onDelete}
            className="px-3 py-2 text-red-600 hover:bg-red-50 rounded text-sm transition-colors"
          >
            Delete
          </button>
        )}
      </div>
    </div>
  );
}

interface CreateTemplateModalProps {
  onClose: () => void;
  onSubmit: (data: any) => void;
}

function CreateTemplateModal({ onClose, onSubmit }: CreateTemplateModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isPublic: false,
    tags: '',
    templateConfig: {
      acceptRequests: true,
      autoApproval: false,
      maxRequestsPerTimeframe: 10,
      timeframeMinutes: 60,
      durationMinutes: 120,
      blockedGenres: [],
      allowedGenres: [],
      explicitContentAllowed: true,
      maxAudienceSize: 100,
      requireApprovalToJoin: false,
      allowAnonymousRequests: true,
      enableVoting: true,
      enableChat: true,
      enableReactions: true,
      moderationLevel: 'basic' as const,
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const tags = formData.tags.split(',').map(tag => tag.trim()).filter(Boolean);
    onSubmit({
      ...formData,
      tags,
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Create Session Template</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Template Name *
              </label>
              <input
                type="text"
                required
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., Wedding Reception, Club Night, Chill Session"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                placeholder="Describe when and how to use this template..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tags (comma-separated)
              </label>
              <input
                type="text"
                value={formData.tags}
                onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., wedding, party, chill, rock, electronic"
              />
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="isPublic"
                checked={formData.isPublic}
                onChange={(e) => setFormData(prev => ({ ...prev, isPublic: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="isPublic" className="text-sm text-gray-700">
                Make this template public (other DJs can use it)
              </label>
            </div>

            {/* Quick Configuration Options */}
            <div className="border-t pt-4">
              <h4 className="font-medium text-gray-900 mb-3">Quick Configuration</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm text-gray-700 mb-1">Max Requests/Hour</label>
                  <input
                    type="number"
                    value={formData.templateConfig.maxRequestsPerTimeframe}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      templateConfig: {
                        ...prev.templateConfig,
                        maxRequestsPerTimeframe: parseInt(e.target.value) || 10
                      }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-700 mb-1">Session Duration (min)</label>
                  <input
                    type="number"
                    value={formData.templateConfig.durationMinutes}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      templateConfig: {
                        ...prev.templateConfig,
                        durationMinutes: parseInt(e.target.value) || 120
                      }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div className="mt-3 space-y-2">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.templateConfig.autoApproval}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      templateConfig: {
                        ...prev.templateConfig,
                        autoApproval: e.target.checked
                      }
                    }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">Auto-approve requests</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.templateConfig.enableVoting}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      templateConfig: {
                        ...prev.templateConfig,
                        enableVoting: e.target.checked
                      }
                    }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">Enable audience voting</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.templateConfig.enableChat}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      templateConfig: {
                        ...prev.templateConfig,
                        enableChat: e.target.checked
                      }
                    }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">Enable live chat</span>
                </label>
              </div>
            </div>

            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Create Template
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
