"use client";

import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../convex/_generated/api';
import { Id } from '../convex/_generated/dataModel';

interface ScheduledSession {
  _id: Id<"scheduledSessions">;
  name: string;
  description?: string;
  scheduledStartTime: number;
  scheduledEndTime: number;
  scheduledDuration: number;
  status: "scheduled" | "active" | "completed" | "cancelled";
  template?: {
    name: string;
    description?: string;
  } | null;
  autoStart: boolean;
  autoEnd: boolean;
}

interface SessionSchedulingProps {
  onSessionStart?: (sessionId: Id<"sessions">) => void;
}

export default function SessionScheduling({ onSessionStart }: SessionSchedulingProps) {
  const [showScheduleForm, setShowScheduleForm] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'list' | 'calendar'>('list');

  // Queries
  const scheduledSessions = useQuery(api.sessionScheduling.getUserScheduledSessions, {
    status: selectedStatus === 'all' ? undefined : selectedStatus as any,
    limit: 50,
  });

  const upcomingSessions = useQuery(api.sessionScheduling.getUpcomingSessions, {
    limit: 5,
    hoursAhead: 24,
  });

  const templates = useQuery(api.sessionTemplates.getUserSessionTemplates, {
    includePublic: true,
    limit: 20,
  });

  // Mutations
  const scheduleSession = useMutation(api.sessionScheduling.scheduleSession);
  const cancelSession = useMutation(api.sessionScheduling.cancelScheduledSession);
  const startSession = useMutation(api.sessionScheduling.startScheduledSession);

  const handleScheduleSession = async (sessionData: any) => {
    try {
      await scheduleSession(sessionData);
      setShowScheduleForm(false);
    } catch (error) {
      console.error('Failed to schedule session:', error);
      alert('Failed to schedule session. Please try again.');
    }
  };

  const handleCancelSession = async (sessionId: Id<"scheduledSessions">) => {
    const reason = prompt('Reason for cancellation (optional):');
    if (reason !== null) { // User didn't cancel the prompt
      try {
        await cancelSession({ scheduledSessionId: sessionId, reason: reason || undefined });
      } catch (error) {
        console.error('Failed to cancel session:', error);
        alert('Failed to cancel session. Please try again.');
      }
    }
  };

  const handleStartSession = async (sessionId: Id<"scheduledSessions">) => {
    try {
      const newSessionId = await startSession({ scheduledSessionId: sessionId });
      if (onSessionStart) {
        onSessionStart(newSessionId);
      }
    } catch (error) {
      console.error('Failed to start session:', error);
      alert('Failed to start session. Please try again.');
    }
  };

  const formatDateTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'active': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const isSessionStartable = (session: ScheduledSession) => {
    const now = Date.now();
    const startTime = session.scheduledStartTime;
    const timeDiff = startTime - now;
    // Allow starting 15 minutes early
    return session.status === 'scheduled' && timeDiff <= (15 * 60 * 1000);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Session Scheduling</h2>
          <p className="text-gray-600">Schedule and manage your upcoming sessions</p>
        </div>
        <button
          onClick={() => setShowScheduleForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Schedule Session
        </button>
      </div>

      {/* Upcoming Sessions Alert */}
      {upcomingSessions && upcomingSessions.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-semibold text-blue-900 mb-2">Upcoming Sessions (Next 24 Hours)</h3>
          <div className="space-y-2">
            {upcomingSessions.map(session => (
              <div key={session._id} className="flex justify-between items-center">
                <div>
                  <span className="font-medium text-blue-900">{session.name}</span>
                  <span className="text-blue-700 ml-2">{session.formattedStartTime}</span>
                </div>
                {isSessionStartable(session) && (
                  <button
                    onClick={() => handleStartSession(session._id)}
                    className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors"
                  >
                    Start Now
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Filters and View Controls */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="flex flex-wrap gap-4 items-center">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status Filter</label>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Sessions</option>
              <option value="scheduled">Scheduled</option>
              <option value="active">Active</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">View Mode</label>
            <div className="flex rounded-lg border border-gray-300 overflow-hidden">
              <button
                onClick={() => setViewMode('list')}
                className={`px-3 py-2 text-sm ${
                  viewMode === 'list'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                List
              </button>
              <button
                onClick={() => setViewMode('calendar')}
                className={`px-3 py-2 text-sm ${
                  viewMode === 'calendar'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                Calendar
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Sessions List */}
      <div className="bg-white rounded-lg shadow-sm border">
        {!scheduledSessions || scheduledSessions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>No scheduled sessions found.</p>
            <button
              onClick={() => setShowScheduleForm(true)}
              className="mt-2 text-blue-600 hover:text-blue-700"
            >
              Schedule your first session
            </button>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {scheduledSessions.map(session => (
              <div key={session._id} className="p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-semibold text-gray-900">{session.name}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(session.status)}`}>
                        {session.status}
                      </span>
                      {session.template && (
                        <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs">
                          Template: {session.template.name}
                        </span>
                      )}
                    </div>
                    
                    {session.description && (
                      <p className="text-gray-600 mb-2">{session.description}</p>
                    )}
                    
                    <div className="flex flex-wrap gap-4 text-sm text-gray-500">
                      <span>📅 {formatDateTime(session.scheduledStartTime)}</span>
                      <span>⏱️ {session.scheduledDuration} minutes</span>
                      {session.autoStart && <span>🚀 Auto-start</span>}
                      {session.autoEnd && <span>🛑 Auto-end</span>}
                    </div>
                  </div>
                  
                  <div className="flex gap-2 ml-4">
                    {session.status === 'scheduled' && isSessionStartable(session) && (
                      <button
                        onClick={() => handleStartSession(session._id)}
                        className="bg-green-600 text-white px-3 py-2 rounded text-sm hover:bg-green-700 transition-colors"
                      >
                        Start
                      </button>
                    )}
                    {session.status === 'scheduled' && (
                      <button
                        onClick={() => handleCancelSession(session._id)}
                        className="bg-red-600 text-white px-3 py-2 rounded text-sm hover:bg-red-700 transition-colors"
                      >
                        Cancel
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Schedule Session Modal */}
      {showScheduleForm && (
        <ScheduleSessionModal
          onClose={() => setShowScheduleForm(false)}
          onSubmit={handleScheduleSession}
          templates={templates || []}
        />
      )}
    </div>
  );
}

interface ScheduleSessionModalProps {
  onClose: () => void;
  onSubmit: (data: any) => void;
  templates: any[];
}

function ScheduleSessionModal({ onClose, onSubmit, templates }: ScheduleSessionModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    scheduledStartTime: '',
    scheduledDuration: 120,
    templateId: '',
    autoStart: false,
    autoEnd: true,
    reminderSettings: {
      sendReminders: true,
      reminderTimes: [60, 15], // 1 hour and 15 minutes before
      notifyFollowers: false,
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const startTime = new Date(formData.scheduledStartTime).getTime();
    if (startTime <= Date.now()) {
      alert('Scheduled start time must be in the future');
      return;
    }

    onSubmit({
      ...formData,
      scheduledStartTime: startTime,
      templateId: formData.templateId || undefined,
    });
  };

  // Get minimum datetime (now + 1 hour)
  const minDateTime = new Date(Date.now() + 60 * 60 * 1000).toISOString().slice(0, 16);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Schedule Session</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Session Name *
              </label>
              <input
                type="text"
                required
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., Friday Night Mix, Wedding Reception"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                placeholder="Optional description for your session..."
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Start Time *
                </label>
                <input
                  type="datetime-local"
                  required
                  min={minDateTime}
                  value={formData.scheduledStartTime}
                  onChange={(e) => setFormData(prev => ({ ...prev, scheduledStartTime: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Duration (minutes)
                </label>
                <input
                  type="number"
                  min="30"
                  max="480"
                  value={formData.scheduledDuration}
                  onChange={(e) => setFormData(prev => ({ ...prev, scheduledDuration: parseInt(e.target.value) || 120 }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {templates.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Use Template (optional)
                </label>
                <select
                  value={formData.templateId}
                  onChange={(e) => setFormData(prev => ({ ...prev, templateId: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">No template</option>
                  {templates.map(template => (
                    <option key={template._id} value={template._id}>
                      {template.name} {template.creator ? `(by ${template.creator.djName})` : ''}
                    </option>
                  ))}
                </select>
              </div>
            )}

            <div className="space-y-2">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={formData.autoStart}
                  onChange={(e) => setFormData(prev => ({ ...prev, autoStart: e.target.checked }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Auto-start session at scheduled time</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={formData.autoEnd}
                  onChange={(e) => setFormData(prev => ({ ...prev, autoEnd: e.target.checked }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Auto-end session after duration</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={formData.reminderSettings.sendReminders}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    reminderSettings: {
                      ...prev.reminderSettings,
                      sendReminders: e.target.checked
                    }
                  }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Send reminder notifications</span>
              </label>
            </div>

            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Schedule Session
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
