"use client";

import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useAction } from "convex/react";
import { api } from "../convex/_generated/api";
import { Button } from './ui/button';
import { Card } from './ui/card';

interface PassPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  durationHours: number;
  maxRequests: number;
  popular?: boolean;
}

const PASS_PLANS: PassPlan[] = [
  {
    id: 'free',
    name: 'Free Pass',
    description: '20 minutes or 3 requests',
    price: 0,
    durationHours: 0.33,
    maxRequests: 3,
  },
  {
    id: '24hour',
    name: '24-Hour Pass',
    description: '24 hours of unlimited requests',
    price: 999,
    durationHours: 24,
    maxRequests: 100,
    popular: true,
  },
  {
    id: '48hour',
    name: '48-Hour Pass',
    description: '48 hours of unlimited requests',
    price: 1799,
    durationHours: 48,
    maxRequests: 100,
  },
  {
    id: '7day',
    name: '7-Day Pass',
    description: '7 days of unlimited requests',
    price: 4999,
    durationHours: 168,
    maxRequests: 100,
  },
];

export default function PassPurchase() {
  const [isLoading, setIsLoading] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Queries and mutations
  const activePass = useQuery(api.payments.getUserActivePass);
  const passHistory = useQuery(api.payments.getUserPassHistory, { limit: 5 });
  const createCheckoutSession = useAction(api.payments.createCheckoutSession);
  const activateFreePass = useMutation(api.payments.activateFreePass);

  const formatPrice = (priceInCents: number) => {
    if (priceInCents === 0) return 'Free';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(priceInCents / 100);
  };

  const formatTimeRemaining = (milliseconds: number) => {
    const hours = Math.floor(milliseconds / (1000 * 60 * 60));
    const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days}d ${hours % 24}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  const handlePurchase = async (planId: string) => {
    if (planId === 'free') {
      setIsLoading('free');
      setError(null);
      try {
        await activateFreePass();
      } catch (error: any) {
        setError(error.message || 'Failed to activate free pass');
      } finally {
        setIsLoading(null);
      }
      return;
    }

    setIsLoading(planId);
    setError(null);

    try {
      const result = await createCheckoutSession({
        planId: planId as any,
        successUrl: `${window.location.origin}/dashboard?payment=success`,
        cancelUrl: `${window.location.origin}/dashboard?payment=cancelled`,
      });

      if (result.success && result.url) {
        window.location.href = result.url;
      } else {
        setError(result.error || 'Failed to create checkout session');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to start checkout');
    } finally {
      setIsLoading(null);
    }
  };

  return (
    <div className="space-y-6">
      {/* Active Pass Status */}
      {activePass && (
        <Card className="bg-green-600/20 border-green-500/30 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-green-200">
                ✅ Active Pass: {activePass.plan?.name || 'Unknown Plan'}
              </h3>
              <p className="text-green-300/80">
                {activePass.timeRemaining > 0 
                  ? `${formatTimeRemaining(activePass.timeRemaining)} remaining`
                  : 'Expired'
                }
              </p>
            </div>
            <div className="text-right">
              <div className="text-green-300 font-medium">
                {activePass.plan?.description || 'Active subscription'}
              </div>
              <div className="text-green-400 text-sm">
                Status: {activePass.subscription.status}
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Error Message */}
      {error && (
        <Card className="bg-red-600/20 border-red-500/30 p-4">
          <div className="text-red-200">{error}</div>
        </Card>
      )}

      {/* Pass Plans */}
      <div>
        <h2 className="text-2xl font-bold text-white mb-6">Choose Your Pass</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {PASS_PLANS.map((plan) => (
            <Card 
              key={plan.id} 
              className={`bg-white/10 backdrop-blur-md border-white/20 p-6 relative ${
                plan.popular ? 'ring-2 ring-purple-500' : ''
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}
              
              <div className="text-center">
                <h3 className="text-xl font-bold text-white mb-2">{plan.name}</h3>
                <div className="text-3xl font-bold text-purple-300 mb-2">
                  {formatPrice(plan.price)}
                </div>
                <p className="text-white/80 text-sm mb-4">{plan.description}</p>
                
                <div className="space-y-2 mb-6">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-white/70">Duration:</span>
                    <span className="text-white">
                      {plan.durationHours < 1 
                        ? `${Math.round(plan.durationHours * 60)}min`
                        : plan.durationHours < 24
                        ? `${plan.durationHours}h`
                        : `${Math.round(plan.durationHours / 24)}d`
                      }
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-white/70">Max Requests:</span>
                    <span className="text-white">{plan.maxRequests}</span>
                  </div>
                </div>

                <Button
                  onClick={() => handlePurchase(plan.id)}
                  disabled={isLoading !== null || (activePass && plan.id !== 'free')}
                  className={`w-full ${
                    plan.popular 
                      ? 'bg-purple-600 hover:bg-purple-700' 
                      : plan.id === 'free'
                      ? 'bg-green-600 hover:bg-green-700'
                      : 'bg-blue-600 hover:bg-blue-700'
                  } text-white`}
                >
                  {isLoading === plan.id ? (
                    'Processing...'
                  ) : activePass && plan.id !== 'free' ? (
                    'Already Active'
                  ) : plan.id === 'free' ? (
                    'Start Free'
                  ) : (
                    'Purchase'
                  )}
                </Button>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Pass History */}
      {passHistory && passHistory.length > 0 && (
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
          <h3 className="text-xl font-bold text-white mb-4">Pass History</h3>
          <div className="space-y-3">
            {passHistory.map(({ subscription, plan }) => (
              <div key={subscription._id} className="bg-white/10 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-semibold text-white">
                      {plan?.name || 'Unknown Plan'}
                    </div>
                    <div className="text-white/80 text-sm">
                      {plan?.description || 'No description'}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`text-sm px-2 py-1 rounded ${
                      subscription.status === 'active' 
                        ? 'bg-green-600/20 text-green-300'
                        : subscription.status === 'past_due'
                        ? 'bg-red-600/20 text-red-300'
                        : 'bg-gray-600/20 text-gray-300'
                    }`}>
                      {subscription.status}
                    </div>
                    <div className="text-white/60 text-xs mt-1">
                      {new Date(subscription.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Features Comparison */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <h3 className="text-xl font-bold text-white mb-4">What's Included</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold text-white mb-3">All Plans Include:</h4>
            <ul className="space-y-2 text-white/80">
              <li className="flex items-center">
                <span className="text-green-400 mr-2">✓</span>
                Real-time song request management
              </li>
              <li className="flex items-center">
                <span className="text-green-400 mr-2">✓</span>
                QR code session sharing
              </li>
              <li className="flex items-center">
                <span className="text-green-400 mr-2">✓</span>
                Mobile-responsive audience interface
              </li>
              <li className="flex items-center">
                <span className="text-green-400 mr-2">✓</span>
                Session analytics and insights
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold text-white mb-3">Paid Plans Add:</h4>
            <ul className="space-y-2 text-white/80">
              <li className="flex items-center">
                <span className="text-purple-400 mr-2">✓</span>
                Extended session duration
              </li>
              <li className="flex items-center">
                <span className="text-purple-400 mr-2">✓</span>
                Higher request limits
              </li>
              <li className="flex items-center">
                <span className="text-purple-400 mr-2">✓</span>
                Priority customer support
              </li>
              <li className="flex items-center">
                <span className="text-purple-400 mr-2">✓</span>
                Advanced session customization
              </li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
}
