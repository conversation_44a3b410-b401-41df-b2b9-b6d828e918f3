"use client";

import React, { useState } from 'react';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  Activity, 
  Clock, 
  Database, 
  TrendingUp, 
  TrendingDown,
  BarChart3,
  Zap,
  AlertTriangle,
  CheckCircle,
  RefreshCw
} from 'lucide-react';

export default function PerformanceMonitor() {
  const [timeRange, setTimeRange] = useState<number>(24 * 60 * 60 * 1000); // 24 hours
  const [selectedQuery, setSelectedQuery] = useState<string>('');

  const performanceData = useQuery(
    api.performance.getPerformanceMetrics,
    { timeRange, queryName: selectedQuery || undefined }
  );

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms.toFixed(1)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getPerformanceStatus = (avgTime: number) => {
    if (avgTime < 100) return { status: 'excellent', color: 'bg-green-600', icon: CheckCircle };
    if (avgTime < 500) return { status: 'good', color: 'bg-blue-600', icon: TrendingUp };
    if (avgTime < 1000) return { status: 'warning', color: 'bg-yellow-600', icon: AlertTriangle };
    return { status: 'critical', color: 'bg-red-600', icon: TrendingDown };
  };

  const timeRangeOptions = [
    { value: 60 * 60 * 1000, label: '1 Hour' },
    { value: 6 * 60 * 60 * 1000, label: '6 Hours' },
    { value: 24 * 60 * 60 * 1000, label: '24 Hours' },
    { value: 7 * 24 * 60 * 60 * 1000, label: '7 Days' },
  ];

  if (!performanceData) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin text-white/60" />
        <span className="ml-2 text-white/60">Loading performance data...</span>
      </div>
    );
  }

  const { aggregatedMetrics, totalQueries, recentMetrics } = performanceData;
  const queryNames = Object.keys(aggregatedMetrics);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white flex items-center">
          <Activity className="w-6 h-6 mr-2" />
          Performance Monitor
        </h2>
        <div className="flex items-center space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(Number(e.target.value))}
            className="bg-gray-800 border-gray-600 text-white rounded px-3 py-1"
          >
            {timeRangeOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
            className="text-white border-white/30"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/60 text-sm">Total Queries</p>
              <p className="text-2xl font-bold text-white">{formatNumber(totalQueries)}</p>
            </div>
            <Database className="w-8 h-8 text-blue-400" />
          </div>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/60 text-sm">Query Types</p>
              <p className="text-2xl font-bold text-white">{queryNames.length}</p>
            </div>
            <BarChart3 className="w-8 h-8 text-purple-400" />
          </div>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/60 text-sm">Avg Response Time</p>
              <p className="text-2xl font-bold text-white">
                {queryNames.length > 0 
                  ? formatTime(
                      Object.values(aggregatedMetrics).reduce((sum: number, metric: any) => 
                        sum + metric.avgTime, 0
                      ) / queryNames.length
                    )
                  : '0ms'
                }
              </p>
            </div>
            <Clock className="w-8 h-8 text-green-400" />
          </div>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/60 text-sm">Performance Score</p>
              <p className="text-2xl font-bold text-white">
                {queryNames.length > 0 
                  ? Math.round(
                      Object.values(aggregatedMetrics).reduce((sum: number, metric: any) => {
                        const score = metric.avgTime < 100 ? 100 : 
                                     metric.avgTime < 500 ? 80 : 
                                     metric.avgTime < 1000 ? 60 : 40;
                        return sum + score;
                      }, 0) / queryNames.length
                    )
                  : 100
                }%
              </p>
            </div>
            <Zap className="w-8 h-8 text-yellow-400" />
          </div>
        </Card>
      </div>

      {/* Query Performance Breakdown */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-bold text-white">Query Performance Breakdown</h3>
          <select
            value={selectedQuery}
            onChange={(e) => setSelectedQuery(e.target.value)}
            className="bg-gray-800 border-gray-600 text-white rounded px-3 py-1"
          >
            <option value="">All Queries</option>
            {queryNames.map(name => (
              <option key={name} value={name}>{name}</option>
            ))}
          </select>
        </div>

        <div className="space-y-3">
          {Object.entries(aggregatedMetrics).map(([queryName, metrics]: [string, any]) => {
            const { status, color, icon: StatusIcon } = getPerformanceStatus(metrics.avgTime);
            
            return (
              <div key={queryName} className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <StatusIcon className="w-5 h-5 text-white" />
                    <h4 className="font-semibold text-white">{queryName}</h4>
                    <Badge className={`${color} text-white text-xs`}>
                      {status}
                    </Badge>
                  </div>
                  <div className="text-right">
                    <p className="text-white font-semibold">{formatTime(metrics.avgTime)}</p>
                    <p className="text-white/60 text-xs">avg response</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                  <div>
                    <p className="text-white/60">Executions</p>
                    <p className="text-white font-semibold">{formatNumber(metrics.count)}</p>
                  </div>
                  <div>
                    <p className="text-white/60">Min Time</p>
                    <p className="text-white font-semibold">{formatTime(metrics.minTime)}</p>
                  </div>
                  <div>
                    <p className="text-white/60">Max Time</p>
                    <p className="text-white font-semibold">{formatTime(metrics.maxTime)}</p>
                  </div>
                  <div>
                    <p className="text-white/60">Avg Results</p>
                    <p className="text-white font-semibold">{metrics.avgResults.toFixed(1)}</p>
                  </div>
                  <div>
                    <p className="text-white/60">Total Time</p>
                    <p className="text-white font-semibold">{formatTime(metrics.totalTime)}</p>
                  </div>
                </div>

                {/* Performance bar */}
                <div className="mt-3">
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${color}`}
                      style={{ 
                        width: `${Math.min(100, Math.max(10, 100 - (metrics.avgTime / 10)))}%` 
                      }}
                    />
                  </div>
                </div>
              </div>
            );
          })}

          {queryNames.length === 0 && (
            <div className="text-center py-8 text-white/60">
              <Activity className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No performance data available for the selected time range.</p>
              <p className="text-sm mt-2">Performance metrics will appear as queries are executed.</p>
            </div>
          )}
        </div>
      </Card>

      {/* Recent Query Activity */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <h3 className="text-xl font-bold text-white mb-4">Recent Query Activity</h3>
        
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {recentMetrics.map((metric, index) => {
            const { color } = getPerformanceStatus(metric.executionTime);
            
            return (
              <div key={index} className="flex items-center justify-between bg-white/5 rounded p-3">
                <div className="flex items-center space-x-3">
                  <div className={`w-2 h-2 rounded-full ${color}`} />
                  <span className="text-white font-medium">{metric.queryName}</span>
                  <span className="text-white/60 text-sm">
                    {new Date(metric.timestamp).toLocaleTimeString()}
                  </span>
                </div>
                <div className="flex items-center space-x-4 text-sm">
                  <span className="text-white">{formatTime(metric.executionTime)}</span>
                  <span className="text-white/60">{metric.resultCount} results</span>
                </div>
              </div>
            );
          })}

          {recentMetrics.length === 0 && (
            <div className="text-center py-4 text-white/60">
              <p>No recent query activity.</p>
            </div>
          )}
        </div>
      </Card>

      {/* Performance Tips */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <h3 className="text-xl font-bold text-white mb-4">Performance Optimization Tips</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Use Appropriate Indexes</h4>
                <p className="text-white/60 text-sm">Ensure queries use the most specific index available for optimal performance.</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Implement Pagination</h4>
                <p className="text-white/60 text-sm">Use pagination for large result sets to improve response times and user experience.</p>
              </div>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Minimize Data Transfer</h4>
                <p className="text-white/60 text-sm">Only fetch required fields and exclude large content from list views.</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Batch Operations</h4>
                <p className="text-white/60 text-sm">Use batch loading for multiple related entities to reduce query count.</p>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
