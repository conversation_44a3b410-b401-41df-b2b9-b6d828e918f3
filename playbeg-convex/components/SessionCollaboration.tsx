"use client";

import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../convex/_generated/api';
import { Id } from '../convex/_generated/dataModel';

interface SessionCollaborationProps {
  sessionId: Id<"sessions">;
  isOwner: boolean;
}

interface Collaborator {
  _id: Id<"sessionCollaborators">;
  userId: Id<"users">;
  role: "co_host" | "moderator" | "guest_dj" | "observer";
  status: "invited" | "active" | "declined" | "removed";
  permissions: {
    canManageRequests: boolean;
    canControlPlayback: boolean;
    canModerateChat: boolean;
    canInviteOthers: boolean;
    canEditSessionSettings: boolean;
    canViewAnalytics: boolean;
  };
  user?: {
    _id: Id<"users">;
    name?: string;
    email?: string;
  } | null;
  djProfile?: {
    _id: Id<"djProfiles">;
    displayName?: string;
  } | null;
  invitedAt: number;
  respondedAt?: number;
}

export default function SessionCollaboration({ sessionId, isOwner }: SessionCollaborationProps) {
  const [showInviteForm, setShowInviteForm] = useState(false);
  const [selectedCollaborator, setSelectedCollaborator] = useState<Collaborator | null>(null);

  // Queries
  const collaborators = useQuery(api.sessionCollaboration.getSessionCollaborators, {
    sessionId,
    includeInvited: true,
  });

  // Mutations
  const inviteCollaborator = useMutation(api.sessionCollaboration.inviteCollaborator);
  const respondToInvite = useMutation(api.sessionCollaboration.respondToCollaborationInvite);
  const updatePermissions = useMutation(api.sessionCollaboration.updateCollaboratorPermissions);
  const removeCollaborator = useMutation(api.sessionCollaboration.removeCollaborator);

  const handleInviteCollaborator = async (inviteData: any) => {
    try {
      await inviteCollaborator({
        sessionId,
        ...inviteData,
      });
      setShowInviteForm(false);
    } catch (error) {
      console.error('Failed to invite collaborator:', error);
      alert('Failed to send invitation. Please check the email address and try again.');
    }
  };

  const handleRespondToInvite = async (collaborationId: Id<"sessionCollaborators">, response: 'accept' | 'decline') => {
    try {
      await respondToInvite({
        collaborationId,
        response,
      });
    } catch (error) {
      console.error('Failed to respond to invitation:', error);
      alert('Failed to respond to invitation. Please try again.');
    }
  };

  const handleUpdatePermissions = async (collaborationId: Id<"sessionCollaborators">, updates: any) => {
    try {
      await updatePermissions({
        collaborationId,
        ...updates,
      });
      setSelectedCollaborator(null);
    } catch (error) {
      console.error('Failed to update permissions:', error);
      alert('Failed to update permissions. Please try again.');
    }
  };

  const handleRemoveCollaborator = async (collaborationId: Id<"sessionCollaborators">) => {
    if (confirm('Are you sure you want to remove this collaborator?')) {
      try {
        await removeCollaborator({
          collaborationId,
        });
      } catch (error) {
        console.error('Failed to remove collaborator:', error);
        alert('Failed to remove collaborator. Please try again.');
      }
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'co_host': return 'bg-purple-100 text-purple-800';
      case 'moderator': return 'bg-blue-100 text-blue-800';
      case 'guest_dj': return 'bg-green-100 text-green-800';
      case 'observer': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'invited': return 'bg-yellow-100 text-yellow-800';
      case 'declined': return 'bg-red-100 text-red-800';
      case 'removed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const activeCollaborators = collaborators?.filter(c => c.status === 'active') || [];
  const pendingInvites = collaborators?.filter(c => c.status === 'invited') || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Session Collaboration</h3>
          <p className="text-gray-600">Manage collaborators and permissions for this session</p>
        </div>
        {isOwner && (
          <button
            onClick={() => setShowInviteForm(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Invite Collaborator
          </button>
        )}
      </div>

      {/* Pending Invitations */}
      {pendingInvites.length > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="font-medium text-yellow-900 mb-3">Pending Invitations</h4>
          <div className="space-y-2">
            {pendingInvites.map(collaborator => (
              <div key={collaborator._id} className="flex justify-between items-center bg-white p-3 rounded border">
                <div>
                  <span className="font-medium text-gray-900">
                    {collaborator.djProfile?.displayName || collaborator.user?.name || 'Unknown'}
                  </span>
                  <span className="text-gray-600 ml-2">({collaborator.user?.email})</span>
                  <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getRoleColor(collaborator.role)}`}>
                    {collaborator.role.replace('_', ' ')}
                  </span>
                </div>
                <div className="flex gap-2">
                  <span className="text-sm text-gray-500">
                    Invited {new Date(collaborator.invitedAt).toLocaleDateString()}
                  </span>
                  {isOwner && (
                    <button
                      onClick={() => handleRemoveCollaborator(collaborator._id)}
                      className="text-red-600 hover:text-red-700 text-sm"
                    >
                      Cancel
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Active Collaborators */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-4 border-b">
          <h4 className="font-medium text-gray-900">
            Active Collaborators ({activeCollaborators.length})
          </h4>
        </div>
        
        {activeCollaborators.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <p>No active collaborators yet.</p>
            {isOwner && (
              <button
                onClick={() => setShowInviteForm(true)}
                className="mt-2 text-blue-600 hover:text-blue-700"
              >
                Invite your first collaborator
              </button>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {activeCollaborators.map(collaborator => (
              <div key={collaborator._id} className="p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <span className="font-medium text-gray-900">
                        {collaborator.djProfile?.displayName || collaborator.user?.name || 'Unknown'}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs ${getRoleColor(collaborator.role)}`}>
                        {collaborator.role.replace('_', ' ')}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(collaborator.status)}`}>
                        {collaborator.status}
                      </span>
                    </div>
                    
                    <div className="text-sm text-gray-600 mb-2">
                      {collaborator.user?.email}
                    </div>
                    
                    {/* Permissions Summary */}
                    <div className="flex flex-wrap gap-2">
                      {Object.entries(collaborator.permissions).map(([permission, enabled]) => {
                        if (!enabled) return null;
                        const permissionLabels: Record<string, string> = {
                          canManageRequests: 'Manage Requests',
                          canControlPlayback: 'Control Playback',
                          canModerateChat: 'Moderate Chat',
                          canInviteOthers: 'Invite Others',
                          canEditSessionSettings: 'Edit Settings',
                          canViewAnalytics: 'View Analytics',
                        };
                        return (
                          <span
                            key={permission}
                            className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs"
                          >
                            {permissionLabels[permission]}
                          </span>
                        );
                      })}
                    </div>
                  </div>
                  
                  {isOwner && (
                    <div className="flex gap-2 ml-4">
                      <button
                        onClick={() => setSelectedCollaborator(collaborator)}
                        className="text-blue-600 hover:text-blue-700 text-sm"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleRemoveCollaborator(collaborator._id)}
                        className="text-red-600 hover:text-red-700 text-sm"
                      >
                        Remove
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Invite Collaborator Modal */}
      {showInviteForm && (
        <InviteCollaboratorModal
          onClose={() => setShowInviteForm(false)}
          onSubmit={handleInviteCollaborator}
        />
      )}

      {/* Edit Permissions Modal */}
      {selectedCollaborator && (
        <EditPermissionsModal
          collaborator={selectedCollaborator}
          onClose={() => setSelectedCollaborator(null)}
          onSubmit={(updates) => handleUpdatePermissions(selectedCollaborator._id, updates)}
        />
      )}
    </div>
  );
}

interface InviteCollaboratorModalProps {
  onClose: () => void;
  onSubmit: (data: any) => void;
}

function InviteCollaboratorModal({ onClose, onSubmit }: InviteCollaboratorModalProps) {
  const [formData, setFormData] = useState({
    inviteeEmail: '',
    role: 'guest_dj' as const,
    message: '',
    permissions: {
      canManageRequests: true,
      canControlPlayback: true,
      canModerateChat: false,
      canInviteOthers: false,
      canEditSessionSettings: false,
      canViewAnalytics: false,
    },
  });

  const handleRoleChange = (role: string) => {
    const rolePermissions = {
      co_host: {
        canManageRequests: true,
        canControlPlayback: true,
        canModerateChat: true,
        canInviteOthers: true,
        canEditSessionSettings: true,
        canViewAnalytics: true,
      },
      moderator: {
        canManageRequests: true,
        canControlPlayback: false,
        canModerateChat: true,
        canInviteOthers: false,
        canEditSessionSettings: false,
        canViewAnalytics: true,
      },
      guest_dj: {
        canManageRequests: true,
        canControlPlayback: true,
        canModerateChat: false,
        canInviteOthers: false,
        canEditSessionSettings: false,
        canViewAnalytics: false,
      },
      observer: {
        canManageRequests: false,
        canControlPlayback: false,
        canModerateChat: false,
        canInviteOthers: false,
        canEditSessionSettings: false,
        canViewAnalytics: true,
      },
    };

    setFormData(prev => ({
      ...prev,
      role: role as any,
      permissions: rolePermissions[role as keyof typeof rolePermissions],
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Invite Collaborator</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email Address *
              </label>
              <input
                type="email"
                required
                value={formData.inviteeEmail}
                onChange={(e) => setFormData(prev => ({ ...prev, inviteeEmail: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Role
              </label>
              <select
                value={formData.role}
                onChange={(e) => handleRoleChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="guest_dj">Guest DJ - Can manage requests and control playback</option>
                <option value="moderator">Moderator - Can manage requests and moderate chat</option>
                <option value="co_host">Co-Host - Full control except session deletion</option>
                <option value="observer">Observer - Read-only access with analytics</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Personal Message (optional)
              </label>
              <textarea
                value={formData.message}
                onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                placeholder="Add a personal message to the invitation..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Permissions
              </label>
              <div className="space-y-2">
                {Object.entries(formData.permissions).map(([permission, enabled]) => {
                  const permissionLabels: Record<string, string> = {
                    canManageRequests: 'Manage song requests',
                    canControlPlayback: 'Control playback',
                    canModerateChat: 'Moderate chat',
                    canInviteOthers: 'Invite other collaborators',
                    canEditSessionSettings: 'Edit session settings',
                    canViewAnalytics: 'View session analytics',
                  };
                  return (
                    <label key={permission} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={enabled}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          permissions: {
                            ...prev.permissions,
                            [permission]: e.target.checked,
                          },
                        }))}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{permissionLabels[permission]}</span>
                    </label>
                  );
                })}
              </div>
            </div>

            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Send Invitation
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

interface EditPermissionsModalProps {
  collaborator: Collaborator;
  onClose: () => void;
  onSubmit: (updates: any) => void;
}

function EditPermissionsModal({ collaborator, onClose, onSubmit }: EditPermissionsModalProps) {
  const [formData, setFormData] = useState({
    role: collaborator.role,
    permissions: { ...collaborator.permissions },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-lg w-full">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">
              Edit Permissions - {collaborator.djProfile?.displayName || collaborator.user?.name}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Role
              </label>
              <select
                value={formData.role}
                onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="guest_dj">Guest DJ</option>
                <option value="moderator">Moderator</option>
                <option value="co_host">Co-Host</option>
                <option value="observer">Observer</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Permissions
              </label>
              <div className="space-y-2">
                {Object.entries(formData.permissions).map(([permission, enabled]) => {
                  const permissionLabels: Record<string, string> = {
                    canManageRequests: 'Manage song requests',
                    canControlPlayback: 'Control playback',
                    canModerateChat: 'Moderate chat',
                    canInviteOthers: 'Invite other collaborators',
                    canEditSessionSettings: 'Edit session settings',
                    canViewAnalytics: 'View session analytics',
                  };
                  return (
                    <label key={permission} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={enabled}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          permissions: {
                            ...prev.permissions,
                            [permission]: e.target.checked,
                          },
                        }))}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{permissionLabels[permission]}</span>
                    </label>
                  );
                })}
              </div>
            </div>

            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Update Permissions
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
