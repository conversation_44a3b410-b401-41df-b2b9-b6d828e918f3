"use client";

import React, { useState } from 'react';
import { useQuery, useAction } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { 
  Rocket, 
  Database, 
  CheckCircle, 
  XCircle,
  AlertTriangle,
  Clock,
  Play,
  RefreshCw,
  Shield,
  Monitor,
  FileText,
  Settings,
  Activity,
  Zap
} from 'lucide-react';

export default function ProductionDeploymentDashboard() {
  const [isRunning, setIsRunning] = useState(false);
  const [migrationConfig, setMigrationConfig] = useState({
    tables: ['users', 'djProfiles', 'sessions', 'songRequests', 'blogPosts'],
    batchSize: 100,
    validateData: true,
    dryRun: true,
  });

  const migrationStatus = useQuery(api.migration.getMigrationStatus, { limit: 20 });
  const executeMigration = useAction(api.migration.executeMigration);
  const validateProductionReadiness = useAction(api.migration.validateProductionReadiness);
  const createDeploymentChecklist = useAction(api.migration.createDeploymentChecklist);

  const [readinessCheck, setReadinessCheck] = useState<any>(null);
  const [deploymentChecklist, setDeploymentChecklist] = useState<any>(null);

  const handleRunMigration = async () => {
    setIsRunning(true);
    try {
      const result = await executeMigration({
        migrationPlan: migrationConfig,
        supabaseConfig: {
          url: 'https://your-project.supabase.co',
          key: 'your-anon-key',
        },
      });
      console.log('Migration completed:', result);
    } catch (error) {
      console.error('Migration failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const handleValidateReadiness = async () => {
    try {
      const result = await validateProductionReadiness();
      setReadinessCheck(result);
    } catch (error) {
      console.error('Readiness check failed:', error);
    }
  };

  const handleCreateChecklist = async () => {
    try {
      const result = await createDeploymentChecklist();
      setDeploymentChecklist(result);
    } catch (error) {
      console.error('Failed to create checklist:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': case 'passed': return 'bg-green-600';
      case 'in_progress': return 'bg-blue-600';
      case 'failed': return 'bg-red-600';
      case 'warning': return 'bg-yellow-600';
      default: return 'bg-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': case 'passed': return <CheckCircle className="w-4 h-4" />;
      case 'in_progress': return <Clock className="w-4 h-4" />;
      case 'failed': return <XCircle className="w-4 h-4" />;
      case 'warning': return <AlertTriangle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white flex items-center">
          <Rocket className="w-6 h-6 mr-2" />
          Production Deployment Dashboard
        </h2>
        <Button
          variant="outline"
          size="sm"
          onClick={() => window.location.reload()}
          className="text-white border-white/30"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Migration Overview */}
      {migrationStatus && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Total Migrations</p>
                <p className="text-2xl font-bold text-white">{migrationStatus.summary.totalMigrations}</p>
              </div>
              <Database className="w-8 h-8 text-purple-400" />
            </div>
          </Card>

          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Completed</p>
                <p className="text-2xl font-bold text-white">{migrationStatus.summary.completedMigrations}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-400" />
            </div>
          </Card>

          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">In Progress</p>
                <p className="text-2xl font-bold text-white">{migrationStatus.summary.inProgressMigrations}</p>
              </div>
              <Clock className="w-8 h-8 text-blue-400" />
            </div>
          </Card>

          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Failed</p>
                <p className="text-2xl font-bold text-white">{migrationStatus.summary.failedMigrations}</p>
              </div>
              <XCircle className="w-8 h-8 text-red-400" />
            </div>
          </Card>
        </div>
      )}

      {/* Migration Configuration */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <h3 className="text-xl font-bold text-white mb-4 flex items-center">
          <Database className="w-5 h-5 mr-2" />
          Data Migration Configuration
        </h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="batchSize" className="text-white">Batch Size</Label>
              <Input
                id="batchSize"
                type="number"
                value={migrationConfig.batchSize}
                onChange={(e) => setMigrationConfig(prev => ({ ...prev, batchSize: parseInt(e.target.value) }))}
                className="bg-gray-800 border-gray-600 text-white"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="validateData"
                checked={migrationConfig.validateData}
                onChange={(e) => setMigrationConfig(prev => ({ ...prev, validateData: e.target.checked }))}
                className="rounded"
              />
              <Label htmlFor="validateData" className="text-white">Validate Data</Label>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="dryRun"
                checked={migrationConfig.dryRun}
                onChange={(e) => setMigrationConfig(prev => ({ ...prev, dryRun: e.target.checked }))}
                className="rounded"
              />
              <Label htmlFor="dryRun" className="text-white">Dry Run (Test Only)</Label>
            </div>
          </div>

          <div>
            <h4 className="text-white font-semibold mb-3">Tables to Migrate</h4>
            <div className="space-y-2">
              {migrationConfig.tables.map((table, index) => (
                <div key={index} className="flex items-center justify-between bg-white/5 rounded p-2">
                  <span className="text-white">{table}</span>
                  <Badge variant="outline" className="text-xs">
                    Ready
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="mt-6">
          <Button
            onClick={handleRunMigration}
            disabled={isRunning}
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            {isRunning ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Running Migration...
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                {migrationConfig.dryRun ? 'Run Test Migration' : 'Start Production Migration'}
              </>
            )}
          </Button>
        </div>
      </Card>

      {/* Production Readiness Check */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-bold text-white flex items-center">
            <Shield className="w-5 h-5 mr-2" />
            Production Readiness Check
          </h3>
          <Button
            onClick={handleValidateReadiness}
            variant="outline"
            className="text-white border-white/30"
          >
            <Shield className="w-4 h-4 mr-2" />
            Run Readiness Check
          </Button>
        </div>

        {readinessCheck && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded ${getStatusColor(readinessCheck.overallStatus)}`}>
                  {getStatusIcon(readinessCheck.overallStatus)}
                </div>
                <div>
                  <h4 className="font-semibold text-white">Overall Status</h4>
                  <p className="text-white/60 text-sm">
                    {readinessCheck.summary.passed}/{readinessCheck.summary.total} checks passed
                  </p>
                </div>
              </div>
              <Badge className={`${getStatusColor(readinessCheck.overallStatus)} text-white`}>
                {readinessCheck.overallStatus.toUpperCase()}
              </Badge>
            </div>

            <div className="space-y-2">
              {readinessCheck.checks.map((check: any, index: number) => (
                <div key={index} className="flex items-center justify-between bg-white/5 rounded p-3">
                  <div className="flex items-center space-x-3">
                    <div className={`p-1 rounded ${getStatusColor(check.status)}`}>
                      {getStatusIcon(check.status)}
                    </div>
                    <div>
                      <h5 className="text-white font-medium">{check.name}</h5>
                      <p className="text-white/60 text-sm">{check.message}</p>
                    </div>
                  </div>
                  <Badge className={`${getStatusColor(check.status)} text-white text-xs`}>
                    {check.status}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}
      </Card>

      {/* Deployment Checklist */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-bold text-white flex items-center">
            <FileText className="w-5 h-5 mr-2" />
            Deployment Checklist
          </h3>
          <Button
            onClick={handleCreateChecklist}
            variant="outline"
            className="text-white border-white/30"
          >
            <FileText className="w-4 h-4 mr-2" />
            Generate Checklist
          </Button>
        </div>

        {deploymentChecklist && (
          <div className="space-y-6">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-2xl font-bold text-white">{deploymentChecklist.totalTasks}</p>
                <p className="text-white/60 text-sm">Total Tasks</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-white">{deploymentChecklist.criticalTasks}</p>
                <p className="text-white/60 text-sm">Critical Tasks</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-white">0</p>
                <p className="text-white/60 text-sm">Completed</p>
              </div>
            </div>

            {/* Pre-Deployment */}
            <div>
              <h4 className="text-white font-semibold mb-3">Pre-Deployment Tasks</h4>
              <div className="space-y-2">
                {deploymentChecklist.checklist.preDeployment.map((task: any, index: number) => (
                  <div key={index} className="flex items-center justify-between bg-white/5 rounded p-3">
                    <div className="flex items-center space-x-3">
                      <div className={`p-1 rounded ${getStatusColor(task.status)}`}>
                        {getStatusIcon(task.status)}
                      </div>
                      <div>
                        <h5 className="text-white font-medium">{task.task}</h5>
                        <p className="text-white/60 text-sm">{task.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {task.critical && (
                        <Badge className="bg-red-600 text-white text-xs">Critical</Badge>
                      )}
                      <Badge className={`${getStatusColor(task.status)} text-white text-xs`}>
                        {task.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Deployment */}
            <div>
              <h4 className="text-white font-semibold mb-3">Deployment Tasks</h4>
              <div className="space-y-2">
                {deploymentChecklist.checklist.deployment.map((task: any, index: number) => (
                  <div key={index} className="flex items-center justify-between bg-white/5 rounded p-3">
                    <div className="flex items-center space-x-3">
                      <div className={`p-1 rounded ${getStatusColor(task.status)}`}>
                        {getStatusIcon(task.status)}
                      </div>
                      <div>
                        <h5 className="text-white font-medium">{task.task}</h5>
                        <p className="text-white/60 text-sm">{task.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {task.critical && (
                        <Badge className="bg-red-600 text-white text-xs">Critical</Badge>
                      )}
                      <Badge className={`${getStatusColor(task.status)} text-white text-xs`}>
                        {task.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Post-Deployment */}
            <div>
              <h4 className="text-white font-semibold mb-3">Post-Deployment Tasks</h4>
              <div className="space-y-2">
                {deploymentChecklist.checklist.postDeployment.map((task: any, index: number) => (
                  <div key={index} className="flex items-center justify-between bg-white/5 rounded p-3">
                    <div className="flex items-center space-x-3">
                      <div className={`p-1 rounded ${getStatusColor(task.status)}`}>
                        {getStatusIcon(task.status)}
                      </div>
                      <div>
                        <h5 className="text-white font-medium">{task.task}</h5>
                        <p className="text-white/60 text-sm">{task.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {task.critical && (
                        <Badge className="bg-red-600 text-white text-xs">Critical</Badge>
                      )}
                      <Badge className={`${getStatusColor(task.status)} text-white text-xs`}>
                        {task.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Recent Migration Logs */}
      {migrationStatus && migrationStatus.logs.length > 0 && (
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
          <h3 className="text-xl font-bold text-white mb-4">Recent Migration Activity</h3>
          
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {migrationStatus.logs.map((log, index) => (
              <div key={index} className="flex items-start justify-between bg-white/5 rounded p-3">
                <div className="flex items-start space-x-3">
                  <Activity className="w-5 h-5 text-blue-400 mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-white">{log.eventType}</h4>
                    <p className="text-white/70 text-sm">{log.message}</p>
                    <p className="text-white/60 text-xs">
                      Migration: {log.migrationId.slice(-8)}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-white/60 text-xs">
                    {new Date(log.timestamp).toLocaleString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Deployment Guidelines */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <h3 className="text-xl font-bold text-white mb-4 flex items-center">
          <Settings className="w-5 h-5 mr-2" />
          Deployment Guidelines
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Pre-Deployment</h4>
                <p className="text-white/60 text-sm">
                  Complete all testing, backup data, and validate production environment before deployment.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Migration Process</h4>
                <p className="text-white/60 text-sm">
                  Run dry-run first, then execute production migration during low-traffic hours.
                </p>
              </div>
            </div>
          </div>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Post-Deployment</h4>
                <p className="text-white/60 text-sm">
                  Monitor system performance, validate functionality, and be ready to rollback if needed.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Monitoring</h4>
                <p className="text-white/60 text-sm">
                  Continuously monitor error rates, performance metrics, and user feedback.
                </p>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
