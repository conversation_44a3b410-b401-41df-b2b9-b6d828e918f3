"use client";

import React, { useState } from 'react';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Clock,
  Activity,
  Lock,
  Eye,
  RefreshCw,
  TrendingUp,
  Users,
  Zap
} from 'lucide-react';

export default function SecurityDashboard() {
  const [timeRange, setTimeRange] = useState<number>(24 * 60 * 60 * 1000); // 24 hours

  const securityAuditLog = useQuery(
    api.security.getSecurityAuditLog,
    { timeRange, limit: 50 }
  );

  const userPermissions = useQuery(api.security.getUserPermissions, {});

  const timeRangeOptions = [
    { value: 60 * 60 * 1000, label: '1 Hour' },
    { value: 6 * 60 * 60 * 1000, label: '6 Hours' },
    { value: 24 * 60 * 60 * 1000, label: '24 Hours' },
    { value: 7 * 24 * 60 * 60 * 1000, label: '7 Days' },
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-600';
      case 'high': return 'bg-orange-600';
      case 'medium': return 'bg-yellow-600';
      case 'low': return 'bg-blue-600';
      default: return 'bg-gray-600';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <XCircle className="w-4 h-4" />;
      case 'high': return <AlertTriangle className="w-4 h-4" />;
      case 'medium': return <Clock className="w-4 h-4" />;
      case 'low': return <CheckCircle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (!userPermissions) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin text-white/60" />
        <span className="ml-2 text-white/60">Loading security dashboard...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white flex items-center">
          <Shield className="w-6 h-6 mr-2" />
          Security Dashboard
        </h2>
        <div className="flex items-center space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(Number(e.target.value))}
            className="bg-gray-800 border-gray-600 text-white rounded px-3 py-1"
          >
            {timeRangeOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
            className="text-white border-white/30"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Security Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/60 text-sm">Security Status</p>
              <p className="text-2xl font-bold text-green-400">Secure</p>
              <p className="text-green-400 text-xs">All systems operational</p>
            </div>
            <Shield className="w-8 h-8 text-green-400" />
          </div>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/60 text-sm">Security Events</p>
              <p className="text-2xl font-bold text-white">{securityAuditLog?.totalCount || 0}</p>
              <p className="text-blue-400 text-xs">Last {timeRange / (60 * 60 * 1000)}h</p>
            </div>
            <Activity className="w-8 h-8 text-blue-400" />
          </div>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/60 text-sm">User Role</p>
              <p className="text-2xl font-bold text-white">
                {userPermissions.permissions.isDJ ? 'DJ' : 'User'}
              </p>
              <p className="text-purple-400 text-xs">
                {userPermissions.permissions.isAdmin ? 'Admin' : 'Standard'}
              </p>
            </div>
            <Users className="w-8 h-8 text-purple-400" />
          </div>
        </Card>

        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/60 text-sm">Permissions</p>
              <p className="text-2xl font-bold text-white">
                {Object.values(userPermissions.permissions).filter(Boolean).length}
              </p>
              <p className="text-yellow-400 text-xs">Active permissions</p>
            </div>
            <Lock className="w-8 h-8 text-yellow-400" />
          </div>
        </Card>
      </div>

      {/* User Permissions */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <h3 className="text-xl font-bold text-white mb-4 flex items-center">
          <Lock className="w-5 h-5 mr-2" />
          Your Permissions
        </h3>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {Object.entries(userPermissions.permissions).map(([permission, granted]) => (
            <div key={permission} className="flex items-center space-x-2">
              {granted ? (
                <CheckCircle className="w-4 h-4 text-green-400" />
              ) : (
                <XCircle className="w-4 h-4 text-red-400" />
              )}
              <span className={`text-sm ${granted ? 'text-white' : 'text-white/60'}`}>
                {permission.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
              </span>
            </div>
          ))}
        </div>
      </Card>

      {/* Security Events Log */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <h3 className="text-xl font-bold text-white mb-4 flex items-center">
          <Eye className="w-5 h-5 mr-2" />
          Security Events Log
        </h3>
        
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {securityAuditLog?.events.map((event, index) => (
            <div key={index} className="flex items-start justify-between bg-white/5 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <div className={`p-1 rounded ${getSeverityColor(event.severity)}`}>
                  {getSeverityIcon(event.severity)}
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <h4 className="font-semibold text-white">{event.eventType}</h4>
                    <Badge className={`${getSeverityColor(event.severity)} text-white text-xs`}>
                      {event.severity}
                    </Badge>
                  </div>
                  <p className="text-white/70 text-sm mb-2">{event.description}</p>
                  <div className="flex items-center space-x-4 text-xs text-white/60">
                    <span>{formatDate(event.timestamp)}</span>
                    {event.ipAddress && <span>IP: {event.ipAddress}</span>}
                    {event.userId && <span>User ID: {event.userId.slice(-8)}</span>}
                  </div>
                </div>
              </div>
            </div>
          ))}

          {(!securityAuditLog?.events || securityAuditLog.events.length === 0) && (
            <div className="text-center py-8 text-white/60">
              <Shield className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No security events recorded for the selected time range.</p>
              <p className="text-sm mt-2">This is a good sign - your system is secure!</p>
            </div>
          )}
        </div>
      </Card>

      {/* Security Recommendations */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <h3 className="text-xl font-bold text-white mb-4 flex items-center">
          <TrendingUp className="w-5 h-5 mr-2" />
          Security Recommendations
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Strong Authentication</h4>
                <p className="text-white/60 text-sm">
                  Your account is protected with secure authentication. Keep your credentials safe.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Rate Limiting Active</h4>
                <p className="text-white/60 text-sm">
                  Rate limiting is protecting your sessions from spam and abuse.
                </p>
              </div>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Content Moderation</h4>
                <p className="text-white/60 text-sm">
                  Automated content filtering helps maintain a safe environment.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-0.5" />
              <div>
                <h4 className="text-white font-semibold">Secure Data Storage</h4>
                <p className="text-white/60 text-sm">
                  Your data is encrypted and stored securely with regular backups.
                </p>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Security Best Practices */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <h3 className="text-xl font-bold text-white mb-4 flex items-center">
          <Zap className="w-5 h-5 mr-2" />
          Security Best Practices
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="bg-white/5 rounded-lg p-4">
            <h4 className="text-white font-semibold mb-2">Session Security</h4>
            <ul className="text-white/60 text-sm space-y-1">
              <li>• Use unique session names</li>
              <li>• Enable request moderation</li>
              <li>• Set appropriate audience limits</li>
              <li>• Monitor session activity</li>
            </ul>
          </div>
          <div className="bg-white/5 rounded-lg p-4">
            <h4 className="text-white font-semibold mb-2">Account Protection</h4>
            <ul className="text-white/60 text-sm space-y-1">
              <li>• Keep login credentials secure</li>
              <li>• Log out from shared devices</li>
              <li>• Review account activity regularly</li>
              <li>• Report suspicious behavior</li>
            </ul>
          </div>
          <div className="bg-white/5 rounded-lg p-4">
            <h4 className="text-white font-semibold mb-2">Content Guidelines</h4>
            <ul className="text-white/60 text-sm space-y-1">
              <li>• Moderate inappropriate requests</li>
              <li>• Use content filtering features</li>
              <li>• Report abusive users</li>
              <li>• Maintain community standards</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
}
