"use client";

import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Play, Pause, Plus, Users, Music, Calendar, Clock } from "lucide-react";
import { useState } from "react";

export function SessionManager() {
  const [isCreating, setIsCreating] = useState(false);
  
  // Get current user with profile
  const userWithProfile = useQuery(api.users.getCurrentUserWithProfile);
  const djProfile = userWithProfile?.djProfile;
  
  // For now, we'll show a placeholder since we don't have session queries yet
  // In a real implementation, we'd query sessions by DJ ID
  
  const handleCreateSession = async () => {
    setIsCreating(true);
    try {
      // TODO: Implement session creation
      console.log("Creating session...");
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
    } catch (error) {
      console.error("Failed to create session:", error);
    } finally {
      setIsCreating(false);
    }
  };

  if (!djProfile) {
    return (
      <Card className="bg-gray-800/50 border-purple-500/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Music className="w-5 h-5 mr-2" />
            Session Manager
          </CardTitle>
          <CardDescription className="text-gray-400">
            Create a DJ profile first to manage sessions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Music className="w-12 h-12 text-gray-500 mx-auto mb-4" />
            <p className="text-gray-400 mb-4">DJ profile required</p>
            <p className="text-gray-500 text-sm">
              Create your DJ profile to start managing sessions and receiving song requests.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Mock session data for demonstration
  const mockSessions = [
    {
      id: "1",
      name: "Friday Night Vibes",
      status: "active",
      createdAt: Date.now() - 3600000, // 1 hour ago
      requestCount: 12,
      maxRequests: 50,
    },
    {
      id: "2", 
      name: "Chill Sunday Session",
      status: "inactive",
      createdAt: Date.now() - 86400000, // 1 day ago
      requestCount: 8,
      maxRequests: 25,
    }
  ];

  const activeSessions = mockSessions.filter(s => s.status === "active");
  const recentSessions = mockSessions.slice(0, 3);

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-gray-800/50 border-purple-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">Create Session</h3>
              <Plus className="w-6 h-6 text-purple-400" />
            </div>
            <p className="text-gray-400 mb-4 text-sm">Start a new DJ session to receive song requests</p>
            <Button 
              onClick={handleCreateSession}
              disabled={isCreating}
              className="w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700"
            >
              {isCreating ? "Creating..." : "New Session"}
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gray-800/50 border-purple-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">Active Sessions</h3>
              <div className={`w-3 h-3 rounded-full ${activeSessions.length > 0 ? 'bg-green-400 animate-pulse' : 'bg-gray-400'}`}></div>
            </div>
            <p className="text-gray-400 mb-4 text-sm">
              {activeSessions.length} active session{activeSessions.length !== 1 ? 's' : ''}
            </p>
            <Button 
              variant="outline" 
              className="w-full border-gray-600 text-gray-300 hover:bg-gray-700"
              disabled={activeSessions.length === 0}
            >
              {activeSessions.length > 0 ? "Manage Active" : "No Active Sessions"}
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gray-800/50 border-purple-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">Total Requests</h3>
              <Users className="w-6 h-6 text-purple-400" />
            </div>
            <p className="text-gray-400 mb-4 text-sm">
              {mockSessions.reduce((sum, s) => sum + s.requestCount, 0)} total requests received
            </p>
            <Button 
              variant="outline" 
              className="w-full border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              View Analytics
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Recent Sessions */}
      <Card className="bg-gray-800/50 border-purple-500/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Calendar className="w-5 h-5 mr-2" />
            Recent Sessions
          </CardTitle>
          <CardDescription className="text-gray-400">
            Your latest DJ sessions and their status
          </CardDescription>
        </CardHeader>
        <CardContent>
          {recentSessions.length > 0 ? (
            <div className="space-y-4">
              {recentSessions.map((session) => (
                <div
                  key={session.id}
                  className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg border border-gray-600/30"
                >
                  <div className="flex items-center space-x-4">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      session.status === 'active' 
                        ? 'bg-green-500/20 text-green-400' 
                        : 'bg-gray-500/20 text-gray-400'
                    }`}>
                      {session.status === 'active' ? <Play className="w-5 h-5" /> : <Pause className="w-5 h-5" />}
                    </div>
                    <div>
                      <h4 className="font-medium text-white">{session.name}</h4>
                      <div className="flex items-center space-x-4 text-sm text-gray-400">
                        <span className="flex items-center">
                          <Clock className="w-4 h-4 mr-1" />
                          {new Date(session.createdAt).toLocaleDateString()}
                        </span>
                        <span className="flex items-center">
                          <Users className="w-4 h-4 mr-1" />
                          {session.requestCount}/{session.maxRequests} requests
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant={session.status === 'active' ? 'default' : 'secondary'}>
                      {session.status === 'active' ? 'Live' : 'Ended'}
                    </Badge>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      className="border-gray-600 text-gray-300 hover:bg-gray-700"
                    >
                      {session.status === 'active' ? 'Manage' : 'View'}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Music className="w-12 h-12 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400 mb-4">No sessions yet</p>
              <p className="text-gray-500 text-sm mb-6">
                Create your first session to start receiving song requests from your audience.
              </p>
              <Button 
                onClick={handleCreateSession}
                disabled={isCreating}
                className="bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700"
              >
                {isCreating ? "Creating..." : "Create Your First Session"}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
