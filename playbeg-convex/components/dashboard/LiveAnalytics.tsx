"use client";

import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Users, 
  Music, 
  TrendingUp, 
  Clock, 
  Activity,
  Heart,
  MessageCircle,
  ThumbsUp,
  BarChart3
} from "lucide-react";
import { useEffect, useState } from "react";

interface LiveAnalyticsProps {
  sessionId: string;
  refreshInterval?: number; // in milliseconds
}

export function LiveAnalytics({ sessionId, refreshInterval = 3000 }: LiveAnalyticsProps) {
  const [lastUpdate, setLastUpdate] = useState(Date.now());

  // Get live session analytics
  const analytics = useQuery(api.liveAnalytics.getLiveSessionAnalytics, {
    sessionId: sessionId as any,
    timeRange: "session_start",
  });

  // Get engagement trends
  const trends = useQuery(api.liveAnalytics.getEngagementTrends, {
    sessionId: sessionId as any,
    intervalMinutes: 5,
  });

  // Get reaction summary
  const reactions = useQuery(api.audienceEngagement.getReactionSummary, {
    sessionId: sessionId as any,
    timeRange: "last_hour",
  });

  // Auto-refresh data
  useEffect(() => {
    const interval = setInterval(() => {
      setLastUpdate(Date.now());
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [refreshInterval]);

  if (!analytics) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="bg-gray-800/50 border-purple-500/20">
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-700 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-700 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const formatDuration = (ms: number) => {
    const hours = Math.floor(ms / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  return (
    <div className="space-y-6">
      {/* Real-time Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-gray-800/50 border-purple-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Current Listeners</p>
                <p className="text-2xl font-bold text-white">{analytics.currentListeners}</p>
              </div>
              <div className="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center">
                <Users className="w-6 h-6 text-blue-400" />
              </div>
            </div>
            <div className="mt-2 flex items-center">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-2"></div>
              <span className="text-xs text-gray-400">Live</span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800/50 border-purple-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Song Requests</p>
                <p className="text-2xl font-bold text-white">{analytics.totalRequests}</p>
              </div>
              <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center">
                <Music className="w-6 h-6 text-purple-400" />
              </div>
            </div>
            <div className="mt-2">
              <span className="text-xs text-green-400">
                {analytics.approvalRate.toFixed(1)}% approved
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800/50 border-purple-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Engagement Rate</p>
                <p className="text-2xl font-bold text-white">{analytics.engagementRate}</p>
              </div>
              <div className="w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-green-400" />
              </div>
            </div>
            <div className="mt-2">
              <span className="text-xs text-gray-400">
                events per user/hour
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800/50 border-purple-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-400">Session Duration</p>
                <p className="text-2xl font-bold text-white">
                  {formatDuration(analytics.sessionDuration)}
                </p>
              </div>
              <div className="w-12 h-12 bg-orange-500/20 rounded-full flex items-center justify-center">
                <Clock className="w-6 h-6 text-orange-400" />
              </div>
            </div>
            <div className="mt-2">
              <span className="text-xs text-gray-400">
                {analytics.uniqueUsers} unique users
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Activity Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-gray-800/50 border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Activity className="w-5 h-5 mr-2" />
              Activity Breakdown
            </CardTitle>
            <CardDescription className="text-gray-400">
              Event types in this session
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(analytics.eventBreakdown).map(([eventType, count]) => (
                <div key={eventType} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-purple-400 rounded-full mr-3"></div>
                    <span className="text-white capitalize">
                      {eventType.replace('_', ' ')}
                    </span>
                  </div>
                  <Badge variant="secondary">{count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Reactions Summary */}
        {reactions && (
          <Card className="bg-gray-800/50 border-purple-500/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Heart className="w-5 h-5 mr-2" />
                Audience Reactions
              </CardTitle>
              <CardDescription className="text-gray-400">
                Last hour reactions ({reactions.totalReactions} total)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {reactions.topReactions.map((reaction, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <span className="text-2xl mr-3">{reaction.type}</span>
                      <span className="text-white">
                        {reaction.count} reaction{reaction.count !== 1 ? 's' : ''}
                      </span>
                    </div>
                    <div className="w-16 bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-purple-400 h-2 rounded-full"
                        style={{ 
                          width: `${(reaction.count / reactions.totalReactions) * 100}%` 
                        }}
                      ></div>
                    </div>
                  </div>
                ))}
                {reactions.topReactions.length === 0 && (
                  <p className="text-gray-400 text-center py-4">
                    No reactions yet
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Engagement Trends */}
      {trends && trends.intervals.length > 0 && (
        <Card className="bg-gray-800/50 border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <BarChart3 className="w-5 h-5 mr-2" />
              Engagement Trends
            </CardTitle>
            <CardDescription className="text-gray-400">
              Activity over time (5-minute intervals)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Simple bar chart representation */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-2xl font-bold text-white">
                    {trends.intervals.reduce((sum, interval) => sum + interval.eventCount, 0)}
                  </p>
                  <p className="text-sm text-gray-400">Total Events</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-white">
                    {Math.max(...trends.intervals.map(i => i.uniqueUsers))}
                  </p>
                  <p className="text-sm text-gray-400">Peak Users</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-white">
                    {Math.max(...trends.intervals.map(i => i.songRequests))}
                  </p>
                  <p className="text-sm text-gray-400">Peak Requests</p>
                </div>
              </div>
              
              {/* Last few intervals */}
              <div className="space-y-2">
                <p className="text-sm text-gray-400 mb-3">Recent Activity (last 4 intervals)</p>
                {trends.intervals.slice(-4).map((interval, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-700/30 rounded">
                    <span className="text-sm text-gray-300">
                      {new Date(interval.startTime).toLocaleTimeString()}
                    </span>
                    <div className="flex items-center space-x-4 text-sm">
                      <span className="text-blue-400">{interval.uniqueUsers} users</span>
                      <span className="text-purple-400">{interval.songRequests} requests</span>
                      <span className="text-green-400">{interval.eventCount} events</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Last Updated */}
      <div className="text-center">
        <p className="text-xs text-gray-500">
          Last updated: {new Date(analytics.lastUpdated).toLocaleTimeString()}
        </p>
      </div>
    </div>
  );
}
