"use client";

import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Heart, 
  MessageCircle, 
  ThumbsUp, 
  ThumbsDown,
  Send,
  Users,
  Smile
} from "lucide-react";
import { useState, useEffect } from "react";

interface AudienceEngagementProps {
  sessionId: string;
  isAudience?: boolean; // true if user is audience member, false if DJ
}

export function AudienceEngagement({ sessionId, isAudience = false }: AudienceEngagementProps) {
  const [chatMessage, setChatMessage] = useState("");
  const [selectedReaction, setSelectedReaction] = useState<string | null>(null);

  // Get session reactions
  const reactions = useQuery(api.audienceEngagement.getSessionReactions, {
    sessionId: sessionId as any,
    limit: 20,
  });

  // Get chat messages
  const chatMessages = useQuery(api.audienceEngagement.getChatMessages, {
    sessionId: sessionId as any,
    limit: 50,
  });

  // Get reaction summary
  const reactionSummary = useQuery(api.audienceEngagement.getReactionSummary, {
    sessionId: sessionId as any,
    timeRange: "last_hour",
  });

  // Mutations
  const addReaction = useMutation(api.audienceEngagement.addReaction);
  const sendChat = useMutation(api.audienceEngagement.sendChatMessage);

  const reactionEmojis = ["🔥", "❤️", "👏", "🎵", "🎉", "😍", "🤘", "💯"];

  const handleAddReaction = async (reactionType: string) => {
    try {
      await addReaction({
        sessionId: sessionId as any,
        reactionType: reactionType as any,
        targetType: "session",
      });
      setSelectedReaction(reactionType);
      setTimeout(() => setSelectedReaction(null), 1000);
    } catch (error) {
      console.error("Failed to add reaction:", error);
    }
  };

  const handleSendMessage = async () => {
    if (!chatMessage.trim()) return;

    try {
      await sendChat({
        sessionId: sessionId as any,
        message: chatMessage.trim(),
        messageType: "text",
      });
      setChatMessage("");
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="space-y-6">
      {/* Reaction Summary */}
      {reactionSummary && (
        <Card className="bg-gray-800/50 border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Heart className="w-5 h-5 mr-2" />
              Live Reactions
            </CardTitle>
            <CardDescription className="text-gray-400">
              {reactionSummary.totalReactions} reactions from {reactionSummary.uniqueReactors} people
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Quick Reaction Buttons */}
            <div className="grid grid-cols-4 md:grid-cols-8 gap-2 mb-4">
              {reactionEmojis.map((emoji) => (
                <Button
                  key={emoji}
                  variant="outline"
                  size="sm"
                  onClick={() => handleAddReaction(emoji)}
                  disabled={!isAudience}
                  className={`text-2xl h-12 border-gray-600 hover:bg-gray-700 transition-all ${
                    selectedReaction === emoji ? 'scale-125 bg-purple-600' : ''
                  }`}
                >
                  {emoji}
                </Button>
              ))}
            </div>

            {/* Top Reactions */}
            {reactionSummary.topReactions.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-400">Most Popular</h4>
                <div className="flex flex-wrap gap-2">
                  {reactionSummary.topReactions.map((reaction, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="bg-gray-700/50 text-white"
                    >
                      {reaction.type} {reaction.count}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Live Chat */}
      <Card className="bg-gray-800/50 border-purple-500/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <MessageCircle className="w-5 h-5 mr-2" />
            Live Chat
          </CardTitle>
          <CardDescription className="text-gray-400">
            Chat with other listeners
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Chat Messages */}
          <div className="h-64 overflow-y-auto mb-4 space-y-2 bg-gray-900/30 rounded-lg p-3">
            {chatMessages && chatMessages.length > 0 ? (
              chatMessages.map((message) => (
                <div key={message._id} className="flex items-start space-x-2">
                  <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                    {message.user?.name?.charAt(0) || "?"}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="text-white font-medium text-sm">
                        {message.user?.name || "Anonymous"}
                      </span>
                      <span className="text-gray-500 text-xs">{message.timeAgo}</span>
                    </div>
                    <p className="text-gray-300 text-sm">{message.message}</p>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <MessageCircle className="w-8 h-8 text-gray-500 mx-auto mb-2" />
                <p className="text-gray-400 text-sm">No messages yet</p>
                <p className="text-gray-500 text-xs">Be the first to say something!</p>
              </div>
            )}
          </div>

          {/* Chat Input */}
          {isAudience && (
            <div className="flex space-x-2">
              <input
                type="text"
                value={chatMessage}
                onChange={(e) => setChatMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type a message..."
                maxLength={500}
                className="flex-1 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
              />
              <Button
                onClick={handleSendMessage}
                disabled={!chatMessage.trim()}
                className="bg-purple-600 hover:bg-purple-700"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
          )}
          
          {!isAudience && (
            <div className="text-center py-2">
              <p className="text-gray-400 text-sm">
                Join as audience member to participate in chat
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Reactions Stream */}
      {reactions && reactions.length > 0 && (
        <Card className="bg-gray-800/50 border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Smile className="w-5 h-5 mr-2" />
              Recent Reactions
            </CardTitle>
            <CardDescription className="text-gray-400">
              Live reaction stream
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {reactions.slice(0, 10).map((reaction) => (
                <div key={reaction._id} className="flex items-center space-x-3 text-sm">
                  <span className="text-2xl">{reaction.reactionType}</span>
                  <span className="text-white">
                    {reaction.user?.name || "Anonymous"}
                  </span>
                  <span className="text-gray-400">{reaction.timeAgo}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Engagement Stats for DJ */}
      {!isAudience && reactionSummary && (
        <Card className="bg-gray-800/50 border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Users className="w-5 h-5 mr-2" />
              Audience Engagement
            </CardTitle>
            <CardDescription className="text-gray-400">
              How your audience is responding
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-white">
                  {reactionSummary.totalReactions}
                </div>
                <p className="text-sm text-gray-400">Total Reactions</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">
                  {reactionSummary.uniqueReactors}
                </div>
                <p className="text-sm text-gray-400">Active Users</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">
                  {chatMessages?.length || 0}
                </div>
                <p className="text-sm text-gray-400">Chat Messages</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">
                  {reactionSummary.topReactions.length}
                </div>
                <p className="text-sm text-gray-400">Reaction Types</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
