"use client";

import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FileUpload } from "@/components/ui/file-upload";
import { User, Music, Calendar, Settings, Camera, Upload } from "lucide-react";
import { useState } from "react";

export function DJProfileCard() {
  const [isCreating, setIsCreating] = useState(false);
  const [isCompletingSetup, setIsCompletingSetup] = useState(false);
  const [showProfilePictureUpload, setShowProfilePictureUpload] = useState(false);

  // Get current user with profile
  const userWithProfile = useQuery(api.users.getCurrentUserWithProfile);
  const userStatus = useQuery(api.users.checkUserStatus);

  // Get user's profile pictures
  const profilePictures = useQuery(api.fileStorage.getUserFiles, {
    purpose: "profile_picture",
    limit: 1,
  });

  // Mutations
  const initializeSetup = useMutation(api.users.initializeUserSetup);
  const updateProfilePicture = useMutation(api.fileStorage.updateProfilePicture);
  const completeOnboarding = useMutation(api.users.completeOnboarding);
  
  const handleCreateDJProfile = async () => {
    setIsCreating(true);
    try {
      await initializeSetup({
        createDjProfile: true,
        displayName: userWithProfile?.user?.name || "New DJ",
      });
    } catch (error) {
      console.error("Failed to create DJ profile:", error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleProfilePictureUpload = async (fileId: string, url: string) => {
    try {
      await updateProfilePicture({ storageId: fileId as any });
      setShowProfilePictureUpload(false);
    } catch (error) {
      console.error("Failed to update profile picture:", error);
    }
  };

  const handleEditProfile = () => {
    // Navigate to profile settings or open edit modal
    console.log("Edit profile clicked");
    // For now, we'll just show an alert - in a real app this would open a modal or navigate
    alert("Profile editing functionality would be implemented here. This could open a modal with editable fields for display name, bio, etc.");
  };

  const handleCompleteSetup = async () => {
    setIsCompletingSetup(true);
    try {
      const result = await completeOnboarding();
      console.log("Onboarding completed:", result);
      if (result.alreadyCompleted) {
        alert("Setup is already complete!");
      } else {
        alert("Setup completed successfully! Welcome to PlayBeg!");
      }
    } catch (error) {
      console.error("Failed to complete onboarding:", error);
      alert("Failed to complete setup. Please try again.");
    } finally {
      setIsCompletingSetup(false);
    }
  };

  const currentProfilePicture = profilePictures?.[0];

  if (!userWithProfile) {
    return (
      <Card className="bg-gray-800/50 border-purple-500/20">
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-700 rounded w-3/4 mb-2"></div>
            <div className="h-4 bg-gray-700 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { user, djProfile, hasDjProfile, onboardingComplete } = userWithProfile;

  if (!hasDjProfile) {
    return (
      <Card className="bg-gray-800/50 border-purple-500/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <User className="w-5 h-5 mr-2" />
            Create DJ Profile
          </CardTitle>
          <CardDescription className="text-gray-400">
            Set up your DJ profile to start creating sessions and receiving requests
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-purple-500/20 border border-purple-500/30 rounded-lg">
              <h4 className="text-white font-medium mb-2">Welcome, {user?.name || user?.email}!</h4>
              <p className="text-gray-300 text-sm mb-4">
                Create your DJ profile to unlock all PlayBeg features including session management, 
                song request handling, and real-time audience interaction.
              </p>
              <Button 
                onClick={handleCreateDJProfile}
                disabled={isCreating}
                className="w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700"
              >
                {isCreating ? "Creating..." : "Create DJ Profile"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-gray-800/50 border-purple-500/20">
      <CardHeader>
        <CardTitle className="text-white flex items-center justify-between">
          <div className="flex items-center">
            <Music className="w-5 h-5 mr-2" />
            DJ Profile
          </div>
          <Badge variant={onboardingComplete ? "default" : "secondary"}>
            {onboardingComplete ? "Complete" : "Setup Needed"}
          </Badge>
        </CardTitle>
        <CardDescription className="text-gray-400">
          Your DJ profile and account status
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Profile Picture Section */}
          <div className="flex items-center space-x-4">
            <div className="relative">
              <div className="w-20 h-20 bg-gray-600 rounded-full flex items-center justify-center overflow-hidden">
                {currentProfilePicture?.url ? (
                  <img
                    src={currentProfilePicture.url}
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <User className="w-8 h-8 text-gray-400" />
                )}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowProfilePictureUpload(!showProfilePictureUpload)}
                className="absolute -bottom-2 -right-2 w-8 h-8 rounded-full p-0 border-gray-600 bg-gray-800 hover:bg-gray-700"
              >
                <Camera className="w-4 h-4" />
              </Button>
            </div>
            <div className="flex-1">
              <h4 className="text-white font-medium">{djProfile?.displayName || "Unnamed DJ"}</h4>
              <p className="text-gray-400 text-sm">{user?.email || "No email"}</p>
              {currentProfilePicture && (
                <p className="text-xs text-gray-500 mt-1">
                  Picture uploaded {new Date(currentProfilePicture.uploadedAt).toLocaleDateString()}
                </p>
              )}
            </div>
          </div>

          {/* Profile Picture Upload */}
          {showProfilePictureUpload && (
            <div className="border border-gray-600 rounded-lg p-4 bg-gray-700/30">
              <h5 className="text-white font-medium mb-3 flex items-center">
                <Upload className="w-4 h-4 mr-2" />
                Update Profile Picture
              </h5>
              <FileUpload
                purpose="profile_picture"
                onUploadComplete={handleProfilePictureUpload}
                onUploadError={(error) => console.error("Profile picture upload error:", error)}
                maxSize={5 * 1024 * 1024} // 5MB limit for profile pictures
                className="mb-3"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowProfilePictureUpload(false)}
                className="border-gray-600 text-gray-300 hover:bg-gray-700"
              >
                Cancel
              </Button>
            </div>
          )}

          {/* Profile Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm text-gray-400">Display Name</label>
              <p className="text-white font-medium">{djProfile?.displayName || "Not set"}</p>
            </div>
            <div>
              <label className="text-sm text-gray-400">Email</label>
              <p className="text-white font-medium">{user?.email || "Not available"}</p>
            </div>
          </div>

          {/* Status Indicators */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-700">
            <div className="text-center">
              <div className={`w-3 h-3 rounded-full mx-auto mb-2 ${userStatus?.isAuthenticated ? 'bg-green-400' : 'bg-red-400'}`}></div>
              <p className="text-xs text-gray-400">Auth Status</p>
              <p className="text-white text-sm font-medium">{userStatus?.isAuthenticated ? 'Active' : 'Inactive'}</p>
            </div>
            <div className="text-center">
              <div className={`w-3 h-3 rounded-full mx-auto mb-2 ${userStatus?.hasDjProfile ? 'bg-green-400' : 'bg-gray-400'}`}></div>
              <p className="text-xs text-gray-400">DJ Profile</p>
              <p className="text-white text-sm font-medium">{userStatus?.hasDjProfile ? 'Created' : 'Missing'}</p>
            </div>
            <div className="text-center">
              <div className={`w-3 h-3 rounded-full mx-auto mb-2 ${onboardingComplete ? 'bg-green-400' : 'bg-orange-400'}`}></div>
              <p className="text-xs text-gray-400">Onboarding</p>
              <p className="text-white text-sm font-medium">{onboardingComplete ? 'Done' : 'Pending'}</p>
            </div>
            <div className="text-center">
              <div className="w-3 h-3 rounded-full mx-auto mb-2 bg-blue-400"></div>
              <p className="text-xs text-gray-400">Member Since</p>
              <p className="text-white text-sm font-medium">
                {djProfile ? new Date(djProfile.createdAt).toLocaleDateString() : 'N/A'}
              </p>
            </div>
          </div>

          {/* Actions */}
          <div className="flex space-x-2 pt-4">
            <Button
              variant="outline"
              size="sm"
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
              onClick={handleEditProfile}
            >
              <Settings className="w-4 h-4 mr-2" />
              Edit Profile
            </Button>
            {!onboardingComplete && (
              <Button
                size="sm"
                className="bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700"
                onClick={handleCompleteSetup}
                disabled={isCompletingSetup}
              >
                {isCompletingSetup ? "Completing..." : "Complete Setup"}
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
