"use client";

import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Bell, 
  Music, 
  Users, 
  AlertCircle, 
  TrendingUp,
  X,
  Check,
  Eye,
  ExternalLink
} from "lucide-react";
import { useState, useEffect } from "react";

interface NotificationCenterProps {
  sessionId?: string;
  showAll?: boolean;
  maxNotifications?: number;
}

export function NotificationCenter({ 
  sessionId, 
  showAll = false, 
  maxNotifications = 10 
}: NotificationCenterProps) {
  const [showNotifications, setShowNotifications] = useState(false);

  // Get notification counts
  const notificationCounts = useQuery(api.notifications.getNotificationCounts);

  // Get notifications
  const notifications = useQuery(api.notifications.getUserNotifications, {
    limit: maxNotifications,
    includeRead: showAll,
  });

  // Get notification feed for session
  const sessionNotifications = sessionId ? useQuery(api.notifications.getNotificationFeed, {
    sessionId: sessionId as any,
    limit: 5,
  }) : null;

  // Mutations
  const markAsRead = useMutation(api.notifications.markNotificationRead);
  const markAllAsRead = useMutation(api.notifications.markAllNotificationsRead);

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markAsRead({ notificationId: notificationId as any });
    } catch (error) {
      console.error("Failed to mark notification as read:", error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead({});
    } catch (error) {
      console.error("Failed to mark all notifications as read:", error);
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "song_request":
        return <Music className="w-4 h-4" />;
      case "audience_interaction":
        return <Users className="w-4 h-4" />;
      case "engagement_milestone":
        return <TrendingUp className="w-4 h-4" />;
      case "system_alert":
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <Bell className="w-4 h-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-500/20 text-red-400 border-red-500/30";
      case "high":
        return "bg-orange-500/20 text-orange-400 border-orange-500/30";
      case "medium":
        return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "low":
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "song_request":
        return "Song Request";
      case "session_status":
        return "Session";
      case "audience_interaction":
        return "Audience";
      case "system_alert":
        return "System";
      case "engagement_milestone":
        return "Milestone";
      default:
        return "Notification";
    }
  };

  return (
    <div className="relative">
      {/* Notification Bell */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setShowNotifications(!showNotifications)}
        className="relative text-gray-300 hover:text-white"
      >
        <Bell className="w-5 h-5" />
        {notificationCounts && notificationCounts.total > 0 && (
          <Badge 
            className="absolute -top-2 -right-2 w-5 h-5 p-0 flex items-center justify-center bg-red-500 text-white text-xs"
          >
            {notificationCounts.total > 99 ? "99+" : notificationCounts.total}
          </Badge>
        )}
      </Button>

      {/* Notification Dropdown */}
      {showNotifications && (
        <div className="absolute right-0 top-full mt-2 w-96 z-50">
          <Card className="bg-gray-800/95 backdrop-blur-md border-purple-500/20 shadow-xl">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-white flex items-center">
                  <Bell className="w-5 h-5 mr-2" />
                  Notifications
                </CardTitle>
                <div className="flex items-center space-x-2">
                  {notificationCounts && notificationCounts.total > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleMarkAllAsRead}
                      className="text-xs text-gray-400 hover:text-white"
                    >
                      <Check className="w-3 h-3 mr-1" />
                      Mark all read
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowNotifications(false)}
                    className="text-gray-400 hover:text-white"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              {notificationCounts && (
                <CardDescription className="text-gray-400">
                  {notificationCounts.total} unread notification{notificationCounts.total !== 1 ? 's' : ''}
                </CardDescription>
              )}
            </CardHeader>
            <CardContent className="p-0 max-h-96 overflow-y-auto">
              {notifications && notifications.length > 0 ? (
                <div className="space-y-1">
                  {notifications.map((notification) => (
                    <div
                      key={notification._id}
                      className={`p-4 border-l-4 hover:bg-gray-700/30 transition-colors ${
                        notification.isRead 
                          ? "border-gray-600 opacity-60" 
                          : getPriorityColor(notification.priority)
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-3 flex-1">
                          <div className={`mt-1 ${notification.isRead ? 'text-gray-500' : 'text-purple-400'}`}>
                            {getNotificationIcon(notification.type)}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-1">
                              <h4 className={`font-medium text-sm ${
                                notification.isRead ? 'text-gray-400' : 'text-white'
                              }`}>
                                {notification.title}
                              </h4>
                              <Badge 
                                variant="secondary" 
                                className="text-xs"
                              >
                                {getTypeLabel(notification.type)}
                              </Badge>
                            </div>
                            <p className={`text-sm ${
                              notification.isRead ? 'text-gray-500' : 'text-gray-300'
                            }`}>
                              {notification.message}
                            </p>
                            <p className="text-xs text-gray-500 mt-1">
                              {new Date(notification.createdAt).toLocaleTimeString()}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-1 ml-2">
                          {notification.actionData?.actionUrl && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                // In a real app, you'd navigate to the URL
                                console.log("Navigate to:", notification.actionData?.actionUrl);
                              }}
                              className="text-gray-400 hover:text-white p-1"
                            >
                              <ExternalLink className="w-3 h-3" />
                            </Button>
                          )}
                          {!notification.isRead && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleMarkAsRead(notification._id)}
                              className="text-gray-400 hover:text-white p-1"
                            >
                              <Eye className="w-3 h-3" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-8 text-center">
                  <Bell className="w-12 h-12 text-gray-500 mx-auto mb-4" />
                  <p className="text-gray-400 mb-2">No notifications</p>
                  <p className="text-gray-500 text-sm">
                    You're all caught up!
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Session-specific notifications (if sessionId provided) */}
      {sessionId && sessionNotifications && sessionNotifications.length > 0 && (
        <div className="mt-4">
          <Card className="bg-gray-800/50 border-purple-500/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-white text-sm">Session Activity</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {sessionNotifications.slice(0, 3).map((notification) => (
                <div
                  key={notification._id}
                  className="flex items-center space-x-3 p-2 bg-gray-700/30 rounded text-sm"
                >
                  <div className="text-purple-400">
                    {getNotificationIcon(notification.type)}
                  </div>
                  <div className="flex-1">
                    <p className="text-white">{notification.title}</p>
                    <p className="text-gray-400 text-xs">{notification.timeAgo}</p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Notification counts summary */}
      {notificationCounts && !showNotifications && (
        <div className="absolute right-0 top-full mt-1 text-xs text-gray-500">
          {notificationCounts.song_request > 0 && (
            <span className="mr-2">{notificationCounts.song_request} requests</span>
          )}
          {notificationCounts.audience_interaction > 0 && (
            <span>{notificationCounts.audience_interaction} interactions</span>
          )}
        </div>
      )}
    </div>
  );
}
