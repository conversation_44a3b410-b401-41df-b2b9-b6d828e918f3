"use client";

import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FileUpload } from "@/components/ui/file-upload";
import { 
  Image, 
  Trash2, 
  Download, 
  Eye, 
  Upload,
  HardDrive,
  FileImage,
  Calendar
} from "lucide-react";
import { useState } from "react";

interface FileManagerProps {
  purpose?: "profile_picture" | "sponsor_logo" | "session_artwork" | "other";
  showUpload?: boolean;
  maxFiles?: number;
}

export function FileManager({ 
  purpose, 
  showUpload = true, 
  maxFiles = 10 
}: FileManagerProps) {
  const [selectedPurpose, setSelectedPurpose] = useState<typeof purpose>(purpose);
  
  // Get user's files
  const userFiles = useQuery(api.fileStorage.getUserFiles, {
    purpose: selectedPurpose,
    limit: maxFiles,
  });

  // Get storage statistics
  const storageStats = useQuery(api.fileStorage.getStorageStats);

  // Delete file mutation
  const deleteFile = useMutation(api.fileStorage.deleteFile);

  const handleFileUpload = (fileId: string, url: string) => {
    console.log("File uploaded:", { fileId, url });
    // Files will automatically appear in the list due to real-time updates
  };

  const handleFileDelete = async (fileId: string) => {
    try {
      await deleteFile({ fileId });
    } catch (error) {
      console.error("Failed to delete file:", error);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const getPurposeColor = (purpose: string) => {
    switch (purpose) {
      case "profile_picture": return "bg-blue-500/20 text-blue-400";
      case "sponsor_logo": return "bg-green-500/20 text-green-400";
      case "session_artwork": return "bg-purple-500/20 text-purple-400";
      default: return "bg-gray-500/20 text-gray-400";
    }
  };

  const getPurposeLabel = (purpose: string) => {
    switch (purpose) {
      case "profile_picture": return "Profile Picture";
      case "sponsor_logo": return "Sponsor Logo";
      case "session_artwork": return "Session Artwork";
      default: return "Other";
    }
  };

  return (
    <div className="space-y-6">
      {/* Storage Statistics */}
      {storageStats && (
        <Card className="bg-gray-800/50 border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <HardDrive className="w-5 h-5 mr-2" />
              Storage Usage
            </CardTitle>
            <CardDescription className="text-gray-400">
              Your file storage statistics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-white">{storageStats.totalFiles}</div>
                <p className="text-sm text-gray-400">Total Files</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">{storageStats.totalSizeMB}</div>
                <p className="text-sm text-gray-400">MB Used</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">{storageStats.maxSizeMB}</div>
                <p className="text-sm text-gray-400">MB Limit</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">
                  {Math.round((storageStats.totalSizeMB / storageStats.maxSizeMB) * 100)}%
                </div>
                <p className="text-sm text-gray-400">Used</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* File Upload */}
      {showUpload && (
        <Card className="bg-gray-800/50 border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Upload className="w-5 h-5 mr-2" />
              Upload Files
            </CardTitle>
            <CardDescription className="text-gray-400">
              Upload images for your profile, sessions, or sponsors
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Purpose Selector */}
            {!purpose && (
              <div className="mb-4">
                <label className="text-sm text-gray-400 mb-2 block">File Purpose</label>
                <div className="flex flex-wrap gap-2">
                  {["profile_picture", "sponsor_logo", "session_artwork", "other"].map((p) => (
                    <Button
                      key={p}
                      variant={selectedPurpose === p ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedPurpose(p as any)}
                      className={selectedPurpose === p 
                        ? "bg-purple-600 hover:bg-purple-700" 
                        : "border-gray-600 text-gray-300 hover:bg-gray-700"
                      }
                    >
                      {getPurposeLabel(p)}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            <FileUpload
              purpose={selectedPurpose || "other"}
              onUploadComplete={handleFileUpload}
              onUploadError={(error) => console.error("Upload error:", error)}
            />
          </CardContent>
        </Card>
      )}

      {/* File List */}
      <Card className="bg-gray-800/50 border-purple-500/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <FileImage className="w-5 h-5 mr-2" />
            Your Files
          </CardTitle>
          <CardDescription className="text-gray-400">
            Manage your uploaded files
          </CardDescription>
        </CardHeader>
        <CardContent>
          {userFiles && userFiles.length > 0 ? (
            <div className="space-y-4">
              {userFiles.map((file) => (
                <div
                  key={file._id}
                  className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg border border-gray-600/30"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gray-600 rounded-lg flex items-center justify-center">
                      {file.url ? (
                        <img
                          src={file.url}
                          alt={file.fileName}
                          className="w-full h-full object-cover rounded-lg"
                        />
                      ) : (
                        <Image className="w-6 h-6 text-gray-400" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-white truncate">{file.fileName}</h4>
                      <div className="flex items-center space-x-4 text-sm text-gray-400">
                        <span>{formatFileSize(file.fileSize)}</span>
                        <span className="flex items-center">
                          <Calendar className="w-3 h-3 mr-1" />
                          {formatDate(file.uploadedAt)}
                        </span>
                      </div>
                    </div>
                    <Badge className={getPurposeColor(file.purpose)}>
                      {getPurposeLabel(file.purpose)}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {file.url && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.open(file.url, "_blank")}
                        className="text-gray-400 hover:text-white"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleFileDelete(file._id)}
                      className="text-gray-400 hover:text-red-400"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <FileImage className="w-12 h-12 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400 mb-2">No files uploaded yet</p>
              <p className="text-gray-500 text-sm">
                Upload your first file to get started
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
