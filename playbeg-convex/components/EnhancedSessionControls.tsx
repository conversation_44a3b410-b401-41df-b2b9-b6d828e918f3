"use client";

import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../convex/_generated/api';
import { Id } from '../convex/_generated/dataModel';

interface EnhancedSessionControlsProps {
  sessionId: Id<"sessions">;
}

interface SessionControls {
  sessionId: Id<"sessions">;
  name: string;
  active: boolean;
  isPaused: boolean;
  pausedAt?: number;
  pauseReason?: string;
  totalPausedTime: number;
  currentAudienceSize: number;
  maxAudienceSize?: number;
  requireApprovalToJoin?: boolean;
  allowAnonymousRequests?: boolean;
  allowedCountries?: string[];
  blockedCountries?: string[];
  restrictionMode?: "none" | "allowlist" | "blocklist";
  sessionBranding?: {
    primaryColor?: string;
    secondaryColor?: string;
    logoStorageId?: Id<"_storage">;
    backgroundImageStorageId?: Id<"_storage">;
    customMessage?: string;
    customCSS?: string;
  };
  isOwner: boolean;
  permissions: {
    canManageRequests: boolean;
    canControlPlayback: boolean;
    canModerateChat: boolean;
    canInviteOthers: boolean;
    canEditSessionSettings: boolean;
    canViewAnalytics: boolean;
  };
  sessionDuration: number;
  activeDuration: number;
}

export default function EnhancedSessionControls({ sessionId }: EnhancedSessionControlsProps) {
  const [showBrandingModal, setShowBrandingModal] = useState(false);
  const [showCapacityModal, setShowCapacityModal] = useState(false);
  const [showGeoModal, setShowGeoModal] = useState(false);

  // Queries
  const sessionControls = useQuery(api.enhancedSessionControls.getSessionControls, {
    sessionId,
  });

  // Mutations
  const togglePause = useMutation(api.enhancedSessionControls.toggleSessionPause);
  const updateCapacity = useMutation(api.enhancedSessionControls.updateSessionCapacity);
  const updateGeoRestrictions = useMutation(api.enhancedSessionControls.updateGeographicRestrictions);
  const updateBranding = useMutation(api.enhancedSessionControls.updateSessionBranding);
  const kickUser = useMutation(api.enhancedSessionControls.kickUserFromSession);

  if (!sessionControls) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const handleTogglePause = async () => {
    const reason = sessionControls.isPaused ? undefined : prompt('Reason for pausing (optional):');
    if (sessionControls.isPaused || reason !== null) {
      try {
        await togglePause({ sessionId, reason: reason || undefined });
      } catch (error) {
        console.error('Failed to toggle pause:', error);
        alert('Failed to update session status. Please try again.');
      }
    }
  };

  const formatDuration = (milliseconds: number) => {
    const minutes = Math.floor(milliseconds / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return hours > 0 ? `${hours}h ${remainingMinutes}m` : `${remainingMinutes}m`;
  };

  const canControlPlayback = sessionControls.isOwner || sessionControls.permissions.canControlPlayback;
  const canEditSettings = sessionControls.isOwner || sessionControls.permissions.canEditSessionSettings;
  const canModerate = sessionControls.isOwner || sessionControls.permissions.canModerateChat;

  return (
    <div className="space-y-6">
      {/* Session Status */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{sessionControls.name}</h3>
            <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                sessionControls.active 
                  ? sessionControls.isPaused 
                    ? 'bg-yellow-100 text-yellow-800' 
                    : 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {sessionControls.active 
                  ? sessionControls.isPaused ? 'Paused' : 'Live'
                  : 'Inactive'
                }
              </span>
              <span>👥 {sessionControls.currentAudienceSize} listeners</span>
              <span>⏱️ {formatDuration(sessionControls.sessionDuration)} total</span>
              <span>🎵 {formatDuration(sessionControls.activeDuration)} active</span>
            </div>
          </div>
          
          {canControlPlayback && (
            <div className="flex gap-2">
              <button
                onClick={handleTogglePause}
                className={`px-4 py-2 rounded-lg text-white transition-colors ${
                  sessionControls.isPaused
                    ? 'bg-green-600 hover:bg-green-700'
                    : 'bg-yellow-600 hover:bg-yellow-700'
                }`}
              >
                {sessionControls.isPaused ? '▶️ Resume' : '⏸️ Pause'}
              </button>
            </div>
          )}
        </div>

        {sessionControls.isPaused && sessionControls.pauseReason && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <p className="text-sm text-yellow-800">
              <strong>Paused:</strong> {sessionControls.pauseReason}
            </p>
            <p className="text-xs text-yellow-600 mt-1">
              Paused since {new Date(sessionControls.pausedAt!).toLocaleString()}
            </p>
          </div>
        )}
      </div>

      {/* Quick Controls */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Audience Controls */}
        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium text-gray-900">Audience</h4>
            {canEditSettings && (
              <button
                onClick={() => setShowCapacityModal(true)}
                className="text-blue-600 hover:text-blue-700 text-sm"
              >
                Settings
              </button>
            )}
          </div>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Current</span>
              <span className="font-medium">{sessionControls.currentAudienceSize}</span>
            </div>
            {sessionControls.maxAudienceSize && (
              <div className="flex justify-between">
                <span className="text-gray-600">Max Capacity</span>
                <span className="font-medium">{sessionControls.maxAudienceSize}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="text-gray-600">Approval Required</span>
              <span className={sessionControls.requireApprovalToJoin ? 'text-yellow-600' : 'text-green-600'}>
                {sessionControls.requireApprovalToJoin ? 'Yes' : 'No'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Anonymous Requests</span>
              <span className={sessionControls.allowAnonymousRequests ? 'text-green-600' : 'text-red-600'}>
                {sessionControls.allowAnonymousRequests ? 'Allowed' : 'Blocked'}
              </span>
            </div>
          </div>
        </div>

        {/* Geographic Controls */}
        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium text-gray-900">Geographic</h4>
            {canEditSettings && (
              <button
                onClick={() => setShowGeoModal(true)}
                className="text-blue-600 hover:text-blue-700 text-sm"
              >
                Settings
              </button>
            )}
          </div>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Restriction Mode</span>
              <span className="font-medium capitalize">
                {sessionControls.restrictionMode || 'None'}
              </span>
            </div>
            {sessionControls.allowedCountries && sessionControls.allowedCountries.length > 0 && (
              <div>
                <span className="text-gray-600">Allowed Countries</span>
                <div className="text-xs text-green-600 mt-1">
                  {sessionControls.allowedCountries.slice(0, 3).join(', ')}
                  {sessionControls.allowedCountries.length > 3 && ` +${sessionControls.allowedCountries.length - 3} more`}
                </div>
              </div>
            )}
            {sessionControls.blockedCountries && sessionControls.blockedCountries.length > 0 && (
              <div>
                <span className="text-gray-600">Blocked Countries</span>
                <div className="text-xs text-red-600 mt-1">
                  {sessionControls.blockedCountries.slice(0, 3).join(', ')}
                  {sessionControls.blockedCountries.length > 3 && ` +${sessionControls.blockedCountries.length - 3} more`}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Branding Controls */}
        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium text-gray-900">Branding</h4>
            {canEditSettings && (
              <button
                onClick={() => setShowBrandingModal(true)}
                className="text-blue-600 hover:text-blue-700 text-sm"
              >
                Customize
              </button>
            )}
          </div>
          <div className="space-y-2 text-sm">
            {sessionControls.sessionBranding?.primaryColor && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Primary Color</span>
                <div className="flex items-center gap-2">
                  <div
                    className="w-4 h-4 rounded border"
                    style={{ backgroundColor: sessionControls.sessionBranding.primaryColor }}
                  />
                  <span className="text-xs font-mono">
                    {sessionControls.sessionBranding.primaryColor}
                  </span>
                </div>
              </div>
            )}
            {sessionControls.sessionBranding?.customMessage && (
              <div>
                <span className="text-gray-600">Custom Message</span>
                <div className="text-xs text-gray-500 mt-1 truncate">
                  {sessionControls.sessionBranding.customMessage}
                </div>
              </div>
            )}
            {!sessionControls.sessionBranding && (
              <div className="text-gray-500 text-center py-2">
                No custom branding
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modals */}
      {showCapacityModal && (
        <CapacitySettingsModal
          sessionId={sessionId}
          currentSettings={{
            maxAudienceSize: sessionControls.maxAudienceSize,
            requireApprovalToJoin: sessionControls.requireApprovalToJoin,
            allowAnonymousRequests: sessionControls.allowAnonymousRequests,
          }}
          onClose={() => setShowCapacityModal(false)}
          onUpdate={updateCapacity}
        />
      )}

      {showGeoModal && (
        <GeographicSettingsModal
          sessionId={sessionId}
          currentSettings={{
            allowedCountries: sessionControls.allowedCountries,
            blockedCountries: sessionControls.blockedCountries,
            restrictionMode: sessionControls.restrictionMode,
          }}
          onClose={() => setShowGeoModal(false)}
          onUpdate={updateGeoRestrictions}
        />
      )}

      {showBrandingModal && (
        <BrandingSettingsModal
          sessionId={sessionId}
          currentBranding={sessionControls.sessionBranding}
          onClose={() => setShowBrandingModal(false)}
          onUpdate={updateBranding}
        />
      )}
    </div>
  );
}

interface CapacitySettingsModalProps {
  sessionId: Id<"sessions">;
  currentSettings: {
    maxAudienceSize?: number;
    requireApprovalToJoin?: boolean;
    allowAnonymousRequests?: boolean;
  };
  onClose: () => void;
  onUpdate: (args: any) => Promise<any>;
}

function CapacitySettingsModal({ sessionId, currentSettings, onClose, onUpdate }: CapacitySettingsModalProps) {
  const [formData, setFormData] = useState({
    maxAudienceSize: currentSettings.maxAudienceSize || 0,
    requireApprovalToJoin: currentSettings.requireApprovalToJoin || false,
    allowAnonymousRequests: currentSettings.allowAnonymousRequests !== false,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await onUpdate({
        sessionId,
        maxAudienceSize: formData.maxAudienceSize > 0 ? formData.maxAudienceSize : undefined,
        requireApprovalToJoin: formData.requireApprovalToJoin,
        allowAnonymousRequests: formData.allowAnonymousRequests,
      });
      onClose();
    } catch (error) {
      console.error('Failed to update capacity settings:', error);
      alert('Failed to update settings. Please try again.');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Audience Settings</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Maximum Audience Size
              </label>
              <input
                type="number"
                min="0"
                value={formData.maxAudienceSize}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  maxAudienceSize: parseInt(e.target.value) || 0 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="0 = unlimited"
              />
              <p className="text-xs text-gray-500 mt-1">Set to 0 for unlimited capacity</p>
            </div>

            <div className="space-y-3">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={formData.requireApprovalToJoin}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    requireApprovalToJoin: e.target.checked 
                  }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Require approval to join session</span>
              </label>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={formData.allowAnonymousRequests}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    allowAnonymousRequests: e.target.checked 
                  }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Allow anonymous song requests</span>
              </label>
            </div>

            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Update Settings
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

interface GeographicSettingsModalProps {
  sessionId: Id<"sessions">;
  currentSettings: {
    allowedCountries?: string[];
    blockedCountries?: string[];
    restrictionMode?: "none" | "allowlist" | "blocklist";
  };
  onClose: () => void;
  onUpdate: (args: any) => Promise<any>;
}

function GeographicSettingsModal({ sessionId, currentSettings, onClose, onUpdate }: GeographicSettingsModalProps) {
  const [formData, setFormData] = useState({
    restrictionMode: currentSettings.restrictionMode || 'none',
    allowedCountries: (currentSettings.allowedCountries || []).join(', '),
    blockedCountries: (currentSettings.blockedCountries || []).join(', '),
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await onUpdate({
        sessionId,
        restrictionMode: formData.restrictionMode,
        allowedCountries: formData.allowedCountries 
          ? formData.allowedCountries.split(',').map(c => c.trim()).filter(Boolean)
          : undefined,
        blockedCountries: formData.blockedCountries 
          ? formData.blockedCountries.split(',').map(c => c.trim()).filter(Boolean)
          : undefined,
      });
      onClose();
    } catch (error) {
      console.error('Failed to update geographic settings:', error);
      alert('Failed to update settings. Please try again.');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Geographic Restrictions</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Restriction Mode
              </label>
              <select
                value={formData.restrictionMode}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  restrictionMode: e.target.value as any 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="none">No restrictions</option>
                <option value="allowlist">Allow only specific countries</option>
                <option value="blocklist">Block specific countries</option>
              </select>
            </div>

            {formData.restrictionMode === 'allowlist' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Allowed Countries
                </label>
                <input
                  type="text"
                  value={formData.allowedCountries}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    allowedCountries: e.target.value 
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="US, CA, GB, AU (comma-separated country codes)"
                />
                <p className="text-xs text-gray-500 mt-1">Use ISO 3166-1 alpha-2 country codes</p>
              </div>
            )}

            {formData.restrictionMode === 'blocklist' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Blocked Countries
                </label>
                <input
                  type="text"
                  value={formData.blockedCountries}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    blockedCountries: e.target.value 
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="CN, RU, IR (comma-separated country codes)"
                />
                <p className="text-xs text-gray-500 mt-1">Use ISO 3166-1 alpha-2 country codes</p>
              </div>
            )}

            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Update Settings
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

interface BrandingSettingsModalProps {
  sessionId: Id<"sessions">;
  currentBranding?: {
    primaryColor?: string;
    secondaryColor?: string;
    logoStorageId?: Id<"_storage">;
    backgroundImageStorageId?: Id<"_storage">;
    customMessage?: string;
    customCSS?: string;
  };
  onClose: () => void;
  onUpdate: (args: any) => Promise<any>;
}

function BrandingSettingsModal({ sessionId, currentBranding, onClose, onUpdate }: BrandingSettingsModalProps) {
  const [formData, setFormData] = useState({
    primaryColor: currentBranding?.primaryColor || '#3B82F6',
    secondaryColor: currentBranding?.secondaryColor || '#1F2937',
    customMessage: currentBranding?.customMessage || '',
    customCSS: currentBranding?.customCSS || '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await onUpdate({
        sessionId,
        branding: formData,
      });
      onClose();
    } catch (error) {
      console.error('Failed to update branding:', error);
      alert('Failed to update branding. Please try again.');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-lg w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Session Branding</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Primary Color
                </label>
                <div className="flex gap-2">
                  <input
                    type="color"
                    value={formData.primaryColor}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      primaryColor: e.target.value 
                    }))}
                    className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                  />
                  <input
                    type="text"
                    value={formData.primaryColor}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      primaryColor: e.target.value 
                    }))}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Secondary Color
                </label>
                <div className="flex gap-2">
                  <input
                    type="color"
                    value={formData.secondaryColor}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      secondaryColor: e.target.value 
                    }))}
                    className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                  />
                  <input
                    type="text"
                    value={formData.secondaryColor}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      secondaryColor: e.target.value 
                    }))}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
                  />
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Custom Message
              </label>
              <textarea
                value={formData.customMessage}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  customMessage: e.target.value 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                placeholder="Welcome message or instructions for your audience..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Custom CSS (Advanced)
              </label>
              <textarea
                value={formData.customCSS}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  customCSS: e.target.value 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
                rows={4}
                placeholder=".session-container { background: linear-gradient(...); }"
              />
              <p className="text-xs text-gray-500 mt-1">
                Advanced users can add custom CSS to further customize the session appearance
              </p>
            </div>

            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Update Branding
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
