"use client";

import React, { useState, useEffect } from 'react';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Button } from '../ui/button';
import { Card } from '../ui/card';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Badge } from '../ui/badge';
import { 
  Search, 
  Filter, 
  X, 
  Music, 
  Calendar, 
  FileText,
  Users,
  Play,
  Eye,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

interface SearchFilters {
  contentType: 'sessions' | 'songRequests' | 'blogPosts' | 'all';
  searchQuery: string;
  // Session filters
  active?: boolean;
  weddingMode?: boolean;
  // Song request filters
  status?: string;
  artistName?: string;
  // Blog post filters
  category?: string;
  author?: string;
  featured?: boolean;
}

export default function AdvancedSearchInterface() {
  const [filters, setFilters] = useState<SearchFilters>({
    contentType: 'all',
    searchQuery: '',
  });
  const [showFilters, setShowFilters] = useState(false);
  const [searchResults, setSearchResults] = useState<any>(null);

  // Search queries
  const globalResults = useQuery(
    api.search.globalSearch,
    filters.contentType === 'all' && filters.searchQuery.length > 0
      ? { 
          searchQuery: filters.searchQuery,
          limit: 20 
        }
      : "skip"
  );

  const sessionResults = useQuery(
    api.search.searchSessions,
    filters.contentType === 'sessions' && filters.searchQuery.length > 0
      ? {
          searchQuery: filters.searchQuery,
          active: filters.active,
          weddingMode: filters.weddingMode,
          limit: 20
        }
      : "skip"
  );

  const songRequestResults = useQuery(
    api.search.searchSongRequests,
    filters.contentType === 'songRequests' && filters.searchQuery.length > 0
      ? {
          searchQuery: filters.searchQuery,
          status: filters.status as any,
          artistName: filters.artistName,
          limit: 20
        }
      : "skip"
  );

  const blogPostResults = useQuery(
    api.search.searchBlogPosts,
    filters.contentType === 'blogPosts' && filters.searchQuery.length > 0
      ? {
          searchQuery: filters.searchQuery,
          category: filters.category,
          author: filters.author,
          featured: filters.featured,
          limit: 20
        }
      : "skip"
  );

  const filterOptions = useQuery(
    api.search.getFilterOptions,
    filters.contentType !== 'all' ? { type: filters.contentType } : "skip"
  );

  // Update search results when queries change
  useEffect(() => {
    if (filters.contentType === 'all') {
      setSearchResults(globalResults);
    } else if (filters.contentType === 'sessions') {
      setSearchResults(sessionResults);
    } else if (filters.contentType === 'songRequests') {
      setSearchResults(songRequestResults);
    } else if (filters.contentType === 'blogPosts') {
      setSearchResults(blogPostResults);
    }
  }, [globalResults, sessionResults, songRequestResults, blogPostResults, filters.contentType]);

  const handleSearch = (query: string) => {
    setFilters(prev => ({ ...prev, searchQuery: query }));
  };

  const clearFilters = () => {
    setFilters({
      contentType: 'all',
      searchQuery: '',
    });
    setSearchResults(null);
  };

  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'sessions': return <Music className="w-4 h-4" />;
      case 'songRequests': return <Play className="w-4 h-4" />;
      case 'blogPosts': return <FileText className="w-4 h-4" />;
      default: return <Search className="w-4 h-4" />;
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const renderSearchResult = (result: any, type: string) => {
    return (
      <Card key={result._id} className="bg-white/10 backdrop-blur-md border-white/20 p-4 hover:bg-white/20 transition-colors">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              {getContentTypeIcon(type)}
              <h3 className="font-semibold text-white">
                {result.title || result.name || result.songTitle}
              </h3>
              <Badge variant="outline" className="text-xs">
                {type === 'sessions' ? 'Session' : 
                 type === 'songRequests' ? 'Song Request' : 
                 type === 'blogPosts' ? 'Blog Post' : type}
              </Badge>
            </div>
            
            <p className="text-white/70 text-sm mb-2">
              {result.description || result.excerpt || 
               (type === 'songRequests' ? `by ${result.artistName}` : '')}
            </p>
            
            <div className="flex items-center space-x-4 text-xs text-white/60">
              {result.createdAt && (
                <span className="flex items-center">
                  <Calendar className="w-3 h-3 mr-1" />
                  {formatDate(result.createdAt)}
                </span>
              )}
              {result.status && (
                <Badge className="text-xs" variant={
                  result.status === 'active' || result.status === 'published' ? 'default' : 'secondary'
                }>
                  {result.status}
                </Badge>
              )}
              {result.viewCount !== undefined && (
                <span className="flex items-center">
                  <Eye className="w-3 h-3 mr-1" />
                  {result.viewCount} views
                </span>
              )}
            </div>
          </div>
        </div>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Search Header */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <div className="space-y-4">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-5 h-5" />
            <Input
              value={filters.searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              placeholder="Search sessions, song requests, blog posts..."
              className="pl-10 bg-gray-800 border-gray-600 text-white"
            />
          </div>

          {/* Content Type Selector */}
          <div className="flex items-center space-x-2">
            <Label className="text-white text-sm">Search in:</Label>
            <div className="flex space-x-2">
              {[
                { value: 'all', label: 'All Content', icon: Search },
                { value: 'sessions', label: 'Sessions', icon: Music },
                { value: 'songRequests', label: 'Song Requests', icon: Play },
                { value: 'blogPosts', label: 'Blog Posts', icon: FileText },
              ].map((type) => {
                const Icon = type.icon;
                return (
                  <button
                    key={type.value}
                    onClick={() => setFilters(prev => ({ ...prev, contentType: type.value as any }))}
                    className={`flex items-center space-x-1 px-3 py-1 rounded-md text-sm transition-colors ${
                      filters.contentType === type.value
                        ? 'bg-purple-600 text-white'
                        : 'bg-gray-700 text-white/70 hover:bg-gray-600'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{type.label}</span>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Advanced Filters Toggle */}
          {filters.contentType !== 'all' && (
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="text-white border-white/30"
              >
                <Filter className="w-4 h-4 mr-2" />
                Advanced Filters
                {showFilters ? <ChevronUp className="w-4 h-4 ml-2" /> : <ChevronDown className="w-4 h-4 ml-2" />}
              </Button>
              
              {Object.keys(filters).length > 2 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearFilters}
                  className="text-white border-white/30"
                >
                  <X className="w-4 h-4 mr-2" />
                  Clear Filters
                </Button>
              )}
            </div>
          )}

          {/* Advanced Filters Panel */}
          {showFilters && filters.contentType !== 'all' && filterOptions && (
            <div className="bg-white/5 rounded-lg p-4 space-y-4">
              <h4 className="font-semibold text-white">Advanced Filters</h4>
              
              {/* Session Filters */}
              {filters.contentType === 'sessions' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-white text-sm">Status</Label>
                    <select
                      value={filters.active?.toString() || ''}
                      onChange={(e) => setFilters(prev => ({ 
                        ...prev, 
                        active: e.target.value ? e.target.value === 'true' : undefined 
                      }))}
                      className="w-full bg-gray-800 border-gray-600 text-white rounded px-3 py-1"
                    >
                      <option value="">All Statuses</option>
                      <option value="true">Active</option>
                      <option value="false">Inactive</option>
                    </select>
                  </div>
                  <div>
                    <Label className="text-white text-sm">Mode</Label>
                    <select
                      value={filters.weddingMode?.toString() || ''}
                      onChange={(e) => setFilters(prev => ({ 
                        ...prev, 
                        weddingMode: e.target.value ? e.target.value === 'true' : undefined 
                      }))}
                      className="w-full bg-gray-800 border-gray-600 text-white rounded px-3 py-1"
                    >
                      <option value="">All Modes</option>
                      <option value="true">Wedding Mode</option>
                      <option value="false">Regular Mode</option>
                    </select>
                  </div>
                </div>
              )}

              {/* Song Request Filters */}
              {filters.contentType === 'songRequests' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-white text-sm">Status</Label>
                    <select
                      value={filters.status || ''}
                      onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value || undefined }))}
                      className="w-full bg-gray-800 border-gray-600 text-white rounded px-3 py-1"
                    >
                      <option value="">All Statuses</option>
                      {filterOptions.statuses?.map((status: any) => (
                        <option key={status.value} value={status.value}>{status.label}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <Label className="text-white text-sm">Artist</Label>
                    <Input
                      value={filters.artistName || ''}
                      onChange={(e) => setFilters(prev => ({ ...prev, artistName: e.target.value || undefined }))}
                      placeholder="Filter by artist..."
                      className="bg-gray-800 border-gray-600 text-white"
                    />
                  </div>
                </div>
              )}

              {/* Blog Post Filters */}
              {filters.contentType === 'blogPosts' && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label className="text-white text-sm">Category</Label>
                    <select
                      value={filters.category || ''}
                      onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value || undefined }))}
                      className="w-full bg-gray-800 border-gray-600 text-white rounded px-3 py-1"
                    >
                      <option value="">All Categories</option>
                      {filterOptions.categories?.map((cat: any) => (
                        <option key={cat.value} value={cat.value}>{cat.label}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <Label className="text-white text-sm">Author</Label>
                    <select
                      value={filters.author || ''}
                      onChange={(e) => setFilters(prev => ({ ...prev, author: e.target.value || undefined }))}
                      className="w-full bg-gray-800 border-gray-600 text-white rounded px-3 py-1"
                    >
                      <option value="">All Authors</option>
                      {filterOptions.authors?.map((author: any) => (
                        <option key={author.value} value={author.value}>{author.label}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <Label className="text-white text-sm">Featured</Label>
                    <select
                      value={filters.featured?.toString() || ''}
                      onChange={(e) => setFilters(prev => ({ 
                        ...prev, 
                        featured: e.target.value ? e.target.value === 'true' : undefined 
                      }))}
                      className="w-full bg-gray-800 border-gray-600 text-white rounded px-3 py-1"
                    >
                      <option value="">All Posts</option>
                      <option value="true">Featured Only</option>
                      <option value="false">Regular Only</option>
                    </select>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </Card>

      {/* Search Results */}
      {searchResults && filters.searchQuery.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-bold text-white">
              Search Results
              {searchResults.totalResults !== undefined && (
                <span className="text-white/60 ml-2">({searchResults.totalResults} found)</span>
              )}
            </h3>
          </div>

          {/* Global Search Results */}
          {filters.contentType === 'all' && searchResults.totalResults > 0 && (
            <div className="space-y-6">
              {searchResults.sessions?.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-white mb-3 flex items-center">
                    <Music className="w-5 h-5 mr-2" />
                    Sessions ({searchResults.sessions.length})
                  </h4>
                  <div className="space-y-3">
                    {searchResults.sessions.map((result: any) => renderSearchResult(result, 'sessions'))}
                  </div>
                </div>
              )}

              {searchResults.songRequests?.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-white mb-3 flex items-center">
                    <Play className="w-5 h-5 mr-2" />
                    Song Requests ({searchResults.songRequests.length})
                  </h4>
                  <div className="space-y-3">
                    {searchResults.songRequests.map((result: any) => renderSearchResult(result, 'songRequests'))}
                  </div>
                </div>
              )}

              {searchResults.blogPosts?.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-white mb-3 flex items-center">
                    <FileText className="w-5 h-5 mr-2" />
                    Blog Posts ({searchResults.blogPosts.length})
                  </h4>
                  <div className="space-y-3">
                    {searchResults.blogPosts.map((result: any) => renderSearchResult(result, 'blogPosts'))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Specific Content Type Results */}
          {filters.contentType !== 'all' && Array.isArray(searchResults) && (
            <div className="space-y-3">
              {searchResults.map((result: any) => renderSearchResult(result, filters.contentType))}
            </div>
          )}

          {/* No Results */}
          {((filters.contentType === 'all' && searchResults.totalResults === 0) ||
            (filters.contentType !== 'all' && Array.isArray(searchResults) && searchResults.length === 0)) && (
            <Card className="bg-white/10 backdrop-blur-md border-white/20 p-8 text-center">
              <Search className="w-12 h-12 text-white/40 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">No results found</h3>
              <p className="text-white/60">
                Try adjusting your search terms or filters to find what you're looking for.
              </p>
            </Card>
          )}
        </div>
      )}

      {/* Empty State */}
      {!searchResults && filters.searchQuery.length === 0 && (
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-8 text-center">
          <Search className="w-16 h-16 text-white/40 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">Advanced Search</h3>
          <p className="text-white/60 mb-4">
            Search across sessions, song requests, and blog posts with powerful filtering options.
          </p>
          <div className="flex items-center justify-center space-x-4 text-sm text-white/60">
            <span className="flex items-center">
              <Music className="w-4 h-4 mr-1" />
              Sessions
            </span>
            <span className="flex items-center">
              <Play className="w-4 h-4 mr-1" />
              Song Requests
            </span>
            <span className="flex items-center">
              <FileText className="w-4 h-4 mr-1" />
              Blog Posts
            </span>
          </div>
        </Card>
      )}
    </div>
  );
}
