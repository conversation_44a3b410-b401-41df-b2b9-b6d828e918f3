"use client";

import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../convex/_generated/api';
import { Id } from '../convex/_generated/dataModel';
import SessionTemplates from './SessionTemplates';
import SessionScheduling from './SessionScheduling';
import SessionAnalyticsDashboard from './SessionAnalyticsDashboard';
import SessionCollaboration from './SessionCollaboration';
import EnhancedSessionControls from './EnhancedSessionControls';

interface AdvancedSessionManagementDashboardProps {
  currentSessionId?: Id<"sessions">;
}

export default function AdvancedSessionManagementDashboard({ 
  currentSessionId 
}: AdvancedSessionManagementDashboardProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'templates' | 'scheduling' | 'analytics' | 'collaboration' | 'controls'>('overview');

  // Queries for overview data
  const userTemplates = useQuery(api.sessionTemplates.getUserSessionTemplates, {
    includePublic: false,
    limit: 5,
  });

  const upcomingSessions = useQuery(api.sessionScheduling.getUpcomingSessions, {
    limit: 3,
    hoursAhead: 24,
  });

  const analytics = useQuery(api.sessionAnalytics.getDJSessionAnalytics, {
    timeRange: 'last_week',
    includeScheduled: true,
  });

  const sessionControls = currentSessionId ? useQuery(api.enhancedSessionControls.getSessionControls, {
    sessionId: currentSessionId,
  }) : null;

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'templates', label: 'Templates', icon: '📋' },
    { id: 'scheduling', label: 'Scheduling', icon: '📅' },
    { id: 'analytics', label: 'Analytics', icon: '📈' },
    { id: 'collaboration', label: 'Collaboration', icon: '👥' },
    { id: 'controls', label: 'Session Controls', icon: '🎛️' },
  ];

  const formatDuration = (milliseconds: number) => {
    const minutes = Math.floor(milliseconds / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return hours > 0 ? `${hours}h ${remainingMinutes}m` : `${remainingMinutes}m`;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Advanced Session Management</h1>
                <p className="mt-2 text-gray-600">
                  Comprehensive tools for managing your DJ sessions, templates, scheduling, and collaboration
                </p>
              </div>
              {currentSessionId && sessionControls && (
                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">{sessionControls.name}</div>
                    <div className="text-sm text-gray-500">
                      {sessionControls.active ? (
                        sessionControls.isPaused ? (
                          <span className="text-yellow-600">⏸️ Paused</span>
                        ) : (
                          <span className="text-green-600">🔴 Live</span>
                        )
                      ) : (
                        <span className="text-gray-600">⚫ Inactive</span>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-500">👥 {sessionControls.currentAudienceSize} listeners</div>
                    <div className="text-sm text-gray-500">⏱️ {formatDuration(sessionControls.sessionDuration)}</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <div className="flex items-center">
                  <div className="text-2xl">📋</div>
                  <div className="ml-4">
                    <div className="text-2xl font-bold text-gray-900">
                      {userTemplates?.length || 0}
                    </div>
                    <div className="text-sm text-gray-600">Session Templates</div>
                  </div>
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <div className="flex items-center">
                  <div className="text-2xl">📅</div>
                  <div className="ml-4">
                    <div className="text-2xl font-bold text-gray-900">
                      {upcomingSessions?.length || 0}
                    </div>
                    <div className="text-sm text-gray-600">Upcoming Sessions</div>
                  </div>
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <div className="flex items-center">
                  <div className="text-2xl">🎵</div>
                  <div className="ml-4">
                    <div className="text-2xl font-bold text-gray-900">
                      {analytics?.totalSessions || 0}
                    </div>
                    <div className="text-sm text-gray-600">Total Sessions</div>
                  </div>
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <div className="flex items-center">
                  <div className="text-2xl">👥</div>
                  <div className="ml-4">
                    <div className="text-2xl font-bold text-gray-900">
                      {analytics?.uniqueUsers || 0}
                    </div>
                    <div className="text-sm text-gray-600">Unique Listeners</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Recent Templates */}
              <div className="bg-white rounded-lg shadow-sm border">
                <div className="p-6 border-b">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-semibold text-gray-900">Recent Templates</h3>
                    <button
                      onClick={() => setActiveTab('templates')}
                      className="text-blue-600 hover:text-blue-700 text-sm"
                    >
                      View All
                    </button>
                  </div>
                </div>
                <div className="p-6">
                  {userTemplates && userTemplates.length > 0 ? (
                    <div className="space-y-3">
                      {userTemplates.slice(0, 3).map((template) => (
                        <div key={template._id} className="flex justify-between items-center">
                          <div>
                            <div className="font-medium text-gray-900">{template.name}</div>
                            <div className="text-sm text-gray-500">
                              Used {template.usageCount} times
                            </div>
                          </div>
                          <div className="flex gap-1">
                            {template.tags.slice(0, 2).map(tag => (
                              <span key={tag} className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      <p>No templates yet</p>
                      <button
                        onClick={() => setActiveTab('templates')}
                        className="mt-2 text-blue-600 hover:text-blue-700"
                      >
                        Create your first template
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* Upcoming Sessions */}
              <div className="bg-white rounded-lg shadow-sm border">
                <div className="p-6 border-b">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-semibold text-gray-900">Upcoming Sessions</h3>
                    <button
                      onClick={() => setActiveTab('scheduling')}
                      className="text-blue-600 hover:text-blue-700 text-sm"
                    >
                      View All
                    </button>
                  </div>
                </div>
                <div className="p-6">
                  {upcomingSessions && upcomingSessions.length > 0 ? (
                    <div className="space-y-3">
                      {upcomingSessions.map((session) => (
                        <div key={session._id} className="flex justify-between items-center">
                          <div>
                            <div className="font-medium text-gray-900">{session.name}</div>
                            <div className="text-sm text-gray-500">
                              {session.formattedStartTime}
                            </div>
                          </div>
                          <div className="text-sm text-blue-600">
                            in {Math.ceil(session.timeUntilStart / (1000 * 60 * 60))}h
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      <p>No upcoming sessions</p>
                      <button
                        onClick={() => setActiveTab('scheduling')}
                        className="mt-2 text-blue-600 hover:text-blue-700"
                      >
                        Schedule a session
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Performance Overview */}
            {analytics && (
              <div className="bg-white rounded-lg shadow-sm border">
                <div className="p-6 border-b">
                  <h3 className="text-lg font-semibold text-gray-900">Performance Overview (Last Week)</h3>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">{analytics.totalRequests}</div>
                      <div className="text-sm text-gray-600">Song Requests</div>
                      <div className="text-xs text-gray-500 mt-1">
                        {analytics.approvalRate.toFixed(1)}% approval rate
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">{analytics.totalEvents}</div>
                      <div className="text-sm text-gray-600">Engagement Events</div>
                      <div className="text-xs text-gray-500 mt-1">
                        {analytics.averageEventsPerSession.toFixed(1)} per session
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-purple-600">{analytics.peakListeners}</div>
                      <div className="text-sm text-gray-600">Peak Audience</div>
                      <div className="text-xs text-gray-500 mt-1">
                        Across all sessions
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'templates' && (
          <SessionTemplates />
        )}

        {activeTab === 'scheduling' && (
          <SessionScheduling />
        )}

        {activeTab === 'analytics' && (
          <SessionAnalyticsDashboard />
        )}

        {activeTab === 'collaboration' && currentSessionId && (
          <SessionCollaboration 
            sessionId={currentSessionId} 
            isOwner={sessionControls?.isOwner || false}
          />
        )}

        {activeTab === 'controls' && currentSessionId && (
          <EnhancedSessionControls sessionId={currentSessionId} />
        )}

        {activeTab === 'collaboration' && !currentSessionId && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">👥</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Session</h3>
            <p className="text-gray-600">
              Start a session to manage collaborators and permissions
            </p>
          </div>
        )}

        {activeTab === 'controls' && !currentSessionId && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">🎛️</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Session</h3>
            <p className="text-gray-600">
              Start a session to access enhanced session controls
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
