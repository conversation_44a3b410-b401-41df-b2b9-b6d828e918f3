"use client";

import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "../convex/_generated/api";
import { Id } from "../convex/_generated/dataModel";
import { Button } from './ui/button';
import { Card } from './ui/card';

interface NotificationCenterProps {
  userId: Id<"users">;
}

export default function NotificationCenter({ userId }: NotificationCenterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [lastNotificationCount, setLastNotificationCount] = useState(0);

  // Queries
  const notifications = useQuery(api.notifications.getUserNotifications, { 
    userId,
    limit: 20 
  });
  const unreadCount = useQuery(api.notifications.getUnreadCount, { userId });

  // Mutations
  const markAsRead = useMutation(api.notifications.markAsRead);
  const markAllAsRead = useMutation(api.notifications.markAllAsRead);

  // Show browser notification for new notifications
  useEffect(() => {
    if (unreadCount && unreadCount > lastNotificationCount && lastNotificationCount > 0) {
      // Request permission for browser notifications
      if (Notification.permission === 'granted') {
        new Notification('PlayBeg', {
          body: `You have ${unreadCount - lastNotificationCount} new notification(s)`,
          icon: '/convex.svg'
        });
      } else if (Notification.permission !== 'denied') {
        Notification.requestPermission();
      }
    }
    if (unreadCount !== undefined) {
      setLastNotificationCount(unreadCount);
    }
  }, [unreadCount, lastNotificationCount]);

  const handleNotificationClick = async (notificationId: Id<"notifications">) => {
    try {
      await markAsRead({ notificationId });
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead({ userId });
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'border-l-red-500 bg-red-600/10';
      case 'high': return 'border-l-orange-500 bg-orange-600/10';
      case 'medium': return 'border-l-yellow-500 bg-yellow-600/10';
      case 'low': return 'border-l-blue-500 bg-blue-600/10';
      default: return 'border-l-gray-500 bg-gray-600/10';
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'song_request': return '🎵';
      case 'session_status': return '🎛️';
      case 'audience_interaction': return '👥';
      case 'system_alert': return '⚠️';
      case 'engagement_milestone': return '🎉';
      default: return '📢';
    }
  };

  const formatTimeAgo = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  return (
    <div className="relative">
      {/* Notification Bell */}
      <Button
        onClick={() => setIsOpen(!isOpen)}
        variant="outline"
        className="relative text-white border-white/30 hover:bg-white/10"
      >
        <span className="text-lg">🔔</span>
        {unreadCount && unreadCount > 0 && (
          <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </Button>

      {/* Notification Dropdown */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-96 z-50">
          <Card className="bg-white/10 backdrop-blur-md border-white/20 max-h-96 overflow-hidden">
            {/* Header */}
            <div className="p-4 border-b border-white/20">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-white">Notifications</h3>
                {unreadCount && unreadCount > 0 && (
                  <Button
                    onClick={handleMarkAllAsRead}
                    size="sm"
                    variant="outline"
                    className="text-white border-white/30 hover:bg-white/10"
                  >
                    Mark All Read
                  </Button>
                )}
              </div>
            </div>

            {/* Notifications List */}
            <div className="max-h-80 overflow-y-auto">
              {notifications && notifications.length > 0 ? (
                <div className="divide-y divide-white/10">
                  {notifications.map((notification) => (
                    <div
                      key={notification._id}
                      onClick={() => handleNotificationClick(notification._id)}
                      className={`p-4 cursor-pointer hover:bg-white/5 transition-colors border-l-4 ${getPriorityColor(notification.priority)} ${
                        !notification.isRead ? 'bg-white/5' : ''
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="text-2xl">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <h4 className={`text-sm font-medium ${
                              !notification.isRead ? 'text-white' : 'text-white/80'
                            }`}>
                              {notification.title}
                            </h4>
                            {!notification.isRead && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            )}
                          </div>
                          <p className={`text-sm mt-1 ${
                            !notification.isRead ? 'text-white/90' : 'text-white/70'
                          }`}>
                            {notification.message}
                          </p>
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-white/60">
                              {formatTimeAgo(notification.createdAt)}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded ${
                              notification.priority === 'urgent' ? 'bg-red-600/20 text-red-300' :
                              notification.priority === 'high' ? 'bg-orange-600/20 text-orange-300' :
                              notification.priority === 'medium' ? 'bg-yellow-600/20 text-yellow-300' :
                              'bg-blue-600/20 text-blue-300'
                            }`}>
                              {notification.priority}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-8 text-center">
                  <div className="text-4xl mb-4">🔔</div>
                  <p className="text-white/80">No notifications yet</p>
                </div>
              )}
            </div>

            {/* Footer */}
            {notifications && notifications.length > 0 && (
              <div className="p-4 border-t border-white/20 text-center">
                <Button
                  onClick={() => setIsOpen(false)}
                  size="sm"
                  variant="outline"
                  className="text-white border-white/30 hover:bg-white/10"
                >
                  Close
                </Button>
              </div>
            )}
          </Card>
        </div>
      )}

      {/* Overlay to close dropdown */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
