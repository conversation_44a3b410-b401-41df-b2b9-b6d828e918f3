"use client";

import React, { useState } from 'react';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Music, 
  Clock, 
  Activity,
  Calendar,
  Target,
  Zap,
  Heart,
  ThumbsUp,
  PlayCircle,
  PauseCircle,
  RefreshCw
} from 'lucide-react';

export default function AdvancedAnalyticsDashboard() {
  const [timeRange, setTimeRange] = useState<number>(7 * 24 * 60 * 60 * 1000); // 7 days
  const [selectedSession, setSelectedSession] = useState<string>('');

  // Analytics queries
  const sessionAnalytics = useQuery(
    api.analytics.getSessionAnalytics,
    { timeRange }
  );

  const userEngagement = useQuery(
    api.analytics.getUserEngagementAnalytics,
    { timeRange }
  );

  const platformAnalytics = useQuery(
    api.analytics.getPlatformAnalytics,
    { timeRange }
  );

  const timeRangeOptions = [
    { value: 24 * 60 * 60 * 1000, label: '24 Hours' },
    { value: 7 * 24 * 60 * 60 * 1000, label: '7 Days' },
    { value: 30 * 24 * 60 * 60 * 1000, label: '30 Days' },
    { value: 90 * 24 * 60 * 60 * 1000, label: '90 Days' },
  ];

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const formatTime = (minutes: number) => {
    if (minutes >= 60) {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return `${hours}h ${mins}m`;
    }
    return `${minutes}m`;
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  if (!sessionAnalytics || !userEngagement) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin text-white/60" />
        <span className="ml-2 text-white/60">Loading analytics...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white flex items-center">
          <BarChart3 className="w-6 h-6 mr-2" />
          Advanced Analytics
        </h2>
        <div className="flex items-center space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(Number(e.target.value))}
            className="bg-gray-800 border-gray-600 text-white rounded px-3 py-1"
          >
            {timeRangeOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
            className="text-white border-white/30"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overview Metrics */}
      {sessionAnalytics.overview && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Total Sessions</p>
                <p className="text-2xl font-bold text-white">{sessionAnalytics.overview.totalSessions}</p>
                <p className="text-green-400 text-xs">
                  {sessionAnalytics.overview.activeSessions} active
                </p>
              </div>
              <Music className="w-8 h-8 text-purple-400" />
            </div>
          </Card>

          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Total Requests</p>
                <p className="text-2xl font-bold text-white">{sessionAnalytics.overview.totalRequests}</p>
                <p className="text-blue-400 text-xs">
                  {sessionAnalytics.overview.avgRequestsPerSession} avg/session
                </p>
              </div>
              <PlayCircle className="w-8 h-8 text-blue-400" />
            </div>
          </Card>

          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">User Engagement</p>
                <p className="text-2xl font-bold text-white">{userEngagement.metrics.totalSessions}</p>
                <p className="text-yellow-400 text-xs">
                  {formatTime(userEngagement.metrics.avgSessionTime)} avg time
                </p>
              </div>
              <Users className="w-8 h-8 text-yellow-400" />
            </div>
          </Card>

          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Total Reactions</p>
                <p className="text-2xl font-bold text-white">{userEngagement.metrics.totalReactions}</p>
                <p className="text-pink-400 text-xs">
                  {userEngagement.metrics.totalVotes} votes
                </p>
              </div>
              <Heart className="w-8 h-8 text-pink-400" />
            </div>
          </Card>
        </div>
      )}

      {/* Session Activity Chart */}
      {sessionAnalytics.dailyActivity && (
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
          <h3 className="text-xl font-bold text-white mb-4 flex items-center">
            <TrendingUp className="w-5 h-5 mr-2" />
            Session Activity Over Time
          </h3>
          
          <div className="space-y-4">
            {/* Simple bar chart representation */}
            <div className="grid grid-cols-7 gap-2">
              {sessionAnalytics.dailyActivity.slice(-7).map((day, index) => {
                const maxCount = Math.max(...sessionAnalytics.dailyActivity.map(d => d.count));
                const height = maxCount > 0 ? (day.count / maxCount) * 100 : 0;
                
                return (
                  <div key={index} className="flex flex-col items-center space-y-2">
                    <div className="w-full bg-gray-700 rounded-t" style={{ height: '100px' }}>
                      <div 
                        className="w-full bg-purple-500 rounded-t transition-all duration-300"
                        style={{ 
                          height: `${height}%`,
                          marginTop: `${100 - height}%`
                        }}
                      />
                    </div>
                    <div className="text-center">
                      <p className="text-white font-semibold">{day.count}</p>
                      <p className="text-white/60 text-xs">{formatDate(new Date(day.date).getTime())}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </Card>
      )}

      {/* Top Sessions */}
      {sessionAnalytics.topSessions && (
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
          <h3 className="text-xl font-bold text-white mb-4 flex items-center">
            <Target className="w-5 h-5 mr-2" />
            Top Performing Sessions
          </h3>
          
          <div className="space-y-3">
            {sessionAnalytics.topSessions.map((session, index) => (
              <div key={session.id} className="flex items-center justify-between bg-white/5 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-purple-600 rounded-full text-white font-bold">
                    {index + 1}
                  </div>
                  <div>
                    <h4 className="font-semibold text-white">{session.name}</h4>
                    <p className="text-white/60 text-sm">
                      {formatDate(session.createdAt)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <p className="text-white font-semibold">{session.requestCount}</p>
                    <p className="text-white/60 text-xs">requests</p>
                  </div>
                  <Badge className={session.active ? 'bg-green-600' : 'bg-gray-600'}>
                    {session.active ? 'Active' : 'Ended'}
                  </Badge>
                </div>
              </div>
            ))}

            {sessionAnalytics.topSessions.length === 0 && (
              <div className="text-center py-8 text-white/60">
                <Music className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No sessions found for the selected time range.</p>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* User Engagement Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Daily Activity */}
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
          <h3 className="text-xl font-bold text-white mb-4 flex items-center">
            <Activity className="w-5 h-5 mr-2" />
            Your Daily Activity
          </h3>
          
          <div className="space-y-3">
            {userEngagement.dailyActivity.slice(-7).map((day, index) => {
              const maxCount = Math.max(...userEngagement.dailyActivity.map(d => d.count));
              const percentage = maxCount > 0 ? (day.count / maxCount) * 100 : 0;
              
              return (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-white">{formatDate(new Date(day.date).getTime())}</span>
                    <span className="text-white/60">{day.count} sessions</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                </div>
              );
            })}
          </div>
        </Card>

        {/* Reaction Breakdown */}
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
          <h3 className="text-xl font-bold text-white mb-4 flex items-center">
            <Heart className="w-5 h-5 mr-2" />
            Reaction Breakdown
          </h3>
          
          <div className="space-y-3">
            {Object.entries(userEngagement.reactionBreakdown).map(([reaction, count]) => {
              const total = Object.values(userEngagement.reactionBreakdown).reduce((sum, c) => sum + c, 0);
              const percentage = total > 0 ? (count / total) * 100 : 0;
              
              return (
                <div key={reaction} className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-white flex items-center">
                      <span className="text-lg mr-2">{reaction}</span>
                      Reaction
                    </span>
                    <span className="text-white/60">{count} times</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-pink-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                </div>
              );
            })}

            {Object.keys(userEngagement.reactionBreakdown).length === 0 && (
              <div className="text-center py-4 text-white/60">
                <Heart className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>No reactions recorded yet.</p>
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* Platform Analytics (if available) */}
      {platformAnalytics && (
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
          <h3 className="text-xl font-bold text-white mb-4 flex items-center">
            <Zap className="w-5 h-5 mr-2" />
            Platform Overview
          </h3>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-white">{platformAnalytics.metrics.totalUsers}</p>
              <p className="text-white/60 text-sm">Total Users</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-white">{platformAnalytics.metrics.totalDJs}</p>
              <p className="text-white/60 text-sm">DJs</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-white">{platformAnalytics.metrics.totalSessions}</p>
              <p className="text-white/60 text-sm">Sessions</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-white">{platformAnalytics.metrics.djConversionRate}%</p>
              <p className="text-white/60 text-sm">DJ Conversion</p>
            </div>
          </div>
        </Card>
      )}

      {/* Insights and Recommendations */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
        <h3 className="text-xl font-bold text-white mb-4 flex items-center">
          <TrendingUp className="w-5 h-5 mr-2" />
          Insights & Recommendations
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-green-400 rounded-full mt-2" />
              <div>
                <h4 className="text-white font-semibold">Peak Activity</h4>
                <p className="text-white/60 text-sm">
                  Your sessions get the most engagement during evening hours. 
                  Consider scheduling more sessions between 6-9 PM.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-blue-400 rounded-full mt-2" />
              <div>
                <h4 className="text-white font-semibold">Audience Retention</h4>
                <p className="text-white/60 text-sm">
                  Users spend an average of {formatTime(userEngagement.metrics.avgSessionTime)} in your sessions. 
                  Great engagement!
                </p>
              </div>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2" />
              <div>
                <h4 className="text-white font-semibold">Request Volume</h4>
                <p className="text-white/60 text-sm">
                  You're averaging {sessionAnalytics.overview?.avgRequestsPerSession || 0} requests per session. 
                  Consider promoting audience interaction.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-purple-400 rounded-full mt-2" />
              <div>
                <h4 className="text-white font-semibold">Growth Opportunity</h4>
                <p className="text-white/60 text-sm">
                  Your reaction rate is {userEngagement.metrics.totalReactions} total. 
                  Encourage more audience participation with interactive features.
                </p>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
