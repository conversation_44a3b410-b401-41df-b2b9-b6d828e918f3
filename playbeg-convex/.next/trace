[{"name": "generate-buildid", "duration": 106, "timestamp": 1570123737961, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750680759739, "traceId": "870647c1d6c6dbf5"}, {"name": "load-custom-routes", "duration": 119, "timestamp": 1570123738103, "id": 5, "parentId": 1, "tags": {}, "startTime": 1750680759739, "traceId": "870647c1d6c6dbf5"}, {"name": "create-dist-dir", "duration": 92, "timestamp": 1570123761854, "id": 6, "parentId": 1, "tags": {}, "startTime": 1750680759763, "traceId": "870647c1d6c6dbf5"}, {"name": "create-pages-mapping", "duration": 103, "timestamp": 1570123775749, "id": 7, "parentId": 1, "tags": {}, "startTime": 1750680759777, "traceId": "870647c1d6c6dbf5"}, {"name": "collect-app-paths", "duration": 1392, "timestamp": 1570123775871, "id": 8, "parentId": 1, "tags": {}, "startTime": 1750680759777, "traceId": "870647c1d6c6dbf5"}, {"name": "create-app-mapping", "duration": 266, "timestamp": 1570123777280, "id": 9, "parentId": 1, "tags": {}, "startTime": 1750680759778, "traceId": "870647c1d6c6dbf5"}, {"name": "public-dir-conflict-check", "duration": 165, "timestamp": 1570123777714, "id": 10, "parentId": 1, "tags": {}, "startTime": 1750680759779, "traceId": "870647c1d6c6dbf5"}, {"name": "generate-routes-manifest", "duration": 886, "timestamp": 1570123777949, "id": 11, "parentId": 1, "tags": {}, "startTime": 1750680759779, "traceId": "870647c1d6c6dbf5"}, {"name": "create-entrypoints", "duration": 7951, "timestamp": 1570124107549, "id": 15, "parentId": 13, "tags": {}, "startTime": 1750680760109, "traceId": "870647c1d6c6dbf5"}, {"name": "generate-webpack-config", "duration": 139627, "timestamp": 1570124115568, "id": 16, "parentId": 14, "tags": {}, "startTime": 1750680760117, "traceId": "870647c1d6c6dbf5"}, {"name": "next-trace-entrypoint-plugin", "duration": 916, "timestamp": 1570124306703, "id": 18, "parentId": 17, "tags": {}, "startTime": 1750680760308, "traceId": "870647c1d6c6dbf5"}, {"name": "add-entry", "duration": 85608, "timestamp": 1570124308825, "id": 21, "parentId": 19, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1750680760310, "traceId": "870647c1d6c6dbf5"}, {"name": "add-entry", "duration": 93803, "timestamp": 1570124308840, "id": 22, "parentId": 19, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1750680760310, "traceId": "870647c1d6c6dbf5"}, {"name": "add-entry", "duration": 105028, "timestamp": 1570124308848, "id": 23, "parentId": 19, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1750680760310, "traceId": "870647c1d6c6dbf5"}, {"name": "build-module-tsx", "duration": 26170, "timestamp": 1570124458895, "id": 29, "parentId": 17, "tags": {"name": "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/server/inner.tsx", "layer": "rsc"}, "startTime": 1750680760460, "traceId": "870647c1d6c6dbf5"}, {"name": "add-entry", "duration": 179527, "timestamp": 1570124308637, "id": 20, "parentId": 19, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750680760310, "traceId": "870647c1d6c6dbf5"}, {"name": "add-entry", "duration": 179325, "timestamp": 1570124308855, "id": 24, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fdashboard%2Fpage&name=app%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp&appPaths=%2Fdashboard%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750680760310, "traceId": "870647c1d6c6dbf5"}, {"name": "add-entry", "duration": 179335, "timestamp": 1570124308860, "id": 25, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750680760310, "traceId": "870647c1d6c6dbf5"}, {"name": "add-entry", "duration": 179338, "timestamp": 1570124308864, "id": 26, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fserver%2Fpage&name=app%2Fserver%2Fpage&pagePath=private-next-app-dir%2Fserver%2Fpage.tsx&appDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp&appPaths=%2Fserver%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750680760310, "traceId": "870647c1d6c6dbf5"}, {"name": "add-entry", "duration": 179338, "timestamp": 1570124308869, "id": 27, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fsignin%2Fpage&name=app%2Fsignin%2Fpage&pagePath=private-next-app-dir%2Fsignin%2Fpage.tsx&appDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp&appPaths=%2Fsignin%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750680760310, "traceId": "870647c1d6c6dbf5"}, {"name": "add-entry", "duration": 179335, "timestamp": 1570124308873, "id": 28, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ftest-upload%2Fpage&name=app%2Ftest-upload%2Fpage&pagePath=private-next-app-dir%2Ftest-upload%2Fpage.tsx&appDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp&appPaths=%2Ftest-upload%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750680760310, "traceId": "870647c1d6c6dbf5"}, {"name": "build-module-tsx", "duration": 6406, "timestamp": 1570124566142, "id": 64, "parentId": 17, "tags": {"name": "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/server/inner.tsx", "layer": "ssr"}, "startTime": 1750680760567, "traceId": "870647c1d6c6dbf5"}, {"name": "make", "duration": 292907, "timestamp": 1570124308521, "id": 19, "parentId": 17, "tags": {}, "startTime": 1750680760310, "traceId": "870647c1d6c6dbf5"}, {"name": "get-entries", "duration": 264, "timestamp": 1570124602040, "id": 72, "parentId": 71, "tags": {}, "startTime": 1750680760603, "traceId": "870647c1d6c6dbf5"}, {"name": "node-file-trace-plugin", "duration": 20982, "timestamp": 1570124603853, "id": 73, "parentId": 71, "tags": {"traceEntryCount": "14"}, "startTime": 1750680760605, "traceId": "870647c1d6c6dbf5"}, {"name": "collect-traced-files", "duration": 211, "timestamp": 1570124624842, "id": 74, "parentId": 71, "tags": {}, "startTime": 1750680760626, "traceId": "870647c1d6c6dbf5"}, {"name": "finish-modules", "duration": 23105, "timestamp": 1570124601950, "id": 71, "parentId": 18, "tags": {}, "startTime": 1750680760603, "traceId": "870647c1d6c6dbf5"}, {"name": "chunk-graph", "duration": 6069, "timestamp": 1570124638625, "id": 76, "parentId": 75, "tags": {}, "startTime": 1750680760640, "traceId": "870647c1d6c6dbf5"}, {"name": "optimize-modules", "duration": 9, "timestamp": 1570124644746, "id": 78, "parentId": 75, "tags": {}, "startTime": 1750680760646, "traceId": "870647c1d6c6dbf5"}, {"name": "optimize-chunks", "duration": 5294, "timestamp": 1570124644786, "id": 79, "parentId": 75, "tags": {}, "startTime": 1750680760646, "traceId": "870647c1d6c6dbf5"}, {"name": "optimize-tree", "duration": 15, "timestamp": 1570124650112, "id": 80, "parentId": 75, "tags": {}, "startTime": 1750680760651, "traceId": "870647c1d6c6dbf5"}, {"name": "optimize-chunk-modules", "duration": 8942, "timestamp": 1570124650158, "id": 81, "parentId": 75, "tags": {}, "startTime": 1750680760651, "traceId": "870647c1d6c6dbf5"}, {"name": "optimize", "duration": 14406, "timestamp": 1570124644721, "id": 77, "parentId": 75, "tags": {}, "startTime": 1750680760646, "traceId": "870647c1d6c6dbf5"}, {"name": "module-hash", "duration": 6849, "timestamp": 1570124668859, "id": 82, "parentId": 75, "tags": {}, "startTime": 1750680760670, "traceId": "870647c1d6c6dbf5"}, {"name": "code-generation", "duration": 2515, "timestamp": 1570124675746, "id": 83, "parentId": 75, "tags": {}, "startTime": 1750680760677, "traceId": "870647c1d6c6dbf5"}, {"name": "hash", "duration": 2570, "timestamp": 1570124679805, "id": 84, "parentId": 75, "tags": {}, "startTime": 1750680760681, "traceId": "870647c1d6c6dbf5"}, {"name": "code-generation-jobs", "duration": 79, "timestamp": 1570124682375, "id": 85, "parentId": 75, "tags": {}, "startTime": 1750680760683, "traceId": "870647c1d6c6dbf5"}, {"name": "module-assets", "duration": 114, "timestamp": 1570124682436, "id": 86, "parentId": 75, "tags": {}, "startTime": 1750680760683, "traceId": "870647c1d6c6dbf5"}, {"name": "create-chunk-assets", "duration": 1062, "timestamp": 1570124682554, "id": 87, "parentId": 75, "tags": {}, "startTime": 1750680760684, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-js", "duration": 1377, "timestamp": 1570124688103, "id": 89, "parentId": 88, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1750680760689, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-js", "duration": 1322, "timestamp": 1570124688161, "id": 90, "parentId": 88, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1750680760689, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-js", "duration": 1318, "timestamp": 1570124688167, "id": 91, "parentId": 88, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1750680760689, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-js", "duration": 1316, "timestamp": 1570124688170, "id": 92, "parentId": 88, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1750680760689, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-js", "duration": 1314, "timestamp": 1570124688172, "id": 93, "parentId": 88, "tags": {"name": "../app/dashboard/page.js", "cache": "HIT"}, "startTime": 1750680760689, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-js", "duration": 1312, "timestamp": 1570124688175, "id": 94, "parentId": 88, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1750680760689, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-js", "duration": 43, "timestamp": 1570124689444, "id": 96, "parentId": 88, "tags": {"name": "../app/signin/page.js", "cache": "HIT"}, "startTime": 1750680760690, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-js", "duration": 35, "timestamp": 1570124689452, "id": 97, "parentId": 88, "tags": {"name": "../app/test-upload/page.js", "cache": "HIT"}, "startTime": 1750680760690, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-js", "duration": 30, "timestamp": 1570124689458, "id": 98, "parentId": 88, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1750680760690, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-js", "duration": 26, "timestamp": 1570124689462, "id": 99, "parentId": 88, "tags": {"name": "711.js", "cache": "HIT"}, "startTime": 1750680760690, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-js", "duration": 21, "timestamp": 1570124689467, "id": 100, "parentId": 88, "tags": {"name": "144.js", "cache": "HIT"}, "startTime": 1750680760690, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-js", "duration": 18, "timestamp": 1570124689471, "id": 101, "parentId": 88, "tags": {"name": "778.js", "cache": "HIT"}, "startTime": 1750680760690, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-js", "duration": 16, "timestamp": 1570124689473, "id": 102, "parentId": 88, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1750680760690, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-js", "duration": 15, "timestamp": 1570124689475, "id": 103, "parentId": 88, "tags": {"name": "707.js", "cache": "HIT"}, "startTime": 1750680760690, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-js", "duration": 13, "timestamp": 1570124689477, "id": 104, "parentId": 88, "tags": {"name": "713.js", "cache": "HIT"}, "startTime": 1750680760690, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-js", "duration": 12, "timestamp": 1570124689479, "id": 105, "parentId": 88, "tags": {"name": "11.js", "cache": "HIT"}, "startTime": 1750680760690, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-js", "duration": 3784, "timestamp": 1570124688177, "id": 95, "parentId": 88, "tags": {"name": "../app/server/page.js", "cache": "MISS"}, "startTime": 1750680760689, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-webpack-plugin-optimize", "duration": 6240, "timestamp": 1570124685725, "id": 88, "parentId": 17, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1750680760687, "traceId": "870647c1d6c6dbf5"}, {"name": "css-minimizer-plugin", "duration": 72, "timestamp": 1570124692015, "id": 106, "parentId": 17, "tags": {}, "startTime": 1750680760693, "traceId": "870647c1d6c6dbf5"}, {"name": "create-trace-assets", "duration": 823, "timestamp": 1570124692185, "id": 107, "parentId": 18, "tags": {}, "startTime": 1750680760693, "traceId": "870647c1d6c6dbf5"}, {"name": "create-trace-assets", "duration": 336, "timestamp": 1570124693062, "id": 108, "parentId": 18, "tags": {}, "startTime": 1750680760694, "traceId": "870647c1d6c6dbf5"}, {"name": "seal", "duration": 64234, "timestamp": 1570124632981, "id": 75, "parentId": 17, "tags": {}, "startTime": 1750680760634, "traceId": "870647c1d6c6dbf5"}, {"name": "webpack-compilation", "duration": 396822, "timestamp": 1570124305972, "id": 17, "parentId": 14, "tags": {"name": "server"}, "startTime": 1750680760307, "traceId": "870647c1d6c6dbf5"}, {"name": "emit", "duration": 27548, "timestamp": 1570124702970, "id": 109, "parentId": 14, "tags": {}, "startTime": 1750680760704, "traceId": "870647c1d6c6dbf5"}, {"name": "webpack-close", "duration": 17390, "timestamp": 1570124731125, "id": 110, "parentId": 14, "tags": {"name": "server"}, "startTime": 1750680760732, "traceId": "870647c1d6c6dbf5"}, {"name": "webpack-generate-error-stats", "duration": 925, "timestamp": 1570124748545, "id": 111, "parentId": 110, "tags": {}, "startTime": 1750680760750, "traceId": "870647c1d6c6dbf5"}, {"name": "run-webpack-compiler", "duration": 642010, "timestamp": 1570124107546, "id": 14, "parentId": 13, "tags": {}, "startTime": 1750680760109, "traceId": "870647c1d6c6dbf5"}, {"name": "format-webpack-messages", "duration": 33, "timestamp": 1570124749559, "id": 112, "parentId": 13, "tags": {}, "startTime": 1750680760751, "traceId": "870647c1d6c6dbf5"}, {"name": "worker-main-server", "duration": 642264, "timestamp": 1570124107370, "id": 13, "parentId": 1, "tags": {}, "startTime": 1750680760108, "traceId": "870647c1d6c6dbf5"}, {"name": "create-entrypoints", "duration": 8132, "timestamp": 1570125051260, "id": 116, "parentId": 114, "tags": {}, "startTime": 1750680761052, "traceId": "870647c1d6c6dbf5"}, {"name": "generate-webpack-config", "duration": 135530, "timestamp": 1570125059459, "id": 117, "parentId": 115, "tags": {}, "startTime": 1750680761060, "traceId": "870647c1d6c6dbf5"}, {"name": "add-entry", "duration": 66865, "timestamp": 1570125249060, "id": 120, "parentId": 119, "tags": {"request": "next-middleware-loader?absolutePagePath=private-next-root-dir%2Fmiddleware.ts&page=%2Fmiddleware&rootDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex&matchers=W3sicmVnZXhwIjoiXig%2FOlxcLyhfbmV4dFxcL2RhdGFcXC9bXi9dezEsfSkpPyg%2FOlxcLygoPyEuKlxcLi4qfF9uZXh0KS4qKSkoXFwuanNvbik%2FW1xcLyNcXD9dPyQiLCJvcmlnaW5hbFNvdXJjZSI6Ii8oKD8hLipcXC4uKnxfbmV4dCkuKikifSx7InJlZ2V4cCI6Il4oPzpcXC8oX25leHRcXC9kYXRhXFwvW14vXXsxLH0pKT8oPzpcXC8oXFwvP2luZGV4fFxcLz9pbmRleFxcLmpzb24pKT9bXFwvI1xcP10%2FJCIsIm9yaWdpbmFsU291cmNlIjoiLyJ9LHsicmVnZXhwIjoiXig%2FOlxcLyhfbmV4dFxcL2RhdGFcXC9bXi9dezEsfSkpPyg%2FOlxcLyhhcGl8dHJwYykpKC4qKShcXC5qc29uKT9bXFwvI1xcP10%2FJCIsIm9yaWdpbmFsU291cmNlIjoiLyhhcGl8dHJwYykoLiopIn1d&preferredRegion=&middlewareConfig=eyJtYXRjaGVycyI6W3sicmVnZXhwIjoiXig%2FOlxcLyhfbmV4dFxcL2RhdGFcXC9bXi9dezEsfSkpPyg%2FOlxcLygoPyEuKlxcLi4qfF9uZXh0KS4qKSkoXFwuanNvbik%2FW1xcLyNcXD9dPyQiLCJvcmlnaW5hbFNvdXJjZSI6Ii8oKD8hLipcXC4uKnxfbmV4dCkuKikifSx7InJlZ2V4cCI6Il4oPzpcXC8oX25leHRcXC9kYXRhXFwvW14vXXsxLH0pKT8oPzpcXC8oXFwvP2luZGV4fFxcLz9pbmRleFxcLmpzb24pKT9bXFwvI1xcP10%2FJCIsIm9yaWdpbmFsU291cmNlIjoiLyJ9LHsicmVnZXhwIjoiXig%2FOlxcLyhfbmV4dFxcL2RhdGFcXC9bXi9dezEsfSkpPyg%2FOlxcLyhhcGl8dHJwYykpKC4qKShcXC5qc29uKT9bXFwvI1xcP10%2FJCIsIm9yaWdpbmFsU291cmNlIjoiLyhhcGl8dHJwYykoLiopIn1dfQ%3D%3D!"}, "startTime": 1750680761250, "traceId": "870647c1d6c6dbf5"}, {"name": "make", "duration": 67326, "timestamp": 1570125248949, "id": 119, "parentId": 118, "tags": {}, "startTime": 1750680761250, "traceId": "870647c1d6c6dbf5"}, {"name": "chunk-graph", "duration": 1920, "timestamp": 1570125325246, "id": 122, "parentId": 121, "tags": {}, "startTime": 1750680761326, "traceId": "870647c1d6c6dbf5"}, {"name": "optimize-modules", "duration": 9, "timestamp": 1570125327226, "id": 124, "parentId": 121, "tags": {}, "startTime": 1750680761328, "traceId": "870647c1d6c6dbf5"}, {"name": "optimize-chunks", "duration": 781, "timestamp": 1570125327269, "id": 125, "parentId": 121, "tags": {}, "startTime": 1750680761328, "traceId": "870647c1d6c6dbf5"}, {"name": "optimize-tree", "duration": 10, "timestamp": 1570125328079, "id": 126, "parentId": 121, "tags": {}, "startTime": 1750680761329, "traceId": "870647c1d6c6dbf5"}, {"name": "optimize-chunk-modules", "duration": 4522, "timestamp": 1570125328124, "id": 127, "parentId": 121, "tags": {}, "startTime": 1750680761329, "traceId": "870647c1d6c6dbf5"}, {"name": "optimize", "duration": 5472, "timestamp": 1570125327203, "id": 123, "parentId": 121, "tags": {}, "startTime": 1750680761328, "traceId": "870647c1d6c6dbf5"}, {"name": "module-hash", "duration": 2908, "timestamp": 1570125334230, "id": 128, "parentId": 121, "tags": {}, "startTime": 1750680761335, "traceId": "870647c1d6c6dbf5"}, {"name": "code-generation", "duration": 403, "timestamp": 1570125337155, "id": 129, "parentId": 121, "tags": {}, "startTime": 1750680761338, "traceId": "870647c1d6c6dbf5"}, {"name": "hash", "duration": 2012, "timestamp": 1570125338500, "id": 130, "parentId": 121, "tags": {}, "startTime": 1750680761339, "traceId": "870647c1d6c6dbf5"}, {"name": "code-generation-jobs", "duration": 75, "timestamp": 1570125340511, "id": 131, "parentId": 121, "tags": {}, "startTime": 1750680761341, "traceId": "870647c1d6c6dbf5"}, {"name": "module-assets", "duration": 66, "timestamp": 1570125340571, "id": 132, "parentId": 121, "tags": {}, "startTime": 1750680761342, "traceId": "870647c1d6c6dbf5"}, {"name": "create-chunk-assets", "duration": 290, "timestamp": 1570125340641, "id": 133, "parentId": 121, "tags": {}, "startTime": 1750680761342, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-js", "duration": 67, "timestamp": 1570125346406, "id": 135, "parentId": 134, "tags": {"name": "middleware.js", "cache": "HIT"}, "startTime": 1750680761347, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-js", "duration": 12, "timestamp": 1570125346463, "id": 136, "parentId": 134, "tags": {"name": "edge-runtime-webpack.js", "cache": "HIT"}, "startTime": 1750680761347, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-js", "duration": 7, "timestamp": 1570125346469, "id": 137, "parentId": 134, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1750680761347, "traceId": "870647c1d6c6dbf5"}, {"name": "minify-webpack-plugin-optimize", "duration": 905, "timestamp": 1570125345576, "id": 134, "parentId": 118, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1750680761347, "traceId": "870647c1d6c6dbf5"}, {"name": "css-minimizer-plugin", "duration": 44, "timestamp": 1570125346524, "id": 138, "parentId": 118, "tags": {}, "startTime": 1750680761347, "traceId": "870647c1d6c6dbf5"}, {"name": "seal", "duration": 30199, "timestamp": 1570125322779, "id": 121, "parentId": 118, "tags": {}, "startTime": 1750680761324, "traceId": "870647c1d6c6dbf5"}, {"name": "webpack-compilation", "duration": 105944, "timestamp": 1570125247252, "id": 118, "parentId": 115, "tags": {"name": "edge-server"}, "startTime": 1750680761248, "traceId": "870647c1d6c6dbf5"}, {"name": "emit", "duration": 2445, "timestamp": 1570125353346, "id": 139, "parentId": 115, "tags": {}, "startTime": 1750680761354, "traceId": "870647c1d6c6dbf5"}, {"name": "webpack-close", "duration": 288, "timestamp": 1570125356242, "id": 140, "parentId": 115, "tags": {"name": "edge-server"}, "startTime": 1750680761357, "traceId": "870647c1d6c6dbf5"}, {"name": "webpack-generate-error-stats", "duration": 848, "timestamp": 1570125356553, "id": 141, "parentId": 140, "tags": {}, "startTime": 1750680761358, "traceId": "870647c1d6c6dbf5"}, {"name": "run-webpack-compiler", "duration": 306216, "timestamp": 1570125051257, "id": 115, "parentId": 114, "tags": {}, "startTime": 1750680761052, "traceId": "870647c1d6c6dbf5"}, {"name": "format-webpack-messages", "duration": 31, "timestamp": 1570125357476, "id": 142, "parentId": 114, "tags": {}, "startTime": 1750680761358, "traceId": "870647c1d6c6dbf5"}, {"name": "worker-main-edge-server", "duration": 306450, "timestamp": 1570125051098, "id": 114, "parentId": 1, "tags": {}, "startTime": 1750680761052, "traceId": "870647c1d6c6dbf5"}, {"name": "create-entrypoints", "duration": 7707, "timestamp": 1570125658854, "id": 145, "parentId": 143, "tags": {}, "startTime": 1750680761660, "traceId": "870647c1d6c6dbf5"}, {"name": "generate-webpack-config", "duration": 152066, "timestamp": 1570125666627, "id": 146, "parentId": 144, "tags": {}, "startTime": 1750680761668, "traceId": "870647c1d6c6dbf5"}, {"name": "add-entry", "duration": 100482, "timestamp": 1570125871099, "id": 152, "parentId": 148, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1750680761872, "traceId": "870647c1d6c6dbf5"}, {"name": "add-entry", "duration": 120679, "timestamp": 1570125871117, "id": 153, "parentId": 148, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1750680761872, "traceId": "870647c1d6c6dbf5"}]