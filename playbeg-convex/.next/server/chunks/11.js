exports.id=11,exports.ids=[11],exports.modules={203:(e,r,t)=>{"use strict";t.d(r,{default:()=>o});var s=t(687),a=t(6008);let n=new(t(1694)).eH("https://lovely-cormorant-474.convex.cloud");function o({children:e}){return(0,s.jsx)(a.Z,{client:n,children:e})}},589:(e,r,t)=>{"use strict";t.d(r,{g:()=>a});var s=t(6475);let a=(0,s.createServerReference)("00f913389bf07a0ce5471de0fecab54577e23d5f75",s.callServer,void 0,s.findSourceMapURL,"invalidateCache")},1296:(e,r,t)=>{Promise.resolve().then(t.bind(t,203)),Promise.resolve().then(t.bind(t,9433))},1920:(e,r,t)=>{Promise.resolve().then(t.bind(t,8926)),Promise.resolve().then(t.bind(t,6867))},2704:()=>{},4224:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},4303:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00f913389bf07a0ce5471de0fecab54577e23d5f75":()=>s.g});var s=t(519)},4934:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var s=t(687),a=t(3210),n=t(1391),o=t(1843),i=t(6241);let l=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",locked:"relative bg-gray-700/50 text-gray-400 cursor-not-allowed border border-gray-600/30"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:r,size:t,asChild:a=!1,...o},d)=>{let c=a?n.DX:"button";return(0,s.jsx)(c,{className:(0,i.cn)(l({variant:r,size:t,className:e})),ref:d,...o})});d.displayName="Button"},5192:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>o,aR:()=>i});var s=t(687),a=t(3210),n=t(6241);let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));o.displayName="Card";let i=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...r}));i.displayName="CardHeader";let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));l.displayName="CardTitle";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},6241:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(9384),a=t(2348);function n(...e){return(0,a.QP)((0,s.$)(e))}},7776:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},7856:(e,r,t)=>{"use strict";t.d(r,{e:()=>g});var s=t(687),a=t(3210),n=t(1934),o=t(1694),i=t(8999),l=t(4934),d=t(5192),c=t(9979),m=t(6241);let f=a.forwardRef(({className:e,value:r,...t},a)=>(0,s.jsx)(c.bL,{ref:a,className:(0,m.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...t,children:(0,s.jsx)(c.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(r||0)}%)`}})}));f.displayName=c.bL.displayName;var u=t(9005),p=t(22),x=t(6023),h=t(3613),v=t(1860);function g({purpose:e,onUploadComplete:r,onUploadError:t,maxSize:c=0xa00000,accept:g={"image/*":[".jpeg",".jpg",".png",".webp",".gif"]},className:b,disabled:y=!1}){let[N,j]=(0,a.useState)([]),w=(0,o.n_)(i.F.fileStorage.generateUploadUrl),P=(0,o.n_)(i.F.fileStorage.storeFileMetadata),C=(0,a.useCallback)(async s=>{let a={file:s,progress:0,status:"uploading"};j(e=>[...e,a]);try{let t=await w(),a=new XMLHttpRequest;return new Promise((n,o)=>{a.upload.addEventListener("progress",e=>{if(e.lengthComputable){let r=Math.round(e.loaded/e.total*100);j(e=>e.map(e=>e.file===s?{...e,progress:r}:e))}}),a.addEventListener("load",async()=>{if(200===a.status)try{let t=JSON.parse(a.responseText).storageId;j(e=>e.map(e=>e.file===s?{...e,status:"processing",progress:100}:e));let o=await P({storageId:t,fileName:s.name,fileType:s.type,fileSize:s.size,purpose:e,description:`${e} uploaded by user`}),i=URL.createObjectURL(s);j(e=>e.map(e=>e.file===s?{...e,status:"complete",url:i,fileId:o}:e)),r?.(o,i),n()}catch(e){throw Error("Failed to store file metadata")}else throw Error("Upload failed")}),a.addEventListener("error",()=>{o(Error("Upload failed"))});let i=new FormData;i.append("file",s),a.open("POST",t),a.send(i)})}catch(r){let e=r instanceof Error?r.message:"Upload failed";j(r=>r.map(r=>r.file===s?{...r,status:"error",error:e}:r)),t?.(e)}},[w,P,e,r,t]),R=(0,a.useCallback)(e=>{e.forEach(C)},[C]),{getRootProps:U,getInputProps:k,isDragActive:B}=(0,n.VB)({onDrop:R,accept:g,maxSize:c,disabled:y,multiple:!1}),E=e=>{j(r=>r.filter(r=>r.file!==e))},F=e=>e.startsWith("image/")?(0,s.jsx)(u.A,{className:"w-8 h-8"}):(0,s.jsx)(p.A,{className:"w-8 h-8"});return(0,s.jsxs)("div",{className:(0,m.cn)("space-y-4",b),children:[(0,s.jsx)(d.Zp,{className:(0,m.cn)("border-2 border-dashed transition-colors",B?"border-purple-500 bg-purple-50/10":"border-gray-600",y&&"opacity-50 cursor-not-allowed"),children:(0,s.jsx)(d.Wu,{className:"p-6",children:(0,s.jsxs)("div",{...U(),className:(0,m.cn)("flex flex-col items-center justify-center space-y-4 text-center cursor-pointer",y&&"cursor-not-allowed"),children:[(0,s.jsx)("input",{...k()}),(0,s.jsx)(x.A,{className:(0,m.cn)("w-12 h-12",B?"text-purple-500":"text-gray-400")}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-lg font-medium text-white",children:B?"Drop the file here":"Drag & drop a file here"}),(0,s.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"or click to select a file"}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["Max size: ",Math.round(c/1048576),"MB"]})]}),(0,s.jsx)(l.$,{type:"button",variant:"outline",size:"sm",className:"border-gray-600 text-gray-300 hover:bg-gray-700",disabled:y,children:"Choose File"})]})})}),N.length>0&&(0,s.jsx)("div",{className:"space-y-3",children:N.map((e,r)=>(0,s.jsx)(d.Zp,{className:"bg-gray-800/50 border-gray-600",children:(0,s.jsx)(d.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"text-gray-400",children:F(e.file.type)}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-white truncate",children:e.file.name}),(0,s.jsxs)("p",{className:"text-xs text-gray-400",children:[Math.round(e.file.size/1024)," KB"]}),"uploading"===e.status&&(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsx)(f,{value:e.progress,className:"h-2"}),(0,s.jsxs)("p",{className:"text-xs text-gray-400 mt-1",children:["Uploading... ",e.progress,"%"]})]}),"processing"===e.status&&(0,s.jsx)("p",{className:"text-xs text-blue-400 mt-1",children:"Processing..."}),"complete"===e.status&&(0,s.jsx)("p",{className:"text-xs text-green-400 mt-1",children:"Upload complete!"}),"error"===e.status&&(0,s.jsxs)("div",{className:"flex items-center mt-1",children:[(0,s.jsx)(h.A,{className:"w-3 h-3 text-red-400 mr-1"}),(0,s.jsx)("p",{className:"text-xs text-red-400",children:e.error||"Upload failed"})]})]}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>E(e.file),className:"text-gray-400 hover:text-white",children:(0,s.jsx)(v.A,{className:"w-4 h-4"})})]})})},r))})]})}},8014:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m,metadata:()=>c});var s=t(7413),a=t(6389),n=t.n(a),o=t(1189),i=t.n(o);t(2704);var l=t(7788),d=t(8926);let c={title:"Create Next App",description:"Generated by create next app",icons:{icon:"/convex.svg"}};function m({children:e}){return(0,s.jsx)(l.Uh,{children:(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${n().variable} ${i().variable} antialiased`,children:(0,s.jsx)(d.default,{children:e})})})})}},8926:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ConvexClientProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ConvexClientProvider.tsx","default")},8999:(e,r,t)=>{"use strict";t.d(r,{F:()=>s});let s=t(5778).dp},9821:(e,r,t)=>{"use strict";t.d(r,{E:()=>i});var s=t(687);t(3210);var a=t(1843),n=t(6241);let o=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:r,...t}){return(0,s.jsx)("div",{className:(0,n.cn)(o({variant:r}),e),...t})}}};