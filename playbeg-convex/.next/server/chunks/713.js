"use strict";exports.id=713,exports.ids=[713],exports.modules={5303:(e,t,s)=>{s.d(t,{A:()=>r});let r=(0,s(2688).A)("music",[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]])},9323:(e,t,s)=>{s.d(t,{BG:()=>A}),s(687),s(4564);var r=s(8086),i=s(3873),o=s(9975);let n=(0,r.createRequire)((0,i.resolve)("."));var a=Object.create,l=Object.defineProperty,h=Object.getOwnPropertyDescriptor,c=Object.getOwnPropertyNames,d=Object.getPrototypeOf,f=Object.prototype.hasOwnProperty,u=(e=>void 0!==n?n:"undefined"!=typeof Proxy?new Proxy(e,{get:(e,t)=>(void 0!==n?n:e)[t]}):e)(function(e){if(void 0!==n)return n.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')}),_=(e,t)=>function(){return t||(0,e[c(e)[0]])((t={exports:{}}).exports,t),t.exports},p=(e,t,s,r)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of c(t))f.call(e,i)||i===s||l(e,i,{get:()=>t[i],enumerable:!(r=h(t,i))||r.enumerable});return e},m=(e,t,s)=>(s=null!=e?a(d(e)):{},p(!t&&e&&e.__esModule?s:l(s,"default",{value:e,enumerable:!0}),e)),y=_({"../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/stream.js"(e,t){var{Duplex:s}=u("stream");function r(e){e.emit("close")}function i(){!this.destroyed&&this._writableState.finished&&this.destroy()}function o(e){this.removeListener("error",o),this.destroy(),0===this.listenerCount("error")&&this.emit("error",e)}t.exports=function(e,t){let n=!0,a=new s({...t,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return e.on("message",function(t,s){let r=!s&&a._readableState.objectMode?t.toString():t;a.push(r)||e.pause()}),e.once("error",function(e){a.destroyed||(n=!1,a.destroy(e))}),e.once("close",function(){a.destroyed||a.push(null)}),a._destroy=function(t,s){if(e.readyState===e.CLOSED){s(t),process.nextTick(r,a);return}let i=!1;e.once("error",function(e){i=!0,s(e)}),e.once("close",function(){i||s(t),process.nextTick(r,a)}),n&&e.terminate()},a._final=function(t){if(e.readyState===e.CONNECTING){e.once("open",function(){a._final(t)});return}null!==e._socket&&(e._socket._writableState.finished?(t(),a._readableState.endEmitted&&a.destroy()):(e._socket.once("finish",function(){t()}),e.close()))},a._read=function(){e.isPaused&&e.resume()},a._write=function(t,s,r){if(e.readyState===e.CONNECTING){e.once("open",function(){a._write(t,s,r)});return}e.send(t,r)},a.on("end",i),a.on("error",o),a}}}),b=_({"../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/constants.js"(e,t){var s=["nodebuffer","arraybuffer","fragments"],r="undefined"!=typeof Blob;r&&s.push("blob"),t.exports={BINARY_TYPES:s,EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",hasBlob:r,kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}}}),v=_({"../common/temp/node_modules/.pnpm/node-gyp-build@4.8.4/node_modules/node-gyp-build/node-gyp-build.js"(e,t){var s=u("fs"),r=u("path"),i=u("os"),o=process.config&&process.config.variables||{},a=!!process.env.PREBUILDS_ONLY,l=process.versions.modules,h=process.versions&&process.versions.electron||process.env.ELECTRON_RUN_AS_NODE||"undefined"!=typeof window&&window.process&&"renderer"===window.process.type?"electron":process.versions&&process.versions.nw?"node-webkit":"node",c=process.env.npm_config_arch||i.arch(),d=process.env.npm_config_platform||i.platform(),f=process.env.LIBC||("linux"===d&&s.existsSync("/etc/alpine-release")?"musl":"glibc"),_=process.env.ARM_VERSION||("arm64"===c?"8":o.arm_version)||"",p=(process.versions.uv||"").split(".")[0];function m(e){return n(m.resolve(e))}function y(e){try{return s.readdirSync(e)}catch(e){return[]}}function b(e,t){var s=y(e).filter(t);return s[0]&&r.join(e,s[0])}function v(e){return/\.node$/.test(e)}function g(e){var t=e.split("-");if(2===t.length){var s=t[0],r=t[1].split("+");if(s&&r.length&&r.every(Boolean))return{name:e,platform:s,architectures:r}}}function S(e,t){return function(s){return null!=s&&s.platform===e&&s.architectures.includes(t)}}function w(e,t){return e.architectures.length-t.architectures.length}function E(e){var t=e.split("."),s=t.pop(),r={file:e,specificity:0};if("node"===s){for(var i=0;i<t.length;i++){var o=t[i];if("node"===o||"electron"===o||"node-webkit"===o)r.runtime=o;else if("napi"===o)r.napi=!0;else if("abi"===o.slice(0,3))r.abi=o.slice(3);else if("uv"===o.slice(0,2))r.uv=o.slice(2);else if("armv"===o.slice(0,4))r.armv=o.slice(4);else{if("glibc"!==o&&"musl"!==o)continue;r.libc=o}r.specificity++}return r}}function x(e,t){return function(s){var r;return null!=s&&(!s.runtime||s.runtime===e||!!("node"===(r=s).runtime&&r.napi))&&(!s.abi||s.abi===t||!!s.napi)&&(!s.uv||s.uv===p)&&(!s.armv||s.armv===_)&&(!s.libc||s.libc===f)&&!0}}function k(e){return function(t,s){return t.runtime!==s.runtime?t.runtime===e?-1:1:t.abi!==s.abi?t.abi?-1:1:t.specificity!==s.specificity?t.specificity>s.specificity?-1:1:0}}t.exports=m,m.resolve=m.path=function(e){e=r.resolve(e||".");try{var t=n(r.join(e,"package.json")).name.toUpperCase().replace(/-/g,"_");process.env[t+"_PREBUILD"]&&(e=process.env[t+"_PREBUILD"])}catch(e){}if(!a){var s=b(r.join(e,"build/Release"),v);if(s)return s;var i=b(r.join(e,"build/Debug"),v);if(i)return i}var o=m(e);if(o)return o;var u=m(r.dirname(process.execPath));if(u)return u;throw Error("No native build was found for "+["platform="+d,"arch="+c,"runtime="+h,"abi="+l,"uv="+p,_?"armv="+_:"","libc="+f,"node="+process.versions.node,process.versions.electron?"electron="+process.versions.electron:"","webpack=true"].filter(Boolean).join(" ")+"\n    loaded from: "+e+"\n");function m(e){var t=y(r.join(e,"prebuilds")).map(g).filter(S(d,c)).sort(w)[0];if(t){var s=r.join(e,"prebuilds",t.name),i=y(s).map(E).filter(x(h,l)).sort(k(h))[0];if(i)return r.join(s,i.file)}}},m.parseTags=E,m.matchTags=x,m.compareTags=k,m.parseTuple=g,m.matchTuple=S,m.compareTuples=w}}),g=_({"../common/temp/node_modules/.pnpm/node-gyp-build@4.8.4/node_modules/node-gyp-build/index.js"(e,t){"function"==typeof n.addon?t.exports=n.addon.bind(n):t.exports=v()}}),S=_({"../common/temp/node_modules/.pnpm/bufferutil@4.0.9/node_modules/bufferutil/fallback.js"(e,t){t.exports={mask:(e,t,s,r,i)=>{for(var o=0;o<i;o++)s[r+o]=e[o]^t[3&o]},unmask:(e,t)=>{let s=e.length;for(var r=0;r<s;r++)e[r]^=t[3&r]}}}}),w=_({"../common/temp/node_modules/.pnpm/bufferutil@4.0.9/node_modules/bufferutil/index.js"(e,t){try{t.exports=g()(__dirname)}catch(e){t.exports=S()}}}),E=_({"../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/buffer-util.js"(e,t){var{EMPTY_BUFFER:s}=b(),r=Buffer[Symbol.species];function i(e,t,s,r,i){for(let o=0;o<i;o++)s[r+o]=e[o]^t[3&o]}function o(e,t){for(let s=0;s<e.length;s++)e[s]^=t[3&s]}function n(e){let t;return(n.readOnly=!0,Buffer.isBuffer(e))?e:(e instanceof ArrayBuffer?t=new r(e):ArrayBuffer.isView(e)?t=new r(e.buffer,e.byteOffset,e.byteLength):(t=Buffer.from(e),n.readOnly=!1),t)}if(t.exports={concat:function(e,t){if(0===e.length)return s;if(1===e.length)return e[0];let i=Buffer.allocUnsafe(t),o=0;for(let t=0;t<e.length;t++){let s=e[t];i.set(s,o),o+=s.length}return o<t?new r(i.buffer,i.byteOffset,o):i},mask:i,toArrayBuffer:function(e){return e.length===e.buffer.byteLength?e.buffer:e.buffer.slice(e.byteOffset,e.byteOffset+e.length)},toBuffer:n,unmask:o},!process.env.WS_NO_BUFFER_UTIL)try{let e=w();t.exports.mask=function(t,s,r,o,n){n<48?i(t,s,r,o,n):e.mask(t,s,r,o,n)},t.exports.unmask=function(t,s){t.length<32?o(t,s):e.unmask(t,s)}}catch(e){}}}),x=_({"../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/limiter.js"(e,t){var s=Symbol("kDone"),r=Symbol("kRun");t.exports=class{constructor(e){this[s]=()=>{this.pending--,this[r]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[r]()}[r](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[s])}}}}}),k=_({"../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/permessage-deflate.js"(e,t){var s,r=u("zlib"),i=E(),o=x(),{kStatusCode:n}=b(),a=Buffer[Symbol.species],l=Buffer.from([0,0,255,255]),h=Symbol("permessage-deflate"),c=Symbol("total-length"),d=Symbol("callback"),f=Symbol("buffers"),_=Symbol("error");function p(e){this[f].push(e),this[c]+=e.length}function m(e){if(this[c]+=e.length,this[h]._maxPayload<1||this[c]<=this[h]._maxPayload){this[f].push(e);return}this[_]=RangeError("Max payload size exceeded"),this[_].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[_][n]=1009,this.removeListener("data",m),this.reset()}function y(e){this[h]._inflate=null,e[n]=1007,this[d](e)}t.exports=class{constructor(e,t,r){this._maxPayload=0|r,this._options=e||{},this._threshold=void 0!==this._options.threshold?this._options.threshold:1024,this._isServer=!!t,this._deflate=null,this._inflate=null,this.params=null,s||(s=new o(void 0!==this._options.concurrencyLimit?this._options.concurrencyLimit:10))}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:null==this._options.clientMaxWindowBits&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[d];this._deflate.close(),this._deflate=null,e&&e(Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let t=this._options,s=e.find(e=>(!1!==t.serverNoContextTakeover||!e.server_no_context_takeover)&&(!e.server_max_window_bits||!1!==t.serverMaxWindowBits&&("number"!=typeof t.serverMaxWindowBits||!(t.serverMaxWindowBits>e.server_max_window_bits)))&&("number"!=typeof t.clientMaxWindowBits||!!e.client_max_window_bits));if(!s)throw Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(s.server_no_context_takeover=!0),t.clientNoContextTakeover&&(s.client_no_context_takeover=!0),"number"==typeof t.serverMaxWindowBits&&(s.server_max_window_bits=t.serverMaxWindowBits),"number"==typeof t.clientMaxWindowBits?s.client_max_window_bits=t.clientMaxWindowBits:(!0===s.client_max_window_bits||!1===t.clientMaxWindowBits)&&delete s.client_max_window_bits,s}acceptAsClient(e){let t=e[0];if(!1===this._options.clientNoContextTakeover&&t.client_no_context_takeover)throw Error('Unexpected parameter "client_no_context_takeover"');if(t.client_max_window_bits){if(!1===this._options.clientMaxWindowBits||"number"==typeof this._options.clientMaxWindowBits&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw Error('Unexpected or invalid parameter "client_max_window_bits"')}else"number"==typeof this._options.clientMaxWindowBits&&(t.client_max_window_bits=this._options.clientMaxWindowBits);return t}normalizeParams(e){return e.forEach(e=>{Object.keys(e).forEach(t=>{let s=e[t];if(s.length>1)throw Error(`Parameter "${t}" must have only a single value`);if(s=s[0],"client_max_window_bits"===t){if(!0!==s){let e=+s;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${s}`);s=e}else if(!this._isServer)throw TypeError(`Invalid value for parameter "${t}": ${s}`)}else if("server_max_window_bits"===t){let e=+s;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${s}`);s=e}else if("client_no_context_takeover"===t||"server_no_context_takeover"===t){if(!0!==s)throw TypeError(`Invalid value for parameter "${t}": ${s}`)}else throw Error(`Unknown parameter "${t}"`);e[t]=s})}),e}decompress(e,t,r){s.add(s=>{this._decompress(e,t,(e,t)=>{s(),r(e,t)})})}compress(e,t,r){s.add(s=>{this._compress(e,t,(e,t)=>{s(),r(e,t)})})}_decompress(e,t,s){let o=this._isServer?"client":"server";if(!this._inflate){let e=`${o}_max_window_bits`,t="number"!=typeof this.params[e]?r.Z_DEFAULT_WINDOWBITS:this.params[e];this._inflate=r.createInflateRaw({...this._options.zlibInflateOptions,windowBits:t}),this._inflate[h]=this,this._inflate[c]=0,this._inflate[f]=[],this._inflate.on("error",y),this._inflate.on("data",m)}this._inflate[d]=s,this._inflate.write(e),t&&this._inflate.write(l),this._inflate.flush(()=>{let e=this._inflate[_];if(e){this._inflate.close(),this._inflate=null,s(e);return}let r=i.concat(this._inflate[f],this._inflate[c]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[c]=0,this._inflate[f]=[],t&&this.params[`${o}_no_context_takeover`]&&this._inflate.reset()),s(null,r)})}_compress(e,t,s){let o=this._isServer?"server":"client";if(!this._deflate){let e=`${o}_max_window_bits`,t="number"!=typeof this.params[e]?r.Z_DEFAULT_WINDOWBITS:this.params[e];this._deflate=r.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:t}),this._deflate[c]=0,this._deflate[f]=[],this._deflate.on("data",p)}this._deflate[d]=s,this._deflate.write(e),this._deflate.flush(r.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let e=i.concat(this._deflate[f],this._deflate[c]);t&&(e=new a(e.buffer,e.byteOffset,e.length-4)),this._deflate[d]=null,this._deflate[c]=0,this._deflate[f]=[],t&&this.params[`${o}_no_context_takeover`]&&this._deflate.reset(),s(null,e)})}}}}),O=_({"../common/temp/node_modules/.pnpm/utf-8-validate@5.0.10/node_modules/utf-8-validate/fallback.js"(e,t){t.exports=function(e){let t=e.length,s=0;for(;s<t;)if((128&e[s])==0)s++;else if((224&e[s])==192){if(s+1===t||(192&e[s+1])!=128||(254&e[s])==192)return!1;s+=2}else if((240&e[s])==224){if(s+2>=t||(192&e[s+1])!=128||(192&e[s+2])!=128||224===e[s]&&(224&e[s+1])==128||237===e[s]&&(224&e[s+1])==160)return!1;s+=3}else{if((248&e[s])!=240||s+3>=t||(192&e[s+1])!=128||(192&e[s+2])!=128||(192&e[s+3])!=128||240===e[s]&&(240&e[s+1])==128||244===e[s]&&e[s+1]>143||e[s]>244)return!1;s+=4}return!0}}}),T=_({"../common/temp/node_modules/.pnpm/utf-8-validate@5.0.10/node_modules/utf-8-validate/index.js"(e,t){try{t.exports=g()(__dirname)}catch(e){t.exports=O()}}}),C=_({"../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/validation.js"(e,t){var{isUtf8:s}=u("buffer"),{hasBlob:r}=b();function i(e){let t=e.length,s=0;for(;s<t;)if((128&e[s])==0)s++;else if((224&e[s])==192){if(s+1===t||(192&e[s+1])!=128||(254&e[s])==192)return!1;s+=2}else if((240&e[s])==224){if(s+2>=t||(192&e[s+1])!=128||(192&e[s+2])!=128||224===e[s]&&(224&e[s+1])==128||237===e[s]&&(224&e[s+1])==160)return!1;s+=3}else{if((248&e[s])!=240||s+3>=t||(192&e[s+1])!=128||(192&e[s+2])!=128||(192&e[s+3])!=128||240===e[s]&&(240&e[s+1])==128||244===e[s]&&e[s+1]>143||e[s]>244)return!1;s+=4}return!0}if(t.exports={isBlob:function(e){return r&&"object"==typeof e&&"function"==typeof e.arrayBuffer&&"string"==typeof e.type&&"function"==typeof e.stream&&("Blob"===e[Symbol.toStringTag]||"File"===e[Symbol.toStringTag])},isValidStatusCode:function(e){return e>=1e3&&e<=1014&&1004!==e&&1005!==e&&1006!==e||e>=3e3&&e<=4999},isValidUTF8:i,tokenChars:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0]},s)t.exports.isValidUTF8=function(e){return e.length<24?i(e):s(e)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let e=T();t.exports.isValidUTF8=function(t){return t.length<32?i(t):e(t)}}catch(e){}}}),N=_({"../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/receiver.js"(e,t){var{Writable:s}=u("stream"),r=k(),{BINARY_TYPES:i,EMPTY_BUFFER:o,kStatusCode:n,kWebSocket:a}=b(),{concat:l,toArrayBuffer:h,unmask:c}=E(),{isValidStatusCode:d,isValidUTF8:f}=C(),_=Buffer[Symbol.species];t.exports=class extends s{constructor(e={}){super(),this._allowSynchronousEvents=void 0===e.allowSynchronousEvents||e.allowSynchronousEvents,this._binaryType=e.binaryType||i[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=0|e.maxPayload,this._skipUTF8Validation=!!e.skipUTF8Validation,this[a]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(e,t,s){if(8===this._opcode&&0==this._state)return s();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(s)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let t=this._buffers[0];return this._buffers[0]=new _(t.buffer,t.byteOffset+e,t.length-e),new _(t.buffer,t.byteOffset,e)}let t=Buffer.allocUnsafe(e);do{let s=this._buffers[0],r=t.length-e;e>=s.length?t.set(this._buffers.shift(),r):(t.set(new Uint8Array(s.buffer,s.byteOffset,e),r),this._buffers[0]=new _(s.buffer,s.byteOffset+e,s.length-e)),e-=s.length}while(e>0);return t}startLoop(e){this._loop=!0;do switch(this._state){case 0:this.getInfo(e);break;case 1:this.getPayloadLength16(e);break;case 2:this.getPayloadLength64(e);break;case 3:this.getMask();break;case 4:this.getData(e);break;case 5:case 6:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let t=this.consume(2);if((48&t[0])!=0){e(this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3"));return}let s=(64&t[0])==64;if(s&&!this._extensions[r.extensionName]){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._fin=(128&t[0])==128,this._opcode=15&t[0],this._payloadLength=127&t[1],0===this._opcode){if(s){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(!this._fragmented){e(this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._opcode=this._fragmented}else if(1===this._opcode||2===this._opcode){if(this._fragmented){e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._compressed=s}else if(this._opcode>7&&this._opcode<11){if(!this._fin){e(this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN"));return}if(s){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._payloadLength>125||8===this._opcode&&1===this._payloadLength){e(this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH"));return}}else{e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}if(this._fin||this._fragmented||(this._fragmented=this._opcode),this._masked=(128&t[1])==128,this._isServer){if(!this._masked){e(this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK"));return}}else if(this._masked){e(this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK"));return}126===this._payloadLength?this._state=1:127===this._payloadLength?this._state=2:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let t=this.consume(8),s=t.readUInt32BE(0);if(s>2097151){e(this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH"));return}this._payloadLength=0x100000000*s+t.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0)){e(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._masked?this._state=3:this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(e){let t=o;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}t=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!=0&&c(t,this._mask)}if(this._opcode>7){this.controlMessage(t,e);return}if(this._compressed){this._state=5,this.decompress(t,e);return}t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage(e)}decompress(e,t){this._extensions[r.extensionName].decompress(e,this._fin,(e,s)=>{if(e)return t(e);if(s.length){if(this._messageLength+=s.length,this._messageLength>this._maxPayload&&this._maxPayload>0){t(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._fragments.push(s)}this.dataMessage(t),0===this._state&&this.startLoop(t)})}dataMessage(e){if(!this._fin){this._state=0;return}let t=this._messageLength,s=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],2===this._opcode){let r;r="nodebuffer"===this._binaryType?l(s,t):"arraybuffer"===this._binaryType?h(l(s,t)):"blob"===this._binaryType?new Blob(s):s,this._allowSynchronousEvents?(this.emit("message",r,!0),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",r,!0),this._state=0,this.startLoop(e)}))}else{let r=l(s,t);if(!this._skipUTF8Validation&&!f(r)){e(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}5===this._state||this._allowSynchronousEvents?(this.emit("message",r,!1),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",r,!1),this._state=0,this.startLoop(e)}))}}controlMessage(e,t){if(8===this._opcode){if(0===e.length)this._loop=!1,this.emit("conclude",1005,o),this.end();else{let s=e.readUInt16BE(0);if(!d(s)){t(this.createError(RangeError,`invalid status code ${s}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE"));return}let r=new _(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!f(r)){t(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}this._loop=!1,this.emit("conclude",s,r),this.end()}this._state=0;return}this._allowSynchronousEvents?(this.emit(9===this._opcode?"ping":"pong",e),this._state=0):(this._state=6,setImmediate(()=>{this.emit(9===this._opcode?"ping":"pong",e),this._state=0,this.startLoop(t)}))}createError(e,t,s,r,i){this._loop=!1,this._errored=!0;let o=new e(s?`Invalid WebSocket frame: ${t}`:t);return Error.captureStackTrace(o,this.createError),o.code=i,o[n]=r,o}}}}),L=_({"../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/sender.js"(e,t){var s,{Duplex:r}=u("stream"),{randomFillSync:i}=u("crypto"),o=k(),{EMPTY_BUFFER:n,kWebSocket:a,NOOP:l}=b(),{isBlob:h,isValidStatusCode:c}=C(),{mask:d,toBuffer:f}=E(),_=Symbol("kByteLength"),p=Buffer.alloc(4),m=8192;function y(e,t,s){"function"==typeof s&&s(t);for(let s=0;s<e._queue.length;s++){let r=e._queue[s],i=r[r.length-1];"function"==typeof i&&i(t)}}function v(e,t,s){y(e,t,s),e.onerror(t)}t.exports=class e{constructor(e,t,s){this._extensions=t||{},s&&(this._generateMask=s,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._queue=[],this._state=0,this.onerror=l,this[a]=void 0}static frame(e,t){let r,o;let n=!1,a=2,l=!1;t.mask&&(r=t.maskBuffer||p,t.generateMask?t.generateMask(r):(8192===m&&(void 0===s&&(s=Buffer.alloc(8192)),i(s,0,8192),m=0),r[0]=s[m++],r[1]=s[m++],r[2]=s[m++],r[3]=s[m++]),l=(r[0]|r[1]|r[2]|r[3])==0,a=6),"string"==typeof e?o=(!t.mask||l)&&void 0!==t[_]?t[_]:(e=Buffer.from(e)).length:(o=e.length,n=t.mask&&t.readOnly&&!l);let h=o;o>=65536?(a+=8,h=127):o>125&&(a+=2,h=126);let c=Buffer.allocUnsafe(n?o+a:a);return(c[0]=t.fin?128|t.opcode:t.opcode,t.rsv1&&(c[0]|=64),c[1]=h,126===h?c.writeUInt16BE(o,2):127===h&&(c[2]=c[3]=0,c.writeUIntBE(o,4,6)),t.mask)?(c[1]|=128,c[a-4]=r[0],c[a-3]=r[1],c[a-2]=r[2],c[a-1]=r[3],l)?[c,e]:n?(d(e,r,c,a,o),[c]):(d(e,r,e,0,o),[c,e]):[c,e]}close(t,s,r,i){let o;if(void 0===t)o=n;else if("number"==typeof t&&c(t)){if(void 0!==s&&s.length){let e=Buffer.byteLength(s);if(e>123)throw RangeError("The message must not be greater than 123 bytes");(o=Buffer.allocUnsafe(2+e)).writeUInt16BE(t,0),"string"==typeof s?o.write(s,2):o.set(s,2)}else(o=Buffer.allocUnsafe(2)).writeUInt16BE(t,0)}else throw TypeError("First argument must be a valid error code number");let a={[_]:o.length,fin:!0,generateMask:this._generateMask,mask:r,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};0!==this._state?this.enqueue([this.dispatch,o,!1,a,i]):this.sendFrame(e.frame(o,a),i)}ping(t,s,r){let i,o;if("string"==typeof t?(i=Buffer.byteLength(t),o=!1):h(t)?(i=t.size,o=!1):(i=(t=f(t)).length,o=f.readOnly),i>125)throw RangeError("The data size must not be greater than 125 bytes");let n={[_]:i,fin:!0,generateMask:this._generateMask,mask:s,maskBuffer:this._maskBuffer,opcode:9,readOnly:o,rsv1:!1};h(t)?0!==this._state?this.enqueue([this.getBlobData,t,!1,n,r]):this.getBlobData(t,!1,n,r):0!==this._state?this.enqueue([this.dispatch,t,!1,n,r]):this.sendFrame(e.frame(t,n),r)}pong(t,s,r){let i,o;if("string"==typeof t?(i=Buffer.byteLength(t),o=!1):h(t)?(i=t.size,o=!1):(i=(t=f(t)).length,o=f.readOnly),i>125)throw RangeError("The data size must not be greater than 125 bytes");let n={[_]:i,fin:!0,generateMask:this._generateMask,mask:s,maskBuffer:this._maskBuffer,opcode:10,readOnly:o,rsv1:!1};h(t)?0!==this._state?this.enqueue([this.getBlobData,t,!1,n,r]):this.getBlobData(t,!1,n,r):0!==this._state?this.enqueue([this.dispatch,t,!1,n,r]):this.sendFrame(e.frame(t,n),r)}send(e,t,s){let r,i;let n=this._extensions[o.extensionName],a=t.binary?2:1,l=t.compress;"string"==typeof e?(r=Buffer.byteLength(e),i=!1):h(e)?(r=e.size,i=!1):(r=(e=f(e)).length,i=f.readOnly),this._firstFragment?(this._firstFragment=!1,l&&n&&n.params[n._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(l=r>=n._threshold),this._compress=l):(l=!1,a=0),t.fin&&(this._firstFragment=!0);let c={[_]:r,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:a,readOnly:i,rsv1:l};h(e)?0!==this._state?this.enqueue([this.getBlobData,e,this._compress,c,s]):this.getBlobData(e,this._compress,c,s):0!==this._state?this.enqueue([this.dispatch,e,this._compress,c,s]):this.dispatch(e,this._compress,c,s)}getBlobData(t,s,r,i){this._bufferedBytes+=r[_],this._state=2,t.arrayBuffer().then(t=>{if(this._socket.destroyed){let e=Error("The socket was closed while the blob was being read");process.nextTick(y,this,e,i);return}this._bufferedBytes-=r[_];let o=f(t);s?this.dispatch(o,s,r,i):(this._state=0,this.sendFrame(e.frame(o,r),i),this.dequeue())}).catch(e=>{process.nextTick(v,this,e,i)})}dispatch(t,s,r,i){if(!s){this.sendFrame(e.frame(t,r),i);return}let n=this._extensions[o.extensionName];this._bufferedBytes+=r[_],this._state=1,n.compress(t,r.fin,(t,s)=>{if(this._socket.destroyed){y(this,Error("The socket was closed while data was being compressed"),i);return}this._bufferedBytes-=r[_],this._state=0,r.readOnly=!1,this.sendFrame(e.frame(s,r),i),this.dequeue()})}dequeue(){for(;0===this._state&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][_],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][_],this._queue.push(e)}sendFrame(e,t){2===e.length?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}}}}),P=_({"../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/event-target.js"(e,t){var{kForOnEventAttribute:s,kListener:r}=b(),i=Symbol("kCode"),o=Symbol("kData"),n=Symbol("kError"),a=Symbol("kMessage"),l=Symbol("kReason"),h=Symbol("kTarget"),c=Symbol("kType"),d=Symbol("kWasClean"),f=class{constructor(e){this[h]=null,this[c]=e}get target(){return this[h]}get type(){return this[c]}};Object.defineProperty(f.prototype,"target",{enumerable:!0}),Object.defineProperty(f.prototype,"type",{enumerable:!0});var u=class extends f{constructor(e,t={}){super(e),this[i]=void 0===t.code?0:t.code,this[l]=void 0===t.reason?"":t.reason,this[d]=void 0!==t.wasClean&&t.wasClean}get code(){return this[i]}get reason(){return this[l]}get wasClean(){return this[d]}};Object.defineProperty(u.prototype,"code",{enumerable:!0}),Object.defineProperty(u.prototype,"reason",{enumerable:!0}),Object.defineProperty(u.prototype,"wasClean",{enumerable:!0});var _=class extends f{constructor(e,t={}){super(e),this[n]=void 0===t.error?null:t.error,this[a]=void 0===t.message?"":t.message}get error(){return this[n]}get message(){return this[a]}};Object.defineProperty(_.prototype,"error",{enumerable:!0}),Object.defineProperty(_.prototype,"message",{enumerable:!0});var p=class extends f{constructor(e,t={}){super(e),this[o]=void 0===t.data?null:t.data}get data(){return this[o]}};function m(e,t,s){"object"==typeof e&&e.handleEvent?e.handleEvent.call(e,s):e.call(t,s)}Object.defineProperty(p.prototype,"data",{enumerable:!0}),t.exports={CloseEvent:u,ErrorEvent:_,Event:f,EventTarget:{addEventListener(e,t,i={}){let o;for(let o of this.listeners(e))if(!i[s]&&o[r]===t&&!o[s])return;if("message"===e)o=function(e,s){let r=new p("message",{data:s?e:e.toString()});r[h]=this,m(t,this,r)};else if("close"===e)o=function(e,s){let r=new u("close",{code:e,reason:s.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});r[h]=this,m(t,this,r)};else if("error"===e)o=function(e){let s=new _("error",{error:e,message:e.message});s[h]=this,m(t,this,s)};else{if("open"!==e)return;o=function(){let e=new f("open");e[h]=this,m(t,this,e)}}o[s]=!!i[s],o[r]=t,i.once?this.once(e,o):this.on(e,o)},removeEventListener(e,t){for(let i of this.listeners(e))if(i[r]===t&&!i[s]){this.removeListener(e,i);break}}},MessageEvent:p}}}),B=_({"../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/extension.js"(e,t){var{tokenChars:s}=C();function r(e,t,s){void 0===e[t]?e[t]=[s]:e[t].push(s)}t.exports={format:function(e){return Object.keys(e).map(t=>{let s=e[t];return Array.isArray(s)||(s=[s]),s.map(e=>[t].concat(Object.keys(e).map(t=>{let s=e[t];return Array.isArray(s)||(s=[s]),s.map(e=>!0===e?t:`${t}=${e}`).join("; ")})).join("; ")).join(", ")}).join(", ")},parse:function(e){let t,i;let o=Object.create(null),n=Object.create(null),a=!1,l=!1,h=!1,c=-1,d=-1,f=-1,u=0;for(;u<e.length;u++)if(d=e.charCodeAt(u),void 0===t){if(-1===f&&1===s[d])-1===c&&(c=u);else if(0!==u&&(32===d||9===d))-1===f&&-1!==c&&(f=u);else if(59===d||44===d){if(-1===c)throw SyntaxError(`Unexpected character at index ${u}`);-1===f&&(f=u);let s=e.slice(c,f);44===d?(r(o,s,n),n=Object.create(null)):t=s,c=f=-1}else throw SyntaxError(`Unexpected character at index ${u}`)}else if(void 0===i){if(-1===f&&1===s[d])-1===c&&(c=u);else if(32===d||9===d)-1===f&&-1!==c&&(f=u);else if(59===d||44===d){if(-1===c)throw SyntaxError(`Unexpected character at index ${u}`);-1===f&&(f=u),r(n,e.slice(c,f),!0),44===d&&(r(o,t,n),n=Object.create(null),t=void 0),c=f=-1}else if(61===d&&-1!==c&&-1===f)i=e.slice(c,u),c=f=-1;else throw SyntaxError(`Unexpected character at index ${u}`)}else if(l){if(1!==s[d])throw SyntaxError(`Unexpected character at index ${u}`);-1===c?c=u:a||(a=!0),l=!1}else if(h){if(1===s[d])-1===c&&(c=u);else if(34===d&&-1!==c)h=!1,f=u;else if(92===d)l=!0;else throw SyntaxError(`Unexpected character at index ${u}`)}else if(34===d&&61===e.charCodeAt(u-1))h=!0;else if(-1===f&&1===s[d])-1===c&&(c=u);else if(-1!==c&&(32===d||9===d))-1===f&&(f=u);else if(59===d||44===d){if(-1===c)throw SyntaxError(`Unexpected character at index ${u}`);-1===f&&(f=u);let s=e.slice(c,f);a&&(s=s.replace(/\\/g,""),a=!1),r(n,i,s),44===d&&(r(o,t,n),n=Object.create(null),t=void 0),i=void 0,c=f=-1}else throw SyntaxError(`Unexpected character at index ${u}`);if(-1===c||h||32===d||9===d)throw SyntaxError("Unexpected end of input");-1===f&&(f=u);let _=e.slice(c,f);return void 0===t?r(o,_,n):(void 0===i?r(n,_,!0):a?r(n,i,_.replace(/\\/g,"")):r(n,i,_),r(o,t,n)),o}}}}),R=_({"../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/websocket.js"(e,t){var s=u("events"),r=u("https"),i=u("http"),o=u("net"),n=u("tls"),{randomBytes:a,createHash:l}=u("crypto"),{Duplex:h,Readable:c}=u("stream"),{URL:d}=u("url"),f=k(),_=N(),p=L(),{isBlob:m}=C(),{BINARY_TYPES:y,EMPTY_BUFFER:v,GUID:g,kForOnEventAttribute:S,kListener:w,kStatusCode:x,kWebSocket:O,NOOP:T}=b(),{EventTarget:{addEventListener:R,removeEventListener:I}}=P(),{format:U,parse:j}=B(),{toBuffer:D}=E(),M=Symbol("kAborted"),W=[8,13],A=["CONNECTING","OPEN","CLOSING","CLOSED"],F=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/,$=class e extends s{constructor(t,s,o){super(),this._binaryType=y[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=v,this._closeTimer=null,this._errorEmitted=!1,this._extensions={},this._paused=!1,this._protocol="",this._readyState=e.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,null!==t?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,void 0===s?s=[]:Array.isArray(s)||("object"==typeof s&&null!==s?(o=s,s=[]):s=[s]),function e(t,s,o,n){let h,c,u,_;let p={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:W[1],maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...n,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(t._autoPong=p.autoPong,!W.includes(p.protocolVersion))throw RangeError(`Unsupported protocol version: ${p.protocolVersion} (supported versions: ${W.join(", ")})`);if(s instanceof d)h=s;else try{h=new d(s)}catch(e){throw SyntaxError(`Invalid URL: ${s}`)}"http:"===h.protocol?h.protocol="ws:":"https:"===h.protocol&&(h.protocol="wss:"),t._url=h.href;let m="wss:"===h.protocol,y="ws+unix:"===h.protocol;if("ws:"===h.protocol||m||y?y&&!h.pathname?c="The URL's pathname is empty":h.hash&&(c="The URL contains a fragment identifier"):c='The URL\'s protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"',c){let e=SyntaxError(c);if(0===t._redirects)throw e;G(t,e);return}let b=m?443:80,v=a(16).toString("base64"),S=m?r.request:i.request,w=new Set;if(p.createConnection=p.createConnection||(m?q:V),p.defaultPort=p.defaultPort||b,p.port=h.port||b,p.host=h.hostname.startsWith("[")?h.hostname.slice(1,-1):h.hostname,p.headers={...p.headers,"Sec-WebSocket-Version":p.protocolVersion,"Sec-WebSocket-Key":v,Connection:"Upgrade",Upgrade:"websocket"},p.path=h.pathname+h.search,p.timeout=p.handshakeTimeout,p.perMessageDeflate&&(u=new f(!0!==p.perMessageDeflate?p.perMessageDeflate:{},!1,p.maxPayload),p.headers["Sec-WebSocket-Extensions"]=U({[f.extensionName]:u.offer()})),o.length){for(let e of o){if("string"!=typeof e||!F.test(e)||w.has(e))throw SyntaxError("An invalid or duplicated subprotocol was specified");w.add(e)}p.headers["Sec-WebSocket-Protocol"]=o.join(",")}if(p.origin&&(p.protocolVersion<13?p.headers["Sec-WebSocket-Origin"]=p.origin:p.headers.Origin=p.origin),(h.username||h.password)&&(p.auth=`${h.username}:${h.password}`),y){let e=p.path.split(":");p.socketPath=e[0],p.path=e[1]}if(p.followRedirects){if(0===t._redirects){t._originalIpc=y,t._originalSecure=m,t._originalHostOrSocketPath=y?p.socketPath:h.host;let e=n&&n.headers;if(n={...n,headers:{}},e)for(let[t,s]of Object.entries(e))n.headers[t.toLowerCase()]=s}else if(0===t.listenerCount("redirect")){let e=y?!!t._originalIpc&&p.socketPath===t._originalHostOrSocketPath:!t._originalIpc&&h.host===t._originalHostOrSocketPath;e&&(!t._originalSecure||m)||(delete p.headers.authorization,delete p.headers.cookie,e||delete p.headers.host,p.auth=void 0)}p.auth&&!n.headers.authorization&&(n.headers.authorization="Basic "+Buffer.from(p.auth).toString("base64")),_=t._req=S(p),t._redirects&&t.emit("redirect",t.url,_)}else _=t._req=S(p);p.timeout&&_.on("timeout",()=>{z(t,_,"Opening handshake has timed out")}),_.on("error",e=>{null===_||_[M]||(_=t._req=null,G(t,e))}),_.on("response",r=>{let i=r.headers.location,a=r.statusCode;if(i&&p.followRedirects&&a>=300&&a<400){let r;if(++t._redirects>p.maxRedirects){z(t,_,"Maximum redirects exceeded");return}_.abort();try{r=new d(i,s)}catch(e){G(t,SyntaxError(`Invalid URL: ${i}`));return}e(t,r,o,n)}else t.emit("unexpected-response",_,r)||z(t,_,`Unexpected server response: ${r.statusCode}`)}),_.on("upgrade",(e,s,r)=>{let i;if(t.emit("upgrade",e),t.readyState!==$.CONNECTING)return;_=t._req=null;let o=e.headers.upgrade;if(void 0===o||"websocket"!==o.toLowerCase()){z(t,s,"Invalid Upgrade header");return}let n=l("sha1").update(v+g).digest("base64");if(e.headers["sec-websocket-accept"]!==n){z(t,s,"Invalid Sec-WebSocket-Accept header");return}let a=e.headers["sec-websocket-protocol"];if(void 0!==a?w.size?w.has(a)||(i="Server sent an invalid subprotocol"):i="Server sent a subprotocol but none was requested":w.size&&(i="Server sent no subprotocol"),i){z(t,s,i);return}a&&(t._protocol=a);let h=e.headers["sec-websocket-extensions"];if(void 0!==h){let e;if(!u){z(t,s,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}try{e=j(h)}catch(e){z(t,s,"Invalid Sec-WebSocket-Extensions header");return}let r=Object.keys(e);if(1!==r.length||r[0]!==f.extensionName){z(t,s,"Server indicated an extension that was not requested");return}try{u.accept(e[f.extensionName])}catch(e){z(t,s,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[f.extensionName]=u}t.setSocket(s,r,{allowSynchronousEvents:p.allowSynchronousEvents,generateMask:p.generateMask,maxPayload:p.maxPayload,skipUTF8Validation:p.skipUTF8Validation})}),p.finishRequest?p.finishRequest(_,t):_.end()}(this,t,s,o)):(this._autoPong=o.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){y.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(t,s,r){let i=new _({allowSynchronousEvents:r.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:r.maxPayload,skipUTF8Validation:r.skipUTF8Validation}),o=new p(t,this._extensions,r.generateMask);this._receiver=i,this._sender=o,this._socket=t,i[O]=this,o[O]=this,t[O]=this,i.on("conclude",X),i.on("drain",K),i.on("error",Y),i.on("message",J),i.on("ping",Q),i.on("pong",ee),o.onerror=es,t.setTimeout&&t.setTimeout(0),t.setNoDelay&&t.setNoDelay(),s.length>0&&t.unshift(s),t.on("close",ei),t.on("data",eo),t.on("end",en),t.on("error",ea),this._readyState=e.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=e.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[f.extensionName]&&this._extensions[f.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=e.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(t,s){if(this.readyState!==e.CLOSED){if(this.readyState===e.CONNECTING){z(this,this._req,"WebSocket was closed before the connection was established");return}if(this.readyState===e.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=e.CLOSING,this._sender.close(t,s,!this._isServer,e=>{!e&&(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),er(this)}}pause(){this.readyState!==e.CONNECTING&&this.readyState!==e.CLOSED&&(this._paused=!0,this._socket.pause())}ping(t,s,r){if(this.readyState===e.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof t?(r=t,t=s=void 0):"function"==typeof s&&(r=s,s=void 0),"number"==typeof t&&(t=t.toString()),this.readyState!==e.OPEN){H(this,t,r);return}void 0===s&&(s=!this._isServer),this._sender.ping(t||v,s,r)}pong(t,s,r){if(this.readyState===e.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof t?(r=t,t=s=void 0):"function"==typeof s&&(r=s,s=void 0),"number"==typeof t&&(t=t.toString()),this.readyState!==e.OPEN){H(this,t,r);return}void 0===s&&(s=!this._isServer),this._sender.pong(t||v,s,r)}resume(){this.readyState!==e.CONNECTING&&this.readyState!==e.CLOSED&&(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(t,s,r){if(this.readyState===e.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof s&&(r=s,s={}),"number"==typeof t&&(t=t.toString()),this.readyState!==e.OPEN){H(this,t,r);return}let i={binary:"string"!=typeof t,mask:!this._isServer,compress:!0,fin:!0,...s};this._extensions[f.extensionName]||(i.compress=!1),this._sender.send(t||v,i,r)}terminate(){if(this.readyState!==e.CLOSED){if(this.readyState===e.CONNECTING){z(this,this._req,"WebSocket was closed before the connection was established");return}this._socket&&(this._readyState=e.CLOSING,this._socket.destroy())}}};function G(e,t){e._readyState=$.CLOSING,e._errorEmitted=!0,e.emit("error",t),e.emitClose()}function V(e){return e.path=e.socketPath,o.connect(e)}function q(e){return e.path=void 0,e.servername||""===e.servername||(e.servername=o.isIP(e.host)?"":e.host),n.connect(e)}function z(e,t,s){e._readyState=$.CLOSING;let r=Error(s);Error.captureStackTrace(r,z),t.setHeader?(t[M]=!0,t.abort(),t.socket&&!t.socket.destroyed&&t.socket.destroy(),process.nextTick(G,e,r)):(t.destroy(r),t.once("error",e.emit.bind(e,"error")),t.once("close",e.emitClose.bind(e)))}function H(e,t,s){if(t){let s=m(t)?t.size:D(t).length;e._socket?e._sender._bufferedBytes+=s:e._bufferedAmount+=s}if(s){let t=Error(`WebSocket is not open: readyState ${e.readyState} (${A[e.readyState]})`);process.nextTick(s,t)}}function X(e,t){let s=this[O];s._closeFrameReceived=!0,s._closeMessage=t,s._closeCode=e,void 0!==s._socket[O]&&(s._socket.removeListener("data",eo),process.nextTick(et,s._socket),1005===e?s.close():s.close(e,t))}function K(){let e=this[O];e.isPaused||e._socket.resume()}function Y(e){let t=this[O];void 0!==t._socket[O]&&(t._socket.removeListener("data",eo),process.nextTick(et,t._socket),t.close(e[x])),t._errorEmitted||(t._errorEmitted=!0,t.emit("error",e))}function Z(){this[O].emitClose()}function J(e,t){this[O].emit("message",e,t)}function Q(e){let t=this[O];t._autoPong&&t.pong(e,!this._isServer,T),t.emit("ping",e)}function ee(e){this[O].emit("pong",e)}function et(e){e.resume()}function es(e){let t=this[O];t.readyState===$.CLOSED||(t.readyState===$.OPEN&&(t._readyState=$.CLOSING,er(t)),this._socket.end(),t._errorEmitted||(t._errorEmitted=!0,t.emit("error",e)))}function er(e){e._closeTimer=setTimeout(e._socket.destroy.bind(e._socket),3e4)}function ei(){let e;let t=this[O];this.removeListener("close",ei),this.removeListener("data",eo),this.removeListener("end",en),t._readyState=$.CLOSING,this._readableState.endEmitted||t._closeFrameReceived||t._receiver._writableState.errorEmitted||null===(e=t._socket.read())||t._receiver.write(e),t._receiver.end(),this[O]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",Z),t._receiver.on("finish",Z))}function eo(e){this[O]._receiver.write(e)||this.pause()}function en(){let e=this[O];e._readyState=$.CLOSING,e._receiver.end(),this.end()}function ea(){let e=this[O];this.removeListener("error",ea),this.on("error",T),e&&(e._readyState=$.CLOSING,this.destroy())}Object.defineProperty($,"CONNECTING",{enumerable:!0,value:A.indexOf("CONNECTING")}),Object.defineProperty($.prototype,"CONNECTING",{enumerable:!0,value:A.indexOf("CONNECTING")}),Object.defineProperty($,"OPEN",{enumerable:!0,value:A.indexOf("OPEN")}),Object.defineProperty($.prototype,"OPEN",{enumerable:!0,value:A.indexOf("OPEN")}),Object.defineProperty($,"CLOSING",{enumerable:!0,value:A.indexOf("CLOSING")}),Object.defineProperty($.prototype,"CLOSING",{enumerable:!0,value:A.indexOf("CLOSING")}),Object.defineProperty($,"CLOSED",{enumerable:!0,value:A.indexOf("CLOSED")}),Object.defineProperty($.prototype,"CLOSED",{enumerable:!0,value:A.indexOf("CLOSED")}),["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(e=>{Object.defineProperty($.prototype,e,{enumerable:!0})}),["open","error","close","message"].forEach(e=>{Object.defineProperty($.prototype,`on${e}`,{enumerable:!0,get(){for(let t of this.listeners(e))if(t[S])return t[w];return null},set(t){for(let t of this.listeners(e))if(t[S]){this.removeListener(e,t);break}"function"==typeof t&&this.addEventListener(e,t,{[S]:!0})}})}),$.prototype.addEventListener=R,$.prototype.removeEventListener=I,t.exports=$}}),I=_({"../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/subprotocol.js"(e,t){var{tokenChars:s}=C();t.exports={parse:function(e){let t=new Set,r=-1,i=-1,o=0;for(;o<e.length;o++){let n=e.charCodeAt(o);if(-1===i&&1===s[n])-1===r&&(r=o);else if(0!==o&&(32===n||9===n))-1===i&&-1!==r&&(i=o);else if(44===n){if(-1===r)throw SyntaxError(`Unexpected character at index ${o}`);-1===i&&(i=o);let s=e.slice(r,i);if(t.has(s))throw SyntaxError(`The "${s}" subprotocol is duplicated`);t.add(s),r=i=-1}else throw SyntaxError(`Unexpected character at index ${o}`)}if(-1===r||-1!==i)throw SyntaxError("Unexpected end of input");let n=e.slice(r,o);if(t.has(n))throw SyntaxError(`The "${n}" subprotocol is duplicated`);return t.add(n),t}}}}),U=_({"../common/temp/node_modules/.pnpm/ws@8.18.0_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/ws/lib/websocket-server.js"(e,t){var s=u("events"),r=u("http"),{Duplex:i}=u("stream"),{createHash:o}=u("crypto"),n=B(),a=k(),l=I(),h=R(),{GUID:c,kWebSocket:d}=b(),f=/^[+/0-9A-Za-z]{22}==$/;function _(e){e._state=2,e.emit("close")}function p(){this.destroy()}function m(e,t,s,i){s=s||r.STATUS_CODES[t],i={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(s),...i},e.once("finish",e.destroy),e.end(`HTTP/1.1 ${t} ${r.STATUS_CODES[t]}\r
`+Object.keys(i).map(e=>`${e}: ${i[e]}`).join("\r\n")+"\r\n\r\n"+s)}function y(e,t,s,r,i){if(e.listenerCount("wsClientError")){let r=Error(i);Error.captureStackTrace(r,y),e.emit("wsClientError",r,s,t)}else m(s,r,i)}t.exports=class extends s{constructor(e,t){if(super(),null==(e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:h,...e}).port&&!e.server&&!e.noServer||null!=e.port&&(e.server||e.noServer)||e.server&&e.noServer)throw TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(null!=e.port?(this._server=r.createServer((e,t)=>{let s=r.STATUS_CODES[426];t.writeHead(426,{"Content-Length":s.length,"Content-Type":"text/plain"}),t.end(s)}),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){let e=this.emit.bind(this,"connection");this._removeListeners=function(e,t){for(let s of Object.keys(t))e.on(s,t[s]);return function(){for(let s of Object.keys(t))e.removeListener(s,t[s])}}(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(t,s,r)=>{this.handleUpgrade(t,s,r,e)}})}!0===e.perMessageDeflate&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=0}address(){if(this.options.noServer)throw Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(2===this._state){e&&this.once("close",()=>{e(Error("The server is not running"))}),process.nextTick(_,this);return}if(e&&this.once("close",e),1!==this._state){if(this._state=1,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients&&this.clients.size?this._shouldEmitClose=!0:process.nextTick(_,this);else{let e=this._server;this._removeListeners(),this._removeListeners=this._server=null,e.close(()=>{_(this)})}}}shouldHandle(e){if(this.options.path){let t=e.url.indexOf("?");if((-1!==t?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,s,r){t.on("error",p);let i=e.headers["sec-websocket-key"],o=e.headers.upgrade,h=+e.headers["sec-websocket-version"];if("GET"!==e.method){y(this,e,t,405,"Invalid HTTP method");return}if(void 0===o||"websocket"!==o.toLowerCase()){y(this,e,t,400,"Invalid Upgrade header");return}if(void 0===i||!f.test(i)){y(this,e,t,400,"Missing or invalid Sec-WebSocket-Key header");return}if(8!==h&&13!==h){y(this,e,t,400,"Missing or invalid Sec-WebSocket-Version header");return}if(!this.shouldHandle(e)){m(t,400);return}let c=e.headers["sec-websocket-protocol"],d=new Set;if(void 0!==c)try{d=l.parse(c)}catch(s){y(this,e,t,400,"Invalid Sec-WebSocket-Protocol header");return}let u=e.headers["sec-websocket-extensions"],_={};if(this.options.perMessageDeflate&&void 0!==u){let s=new a(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let e=n.parse(u);e[a.extensionName]&&(s.accept(e[a.extensionName]),_[a.extensionName]=s)}catch(s){y(this,e,t,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let o={origin:e.headers[`${8===h?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(2===this.options.verifyClient.length){this.options.verifyClient(o,(o,n,a,l)=>{if(!o)return m(t,n||401,a,l);this.completeUpgrade(_,i,d,e,t,s,r)});return}if(!this.options.verifyClient(o))return m(t,401)}this.completeUpgrade(_,i,d,e,t,s,r)}completeUpgrade(e,t,s,r,i,l,h){if(!i.readable||!i.writable)return i.destroy();if(i[d])throw Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>0)return m(i,503);let f=o("sha1").update(t+c).digest("base64"),u=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${f}`],y=new this.options.WebSocket(null,void 0,this.options);if(s.size){let e=this.options.handleProtocols?this.options.handleProtocols(s,r):s.values().next().value;e&&(u.push(`Sec-WebSocket-Protocol: ${e}`),y._protocol=e)}if(e[a.extensionName]){let t=e[a.extensionName].params,s=n.format({[a.extensionName]:[t]});u.push(`Sec-WebSocket-Extensions: ${s}`),y._extensions=e}this.emit("headers",u,r),i.write(u.concat("\r\n").join("\r\n")),i.removeListener("error",p),y.setSocket(i,l,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(y),y.on("close",()=>{this.clients.delete(y),this._shouldEmitClose&&!this.clients.size&&process.nextTick(_,this)})),h(y,r)}}}});m(y(),1),m(N(),1),m(L(),1);var j=m(R(),1);m(U(),1);var D=j.default;(0,o.f)(D),s(5045),s(1694);var M=s(3210),W=s(4905);function A(){return(0,M.useContext)(W.eB)}}};