/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/session/[sessionId]/page";
exports.ids = ["app/session/[sessionId]/page"];
exports.modules = {

/***/ "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2F%40convex-dev%2Fauth%2Fdist%2Fnextjs%2Fserver%2FinvalidateCache.js%22%2C%5B%7B%22id%22%3A%22004537205c560a1ac486f078fff053894f76f7230e%22%2C%22exportedName%22%3A%22invalidateCache%22%7D%5D%5D%5D&__client_imported__=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2F%40convex-dev%2Fauth%2Fdist%2Fnextjs%2Fserver%2FinvalidateCache.js%22%2C%5B%7B%22id%22%3A%22004537205c560a1ac486f078fff053894f76f7230e%22%2C%22exportedName%22%3A%22invalidateCache%22%7D%5D%5D%5D&__client_imported__=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"004537205c560a1ac486f078fff053894f76f7230e\": () => (/* reexport safe */ _Users_robert_hansen_PlayBeg_PlayBeg_playbeg_convex_node_modules_convex_dev_auth_dist_nextjs_server_invalidateCache_js__WEBPACK_IMPORTED_MODULE_0__.invalidateCache)\n/* harmony export */ });\n/* harmony import */ var _Users_robert_hansen_PlayBeg_PlayBeg_playbeg_convex_node_modules_convex_dev_auth_dist_nextjs_server_invalidateCache_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@convex-dev/auth/dist/nextjs/server/invalidateCache.js */ \"(action-browser)/./node_modules/@convex-dev/auth/dist/nextjs/server/invalidateCache.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWFjdGlvbi1lbnRyeS1sb2FkZXIuanM/YWN0aW9ucz0lNUIlNUIlMjIlMkZVc2VycyUyRnJvYmVydC5oYW5zZW4lMkZQbGF5QmVnJTJGUGxheUJlZyUyRnBsYXliZWctY29udmV4JTJGbm9kZV9tb2R1bGVzJTJGJTQwY29udmV4LWRldiUyRmF1dGglMkZkaXN0JTJGbmV4dGpzJTJGc2VydmVyJTJGaW52YWxpZGF0ZUNhY2hlLmpzJTIyJTJDJTVCJTdCJTIyaWQlMjIlM0ElMjIwMDQ1MzcyMDVjNTYwYTFhYzQ4NmYwNzhmZmYwNTM4OTRmNzZmNzIzMGUlMjIlMkMlMjJleHBvcnRlZE5hbWUlMjIlM0ElMjJpbnZhbGlkYXRlQ2FjaGUlMjIlN0QlNUQlNUQlNUQmX19jbGllbnRfaW1wb3J0ZWRfXz10cnVlISIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFDeU0iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGludmFsaWRhdGVDYWNoZSBhcyBcIjAwNDUzNzIwNWM1NjBhMWFjNDg2ZjA3OGZmZjA1Mzg5NGY3NmY3MjMwZVwiIH0gZnJvbSBcIi9Vc2Vycy9yb2JlcnQuaGFuc2VuL1BsYXlCZWcvUGxheUJlZy9wbGF5YmVnLWNvbnZleC9ub2RlX21vZHVsZXMvQGNvbnZleC1kZXYvYXV0aC9kaXN0L25leHRqcy9zZXJ2ZXIvaW52YWxpZGF0ZUNhY2hlLmpzXCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2F%40convex-dev%2Fauth%2Fdist%2Fnextjs%2Fserver%2FinvalidateCache.js%22%2C%5B%7B%22id%22%3A%22004537205c560a1ac486f078fff053894f76f7230e%22%2C%22exportedName%22%3A%22invalidateCache%22%7D%5D%5D%5D&__client_imported__=true!\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"900d2e6c7de9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yb2JlcnQuaGFuc2VuL1BsYXlCZWcvUGxheUJlZy9wbGF5YmVnLWNvbnZleC9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5MDBkMmU2YzdkZTlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_display_swap_preload_true_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"],\"display\":\"swap\",\"preload\":true}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"preload\\\":true}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_display_swap_preload_true_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_display_swap_preload_true_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_display_swap_preload_false_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"],\"display\":\"swap\",\"preload\":false}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"preload\\\":false}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_display_swap_preload_false_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_display_swap_preload_false_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _convex_dev_auth_nextjs_server__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @convex-dev/auth/nextjs/server */ \"(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/index.js\");\n/* harmony import */ var _components_ConvexClientProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ConvexClientProvider */ \"(rsc)/./components/ConvexClientProvider.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"PlayBeg - Interactive DJ Platform\",\n    description: \"The ultimate platform for interactive DJ experiences and real-time audience engagement. Connect with your audience like never before.\",\n    keywords: [\n        \"DJ\",\n        \"music\",\n        \"interactive\",\n        \"requests\",\n        \"audience engagement\",\n        \"events\"\n    ],\n    authors: [\n        {\n            name: \"PlayBeg Team\"\n        }\n    ],\n    creator: \"PlayBeg Inc.\",\n    publisher: \"PlayBeg Inc.\",\n    icons: {\n        icon: \"/convex.svg\",\n        shortcut: \"/convex.svg\",\n        apple: \"/convex.svg\"\n    },\n    openGraph: {\n        title: \"PlayBeg - Interactive DJ Platform\",\n        description: \"The ultimate platform for interactive DJ experiences and real-time audience engagement.\",\n        url: \"https://playbeg.com\",\n        siteName: \"PlayBeg\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"PlayBeg - Interactive DJ Platform\",\n        description: \"The ultimate platform for interactive DJ experiences and real-time audience engagement.\"\n    },\n    robots: {\n        index: true,\n        follow: true\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_convex_dev_auth_nextjs_server__WEBPACK_IMPORTED_MODULE_3__.ConvexAuthNextjsServerProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n            lang: \"en\",\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_display_swap_preload_true_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_display_swap_preload_false_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default().variable)}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased font-sans\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ConvexClientProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/layout.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/layout.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/layout.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/layout.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/session/[sessionId]/page.tsx":
/*!******************************************!*\
  !*** ./app/session/[sessionId]/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/ConvexClientProvider.tsx":
/*!*********************************************!*\
  !*** ./components/ConvexClientProvider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ConvexClientProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ConvexClientProvider.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsession%2F%5BsessionId%5D%2Fpage&page=%2Fsession%2F%5BsessionId%5D%2Fpage&appPaths=%2Fsession%2F%5BsessionId%5D%2Fpage&pagePath=private-next-app-dir%2Fsession%2F%5BsessionId%5D%2Fpage.tsx&appDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsession%2F%5BsessionId%5D%2Fpage&page=%2Fsession%2F%5BsessionId%5D%2Fpage&appPaths=%2Fsession%2F%5BsessionId%5D%2Fpage&pagePath=private-next-app-dir%2Fsession%2F%5BsessionId%5D%2Fpage.tsx&appDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/session/[sessionId]/page.tsx */ \"(rsc)/./app/session/[sessionId]/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'session',\n        {\n        children: [\n        '[sessionId]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/session/[sessionId]/page\",\n        pathname: \"/session/[sessionId]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsession%2F%5BsessionId%5D%2Fpage&page=%2Fsession%2F%5BsessionId%5D%2Fpage&appPaths=%2Fsession%2F%5BsessionId%5D%2Fpage&pagePath=private-next-app-dir%2Fsession%2F%5BsessionId%5D%2Fpage.tsx&appDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp%2Fsession%2F%5BsessionId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp%2Fsession%2F%5BsessionId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/session/[sessionId]/page.tsx */ \"(rsc)/./app/session/[sessionId]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGcm9iZXJ0LmhhbnNlbiUyRlBsYXlCZWclMkZQbGF5QmVnJTJGcGxheWJlZy1jb252ZXglMkZhcHAlMkZzZXNzaW9uJTJGJTVCc2Vzc2lvbklkJTVEJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdMQUF5SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3JvYmVydC5oYW5zZW4vUGxheUJlZy9QbGF5QmVnL3BsYXliZWctY29udmV4L2FwcC9zZXNzaW9uL1tzZXNzaW9uSWRdL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp%2Fsession%2F%5BsessionId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2F%40convex-dev%2Fauth%2Fdist%2Fnextjs%2Fclient.js%22%2C%22ids%22%3A%5B%22ConvexAuthNextjsClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Afalse%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2F%40convex-dev%2Fauth%2Fdist%2Fnextjs%2Fclient.js%22%2C%22ids%22%3A%5B%22ConvexAuthNextjsClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Afalse%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ConvexClientProvider.tsx */ \"(rsc)/./components/ConvexClientProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@convex-dev/auth/dist/nextjs/client.js */ \"(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/client.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2F%40convex-dev%2Fauth%2Fdist%2Fnextjs%2Fclient.js%22%2C%22ids%22%3A%5B%22ConvexAuthNextjsClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Afalse%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/session/[sessionId]/page.tsx":
/*!******************************************!*\
  !*** ./app/session/[sessionId]/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PublicSessionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! convex/react */ \"(ssr)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../convex/_generated/api */ \"(ssr)/./convex/_generated/api.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../components/ui/textarea */ \"(ssr)/./components/ui/textarea.tsx\");\n/* harmony import */ var qrcode_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! qrcode.react */ \"(ssr)/./node_modules/qrcode.react/lib/esm/index.js\");\n/* harmony import */ var _components_SongSearchInput__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../../components/SongSearchInput */ \"(ssr)/./components/SongSearchInput.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction PublicSessionPage() {\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const sessionId = params.sessionId;\n    // Validate session ID format\n    const isValidSessionId = sessionId && sessionId.length > 0 && !sessionId.includes('test');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        songTitle: '',\n        artistName: '',\n        requesterName: '',\n        message: ''\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitMessage, setSubmitMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showQR, setShowQR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Queries with proper error handling\n    const session = (0,convex_react__WEBPACK_IMPORTED_MODULE_3__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.sessions.getPublicSession, isValidSessionId ? {\n        sessionId: sessionId\n    } : \"skip\");\n    const requests = (0,convex_react__WEBPACK_IMPORTED_MODULE_3__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.songRequests.getSessionRequests, session && isValidSessionId ? {\n        sessionId: sessionId,\n        status: \"approved\",\n        limit: 10\n    } : \"skip\");\n    // Mutations\n    const createRequest = (0,convex_react__WEBPACK_IMPORTED_MODULE_3__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.songRequests.createSongRequest);\n    const createValidatedRequest = (0,convex_react__WEBPACK_IMPORTED_MODULE_3__.useAction)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.spotify.createValidatedSongRequest);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!session || !session.acceptRequests) return;\n        setIsSubmitting(true);\n        setSubmitMessage(null);\n        try {\n            // Use Spotify-enhanced request creation if we have Spotify data\n            let result;\n            if (formData.spotifyId) {\n                result = await createValidatedRequest({\n                    sessionId: sessionId,\n                    songTitle: formData.songTitle.trim(),\n                    artistName: formData.artistName.trim(),\n                    requesterName: formData.requesterName.trim(),\n                    spotifyTrackId: formData.spotifyId\n                });\n                if (!result.success) {\n                    throw new Error(result.error);\n                }\n                result = result.request;\n            } else {\n                // Fallback to regular request creation\n                result = await createRequest({\n                    sessionId: sessionId,\n                    songTitle: formData.songTitle.trim(),\n                    artistName: formData.artistName.trim(),\n                    requesterName: formData.requesterName.trim(),\n                    albumArtwork: formData.albumArt\n                });\n            }\n            setSubmitMessage({\n                type: 'success',\n                text: result.message || 'Request submitted successfully!'\n            });\n            // Clear form\n            setFormData({\n                songTitle: '',\n                artistName: '',\n                requesterName: '',\n                message: ''\n            });\n        } catch (error) {\n            setSubmitMessage({\n                type: 'error',\n                text: error.message || 'Failed to submit request'\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSongSelect = (song)=>{\n        setFormData((prev)=>({\n                ...prev,\n                songTitle: song.title,\n                artistName: song.artist,\n                spotifyId: song.spotifyId,\n                albumArt: song.albumArt\n            }));\n    };\n    // Invalid session ID\n    if (!isValidSessionId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-8 text-center max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"❌\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-white mb-4\",\n                        children: \"Invalid Session\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/80 mb-6\",\n                        children: \"The session ID provided is not valid. Please check the link and try again.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"bg-purple-600 hover:bg-purple-700 text-white\",\n                        children: \"Go to Home\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, this);\n    }\n    // Loading state\n    if (session === undefined) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-lg\",\n                children: \"Loading session...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this);\n    }\n    // Session not found or inactive\n    if (!session) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-8 text-center max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83C\\uDFB5\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-white mb-4\",\n                        children: \"Session Not Found\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/80 mb-6\",\n                        children: \"This session is either inactive or doesn't exist. Please check the link and try again.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"bg-purple-600 hover:bg-purple-700 text-white\",\n                        children: \"Go to Home\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                lineNumber: 163,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this);\n    }\n    const currentUrl =  false ? 0 : '';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-black/20 backdrop-blur-md border-b border-white/10 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: session.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                session.weddingModeEnabled && session.weddingCoupleNames && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/80\",\n                                    children: [\n                                        session.weddingCoupleNames.join(' & '),\n                                        \"'s Wedding\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            onClick: ()=>setShowQR(!showQR),\n                            variant: \"outline\",\n                            className: \"text-white border-white/30 hover:bg-white/10\",\n                            children: showQR ? 'Hide QR' : 'Share QR'\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto p-4 space-y-8\",\n                children: [\n                    showQR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"bg-white/10 backdrop-blur-md border-white/20 p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"Share This Session\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-4 rounded-lg inline-block mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(qrcode_react__WEBPACK_IMPORTED_MODULE_9__.QRCodeSVG, {\n                                    value: currentUrl,\n                                    size: 200\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 text-sm\",\n                                children: \"Scan this QR code to join the session\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this),\n                    session.weddingModeEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"bg-white/10 backdrop-blur-md border-white/20 p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-4xl mb-4\",\n                                children: \"\\uD83D\\uDC92\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: [\n                                    session.weddingCoupleNames?.join(' & '),\n                                    \"'s Wedding\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this),\n                            session.weddingDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 mb-2\",\n                                children: session.weddingDate\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, this),\n                            session.weddingHashtag && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-purple-300\",\n                                children: [\n                                    \"#\",\n                                    session.weddingHashtag\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, this),\n                            session.weddingCustomMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/90 mt-4 italic\",\n                                children: [\n                                    '\"',\n                                    session.weddingCustomMessage,\n                                    '\"'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, this),\n                    session.sponsorHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"bg-white/10 backdrop-blur-md border-white/20 p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/90\",\n                                children: session.sponsorHeader\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this),\n                            session.sponsorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/70 text-sm mt-2\",\n                                children: session.sponsorMessage\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-6 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-3\",\n                                                children: \"\\uD83C\\uDFB5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Request a Song\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, this),\n                                    !session.acceptRequests ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: \"⏸️\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/80\",\n                                                children: \"This session is not currently accepting requests.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SongSearchInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                onSongSelect: handleSongSelect,\n                                                placeholder: \"Search for a song or artist...\",\n                                                initialTitle: formData.songTitle,\n                                                initialArtist: formData.artistName\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            (formData.songTitle || formData.artistName) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/10 rounded-lg p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        formData.albumArt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: formData.albumArt,\n                                                            alt: \"Album artwork\",\n                                                            className: \"w-12 h-12 rounded object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-semibold text-white\",\n                                                                    children: [\n                                                                        formData.songTitle,\n                                                                        formData.spotifyId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-2 text-xs bg-green-600 text-white px-2 py-1 rounded\",\n                                                                            children: \"♪ Verified\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                                            lineNumber: 287,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                                    lineNumber: 284,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white/80 text-sm\",\n                                                                    children: formData.artistName\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            type: \"button\",\n                                                            onClick: ()=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        songTitle: '',\n                                                                        artistName: '',\n                                                                        spotifyId: undefined,\n                                                                        albumArt: undefined\n                                                                    })),\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"text-white border-white/30 hover:bg-white/10\",\n                                                            children: \"Change\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-white mb-2\",\n                                                        children: \"Your Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        type: \"text\",\n                                                        value: formData.requesterName,\n                                                        onChange: (e)=>handleInputChange('requesterName', e.target.value),\n                                                        placeholder: \"Enter your name\",\n                                                        className: \"w-full bg-white/20 border-white/30 text-white placeholder-white/60\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-white mb-2\",\n                                                        children: \"Message (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                        value: formData.message,\n                                                        onChange: (e)=>handleInputChange('message', e.target.value),\n                                                        placeholder: \"Add a message with your request...\",\n                                                        className: \"w-full bg-white/20 border-white/30 text-white placeholder-white/60\",\n                                                        rows: 3\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, this),\n                                            submitMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `p-3 rounded-lg ${submitMessage.type === 'success' ? 'bg-green-600/20 border border-green-500/30 text-green-200' : 'bg-red-600/20 border border-red-500/30 text-red-200'}`,\n                                                children: submitMessage.text\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                type: \"submit\",\n                                                disabled: isSubmitting || !formData.songTitle.trim() || !formData.artistName.trim() || !formData.requesterName.trim(),\n                                                className: \"w-full bg-purple-600 hover:bg-purple-700 text-white py-3\",\n                                                children: isSubmitting ? 'Submitting...' : 'Submit Request'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-6 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-3\",\n                                                children: \"✅\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Recent Requests\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 13\n                                    }, this),\n                                    requests && requests.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: requests.map((request)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/10 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold text-white\",\n                                                        children: request.songTitle\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white/80 text-sm\",\n                                                        children: [\n                                                            \"by \",\n                                                            request.artistName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white/60 text-xs mt-1\",\n                                                        children: [\n                                                            \"Requested by \",\n                                                            request.requesterName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, request._id, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: \"\\uD83C\\uDFB6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/80\",\n                                                children: \"No approved requests yet. Be the first to request a song!\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/session/[sessionId]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ConvexClientProvider.tsx":
/*!*********************************************!*\
  !*** ./components/ConvexClientProvider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConvexClientProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _convex_dev_auth_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @convex-dev/auth/nextjs */ \"(ssr)/./node_modules/@convex-dev/auth/dist/nextjs/index.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(ssr)/./node_modules/convex/dist/esm/react/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst convex = new convex_react__WEBPACK_IMPORTED_MODULE_1__.ConvexReactClient(\"https://lovely-cormorant-474.convex.cloud\");\nfunction ConvexClientProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_convex_dev_auth_nextjs__WEBPACK_IMPORTED_MODULE_2__.ConvexAuthNextjsProvider, {\n        client: convex,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ConvexClientProvider.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL0NvbnZleENsaWVudFByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFbUU7QUFDbEI7QUFHakQsTUFBTUUsU0FBUyxJQUFJRCwyREFBaUJBLENBQUNFLDJDQUFrQztBQUV4RCxTQUFTRyxxQkFBcUIsRUFDM0NDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDUCw2RUFBd0JBO1FBQUNRLFFBQVFOO2tCQUMvQks7Ozs7OztBQUdQIiwic291cmNlcyI6WyIvVXNlcnMvcm9iZXJ0LmhhbnNlbi9QbGF5QmVnL1BsYXlCZWcvcGxheWJlZy1jb252ZXgvY29tcG9uZW50cy9Db252ZXhDbGllbnRQcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IENvbnZleEF1dGhOZXh0anNQcm92aWRlciB9IGZyb20gXCJAY29udmV4LWRldi9hdXRoL25leHRqc1wiO1xuaW1wb3J0IHsgQ29udmV4UmVhY3RDbGllbnQgfSBmcm9tIFwiY29udmV4L3JlYWN0XCI7XG5pbXBvcnQgeyBSZWFjdE5vZGUgfSBmcm9tIFwicmVhY3RcIjtcblxuY29uc3QgY29udmV4ID0gbmV3IENvbnZleFJlYWN0Q2xpZW50KHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NPTlZFWF9VUkwhKTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQ29udmV4Q2xpZW50UHJvdmlkZXIoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8Q29udmV4QXV0aE5leHRqc1Byb3ZpZGVyIGNsaWVudD17Y29udmV4fT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0NvbnZleEF1dGhOZXh0anNQcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJDb252ZXhBdXRoTmV4dGpzUHJvdmlkZXIiLCJDb252ZXhSZWFjdENsaWVudCIsImNvbnZleCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19DT05WRVhfVVJMIiwiQ29udmV4Q2xpZW50UHJvdmlkZXIiLCJjaGlsZHJlbiIsImNsaWVudCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ConvexClientProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/SongSearchInput.tsx":
/*!****************************************!*\
  !*** ./components/SongSearchInput.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SongSearchInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! convex/react */ \"(ssr)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../convex/_generated/api */ \"(ssr)/./convex/_generated/api.js\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction SongSearchInput({ onSongSelect, placeholder = \"Search for a song...\", className = \"\", initialTitle = \"\", initialArtist = \"\" }) {\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSearching, setIsSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showResults, setShowResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIndex, setSelectedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [manualEntry, setManualEntry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [manualTitle, setManualTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTitle);\n    const [manualArtist, setManualArtist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialArtist);\n    const searchRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const searchTracks = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useAction)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.spotify.searchTracks);\n    // Debounced search\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SongSearchInput.useEffect\": ()=>{\n            if (searchQuery.length < 2) {\n                setSearchResults([]);\n                setShowResults(false);\n                return;\n            }\n            const timeoutId = setTimeout({\n                \"SongSearchInput.useEffect.timeoutId\": async ()=>{\n                    setIsSearching(true);\n                    try {\n                        const result = await searchTracks({\n                            query: searchQuery,\n                            limit: 8\n                        });\n                        if (result.success) {\n                            setSearchResults(result.tracks);\n                            setShowResults(true);\n                        } else {\n                            setSearchResults([]);\n                            setShowResults(false);\n                        }\n                    } catch (error) {\n                        console.error('Search error:', error);\n                        setSearchResults([]);\n                        setShowResults(false);\n                    } finally{\n                        setIsSearching(false);\n                    }\n                }\n            }[\"SongSearchInput.useEffect.timeoutId\"], 300);\n            return ({\n                \"SongSearchInput.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"SongSearchInput.useEffect\"];\n        }\n    }[\"SongSearchInput.useEffect\"], [\n        searchQuery,\n        searchTracks\n    ]);\n    // Handle keyboard navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SongSearchInput.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"SongSearchInput.useEffect.handleKeyDown\": (e)=>{\n                    if (!showResults) return;\n                    switch(e.key){\n                        case 'ArrowDown':\n                            e.preventDefault();\n                            setSelectedIndex({\n                                \"SongSearchInput.useEffect.handleKeyDown\": (prev)=>prev < searchResults.length - 1 ? prev + 1 : prev\n                            }[\"SongSearchInput.useEffect.handleKeyDown\"]);\n                            break;\n                        case 'ArrowUp':\n                            e.preventDefault();\n                            setSelectedIndex({\n                                \"SongSearchInput.useEffect.handleKeyDown\": (prev)=>prev > 0 ? prev - 1 : -1\n                            }[\"SongSearchInput.useEffect.handleKeyDown\"]);\n                            break;\n                        case 'Enter':\n                            e.preventDefault();\n                            if (selectedIndex >= 0 && selectedIndex < searchResults.length) {\n                                handleSongSelect(searchResults[selectedIndex]);\n                            }\n                            break;\n                        case 'Escape':\n                            setShowResults(false);\n                            setSelectedIndex(-1);\n                            break;\n                    }\n                }\n            }[\"SongSearchInput.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"SongSearchInput.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"SongSearchInput.useEffect\"];\n        }\n    }[\"SongSearchInput.useEffect\"], [\n        showResults,\n        selectedIndex,\n        searchResults\n    ]);\n    // Handle click outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SongSearchInput.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"SongSearchInput.useEffect.handleClickOutside\": (event)=>{\n                    if (searchRef.current && !searchRef.current.contains(event.target)) {\n                        setShowResults(false);\n                        setSelectedIndex(-1);\n                    }\n                }\n            }[\"SongSearchInput.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"SongSearchInput.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"SongSearchInput.useEffect\"];\n        }\n    }[\"SongSearchInput.useEffect\"], []);\n    const handleSongSelect = (track)=>{\n        onSongSelect({\n            title: track.name,\n            artist: track.artists[0],\n            spotifyId: track.id,\n            albumArt: track.albumArt || undefined\n        });\n        setSearchQuery(`${track.name} - ${track.artists[0]}`);\n        setShowResults(false);\n        setSelectedIndex(-1);\n    };\n    const handleManualSubmit = ()=>{\n        if (manualTitle.trim() && manualArtist.trim()) {\n            onSongSelect({\n                title: manualTitle.trim(),\n                artist: manualArtist.trim()\n            });\n            setManualEntry(false);\n        }\n    };\n    const formatDuration = (ms)=>{\n        const minutes = Math.floor(ms / 60000);\n        const seconds = Math.floor(ms % 60000 / 1000);\n        return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n    };\n    if (manualEntry) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `space-y-4 ${className}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-white\",\n                            children: \"Manual Entry\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            onClick: ()=>setManualEntry(false),\n                            variant: \"outline\",\n                            size: \"sm\",\n                            className: \"text-white border-white/30 hover:bg-white/10\",\n                            children: \"Back to Search\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-white mb-2\",\n                                    children: \"Song Title *\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    type: \"text\",\n                                    value: manualTitle,\n                                    onChange: (e)=>setManualTitle(e.target.value),\n                                    placeholder: \"Enter song title\",\n                                    className: \"w-full bg-white/20 border-white/30 text-white placeholder-white/60\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-white mb-2\",\n                                    children: \"Artist Name *\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    type: \"text\",\n                                    value: manualArtist,\n                                    onChange: (e)=>setManualArtist(e.target.value),\n                                    placeholder: \"Enter artist name\",\n                                    className: \"w-full bg-white/20 border-white/30 text-white placeholder-white/60\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            onClick: handleManualSubmit,\n                            disabled: !manualTitle.trim() || !manualArtist.trim(),\n                            className: \"w-full bg-purple-600 hover:bg-purple-700 text-white\",\n                            children: \"Use This Song\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n            lineNumber: 157,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: searchRef,\n        className: `relative ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-white mb-2\",\n                                children: \"Search for a Song\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                type: \"text\",\n                                value: searchQuery,\n                                onChange: (e)=>setSearchQuery(e.target.value),\n                                placeholder: placeholder,\n                                className: \"w-full bg-white/20 border-white/30 text-white placeholder-white/60\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this),\n                            isSearching && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/60 text-sm mt-1\",\n                                children: \"Searching...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: ()=>setManualEntry(true),\n                        variant: \"outline\",\n                        size: \"sm\",\n                        className: \"text-white border-white/30 hover:bg-white/10\",\n                        children: \"Enter Song Manually\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            showResults && searchResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                className: \"absolute top-full left-0 right-0 mt-2 bg-white/10 backdrop-blur-md border-white/20 max-h-96 overflow-y-auto z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-white/80 text-sm mb-2 px-2\",\n                            children: [\n                                \"Found \",\n                                searchResults.length,\n                                \" results\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 13\n                        }, this),\n                        searchResults.map((track, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>handleSongSelect(track),\n                                className: `flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors ${index === selectedIndex ? 'bg-purple-600/30' : 'hover:bg-white/10'}`,\n                                children: [\n                                    track.albumArt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: track.albumArt,\n                                        alt: `${track.album} cover`,\n                                        className: \"w-12 h-12 rounded object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-semibold text-white truncate\",\n                                                children: [\n                                                    track.name,\n                                                    track.explicit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-xs bg-red-600 text-white px-1 rounded\",\n                                                        children: \"E\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white/80 text-sm truncate\",\n                                                children: track.artists.join(', ')\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white/60 text-xs\",\n                                                children: [\n                                                    track.album,\n                                                    \" • \",\n                                                    formatDuration(track.duration)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white/60 text-xs\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400\",\n                                                    children: \"♪\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        track.popularity,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, track.id, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 15\n                            }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                lineNumber: 240,\n                columnNumber: 9\n            }, this),\n            showResults && searchResults.length === 0 && !isSearching && searchQuery.length >= 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                className: \"absolute top-full left-0 right-0 mt-2 bg-white/10 backdrop-blur-md border-white/20 p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-white/80 mb-2\",\n                            children: \"No songs found\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-white/60 text-sm mb-3\",\n                            children: \"Try a different search or enter the song manually\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            onClick: ()=>setManualEntry(true),\n                            size: \"sm\",\n                            className: \"bg-purple-600 hover:bg-purple-700 text-white\",\n                            children: \"Enter Manually\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                lineNumber: 290,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/SongSearchInput.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\",\n            locked: \"relative bg-gray-700/50 text-gray-400 cursor-not-allowed border border-gray-600/30\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/card.tsx\",\n        lineNumber: 31,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/card.tsx\",\n        lineNumber: 46,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/card.tsx\",\n        lineNumber: 58,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/card.tsx\",\n        lineNumber: 66,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFFaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGtZQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyIvVXNlcnMvcm9iZXJ0LmhhbnNlbi9QbGF5QmVnL1BsYXlCZWcvcGxheWJlZy1jb252ZXgvY29tcG9uZW50cy91aS9pbnB1dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJpbnB1dFwiPj4oXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxpbnB1dFxuICAgICAgICB0eXBlPXt0eXBlfVxuICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgIFwiZmxleCBoLTEwIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1iYXNlIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIGZpbGU6dGV4dC1mb3JlZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTAgbWQ6dGV4dC1zbVwiLFxuICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICApfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbklucHV0LmRpc3BsYXlOYW1lID0gXCJJbnB1dFwiXG5cbmV4cG9ydCB7IElucHV0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/textarea.tsx":
/*!************************************!*\
  !*** ./components/ui/textarea.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Textarea: () => (/* binding */ Textarea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Textarea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/textarea.tsx\",\n        lineNumber: 10,\n        columnNumber: 7\n    }, undefined);\n});\nTextarea.displayName = \"Textarea\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3RleHRhcmVhLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBQ007QUFLcEMsTUFBTUUseUJBQVdGLDZDQUFnQixDQUMvQixDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQ3hCLHFCQUNFLDhEQUFDQztRQUNDSCxXQUFXSCw4Q0FBRUEsQ0FDWCx3U0FDQUc7UUFFRkUsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSCxTQUFTTSxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsiL1VzZXJzL3JvYmVydC5oYW5zZW4vUGxheUJlZy9QbGF5QmVnL3BsYXliZWctY29udmV4L2NvbXBvbmVudHMvdWkvdGV4dGFyZWEudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBjbiB9IGZyb20gXCIuLi8uLi9saWIvdXRpbHNcIlxuXG5leHBvcnQgaW50ZXJmYWNlIFRleHRhcmVhUHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5UZXh0YXJlYUhUTUxBdHRyaWJ1dGVzPEhUTUxUZXh0QXJlYUVsZW1lbnQ+IHt9XG5cbmNvbnN0IFRleHRhcmVhID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MVGV4dEFyZWFFbGVtZW50LCBUZXh0YXJlYVByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPHRleHRhcmVhXG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJmbGV4IG1pbi1oLVs4MHB4XSB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtc20gcmluZy1vZmZzZXQtYmFja2dyb3VuZCBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuVGV4dGFyZWEuZGlzcGxheU5hbWUgPSBcIlRleHRhcmVhXCJcblxuZXhwb3J0IHsgVGV4dGFyZWEgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJUZXh0YXJlYSIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsInJlZiIsInRleHRhcmVhIiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/textarea.tsx\n");

/***/ }),

/***/ "(ssr)/./convex/_generated/api.js":
/*!**********************************!*\
  !*** ./convex/_generated/api.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   internal: () => (/* binding */ internal)\n/* harmony export */ });\n/* harmony import */ var convex_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! convex/server */ \"(ssr)/./node_modules/convex/dist/esm/server/index.js\");\n/* eslint-disable */ /**\n * Generated `api` utility.\n *\n * THIS CODE IS AUTOMATICALLY GENERATED.\n *\n * To regenerate, run `npx convex dev`.\n * @module\n */ \n/**\n * A utility for referencing Convex functions in your app's API.\n *\n * Usage:\n * ```js\n * const myFunctionReference = api.myModule.myFunction;\n * ```\n */ const api = convex_server__WEBPACK_IMPORTED_MODULE_0__.anyApi;\nconst internal = convex_server__WEBPACK_IMPORTED_MODULE_0__.anyApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb252ZXgvX2dlbmVyYXRlZC9hcGkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsa0JBQWtCLEdBQ2xCOzs7Ozs7O0NBT0MsR0FFc0M7QUFFdkM7Ozs7Ozs7Q0FPQyxHQUNNLE1BQU1DLE1BQU1ELGlEQUFNQSxDQUFDO0FBQ25CLE1BQU1FLFdBQVdGLGlEQUFNQSxDQUFDIiwic291cmNlcyI6WyIvVXNlcnMvcm9iZXJ0LmhhbnNlbi9QbGF5QmVnL1BsYXlCZWcvcGxheWJlZy1jb252ZXgvY29udmV4L19nZW5lcmF0ZWQvYXBpLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qIGVzbGludC1kaXNhYmxlICovXG4vKipcbiAqIEdlbmVyYXRlZCBgYXBpYCB1dGlsaXR5LlxuICpcbiAqIFRISVMgQ09ERSBJUyBBVVRPTUFUSUNBTExZIEdFTkVSQVRFRC5cbiAqXG4gKiBUbyByZWdlbmVyYXRlLCBydW4gYG5weCBjb252ZXggZGV2YC5cbiAqIEBtb2R1bGVcbiAqL1xuXG5pbXBvcnQgeyBhbnlBcGkgfSBmcm9tIFwiY29udmV4L3NlcnZlclwiO1xuXG4vKipcbiAqIEEgdXRpbGl0eSBmb3IgcmVmZXJlbmNpbmcgQ29udmV4IGZ1bmN0aW9ucyBpbiB5b3VyIGFwcCdzIEFQSS5cbiAqXG4gKiBVc2FnZTpcbiAqIGBgYGpzXG4gKiBjb25zdCBteUZ1bmN0aW9uUmVmZXJlbmNlID0gYXBpLm15TW9kdWxlLm15RnVuY3Rpb247XG4gKiBgYGBcbiAqL1xuZXhwb3J0IGNvbnN0IGFwaSA9IGFueUFwaTtcbmV4cG9ydCBjb25zdCBpbnRlcm5hbCA9IGFueUFwaTtcbiJdLCJuYW1lcyI6WyJhbnlBcGkiLCJhcGkiLCJpbnRlcm5hbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./convex/_generated/api.js\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyIvVXNlcnMvcm9iZXJ0LmhhbnNlbi9QbGF5QmVnL1BsYXlCZWcvcGxheWJlZy1jb252ZXgvbGliL3V0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp%2Fsession%2F%5BsessionId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp%2Fsession%2F%5BsessionId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/session/[sessionId]/page.tsx */ \"(ssr)/./app/session/[sessionId]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGcm9iZXJ0LmhhbnNlbiUyRlBsYXlCZWclMkZQbGF5QmVnJTJGcGxheWJlZy1jb252ZXglMkZhcHAlMkZzZXNzaW9uJTJGJTVCc2Vzc2lvbklkJTVEJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdMQUF5SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3JvYmVydC5oYW5zZW4vUGxheUJlZy9QbGF5QmVnL3BsYXliZWctY29udmV4L2FwcC9zZXNzaW9uL1tzZXNzaW9uSWRdL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp%2Fsession%2F%5BsessionId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2F%40convex-dev%2Fauth%2Fdist%2Fnextjs%2Fclient.js%22%2C%22ids%22%3A%5B%22ConvexAuthNextjsClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Afalse%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2F%40convex-dev%2Fauth%2Fdist%2Fnextjs%2Fclient.js%22%2C%22ids%22%3A%5B%22ConvexAuthNextjsClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Afalse%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ConvexClientProvider.tsx */ \"(ssr)/./components/ConvexClientProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@convex-dev/auth/dist/nextjs/client.js */ \"(ssr)/./node_modules/@convex-dev/auth/dist/nextjs/client.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2F%40convex-dev%2Fauth%2Fdist%2Fnextjs%2Fclient.js%22%2C%22ids%22%3A%5B%22ConvexAuthNextjsClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Afalse%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/convex","vendor-chunks/tailwind-merge","vendor-chunks/@convex-dev","vendor-chunks/path-to-regexp","vendor-chunks/@radix-ui","vendor-chunks/jwt-decode","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/is-network-error","vendor-chunks/clsx","vendor-chunks/qrcode.react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsession%2F%5BsessionId%5D%2Fpage&page=%2Fsession%2F%5BsessionId%5D%2Fpage&appPaths=%2Fsession%2F%5BsessionId%5D%2Fpage&pagePath=private-next-app-dir%2Fsession%2F%5BsessionId%5D%2Fpage.tsx&appDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();