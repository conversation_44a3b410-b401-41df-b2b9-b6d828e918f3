(()=>{var e={};e.id=807,e.ids=[807],e.modules={668:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=t(5239),a=t(8088),l=t(8170),i=t.n(l),n=t(893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let c={children:["",{children:["test-upload",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2120)),"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/test-upload/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,8014)),"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/test-upload/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-upload/page",pathname:"/test-upload",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2120:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/test-upload/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/test-upload/page.tsx","default")},2132:(e,s,t)=>{Promise.resolve().then(t.bind(t,3139))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3139:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var r=t(687),a=t(1694),l=t(6189),i=t(3210),n=t(8999),o=t(4934),c=t(5192),d=t(9821),x=t(7856),p=t(2688);let h=(0,p.A)("hard-drive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]);var m=t(6023);let u=(0,p.A)("file-image",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["circle",{cx:"10",cy:"12",r:"2",key:"737tya"}],["path",{d:"m20 17-1.296-1.296a2.41 2.41 0 0 0-3.408 0L9 22",key:"wt3hpn"}]]);var g=t(9005),y=t(228);let j=(0,p.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),f=(0,p.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);function b({purpose:e,showUpload:s=!0,maxFiles:t=10}){let[l,p]=(0,i.useState)(e),b=(0,a.IT)(n.F.fileStorage.getUserFiles,{purpose:l,limit:t}),v=(0,a.IT)(n.F.fileStorage.getStorageStats),N=(0,a.n_)(n.F.fileStorage.deleteFile),w=async e=>{try{await N({fileId:e})}catch(e){console.error("Failed to delete file:",e)}},k=e=>{if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB"][s]},B=e=>new Date(e).toLocaleDateString(),P=e=>{switch(e){case"profile_picture":return"bg-blue-500/20 text-blue-400";case"sponsor_logo":return"bg-green-500/20 text-green-400";case"session_artwork":return"bg-purple-500/20 text-purple-400";default:return"bg-gray-500/20 text-gray-400"}},_=e=>{switch(e){case"profile_picture":return"Profile Picture";case"sponsor_logo":return"Sponsor Logo";case"session_artwork":return"Session Artwork";default:return"Other"}};return(0,r.jsxs)("div",{className:"space-y-6",children:[v&&(0,r.jsxs)(c.Zp,{className:"bg-gray-800/50 border-purple-500/20",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsxs)(c.ZB,{className:"text-white flex items-center",children:[(0,r.jsx)(h,{className:"w-5 h-5 mr-2"}),"Storage Usage"]}),(0,r.jsx)(c.BT,{className:"text-gray-400",children:"Your file storage statistics"})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-white",children:v.totalFiles}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:"Total Files"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-white",children:v.totalSizeMB}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:"MB Used"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-white",children:v.maxSizeMB}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:"MB Limit"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-white",children:[Math.round(v.totalSizeMB/v.maxSizeMB*100),"%"]}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:"Used"})]})]})})]}),s&&(0,r.jsxs)(c.Zp,{className:"bg-gray-800/50 border-purple-500/20",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsxs)(c.ZB,{className:"text-white flex items-center",children:[(0,r.jsx)(m.A,{className:"w-5 h-5 mr-2"}),"Upload Files"]}),(0,r.jsx)(c.BT,{className:"text-gray-400",children:"Upload images for your profile, sessions, or sponsors"})]}),(0,r.jsxs)(c.Wu,{children:[!e&&(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"text-sm text-gray-400 mb-2 block",children:"File Purpose"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:["profile_picture","sponsor_logo","session_artwork","other"].map(e=>(0,r.jsx)(o.$,{variant:l===e?"default":"outline",size:"sm",onClick:()=>p(e),className:l===e?"bg-purple-600 hover:bg-purple-700":"border-gray-600 text-gray-300 hover:bg-gray-700",children:_(e)},e))})]}),(0,r.jsx)(x.e,{purpose:l||"other",onUploadComplete:(e,s)=>{console.log("File uploaded:",{fileId:e,url:s})},onUploadError:e=>console.error("Upload error:",e)})]})]}),(0,r.jsxs)(c.Zp,{className:"bg-gray-800/50 border-purple-500/20",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsxs)(c.ZB,{className:"text-white flex items-center",children:[(0,r.jsx)(u,{className:"w-5 h-5 mr-2"}),"Your Files"]}),(0,r.jsx)(c.BT,{className:"text-gray-400",children:"Manage your uploaded files"})]}),(0,r.jsx)(c.Wu,{children:b&&b.length>0?(0,r.jsx)("div",{className:"space-y-4",children:b.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-700/50 rounded-lg border border-gray-600/30",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-600 rounded-lg flex items-center justify-center",children:e.url?(0,r.jsx)("img",{src:e.url,alt:e.fileName,className:"w-full h-full object-cover rounded-lg"}):(0,r.jsx)(g.A,{className:"w-6 h-6 text-gray-400"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h4",{className:"font-medium text-white truncate",children:e.fileName}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-400",children:[(0,r.jsx)("span",{children:k(e.fileSize)}),(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(y.A,{className:"w-3 h-3 mr-1"}),B(e.uploadedAt)]})]})]}),(0,r.jsx)(d.E,{className:P(e.purpose),children:_(e.purpose)})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[e.url&&(0,r.jsx)(o.$,{variant:"ghost",size:"sm",onClick:()=>window.open(e.url,"_blank"),className:"text-gray-400 hover:text-white",children:(0,r.jsx)(j,{className:"w-4 h-4"})}),(0,r.jsx)(o.$,{variant:"ghost",size:"sm",onClick:()=>w(e._id),className:"text-gray-400 hover:text-red-400",children:(0,r.jsx)(f,{className:"w-4 h-4"})})]})]},e._id))}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(u,{className:"w-12 h-12 text-gray-500 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-400 mb-2",children:"No files uploaded yet"}),(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"Upload your first file to get started"})]})})]})]})}let v=(0,p.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);function N(){let{isAuthenticated:e,isLoading:s}=(0,a.Z)(),t=(0,l.useRouter)();return s?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center",children:(0,r.jsx)("div",{className:"text-white text-lg",children:"Loading..."})}):e?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900",children:[(0,r.jsx)("header",{className:"bg-gray-900/50 backdrop-blur-md border-b border-purple-500/20",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"flex justify-between items-center h-16",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsxs)(o.$,{variant:"ghost",onClick:()=>t.push("/dashboard"),className:"text-gray-300 hover:text-white mr-4",children:[(0,r.jsx)(v,{className:"w-4 h-4 mr-2"}),"Back to Dashboard"]}),(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center mr-3",children:(0,r.jsx)(m.A,{className:"w-5 h-5 text-white"})}),(0,r.jsx)("h1",{className:"text-xl font-bold text-white",children:"File Upload Test"})]})})})}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-white mb-2",children:"File Upload Integration Test"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Test the file upload functionality including profile pictures, sponsor logos, and session artwork"})]}),(0,r.jsx)(b,{showUpload:!0,maxFiles:20})]})]}):null}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5332:(e,s,t)=>{Promise.resolve().then(t.bind(t,2120))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[711,144,778,707,11],()=>t(668));module.exports=r})();