/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2F%40convex-dev%2Fauth%2Fdist%2Fnextjs%2Fserver%2FinvalidateCache.js%22%2C%5B%7B%22id%22%3A%22004537205c560a1ac486f078fff053894f76f7230e%22%2C%22exportedName%22%3A%22invalidateCache%22%7D%5D%5D%5D&__client_imported__=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2F%40convex-dev%2Fauth%2Fdist%2Fnextjs%2Fserver%2FinvalidateCache.js%22%2C%5B%7B%22id%22%3A%22004537205c560a1ac486f078fff053894f76f7230e%22%2C%22exportedName%22%3A%22invalidateCache%22%7D%5D%5D%5D&__client_imported__=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"004537205c560a1ac486f078fff053894f76f7230e\": () => (/* reexport safe */ _Users_robert_hansen_PlayBeg_PlayBeg_playbeg_convex_node_modules_convex_dev_auth_dist_nextjs_server_invalidateCache_js__WEBPACK_IMPORTED_MODULE_0__.invalidateCache)\n/* harmony export */ });\n/* harmony import */ var _Users_robert_hansen_PlayBeg_PlayBeg_playbeg_convex_node_modules_convex_dev_auth_dist_nextjs_server_invalidateCache_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@convex-dev/auth/dist/nextjs/server/invalidateCache.js */ \"(action-browser)/./node_modules/@convex-dev/auth/dist/nextjs/server/invalidateCache.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWFjdGlvbi1lbnRyeS1sb2FkZXIuanM/YWN0aW9ucz0lNUIlNUIlMjIlMkZVc2VycyUyRnJvYmVydC5oYW5zZW4lMkZQbGF5QmVnJTJGUGxheUJlZyUyRnBsYXliZWctY29udmV4JTJGbm9kZV9tb2R1bGVzJTJGJTQwY29udmV4LWRldiUyRmF1dGglMkZkaXN0JTJGbmV4dGpzJTJGc2VydmVyJTJGaW52YWxpZGF0ZUNhY2hlLmpzJTIyJTJDJTVCJTdCJTIyaWQlMjIlM0ElMjIwMDQ1MzcyMDVjNTYwYTFhYzQ4NmYwNzhmZmYwNTM4OTRmNzZmNzIzMGUlMjIlMkMlMjJleHBvcnRlZE5hbWUlMjIlM0ElMjJpbnZhbGlkYXRlQ2FjaGUlMjIlN0QlNUQlNUQlNUQmX19jbGllbnRfaW1wb3J0ZWRfXz10cnVlISIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFDeU0iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGludmFsaWRhdGVDYWNoZSBhcyBcIjAwNDUzNzIwNWM1NjBhMWFjNDg2ZjA3OGZmZjA1Mzg5NGY3NmY3MjMwZVwiIH0gZnJvbSBcIi9Vc2Vycy9yb2JlcnQuaGFuc2VuL1BsYXlCZWcvUGxheUJlZy9wbGF5YmVnLWNvbnZleC9ub2RlX21vZHVsZXMvQGNvbnZleC1kZXYvYXV0aC9kaXN0L25leHRqcy9zZXJ2ZXIvaW52YWxpZGF0ZUNhY2hlLmpzXCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2F%40convex-dev%2Fauth%2Fdist%2Fnextjs%2Fserver%2FinvalidateCache.js%22%2C%5B%7B%22id%22%3A%22004537205c560a1ac486f078fff053894f76f7230e%22%2C%22exportedName%22%3A%22invalidateCache%22%7D%5D%5D%5D&__client_imported__=true!\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"900d2e6c7de9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yb2JlcnQuaGFuc2VuL1BsYXlCZWcvUGxheUJlZy9wbGF5YmVnLWNvbnZleC9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5MDBkMmU2YzdkZTlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_display_swap_preload_true_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"],\"display\":\"swap\",\"preload\":true}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"preload\\\":true}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_display_swap_preload_true_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_display_swap_preload_true_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_display_swap_preload_false_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"],\"display\":\"swap\",\"preload\":false}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"preload\\\":false}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_display_swap_preload_false_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_display_swap_preload_false_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _convex_dev_auth_nextjs_server__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @convex-dev/auth/nextjs/server */ \"(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/index.js\");\n/* harmony import */ var _components_ConvexClientProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ConvexClientProvider */ \"(rsc)/./components/ConvexClientProvider.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"PlayBeg - Interactive DJ Platform\",\n    description: \"The ultimate platform for interactive DJ experiences and real-time audience engagement. Connect with your audience like never before.\",\n    keywords: [\n        \"DJ\",\n        \"music\",\n        \"interactive\",\n        \"requests\",\n        \"audience engagement\",\n        \"events\"\n    ],\n    authors: [\n        {\n            name: \"PlayBeg Team\"\n        }\n    ],\n    creator: \"PlayBeg Inc.\",\n    publisher: \"PlayBeg Inc.\",\n    icons: {\n        icon: \"/convex.svg\",\n        shortcut: \"/convex.svg\",\n        apple: \"/convex.svg\"\n    },\n    openGraph: {\n        title: \"PlayBeg - Interactive DJ Platform\",\n        description: \"The ultimate platform for interactive DJ experiences and real-time audience engagement.\",\n        url: \"https://playbeg.com\",\n        siteName: \"PlayBeg\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"PlayBeg - Interactive DJ Platform\",\n        description: \"The ultimate platform for interactive DJ experiences and real-time audience engagement.\"\n    },\n    robots: {\n        index: true,\n        follow: true\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_convex_dev_auth_nextjs_server__WEBPACK_IMPORTED_MODULE_3__.ConvexAuthNextjsServerProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n            lang: \"en\",\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_display_swap_preload_true_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_display_swap_preload_false_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default().variable)}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased font-sans\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ConvexClientProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/layout.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/layout.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/layout.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/layout.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ConvexClientProvider.tsx":
/*!*********************************************!*\
  !*** ./components/ConvexClientProvider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ConvexClientProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ConvexClientProvider.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2F%40convex-dev%2Fauth%2Fdist%2Fnextjs%2Fclient.js%22%2C%22ids%22%3A%5B%22ConvexAuthNextjsClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Afalse%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2F%40convex-dev%2Fauth%2Fdist%2Fnextjs%2Fclient.js%22%2C%22ids%22%3A%5B%22ConvexAuthNextjsClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Afalse%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ConvexClientProvider.tsx */ \"(rsc)/./components/ConvexClientProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@convex-dev/auth/dist/nextjs/client.js */ \"(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/client.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2F%40convex-dev%2Fauth%2Fdist%2Fnextjs%2Fclient.js%22%2C%22ids%22%3A%5B%22ConvexAuthNextjsClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Afalse%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/ConvexClientProvider.tsx":
/*!*********************************************!*\
  !*** ./components/ConvexClientProvider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConvexClientProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _convex_dev_auth_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @convex-dev/auth/nextjs */ \"(ssr)/./node_modules/@convex-dev/auth/dist/nextjs/index.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(ssr)/./node_modules/convex/dist/esm/react/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst convex = new convex_react__WEBPACK_IMPORTED_MODULE_1__.ConvexReactClient(\"https://lovely-cormorant-474.convex.cloud\");\nfunction ConvexClientProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_convex_dev_auth_nextjs__WEBPACK_IMPORTED_MODULE_2__.ConvexAuthNextjsProvider, {\n        client: convex,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ConvexClientProvider.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL0NvbnZleENsaWVudFByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFbUU7QUFDbEI7QUFHakQsTUFBTUUsU0FBUyxJQUFJRCwyREFBaUJBLENBQUNFLDJDQUFrQztBQUV4RCxTQUFTRyxxQkFBcUIsRUFDM0NDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDUCw2RUFBd0JBO1FBQUNRLFFBQVFOO2tCQUMvQks7Ozs7OztBQUdQIiwic291cmNlcyI6WyIvVXNlcnMvcm9iZXJ0LmhhbnNlbi9QbGF5QmVnL1BsYXlCZWcvcGxheWJlZy1jb252ZXgvY29tcG9uZW50cy9Db252ZXhDbGllbnRQcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IENvbnZleEF1dGhOZXh0anNQcm92aWRlciB9IGZyb20gXCJAY29udmV4LWRldi9hdXRoL25leHRqc1wiO1xuaW1wb3J0IHsgQ29udmV4UmVhY3RDbGllbnQgfSBmcm9tIFwiY29udmV4L3JlYWN0XCI7XG5pbXBvcnQgeyBSZWFjdE5vZGUgfSBmcm9tIFwicmVhY3RcIjtcblxuY29uc3QgY29udmV4ID0gbmV3IENvbnZleFJlYWN0Q2xpZW50KHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NPTlZFWF9VUkwhKTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQ29udmV4Q2xpZW50UHJvdmlkZXIoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8Q29udmV4QXV0aE5leHRqc1Byb3ZpZGVyIGNsaWVudD17Y29udmV4fT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0NvbnZleEF1dGhOZXh0anNQcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJDb252ZXhBdXRoTmV4dGpzUHJvdmlkZXIiLCJDb252ZXhSZWFjdENsaWVudCIsImNvbnZleCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19DT05WRVhfVVJMIiwiQ29udmV4Q2xpZW50UHJvdmlkZXIiLCJjaGlsZHJlbiIsImNsaWVudCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ConvexClientProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2F%40convex-dev%2Fauth%2Fdist%2Fnextjs%2Fclient.js%22%2C%22ids%22%3A%5B%22ConvexAuthNextjsClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Afalse%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2F%40convex-dev%2Fauth%2Fdist%2Fnextjs%2Fclient.js%22%2C%22ids%22%3A%5B%22ConvexAuthNextjsClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Afalse%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ConvexClientProvider.tsx */ \"(ssr)/./components/ConvexClientProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@convex-dev/auth/dist/nextjs/client.js */ \"(ssr)/./node_modules/@convex-dev/auth/dist/nextjs/client.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2F%40convex-dev%2Fauth%2Fdist%2Fnextjs%2Fclient.js%22%2C%22ids%22%3A%5B%22ConvexAuthNextjsClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Afalse%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/convex","vendor-chunks/@convex-dev","vendor-chunks/path-to-regexp","vendor-chunks/jwt-decode","vendor-chunks/is-network-error"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frobert.hansen%2FPlayBeg%2FPlayBeg%2Fplaybeg-convex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();