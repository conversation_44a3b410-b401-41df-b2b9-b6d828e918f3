(()=>{var e={};e.id=492,e.ids=[492],e.modules={203:(e,r,t)=>{"use strict";t.d(r,{default:()=>i});var n=t(687),o=t(6008);let s=new(t(1694)).eH("https://lovely-cormorant-474.convex.cloud");function i({children:e}){return(0,n.jsx)(o.Z,{client:s,children:e})}},589:(e,r,t)=>{"use strict";t.d(r,{g:()=>o});var n=t(6475);let o=(0,n.createServerReference)("00f913389bf07a0ce5471de0fecab54577e23d5f75",n.callServer,void 0,n.findSourceMapURL,"invalidateCache")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1296:(e,r,t)=>{Promise.resolve().then(t.bind(t,203)),Promise.resolve().then(t.bind(t,9433))},1920:(e,r,t)=>{Promise.resolve().then(t.bind(t,8926)),Promise.resolve().then(t.bind(t,6867))},2704:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4224:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},4303:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00f913389bf07a0ce5471de0fecab54577e23d5f75":()=>n.g});var n=t(519)},5442:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var n=t(5239),o=t(8088),s=t(8170),i=t.n(s),a=t(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,8014)),"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=[],u={require:t,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},7776:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},8014:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u,metadata:()=>c});var n=t(7413),o=t(6389),s=t.n(o),i=t(1189),a=t.n(i);t(2704);var d=t(7788),l=t(8926);let c={title:"Create Next App",description:"Generated by create next app",icons:{icon:"/convex.svg"}};function u({children:e}){return(0,n.jsx)(d.Uh,{children:(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${s().variable} ${a().variable} antialiased`,children:(0,n.jsx)(l.default,{children:e})})})})}},8926:(e,r,t)=>{"use strict";t.d(r,{default:()=>n});let n=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ConvexClientProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ConvexClientProvider.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[711],()=>t(5442));module.exports=n})();