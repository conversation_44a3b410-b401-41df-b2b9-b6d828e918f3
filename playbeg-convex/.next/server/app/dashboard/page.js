(()=>{var e={};e.id=105,e.ids=[105],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2017:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>D});var t=a(687),r=a(1694),l=a(6189),i=a(3210),n=a(8999),c=a(9323),d=a(4934),o=a(5303),x=a(2688);let m=(0,x.A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]),h=(0,x.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);var u=a(5192),g=a(9821),p=a(7856);let j=(0,x.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),y=(0,x.A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);var b=a(6023);let N=(0,x.A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);function v(){let[e,s]=(0,i.useState)(!1),[a,l]=(0,i.useState)(!1),c=(0,r.IT)(n.F.users.getCurrentUserWithProfile),x=(0,r.IT)(n.F.users.checkUserStatus),m=(0,r.IT)(n.F.fileStorage.getUserFiles,{purpose:"profile_picture",limit:1}),h=(0,r.n_)(n.F.users.initializeUserSetup),v=(0,r.n_)(n.F.fileStorage.updateProfilePicture),f=async()=>{s(!0);try{await h({createDjProfile:!0,displayName:c?.user?.name||"New DJ"})}catch(e){console.error("Failed to create DJ profile:",e)}finally{s(!1)}},w=async(e,s)=>{try{await v({storageId:e}),l(!1)}catch(e){console.error("Failed to update profile picture:",e)}},P=m?.[0];if(!c)return(0,t.jsx)(u.Zp,{className:"bg-gray-800/50 border-purple-500/20",children:(0,t.jsx)(u.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"animate-pulse",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-700 rounded w-3/4 mb-2"}),(0,t.jsx)("div",{className:"h-4 bg-gray-700 rounded w-1/2"})]})})});let{user:k,djProfile:A,hasDjProfile:C,onboardingComplete:S}=c;return C?(0,t.jsxs)(u.Zp,{className:"bg-gray-800/50 border-purple-500/20",children:[(0,t.jsxs)(u.aR,{children:[(0,t.jsxs)(u.ZB,{className:"text-white flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(o.A,{className:"w-5 h-5 mr-2"}),"DJ Profile"]}),(0,t.jsx)(g.E,{variant:S?"default":"secondary",children:S?"Complete":"Setup Needed"})]}),(0,t.jsx)(u.BT,{className:"text-gray-400",children:"Your DJ profile and account status"})]}),(0,t.jsx)(u.Wu,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"w-20 h-20 bg-gray-600 rounded-full flex items-center justify-center overflow-hidden",children:P?.url?(0,t.jsx)("img",{src:P.url,alt:"Profile",className:"w-full h-full object-cover"}):(0,t.jsx)(j,{className:"w-8 h-8 text-gray-400"})}),(0,t.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>l(!a),className:"absolute -bottom-2 -right-2 w-8 h-8 rounded-full p-0 border-gray-600 bg-gray-800 hover:bg-gray-700",children:(0,t.jsx)(y,{className:"w-4 h-4"})})]}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"text-white font-medium",children:A?.displayName||"Unnamed DJ"}),(0,t.jsx)("p",{className:"text-gray-400 text-sm",children:k?.email||"No email"}),P&&(0,t.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Picture uploaded ",new Date(P.uploadedAt).toLocaleDateString()]})]})]}),a&&(0,t.jsxs)("div",{className:"border border-gray-600 rounded-lg p-4 bg-gray-700/30",children:[(0,t.jsxs)("h5",{className:"text-white font-medium mb-3 flex items-center",children:[(0,t.jsx)(b.A,{className:"w-4 h-4 mr-2"}),"Update Profile Picture"]}),(0,t.jsx)(p.e,{purpose:"profile_picture",onUploadComplete:w,onUploadError:e=>console.error("Profile picture upload error:",e),maxSize:5242880,className:"mb-3"}),(0,t.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>l(!1),className:"border-gray-600 text-gray-300 hover:bg-gray-700",children:"Cancel"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm text-gray-400",children:"Display Name"}),(0,t.jsx)("p",{className:"text-white font-medium",children:A?.displayName||"Not set"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm text-gray-400",children:"Email"}),(0,t.jsx)("p",{className:"text-white font-medium",children:k?.email||"Not available"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-700",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:`w-3 h-3 rounded-full mx-auto mb-2 ${x?.isAuthenticated?"bg-green-400":"bg-red-400"}`}),(0,t.jsx)("p",{className:"text-xs text-gray-400",children:"Auth Status"}),(0,t.jsx)("p",{className:"text-white text-sm font-medium",children:x?.isAuthenticated?"Active":"Inactive"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:`w-3 h-3 rounded-full mx-auto mb-2 ${x?.hasDjProfile?"bg-green-400":"bg-gray-400"}`}),(0,t.jsx)("p",{className:"text-xs text-gray-400",children:"DJ Profile"}),(0,t.jsx)("p",{className:"text-white text-sm font-medium",children:x?.hasDjProfile?"Created":"Missing"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:`w-3 h-3 rounded-full mx-auto mb-2 ${S?"bg-green-400":"bg-orange-400"}`}),(0,t.jsx)("p",{className:"text-xs text-gray-400",children:"Onboarding"}),(0,t.jsx)("p",{className:"text-white text-sm font-medium",children:S?"Done":"Pending"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full mx-auto mb-2 bg-blue-400"}),(0,t.jsx)("p",{className:"text-xs text-gray-400",children:"Member Since"}),(0,t.jsx)("p",{className:"text-white text-sm font-medium",children:A?new Date(A.createdAt).toLocaleDateString():"N/A"})]})]}),(0,t.jsxs)("div",{className:"flex space-x-2 pt-4",children:[(0,t.jsxs)(d.$,{variant:"outline",size:"sm",className:"border-gray-600 text-gray-300 hover:bg-gray-700",children:[(0,t.jsx)(N,{className:"w-4 h-4 mr-2"}),"Edit Profile"]}),!S&&(0,t.jsx)(d.$,{size:"sm",className:"bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700",children:"Complete Setup"})]})]})})]}):(0,t.jsxs)(u.Zp,{className:"bg-gray-800/50 border-purple-500/20",children:[(0,t.jsxs)(u.aR,{children:[(0,t.jsxs)(u.ZB,{className:"text-white flex items-center",children:[(0,t.jsx)(j,{className:"w-5 h-5 mr-2"}),"Create DJ Profile"]}),(0,t.jsx)(u.BT,{className:"text-gray-400",children:"Set up your DJ profile to start creating sessions and receiving requests"})]}),(0,t.jsx)(u.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"p-4 bg-purple-500/20 border border-purple-500/30 rounded-lg",children:[(0,t.jsxs)("h4",{className:"text-white font-medium mb-2",children:["Welcome, ",k?.name||k?.email,"!"]}),(0,t.jsx)("p",{className:"text-gray-300 text-sm mb-4",children:"Create your DJ profile to unlock all PlayBeg features including session management, song request handling, and real-time audience interaction."}),(0,t.jsx)(d.$,{onClick:f,disabled:e,className:"w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700",children:e?"Creating...":"Create DJ Profile"})]})})})]})}let f=(0,x.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),w=(0,x.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);var P=a(228);let k=(0,x.A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]),A=(0,x.A)("pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]),C=(0,x.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);function S(){let[e,s]=(0,i.useState)(!1),a=(0,r.IT)(n.F.users.getCurrentUserWithProfile),l=a?.djProfile,c=async()=>{s(!0);try{console.log("Creating session..."),await new Promise(e=>setTimeout(e,1e3))}catch(e){console.error("Failed to create session:",e)}finally{s(!1)}};if(!l)return(0,t.jsxs)(u.Zp,{className:"bg-gray-800/50 border-purple-500/20",children:[(0,t.jsxs)(u.aR,{children:[(0,t.jsxs)(u.ZB,{className:"text-white flex items-center",children:[(0,t.jsx)(o.A,{className:"w-5 h-5 mr-2"}),"Session Manager"]}),(0,t.jsx)(u.BT,{className:"text-gray-400",children:"Create a DJ profile first to manage sessions"})]}),(0,t.jsx)(u.Wu,{children:(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(o.A,{className:"w-12 h-12 text-gray-500 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-400 mb-4",children:"DJ profile required"}),(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"Create your DJ profile to start managing sessions and receiving song requests."})]})})]});let x=[{id:"1",name:"Friday Night Vibes",status:"active",createdAt:Date.now()-36e5,requestCount:12,maxRequests:50},{id:"2",name:"Chill Sunday Session",status:"inactive",createdAt:Date.now()-864e5,requestCount:8,maxRequests:25}],m=x.filter(e=>"active"===e.status),h=x.slice(0,3);return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsx)(u.Zp,{className:"bg-gray-800/50 border-purple-500/20",children:(0,t.jsxs)(u.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Create Session"}),(0,t.jsx)(f,{className:"w-6 h-6 text-purple-400"})]}),(0,t.jsx)("p",{className:"text-gray-400 mb-4 text-sm",children:"Start a new DJ session to receive song requests"}),(0,t.jsx)(d.$,{onClick:c,disabled:e,className:"w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700",children:e?"Creating...":"New Session"})]})}),(0,t.jsx)(u.Zp,{className:"bg-gray-800/50 border-purple-500/20",children:(0,t.jsxs)(u.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Active Sessions"}),(0,t.jsx)("div",{className:`w-3 h-3 rounded-full ${m.length>0?"bg-green-400 animate-pulse":"bg-gray-400"}`})]}),(0,t.jsxs)("p",{className:"text-gray-400 mb-4 text-sm",children:[m.length," active session",1!==m.length?"s":""]}),(0,t.jsx)(d.$,{variant:"outline",className:"w-full border-gray-600 text-gray-300 hover:bg-gray-700",disabled:0===m.length,children:m.length>0?"Manage Active":"No Active Sessions"})]})}),(0,t.jsx)(u.Zp,{className:"bg-gray-800/50 border-purple-500/20",children:(0,t.jsxs)(u.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Total Requests"}),(0,t.jsx)(w,{className:"w-6 h-6 text-purple-400"})]}),(0,t.jsxs)("p",{className:"text-gray-400 mb-4 text-sm",children:[x.reduce((e,s)=>e+s.requestCount,0)," total requests received"]}),(0,t.jsx)(d.$,{variant:"outline",className:"w-full border-gray-600 text-gray-300 hover:bg-gray-700",children:"View Analytics"})]})})]}),(0,t.jsxs)(u.Zp,{className:"bg-gray-800/50 border-purple-500/20",children:[(0,t.jsxs)(u.aR,{children:[(0,t.jsxs)(u.ZB,{className:"text-white flex items-center",children:[(0,t.jsx)(P.A,{className:"w-5 h-5 mr-2"}),"Recent Sessions"]}),(0,t.jsx)(u.BT,{className:"text-gray-400",children:"Your latest DJ sessions and their status"})]}),(0,t.jsx)(u.Wu,{children:h.length>0?(0,t.jsx)("div",{className:"space-y-4",children:h.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-700/50 rounded-lg border border-gray-600/30",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:`w-10 h-10 rounded-full flex items-center justify-center ${"active"===e.status?"bg-green-500/20 text-green-400":"bg-gray-500/20 text-gray-400"}`,children:"active"===e.status?(0,t.jsx)(k,{className:"w-5 h-5"}):(0,t.jsx)(A,{className:"w-5 h-5"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-white",children:e.name}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-400",children:[(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(C,{className:"w-4 h-4 mr-1"}),new Date(e.createdAt).toLocaleDateString()]}),(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(w,{className:"w-4 h-4 mr-1"}),e.requestCount,"/",e.maxRequests," requests"]})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(g.E,{variant:"active"===e.status?"default":"secondary",children:"active"===e.status?"Live":"Ended"}),(0,t.jsx)(d.$,{size:"sm",variant:"outline",className:"border-gray-600 text-gray-300 hover:bg-gray-700",children:"active"===e.status?"Manage":"View"})]})]},e.id))}):(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(o.A,{className:"w-12 h-12 text-gray-500 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-400 mb-4",children:"No sessions yet"}),(0,t.jsx)("p",{className:"text-gray-500 text-sm mb-6",children:"Create your first session to start receiving song requests from your audience."}),(0,t.jsx)(d.$,{onClick:c,disabled:e,className:"bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700",children:e?"Creating...":"Create Your First Session"})]})})]})]})}function D(){let{isAuthenticated:e,isLoading:s}=(0,r.Z)(),{signOut:a}=(0,c.BG)(),i=(0,l.useRouter)(),x=(0,r.IT)(n.F.users.getCurrentUser),u=(0,r.IT)(n.F.users.getCurrentUserWithProfile);u?.djProfile;let g=(0,r.IT)(n.F.users.checkUserStatus),p=(0,r.IT)(n.F.djProfiles.listDjProfiles,{limit:10,onboardingStatus:!0}),j=async()=>{await a(),i.push("/signin")};return s?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center",children:(0,t.jsx)("div",{className:"text-white text-lg",children:"Loading..."})}):e?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900",children:[(0,t.jsx)("header",{className:"bg-gray-900/50 backdrop-blur-md border-b border-purple-500/20",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center mr-3",children:(0,t.jsx)(o.A,{className:"w-5 h-5 text-white"})}),(0,t.jsx)("h1",{className:"text-xl font-bold text-white",children:"PlayBeg"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("span",{className:"text-gray-300",children:["Welcome, ",x?.name||x?.email||"DJ"]}),(0,t.jsxs)(d.$,{onClick:j,variant:"ghost",size:"sm",className:"text-gray-300 hover:text-white",children:[(0,t.jsx)(m,{className:"w-4 h-4 mr-2"}),"Sign Out"]})]})]})})}),(0,t.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold text-white mb-2",children:"DJ Dashboard"}),(0,t.jsx)("p",{className:"text-gray-400",children:"Manage your sessions, song requests, and audience engagement"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsx)(v,{})}),(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsx)(S,{})})]}),(0,t.jsx)("div",{className:"mt-8",children:(0,t.jsxs)("div",{className:"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("h3",{className:"text-xl font-semibold text-white flex items-center",children:[(0,t.jsx)(h,{className:"w-5 h-5 mr-2"}),"Quick Stats"]}),(0,t.jsx)(d.$,{variant:"outline",size:"sm",className:"border-gray-600 text-gray-300 hover:bg-gray-700",children:"View Full Analytics"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-white mb-1",children:p?.length||0}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:"Total DJs"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-white mb-1",children:"0"}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:"Active Sessions"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-white mb-1",children:"0"}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:"Song Requests"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-white mb-1",children:g?.isAuthenticated?"100%":"0%"}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:"System Status"})]})]})]})})]})]}):null}},2378:(e,s,a)=>{Promise.resolve().then(a.bind(a,2017))},2994:(e,s,a)=>{Promise.resolve().then(a.bind(a,4118))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4118:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx","default")},8086:e=>{"use strict";e.exports=require("module")},8212:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var t=a(5239),r=a(8088),l=a(8170),i=a.n(l),n=a(893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let d={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,4118)),"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,8014)),"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[711,144,778,707,713,11],()=>a(8212));module.exports=t})();