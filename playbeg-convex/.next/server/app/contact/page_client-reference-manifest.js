globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/contact/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./components/ConvexClientProvider.tsx":{"*":{"id":"(ssr)/./components/ConvexClientProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@convex-dev/auth/dist/nextjs/client.js":{"*":{"id":"(ssr)/./node_modules/@convex-dev/auth/dist/nextjs/client.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/signin/page.tsx":{"*":{"id":"(ssr)/./app/signin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/blog/page.tsx":{"*":{"id":"(ssr)/./app/blog/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/help/page.tsx":{"*":{"id":"(ssr)/./app/help/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/contact/page.tsx":{"*":{"id":"(ssr)/./app/contact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/about/page.tsx":{"*":{"id":"(ssr)/./app/about/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/privacy/page.tsx":{"*":{"id":"(ssr)/./app/privacy/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/page.tsx":{"*":{"id":"(ssr)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ConvexClientProvider.tsx":{"id":"(app-pages-browser)/./components/ConvexClientProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/node_modules/@convex-dev/auth/dist/nextjs/client.js":{"id":"(app-pages-browser)/./node_modules/@convex-dev/auth/dist/nextjs/client.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx":{"id":"(app-pages-browser)/./app/signin/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/node_modules/next/dist/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/node_modules/next/dist/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/blog/page.tsx":{"id":"(app-pages-browser)/./app/blog/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/help/page.tsx":{"id":"(app-pages-browser)/./app/help/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/contact/page.tsx":{"id":"(app-pages-browser)/./app/contact/page.tsx","name":"*","chunks":["app/contact/page","static/chunks/app/contact/page.js"],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/about/page.tsx":{"id":"(app-pages-browser)/./app/about/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/privacy/page.tsx":{"id":"(app-pages-browser)/./app/privacy/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx":{"id":"(app-pages-browser)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/":[],"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/page":[],"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/contact/page":[]},"rscModuleMapping":{"(app-pages-browser)/./components/ConvexClientProvider.tsx":{"*":{"id":"(rsc)/./components/ConvexClientProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@convex-dev/auth/dist/nextjs/client.js":{"*":{"id":"(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/client.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/signin/page.tsx":{"*":{"id":"(rsc)/./app/signin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(rsc)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/blog/page.tsx":{"*":{"id":"(rsc)/./app/blog/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/help/page.tsx":{"*":{"id":"(rsc)/./app/help/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/contact/page.tsx":{"*":{"id":"(rsc)/./app/contact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/about/page.tsx":{"*":{"id":"(rsc)/./app/about/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/privacy/page.tsx":{"*":{"id":"(rsc)/./app/privacy/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/page.tsx":{"*":{"id":"(rsc)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}