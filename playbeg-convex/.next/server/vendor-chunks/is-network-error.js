"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/is-network-error";
exports.ids = ["vendor-chunks/is-network-error"];
exports.modules = {

/***/ "(ssr)/./node_modules/is-network-error/index.js":
/*!************************************************!*\
  !*** ./node_modules/is-network-error/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ isNetworkError)\n/* harmony export */ });\nconst objectToString = Object.prototype.toString;\n\nconst isError = value => objectToString.call(value) === '[object Error]';\n\nconst errorMessages = new Set([\n\t'network error', // Chrome\n\t'Failed to fetch', // Chrome\n\t'NetworkError when attempting to fetch resource.', // Firefox\n\t'The Internet connection appears to be offline.', // Safari 16\n\t'Load failed', // Safari 17+\n\t'Network request failed', // `cross-fetch`\n\t'fetch failed', // Undici (Node.js)\n\t'terminated', // Undici (Node.js)\n]);\n\nfunction isNetworkError(error) {\n\tconst isValid = error\n\t\t&& isError(error)\n\t\t&& error.name === 'TypeError'\n\t\t&& typeof error.message === 'string';\n\n\tif (!isValid) {\n\t\treturn false;\n\t}\n\n\t// We do an extra check for Safari 17+ as it has a very generic error message.\n\t// Network errors in Safari have no stack.\n\tif (error.message === 'Load failed') {\n\t\treturn error.stack === undefined;\n\t}\n\n\treturn errorMessages.has(error.message);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaXMtbmV0d29yay1lcnJvci9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvcm9iZXJ0LmhhbnNlbi9QbGF5QmVnL1BsYXlCZWcvcGxheWJlZy1jb252ZXgvbm9kZV9tb2R1bGVzL2lzLW5ldHdvcmstZXJyb3IvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgb2JqZWN0VG9TdHJpbmcgPSBPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nO1xuXG5jb25zdCBpc0Vycm9yID0gdmFsdWUgPT4gb2JqZWN0VG9TdHJpbmcuY2FsbCh2YWx1ZSkgPT09ICdbb2JqZWN0IEVycm9yXSc7XG5cbmNvbnN0IGVycm9yTWVzc2FnZXMgPSBuZXcgU2V0KFtcblx0J25ldHdvcmsgZXJyb3InLCAvLyBDaHJvbWVcblx0J0ZhaWxlZCB0byBmZXRjaCcsIC8vIENocm9tZVxuXHQnTmV0d29ya0Vycm9yIHdoZW4gYXR0ZW1wdGluZyB0byBmZXRjaCByZXNvdXJjZS4nLCAvLyBGaXJlZm94XG5cdCdUaGUgSW50ZXJuZXQgY29ubmVjdGlvbiBhcHBlYXJzIHRvIGJlIG9mZmxpbmUuJywgLy8gU2FmYXJpIDE2XG5cdCdMb2FkIGZhaWxlZCcsIC8vIFNhZmFyaSAxNytcblx0J05ldHdvcmsgcmVxdWVzdCBmYWlsZWQnLCAvLyBgY3Jvc3MtZmV0Y2hgXG5cdCdmZXRjaCBmYWlsZWQnLCAvLyBVbmRpY2kgKE5vZGUuanMpXG5cdCd0ZXJtaW5hdGVkJywgLy8gVW5kaWNpIChOb2RlLmpzKVxuXSk7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGlzTmV0d29ya0Vycm9yKGVycm9yKSB7XG5cdGNvbnN0IGlzVmFsaWQgPSBlcnJvclxuXHRcdCYmIGlzRXJyb3IoZXJyb3IpXG5cdFx0JiYgZXJyb3IubmFtZSA9PT0gJ1R5cGVFcnJvcidcblx0XHQmJiB0eXBlb2YgZXJyb3IubWVzc2FnZSA9PT0gJ3N0cmluZyc7XG5cblx0aWYgKCFpc1ZhbGlkKSB7XG5cdFx0cmV0dXJuIGZhbHNlO1xuXHR9XG5cblx0Ly8gV2UgZG8gYW4gZXh0cmEgY2hlY2sgZm9yIFNhZmFyaSAxNysgYXMgaXQgaGFzIGEgdmVyeSBnZW5lcmljIGVycm9yIG1lc3NhZ2UuXG5cdC8vIE5ldHdvcmsgZXJyb3JzIGluIFNhZmFyaSBoYXZlIG5vIHN0YWNrLlxuXHRpZiAoZXJyb3IubWVzc2FnZSA9PT0gJ0xvYWQgZmFpbGVkJykge1xuXHRcdHJldHVybiBlcnJvci5zdGFjayA9PT0gdW5kZWZpbmVkO1xuXHR9XG5cblx0cmV0dXJuIGVycm9yTWVzc2FnZXMuaGFzKGVycm9yLm1lc3NhZ2UpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/is-network-error/index.js\n");

/***/ })

};
;