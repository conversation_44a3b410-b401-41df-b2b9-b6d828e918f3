"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@convex-dev";
exports.ids = ["vendor-chunks/@convex-dev"];
exports.modules = {

/***/ "(action-browser)/./node_modules/@convex-dev/auth/dist/nextjs/server/invalidateCache.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@convex-dev/auth/dist/nextjs/server/invalidateCache.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   invalidateCache: () => (/* binding */ invalidateCache)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-server-reference */ \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! private-next-rsc-action-encryption */ \"(action-browser)/./node_modules/next/dist/server/app-render/encryption.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/headers */ \"(action-browser)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-rsc-action-validate */ \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js\");\n/* __next_internal_action_entry_do_not_use__ {\"00f913389bf07a0ce5471de0fecab54577e23d5f75\":\"invalidateCache\"} */ \n\n\nasync function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ invalidateCache() {\n    // Dummy cookie, just to set the header which will invalidate\n    // the client Router Cache.\n    (await (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)()).delete(`__convexAuthCookieForRouterCacheInvalidation${Date.now()}`);\n    return null;\n} //# sourceMappingURL=invalidateCache.js.map\n\n(0,private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_3__.ensureServerEntryExports)([\n    invalidateCache\n]);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(invalidateCache, \"00f913389bf07a0ce5471de0fecab54577e23d5f75\", null);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AY29udmV4LWRldi9hdXRoL2Rpc3QvbmV4dGpzL3NlcnZlci9pbnZhbGlkYXRlQ2FjaGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUV1QztBQUVoQyxLQUFLLGlEQUFVLEVBQWU7SUFDbkMsNkRBQTZEO0lBQzdELDJCQUEyQjtLQUMxQixNQUFNLHFEQUFPLEdBQUUsQ0FBQyxNQUFPLENBQ3RCLCtDQUErQyxJQUFJLENBQUMsR0FBRyxFQUFFLEVBQUUsQ0FDNUQsQ0FBQztJQUNGLE9BQU8sSUFBSSxDQUFDO0FBQ2QsQ0FBQzs7Ozs7MkZBUHFCIiwic291cmNlcyI6WyIvVXNlcnMvcm9iZXJ0LmhhbnNlbi9zcmMvbmV4dGpzL3NlcnZlci9pbnZhbGlkYXRlQ2FjaGUudHMiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@convex-dev/auth/dist/nextjs/server/invalidateCache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/client.js":
/*!*************************************************************!*\
  !*** ./node_modules/@convex-dev/auth/dist/nextjs/client.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ConvexAuthNextjsClientProvider: () => (/* binding */ ConvexAuthNextjsClientProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");

const ConvexAuthNextjsClientProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ConvexAuthNextjsClientProvider() from the server but ConvexAuthNextjsClientProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/node_modules/@convex-dev/auth/dist/nextjs/client.js",
"ConvexAuthNextjsClientProvider",
);

/***/ }),

/***/ "(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/cookies.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@convex-dev/auth/dist/nextjs/server/cookies.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRequestCookies: () => (/* binding */ getRequestCookies),\n/* harmony export */   getRequestCookiesInMiddleware: () => (/* binding */ getRequestCookiesInMiddleware),\n/* harmony export */   getResponseCookies: () => (/* binding */ getResponseCookies)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _server_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../server/utils.js */ \"(rsc)/./node_modules/@convex-dev/auth/dist/server/utils.js\");\n\n\nconst cookies = next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies;\nconst headers = next_headers__WEBPACK_IMPORTED_MODULE_0__.headers;\nasync function getRequestCookies() {\n    // maxAge doesn't matter for request cookies since they're only relevant for the\n    // length of the request\n    return getCookieStore(await headers(), await cookies(), {\n        maxAge: null,\n    });\n}\nasync function getRequestCookiesInMiddleware(request) {\n    // maxAge doesn't matter for request cookies since they're only relevant for the\n    // length of the request\n    return getCookieStore(await headers(), request.cookies, { maxAge: null });\n}\nasync function getResponseCookies(response, cookieConfig) {\n    return getCookieStore(await headers(), response.cookies, cookieConfig);\n}\nfunction getCookieStore(requestHeaders, responseCookies, cookieConfig) {\n    const isLocalhost = _server_utils_js__WEBPACK_IMPORTED_MODULE_1__.isLocalHost(requestHeaders.get(\"Host\") ?? \"\");\n    const prefix = isLocalhost ? \"\" : \"__Host-\";\n    const tokenName = prefix + \"__convexAuthJWT\";\n    const refreshTokenName = prefix + \"__convexAuthRefreshToken\";\n    const verifierName = prefix + \"__convexAuthOAuthVerifier\";\n    function getValue(name) {\n        return responseCookies.get(name)?.value ?? null;\n    }\n    const cookieOptions = getCookieOptions(isLocalhost, cookieConfig);\n    function setValue(name, value) {\n        if (value === null) {\n            // Only request cookies have a `size` property\n            if (\"size\" in responseCookies) {\n                responseCookies.delete(name);\n            }\n            else {\n                // See https://github.com/vercel/next.js/issues/56632\n                // for why .delete({}) doesn't work:\n                responseCookies.set(name, \"\", {\n                    ...cookieOptions,\n                    maxAge: undefined,\n                    expires: 0,\n                });\n            }\n        }\n        else {\n            responseCookies.set(name, value, cookieOptions);\n        }\n    }\n    return {\n        get token() {\n            return getValue(tokenName);\n        },\n        set token(value) {\n            setValue(tokenName, value);\n        },\n        get refreshToken() {\n            return getValue(refreshTokenName);\n        },\n        set refreshToken(value) {\n            setValue(refreshTokenName, value);\n        },\n        get verifier() {\n            return getValue(verifierName);\n        },\n        set verifier(value) {\n            setValue(verifierName, value);\n        },\n    };\n}\nfunction getCookieOptions(isLocalhost, cookieConfig) {\n    // Safari does not send headers with `secure: true` on http:// domains including localhost,\n    // so set `secure: false` (https://codedamn.com/news/web-development/safari-cookie-is-not-being-set)\n    return {\n        secure: isLocalhost ? false : true,\n        httpOnly: true,\n        sameSite: \"lax\",\n        path: \"/\",\n        maxAge: cookieConfig.maxAge ?? undefined,\n    };\n}\n//# sourceMappingURL=cookies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/cookies.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@convex-dev/auth/dist/nextjs/server/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConvexAuthNextjsServerProvider: () => (/* binding */ ConvexAuthNextjsServerProvider),\n/* harmony export */   convexAuthNextjsMiddleware: () => (/* binding */ convexAuthNextjsMiddleware),\n/* harmony export */   convexAuthNextjsToken: () => (/* binding */ convexAuthNextjsToken),\n/* harmony export */   createRouteMatcher: () => (/* reexport safe */ _routeMatcher_js__WEBPACK_IMPORTED_MODULE_9__.createRouteMatcher),\n/* harmony export */   isAuthenticatedNextjs: () => (/* binding */ isAuthenticatedNextjs),\n/* harmony export */   nextjsMiddlewareRedirect: () => (/* binding */ nextjsMiddlewareRedirect)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\n/* harmony import */ var server_only__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! server-only */ \"(rsc)/./node_modules/next/dist/compiled/server-only/empty.js\");\n/* harmony import */ var convex_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! convex/nextjs */ \"(rsc)/./node_modules/convex/dist/esm/nextjs/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _client_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../client.js */ \"(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/client.js\");\n/* harmony import */ var _cookies_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./cookies.js */ \"(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/cookies.js\");\n/* harmony import */ var _proxy_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./proxy.js */ \"(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/proxy.js\");\n/* harmony import */ var _request_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./request.js */ \"(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/request.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/utils.js\");\n/* harmony import */ var _routeMatcher_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./routeMatcher.js */ \"(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/routeMatcher.js\");\n\n\n\n\n\n\n\n\n\n/**\n * Wrap your app with this provider in your root `layout.tsx`.\n */\nasync function ConvexAuthNextjsServerProvider(props) {\n    const { apiRoute, storage, storageNamespace, verbose, children } = props;\n    const serverState = await convexAuthNextjsServerState();\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_client_js__WEBPACK_IMPORTED_MODULE_4__.ConvexAuthNextjsClientProvider, { serverState: serverState, apiRoute: apiRoute, storage: storage, storageNamespace: storageNamespace, verbose: verbose, children: children }));\n}\n/**\n * Retrieve the token for authenticating calls to your\n * Convex backend from Server Components, Server Actions and Route Handlers.\n * @returns The token if the client is authenticated, otherwise `undefined`.\n */\nasync function convexAuthNextjsToken() {\n    return (await (0,_cookies_js__WEBPACK_IMPORTED_MODULE_5__.getRequestCookies)()).token ?? undefined;\n}\n/**\n * Whether the client is authenticated, which you can check\n * in Server Actions, Route Handlers and Middleware.\n *\n * Avoid the pitfall of checking authentication state in layouts,\n * since they won't stop nested pages from rendering.\n */\nasync function isAuthenticatedNextjs(options = {}) {\n    const cookies = await (0,_cookies_js__WEBPACK_IMPORTED_MODULE_5__.getRequestCookies)();\n    return isAuthenticated(cookies.token, options);\n}\n/**\n * Use in your `middleware.ts` to enable your Next.js app to use\n * Convex Auth for authentication on the server.\n *\n * @returns A Next.js middleware.\n */\nfunction convexAuthNextjsMiddleware(\n/**\n * A custom handler, which you can use to decide\n * which routes should be accessible based on the client's authentication.\n */\nhandler, options = {}) {\n    return async (request, event) => {\n        const verbose = options.verbose ?? false;\n        const cookieConfig = options.cookieConfig ?? { maxAge: null };\n        if (cookieConfig.maxAge !== null && cookieConfig.maxAge <= 0) {\n            throw new Error(\"cookieConfig.maxAge must be null or a positive number of seconds\");\n        }\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_6__.logVerbose)(`Begin middleware for request with URL ${request.url}`, verbose);\n        const requestUrl = new URL(request.url);\n        // Proxy signIn and signOut actions to Convex backend\n        const apiRoute = options?.apiRoute ?? \"/api/auth\";\n        if ((0,_proxy_js__WEBPACK_IMPORTED_MODULE_7__.shouldProxyAuthAction)(request, apiRoute)) {\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_6__.logVerbose)(`Proxying auth action to Convex, path matches ${apiRoute} with or without trailing slash`, verbose);\n            return await (0,_proxy_js__WEBPACK_IMPORTED_MODULE_7__.proxyAuthActionToConvex)(request, options);\n        }\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_6__.logVerbose)(`Not proxying auth action to Convex, path ${requestUrl.pathname} does not match ${apiRoute}`, verbose);\n        // Refresh tokens, handle code query param\n        const authResult = await (0,_request_js__WEBPACK_IMPORTED_MODULE_8__.handleAuthenticationInRequest)(request, options);\n        // If redirecting, proceed, the middleware will run again on next request\n        if (authResult.kind === \"redirect\") {\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_6__.logVerbose)(`Redirecting to ${authResult.response.headers.get(\"Location\")}`, verbose);\n            return authResult.response;\n        }\n        let response = null;\n        // Forward cookies to request for custom handler\n        if (authResult.kind === \"refreshTokens\" &&\n            authResult.refreshTokens !== undefined) {\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_6__.logVerbose)(`Forwarding cookies to request`, verbose);\n            await (0,_utils_js__WEBPACK_IMPORTED_MODULE_6__.setAuthCookiesInMiddleware)(request, authResult.refreshTokens);\n        }\n        if (handler === undefined) {\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_6__.logVerbose)(`No custom handler`, verbose);\n            response = next_server__WEBPACK_IMPORTED_MODULE_3__.NextResponse.next({\n                request: {\n                    headers: request.headers,\n                },\n            });\n        }\n        else {\n            // Call the custom handler\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_6__.logVerbose)(`Calling custom handler`, verbose);\n            response =\n                (await handler(request, {\n                    event,\n                    convexAuth: {\n                        getToken: async () => {\n                            const cookies = await (0,_cookies_js__WEBPACK_IMPORTED_MODULE_5__.getRequestCookiesInMiddleware)(request);\n                            return cookies.token ?? undefined;\n                        },\n                        isAuthenticated: async () => {\n                            const cookies = await (0,_cookies_js__WEBPACK_IMPORTED_MODULE_5__.getRequestCookiesInMiddleware)(request);\n                            return isAuthenticated(cookies.token, options);\n                        },\n                    },\n                })) ??\n                    next_server__WEBPACK_IMPORTED_MODULE_3__.NextResponse.next({\n                        request: {\n                            headers: request.headers,\n                        },\n                    });\n        }\n        // Port the cookies from the auth middleware to the response\n        if (authResult.kind === \"refreshTokens\" &&\n            authResult.refreshTokens !== undefined) {\n            const nextResponse = next_server__WEBPACK_IMPORTED_MODULE_3__.NextResponse.next(response);\n            await (0,_utils_js__WEBPACK_IMPORTED_MODULE_6__.setAuthCookies)(nextResponse, authResult.refreshTokens, cookieConfig);\n            return nextResponse;\n        }\n        return response;\n    };\n}\n\n/**\n * Helper for redirecting to a different route from\n * a Next.js middleware.\n *\n * ```ts\n * return nextjsMiddlewareRedirect(request, \"/login\");\n * ```\n */\nfunction nextjsMiddlewareRedirect(\n/**\n * The incoming request handled by the middleware.\n */\nrequest, \n/**\n * The route path to redirect to.\n */\npathname) {\n    const url = request.nextUrl.clone();\n    url.pathname = pathname;\n    return next_server__WEBPACK_IMPORTED_MODULE_3__.NextResponse.redirect(url);\n}\nasync function convexAuthNextjsServerState() {\n    const { token } = await (0,_cookies_js__WEBPACK_IMPORTED_MODULE_5__.getRequestCookies)();\n    return {\n        // The server doesn't share the refresh token with the client\n        // for added security - the client has to use the server\n        // to refresh the access token via cookies.\n        _state: { token, refreshToken: \"dummy\" },\n        _timeFetched: Date.now(),\n    };\n}\nasync function isAuthenticated(token, options) {\n    if (!token) {\n        return false;\n    }\n    try {\n        return await (0,convex_nextjs__WEBPACK_IMPORTED_MODULE_2__.fetchQuery)(\"auth:isAuthenticated\", {}, {\n            ...(0,_utils_js__WEBPACK_IMPORTED_MODULE_6__.getConvexNextjsOptions)(options),\n            token: token,\n        });\n    }\n    catch (e) {\n        if (e.message.includes(\"Could not find public function\")) {\n            throw new Error(\"Server Error: could not find api.auth.isAuthenticated. convex-auth 0.0.76 introduced a new export in convex/auth.ts. Add `isAuthenticated` to the list of functions returned from convexAuth(). See convex-auth changelog for more https://github.com/get-convex/convex-auth/blob/main/CHANGELOG.md\");\n        }\n        else {\n            console.log(\"Returning false from isAuthenticated because\", e);\n        }\n        return false;\n    }\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/proxy.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@convex-dev/auth/dist/nextjs/server/proxy.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   proxyAuthActionToConvex: () => (/* binding */ proxyAuthActionToConvex),\n/* harmony export */   shouldProxyAuthAction: () => (/* binding */ shouldProxyAuthAction)\n/* harmony export */ });\n/* harmony import */ var server_only__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! server-only */ \"(rsc)/./node_modules/next/dist/compiled/server-only/empty.js\");\n/* harmony import */ var convex_nextjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/nextjs */ \"(rsc)/./node_modules/convex/dist/esm/nextjs/index.js\");\n/* harmony import */ var _cookies_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./cookies.js */ \"(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/cookies.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/utils.js\");\n\n\n\n\nasync function proxyAuthActionToConvex(request, options) {\n    const cookieConfig = options?.cookieConfig ?? { maxAge: null };\n    const verbose = options?.verbose ?? false;\n    if (request.method !== \"POST\") {\n        return new Response(\"Invalid method\", { status: 405 });\n    }\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isCorsRequest)(request)) {\n        return new Response(\"Invalid origin\", { status: 403 });\n    }\n    const { action, args } = await request.json();\n    if (action !== \"auth:signIn\" && action !== \"auth:signOut\") {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.logVerbose)(`Invalid action ${action}, returning 400`, verbose);\n        return new Response(\"Invalid action\", { status: 400 });\n    }\n    let token;\n    if (action === \"auth:signIn\" && args.refreshToken !== undefined) {\n        // The client has a dummy refreshToken, the real one is only\n        // stored in cookies.\n        const refreshToken = (await (0,_cookies_js__WEBPACK_IMPORTED_MODULE_3__.getRequestCookies)()).refreshToken;\n        if (refreshToken === null) {\n            console.error(\"Convex Auth: Unexpected missing refreshToken cookie during client refresh\");\n            return new Response(JSON.stringify({ tokens: null }));\n        }\n        args.refreshToken = refreshToken;\n    }\n    else {\n        // Make sure the proxy is authenticated if the client is,\n        // important for signOut and any other logic working\n        // with existing sessions.\n        token = (await (0,_cookies_js__WEBPACK_IMPORTED_MODULE_3__.getRequestCookies)()).token ?? undefined;\n    }\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.logVerbose)(`Fetching action ${action} with args ${JSON.stringify({\n        ...args,\n        refreshToken: (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.getRedactedMessage)(args?.refreshToken ?? \"\"),\n    })}`, verbose);\n    if (action === \"auth:signIn\") {\n        let result;\n        // Do not require auth when refreshing tokens or validating a code since they\n        // are steps in the auth flow.\n        const fetchActionAuthOptions = args.refreshToken !== undefined || args.params?.code !== undefined\n            ? {}\n            : { token };\n        try {\n            result = await (0,convex_nextjs__WEBPACK_IMPORTED_MODULE_1__.fetchAction)(action, args, {\n                ...(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.getConvexNextjsOptions)(options),\n                ...fetchActionAuthOptions,\n            });\n        }\n        catch (error) {\n            console.error(`Hit error while running \\`auth:signIn\\`:`);\n            console.error(error);\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.logVerbose)(`Clearing auth cookies`, verbose);\n            const response = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.jsonResponse)(null);\n            await (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.setAuthCookies)(response, null, cookieConfig);\n            return response;\n        }\n        if (result.redirect !== undefined) {\n            const { redirect } = result;\n            const response = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.jsonResponse)({ redirect });\n            (await (0,_cookies_js__WEBPACK_IMPORTED_MODULE_3__.getResponseCookies)(response, cookieConfig)).verifier =\n                result.verifier;\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.logVerbose)(`Redirecting to ${redirect}`, verbose);\n            return response;\n        }\n        else if (result.tokens !== undefined) {\n            // The server doesn't share the refresh token with the client\n            // for added security - the client has to use the server\n            // to refresh the access token via cookies.\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.logVerbose)(result.tokens === null\n                ? `No tokens returned, clearing auth cookies`\n                : `Setting auth cookies with returned tokens`, verbose);\n            const response = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.jsonResponse)({\n                tokens: result.tokens !== null\n                    ? { token: result.tokens.token, refreshToken: \"dummy\" }\n                    : null,\n            });\n            await (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.setAuthCookies)(response, result.tokens, cookieConfig);\n            return response;\n        }\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.jsonResponse)(result);\n    }\n    else {\n        try {\n            await (0,convex_nextjs__WEBPACK_IMPORTED_MODULE_1__.fetchAction)(action, args, {\n                ...(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.getConvexNextjsOptions)(options),\n                token,\n            });\n        }\n        catch (error) {\n            console.error(`Hit error while running \\`auth:signOut\\`:`);\n            console.error(error);\n        }\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.logVerbose)(`Clearing auth cookies`, verbose);\n        const response = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.jsonResponse)(null);\n        await (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.setAuthCookies)(response, null, cookieConfig);\n        return response;\n    }\n}\nfunction shouldProxyAuthAction(request, apiRoute) {\n    // Handle both with and without trailing slash since this could be configured either way.\n    // https://nextjs.org/docs/app/api-reference/next-config-js/trailingSlash\n    const requestUrl = new URL(request.url);\n    if (apiRoute.endsWith(\"/\")) {\n        return (requestUrl.pathname === apiRoute ||\n            requestUrl.pathname === apiRoute.slice(0, -1));\n    }\n    else {\n        return (requestUrl.pathname === apiRoute || requestUrl.pathname === apiRoute + \"/\");\n    }\n}\n//# sourceMappingURL=proxy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/proxy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/request.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@convex-dev/auth/dist/nextjs/server/request.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handleAuthenticationInRequest: () => (/* binding */ handleAuthenticationInRequest)\n/* harmony export */ });\n/* harmony import */ var convex_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! convex/nextjs */ \"(rsc)/./node_modules/convex/dist/esm/nextjs/index.js\");\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jwt-decode */ \"(rsc)/./node_modules/jwt-decode/build/esm/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _cookies_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./cookies.js */ \"(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/cookies.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/utils.js\");\n\n\n\n\n\nasync function handleAuthenticationInRequest(request, options) {\n    const verbose = options.verbose ?? false;\n    const cookieConfig = options.cookieConfig ?? { maxAge: null };\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.logVerbose)(`Begin handleAuthenticationInRequest`, verbose);\n    const requestUrl = new URL(request.url);\n    // Validate CORS\n    await validateCors(request);\n    // Refresh tokens if necessary\n    const refreshTokens = await getRefreshedTokens(options);\n    // Handle code exchange for OAuth and magic links via server-side redirect\n    const code = requestUrl.searchParams.get(\"code\");\n    if (code &&\n        request.method === \"GET\" &&\n        request.headers.get(\"accept\")?.includes(\"text/html\")) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.logVerbose)(`Handling code exchange for OAuth or magic link`, verbose);\n        const verifier = (await (0,_cookies_js__WEBPACK_IMPORTED_MODULE_4__.getRequestCookies)()).verifier ?? undefined;\n        const redirectUrl = new URL(requestUrl);\n        redirectUrl.searchParams.delete(\"code\");\n        try {\n            const result = await (0,convex_nextjs__WEBPACK_IMPORTED_MODULE_0__.fetchAction)(\"auth:signIn\", { params: { code }, verifier }, (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.getConvexNextjsOptions)(options));\n            if (result.tokens === undefined) {\n                throw new Error(\"Invalid `signIn` action result for code exchange\");\n            }\n            const response = next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(redirectUrl);\n            await (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.setAuthCookies)(response, result.tokens, cookieConfig);\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.logVerbose)(`Successfully validated code, redirecting to ${redirectUrl.toString()} with auth cookies`, verbose);\n            return { kind: \"redirect\", response };\n        }\n        catch (error) {\n            console.error(error);\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.logVerbose)(`Error validating code, redirecting to ${redirectUrl.toString()} and clearing auth cookies`, verbose);\n            const response = next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(redirectUrl);\n            await (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.setAuthCookies)(response, null, cookieConfig);\n            return { kind: \"redirect\", response };\n        }\n    }\n    return { kind: \"refreshTokens\", refreshTokens };\n}\n// If this is a cross-origin request with `Origin` header set\n// do not allow the app to read auth cookies.\nasync function validateCors(request) {\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isCorsRequest)(request)) {\n        const cookies = await (0,_cookies_js__WEBPACK_IMPORTED_MODULE_4__.getRequestCookiesInMiddleware)(request);\n        cookies.token = null;\n        cookies.refreshToken = null;\n        cookies.verifier = null;\n    }\n}\nconst REQUIRED_TOKEN_LIFETIME_MS = 60_000; // 1 minute\nconst MINIMUM_REQUIRED_TOKEN_LIFETIME_MS = 10_000; // 10 seconds\nasync function getRefreshedTokens(options) {\n    const verbose = options.verbose ?? false;\n    const cookies = await (0,_cookies_js__WEBPACK_IMPORTED_MODULE_4__.getRequestCookies)();\n    const { token, refreshToken } = cookies;\n    if (refreshToken === null && token === null) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.logVerbose)(`No tokens to refresh, returning undefined`, verbose);\n        return undefined;\n    }\n    if (refreshToken === null || token === null) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.logVerbose)(`Refresh token null? ${refreshToken === null}, token null? ${token === null}, returning null`, verbose);\n        return null;\n    }\n    const decodedToken = decodeToken(token);\n    if (decodedToken === null) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.logVerbose)(`Failed to decode token, returning null`, verbose);\n        return null;\n    }\n    const totalTokenLifetimeMs = decodedToken.exp * 1000 - decodedToken.iat * 1000;\n    // Check that the token is valid for the next 1 minute\n    // or at least 10% of its valid duration or 10 seconds\n    const minimumExpiration = Date.now() +\n        Math.min(REQUIRED_TOKEN_LIFETIME_MS, Math.max(MINIMUM_REQUIRED_TOKEN_LIFETIME_MS, totalTokenLifetimeMs / 10));\n    if (decodedToken.exp * 1000 > minimumExpiration) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.logVerbose)(`Token expires far enough in the future, no need to refresh, returning undefined`, verbose);\n        return undefined;\n    }\n    try {\n        const result = await (0,convex_nextjs__WEBPACK_IMPORTED_MODULE_0__.fetchAction)(\"auth:signIn\", {\n            refreshToken,\n        }, (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.getConvexNextjsOptions)(options));\n        if (result.tokens === undefined) {\n            throw new Error(\"Invalid `signIn` action result for token refresh\");\n        }\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.logVerbose)(`Successfully refreshed tokens: is null? ${result.tokens === null}`, verbose);\n        return result.tokens;\n    }\n    catch (error) {\n        console.error(error);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.logVerbose)(`Failed to refresh tokens, returning null`, verbose);\n        return null;\n    }\n}\nfunction decodeToken(token) {\n    try {\n        return (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__.jwtDecode)(token);\n    }\n    catch (e) {\n        return null;\n    }\n}\n//# sourceMappingURL=request.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/request.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/routeMatcher.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@convex-dev/auth/dist/nextjs/server/routeMatcher.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRouteMatcher: () => (/* binding */ createRouteMatcher)\n/* harmony export */ });\n/* harmony import */ var path_to_regexp__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! path-to-regexp */ \"(rsc)/./node_modules/path-to-regexp/dist.es2015/index.js\");\n// Adapted from Clerk\n//\n// MIT License\n//\n// Copyright (c) 2022 Clerk, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n// SOFTWARE.\n\n/**\n * Returns a function that accepts a `Request` object and returns whether the request matches the list of\n * predefined routes that can be passed in as the first argument.\n *\n * You can use glob patterns to match multiple routes or a function to match against the request object.\n * Path patterns and limited regular expressions are supported.\n * For more information, see: https://www.npmjs.com/package/path-to-regexp/v/6.3.0\n */\nconst createRouteMatcher = (routes) => {\n    if (typeof routes === \"function\") {\n        return (req) => routes(req);\n    }\n    const routePatterns = [routes || \"\"].flat().filter(Boolean);\n    const matchers = precomputePathRegex(routePatterns);\n    return (req) => matchers.some((matcher) => matcher.test(req.nextUrl.pathname));\n};\nconst precomputePathRegex = (patterns) => {\n    return patterns.map((pattern) => pattern instanceof RegExp ? pattern : pathStringToRegExp(pattern));\n};\nfunction pathStringToRegExp(path) {\n    try {\n        return (0,path_to_regexp__WEBPACK_IMPORTED_MODULE_0__.pathToRegexp)(path);\n    }\n    catch (e) {\n        throw new Error(`Invalid path: ${path}.\\nConsult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp\\n${e.message}`);\n    }\n}\n//# sourceMappingURL=routeMatcher.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/routeMatcher.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/utils.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@convex-dev/auth/dist/nextjs/server/utils.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getConvexNextjsOptions: () => (/* binding */ getConvexNextjsOptions),\n/* harmony export */   getRedactedMessage: () => (/* binding */ getRedactedMessage),\n/* harmony export */   isCorsRequest: () => (/* binding */ isCorsRequest),\n/* harmony export */   jsonResponse: () => (/* binding */ jsonResponse),\n/* harmony export */   logVerbose: () => (/* binding */ logVerbose),\n/* harmony export */   setAuthCookies: () => (/* binding */ setAuthCookies),\n/* harmony export */   setAuthCookiesInMiddleware: () => (/* binding */ setAuthCookiesInMiddleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _cookies_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./cookies.js */ \"(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/cookies.js\");\n\n\nfunction jsonResponse(body) {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(JSON.stringify(body), {\n        headers: { \"Content-Type\": \"application/json\" },\n    });\n}\nasync function setAuthCookies(response, tokens, cookieConfig) {\n    const responseCookies = await (0,_cookies_js__WEBPACK_IMPORTED_MODULE_1__.getResponseCookies)(response, cookieConfig);\n    if (tokens === null) {\n        responseCookies.token = null;\n        responseCookies.refreshToken = null;\n    }\n    else {\n        responseCookies.token = tokens.token;\n        responseCookies.refreshToken = tokens.refreshToken;\n    }\n    responseCookies.verifier = null;\n}\n/**\n * Forward on any auth cookies in the request to the next handler.\n *\n * @param request\n * @param tokens\n */\nasync function setAuthCookiesInMiddleware(request, tokens) {\n    const requestCookies = await (0,_cookies_js__WEBPACK_IMPORTED_MODULE_1__.getRequestCookiesInMiddleware)(request);\n    if (tokens === null) {\n        requestCookies.token = null;\n        requestCookies.refreshToken = null;\n    }\n    else {\n        requestCookies.token = tokens.token;\n        requestCookies.refreshToken = tokens.refreshToken;\n    }\n}\nfunction isCorsRequest(request) {\n    const origin = request.headers.get(\"Origin\");\n    const originURL = origin ? new URL(origin) : null;\n    return (originURL !== null &&\n        (originURL.host !== request.headers.get(\"Host\") ||\n            originURL.protocol !== new URL(request.url).protocol));\n}\nfunction logVerbose(message, verbose) {\n    if (verbose) {\n        console.debug(`[verbose] ${new Date().toISOString()} [ConvexAuthNextjs] ${message}`);\n    }\n}\nfunction getRedactedMessage(value) {\n    const length = 5;\n    if (value.length < length * 2) {\n        return \"<redacted>\";\n    }\n    return (value.substring(0, length) +\n        \"<redacted>\" +\n        value.substring(value.length - length));\n}\n/**\n * @param options - a subset of ConvexAuthNextjsMiddlewareOptions\n * @returns NextjsOptions\n */\nfunction getConvexNextjsOptions(options) {\n    // If `convexUrl` is provided (even if it's undefined), pass it as the `url` option.\n    // `convex/nextjs` has its own logic for falling back to `process.env.NEXT_PUBLIC_CONVEX_URL`\n    // and protecting against accidentally passing in `undefined` (e.g. { convexUrl: process.env.VAR_I_DIDNT_SET })\n    if (Object.hasOwn(options, \"convexUrl\")) {\n        return {\n            url: options.convexUrl,\n        };\n    }\n    return {};\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@convex-dev/auth/dist/nextjs/server/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@convex-dev/auth/dist/server/utils.js":
/*!************************************************************!*\
  !*** ./node_modules/@convex-dev/auth/dist/server/utils.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isLocalHost: () => (/* binding */ isLocalHost),\n/* harmony export */   requireEnv: () => (/* binding */ requireEnv)\n/* harmony export */ });\nfunction requireEnv(name) {\n    const value = process.env[name];\n    if (value === undefined) {\n        throw new Error(`Missing environment variable \\`${name}\\``);\n    }\n    return value;\n}\nfunction isLocalHost(host) {\n    return /(localhost|127\\.0\\.0\\.1):\\d+/.test(host ?? \"\");\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGNvbnZleC1kZXYvYXV0aC9kaXN0L3NlcnZlci91dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBLDBEQUEwRCxLQUFLO0FBQy9EO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvcm9iZXJ0LmhhbnNlbi9QbGF5QmVnL1BsYXlCZWcvcGxheWJlZy1jb252ZXgvbm9kZV9tb2R1bGVzL0Bjb252ZXgtZGV2L2F1dGgvZGlzdC9zZXJ2ZXIvdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIHJlcXVpcmVFbnYobmFtZSkge1xuICAgIGNvbnN0IHZhbHVlID0gcHJvY2Vzcy5lbnZbbmFtZV07XG4gICAgaWYgKHZhbHVlID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBNaXNzaW5nIGVudmlyb25tZW50IHZhcmlhYmxlIFxcYCR7bmFtZX1cXGBgKTtcbiAgICB9XG4gICAgcmV0dXJuIHZhbHVlO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGlzTG9jYWxIb3N0KGhvc3QpIHtcbiAgICByZXR1cm4gLyhsb2NhbGhvc3R8MTI3XFwuMFxcLjBcXC4xKTpcXGQrLy50ZXN0KGhvc3QgPz8gXCJcIik7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD11dGlscy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@convex-dev/auth/dist/server/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@convex-dev/auth/dist/nextjs/client.js":
/*!*************************************************************!*\
  !*** ./node_modules/@convex-dev/auth/dist/nextjs/client.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConvexAuthNextjsClientProvider: () => (/* binding */ ConvexAuthNextjsClientProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_client_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../react/client.js */ \"(ssr)/./node_modules/@convex-dev/auth/dist/react/client.js\");\n/* harmony import */ var _server_invalidateCache_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./server/invalidateCache.js */ \"(ssr)/./node_modules/@convex-dev/auth/dist/nextjs/server/invalidateCache.js\");\n/* __next_internal_client_entry_do_not_use__ ConvexAuthNextjsClientProvider auto */ \n\n\n\nfunction ConvexAuthNextjsClientProvider({ apiRoute, serverState, storage, storageNamespace, verbose, children }) {\n    const call = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConvexAuthNextjsClientProvider.useCallback[call]\": async (action, args)=>{\n            const params = {\n                action,\n                args\n            };\n            const response = await fetch(apiRoute ?? \"/api/auth\", {\n                body: JSON.stringify(params),\n                method: \"POST\"\n            });\n            return await response.json();\n        }\n    }[\"ConvexAuthNextjsClientProvider.useCallback[call]\"], [\n        apiRoute\n    ]);\n    const authClient = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ConvexAuthNextjsClientProvider.useMemo[authClient]\": ()=>({\n                authenticatedCall: call,\n                unauthenticatedCall: call,\n                verbose\n            })\n    }[\"ConvexAuthNextjsClientProvider.useMemo[authClient]\"], [\n        call,\n        verbose\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_react_client_js__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        client: authClient,\n        serverState: serverState,\n        onChange: _server_invalidateCache_js__WEBPACK_IMPORTED_MODULE_3__.invalidateCache,\n        storage: // Handle SSR, Client, etc.\n        // Pretend we always have storage, the component checks\n        // it in first useEffect.\n         true ? undefined : 0,\n        storageNamespace: storageNamespace ?? requireEnv(\"https://lovely-cormorant-474.convex.cloud\", \"NEXT_PUBLIC_CONVEX_URL\"),\n        replaceURL: // Not used, since the redirect is handled by the Next.js server.\n        (url)=>{\n            window.history.replaceState({}, \"\", url);\n        },\n        children: children\n    });\n}\nfunction requireEnv(value, name) {\n    if (value === undefined) {\n        throw new Error(`Missing environment variable \\`${name}\\``);\n    }\n    return value;\n} //# sourceMappingURL=client.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNvbnZleC1kZXYvYXV0aC9kaXN0L25leHRqcy9jbGllbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRXdEO0FBQ047QUFFWTtBQUV4RCxTQUFVLDhCQUE4QixDQUFDLEVBQzdDLFFBQVEsRUFDUixXQUFXLEVBQ1gsT0FBTyxFQUNQLGdCQUFnQixFQUNoQixPQUFPLEVBQ1AsUUFBUSxFQVFUO0lBQ0MsTUFBTSxJQUFJLEdBQW9DLGtEQUFXOzREQUN2RCxLQUFLLEVBQUUsTUFBTSxFQUFFLElBQUksRUFBRSxFQUFFO1lBQ3JCLE1BQU0sTUFBTSxHQUFHO2dCQUFFLE1BQU07Z0JBQUUsSUFBSTtZQUFBLENBQUUsQ0FBQztZQUNoQyxNQUFNLFFBQVEsR0FBRyxNQUFNLEtBQUssQ0FBQyxRQUFRLElBQUksV0FBVyxFQUFFO2dCQUNwRCxJQUFJLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUM7Z0JBQzVCLE1BQU0sRUFBRSxNQUFNO2FBQ2YsQ0FBQyxDQUFDO1lBQ0gsT0FBTyxNQUFNLFFBQVEsQ0FBQyxJQUFJLEVBQUUsQ0FBQztRQUMvQixDQUFDOzJEQUNEO1FBQUMsUUFBUTtLQUFDLENBQ1gsQ0FBQztJQUNGLE1BQU0sVUFBVSxHQUFHLDhDQUFPOzhEQUN4QixHQUFHLEVBQUUsQ0FBQztnQkFDSixpQkFBaUIsRUFBRSxJQUFJO2dCQUN2QixtQkFBbUIsRUFBRSxJQUFJO2dCQUN6QixPQUFPO2NBQ1I7NkRBQ0Q7UUFBQyxJQUFJO1FBQUUsT0FBTztLQUFDLENBQ2hCLENBQUM7SUFDRixPQUFPLHVEQUNKLDBEQUFZO1FBQ1gsTUFBTSxFQUFFLFVBQVU7UUFDbEIsV0FBVyxFQUFFLFdBQVc7UUFDeEIsUUFBUSxFQUFFLHVFQUFlO1FBQ3pCLE9BQU8sRUFDTCwyQkFBMkI7UUFDM0IsdURBQXVEO1FBQ3ZELHlCQUF5QjtRQUN4QixLQUE2QixHQUMxQixTQUFTLEdBQ1QsQ0FFcUIsQ0FBRTtRQUU3QixnQkFBZ0IsRUFDZCxnQkFBZ0IsSUFDaEIsVUFBVSxDQUFDLDJDQUFrQyxFQUFFLHdCQUF3QixDQUFDO1FBRTFFLFVBQVUsRUFDUixpRUFBaUU7UUFDakUsQ0FBQyxHQUFHLEVBQUUsRUFBRTtZQUNOLE1BQU0sQ0FBQyxPQUFPLENBQUMsWUFBWSxDQUFDLEVBQUUsRUFBRSxFQUFFLEVBQUUsR0FBRyxDQUFDLENBQUM7UUFDM0MsQ0FBQztRQUFBLFVBR0YsUUFBUTtJQUFBLEVBQ0ksQ0FDaEI7QUFDSCxDQUFDO0FBRUQsU0FBUyxVQUFVLENBQUMsS0FBeUIsRUFBRSxJQUFZO0lBQ3pELElBQUksS0FBSyxLQUFLLFNBQVMsRUFBRSxDQUFDO1FBQ3hCLE1BQU0sSUFBSSxLQUFLLENBQUMsa0NBQWtDLElBQUksSUFBSSxDQUFDLENBQUM7SUFDOUQsQ0FBQztJQUNELE9BQU8sS0FBSyxDQUFDO0FBQ2YsQ0FBQyIsInNvdXJjZXMiOlsiL1VzZXJzL3JvYmVydC5oYW5zZW4vUGxheUJlZy9zcmMvbmV4dGpzL2NsaWVudC50c3giXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@convex-dev/auth/dist/nextjs/client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@convex-dev/auth/dist/nextjs/index.js":
/*!************************************************************!*\
  !*** ./node_modules/@convex-dev/auth/dist/nextjs/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConvexAuthNextjsProvider: () => (/* binding */ ConvexAuthNextjsProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(ssr)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _react_client_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../react/client.js */ \"(ssr)/./node_modules/@convex-dev/auth/dist/react/client.js\");\n/* __next_internal_client_entry_do_not_use__ ConvexAuthNextjsProvider auto */ \n\n\n/**\n * Replace your `ConvexProvider` in a Client Component with this component\n * to enable authentication in your Next.js app.\n *\n * ```tsx\n * \"use client\";\n *\n * import { ConvexAuthNextjsProvider } from \"@convex-dev/auth/nextjs\";\n * import { ConvexReactClient } from \"convex/react\";\n * import { ReactNode } from \"react\";\n *\n * const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);\n *\n * export default function ConvexClientProvider({\n *   children,\n * }: {\n *   children: ReactNode;\n * }) {\n *   return (\n *     <ConvexAuthNextjsProvider client={convex}>\n *       {children}\n *     </ConvexAuthNextjsProvider>\n *   );\n * }\n * ```\n */ function ConvexAuthNextjsProvider(props) {\n    const { client, children } = props;\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(convex_react__WEBPACK_IMPORTED_MODULE_1__.ConvexProviderWithAuth, {\n        client: client,\n        useAuth: _react_client_js__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        children: children\n    });\n} //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNvbnZleC1kZXYvYXV0aC9kaXN0L25leHRqcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUV5RTtBQUU1QjtBQUU3Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztHQXlCRyxDQUNHLFNBQVUsd0JBQXdCLENBQUMsS0FVeEM7SUFDQyxNQUFNLEVBQUUsTUFBTSxFQUFFLFFBQVEsRUFBRSxHQUFHLEtBQUssQ0FBQztJQUNuQyxPQUFPLHVEQUNKLGdFQUFzQjtRQUFDLE1BQU0sRUFBRSxNQUFNO1FBQUUsT0FBTyxFQUFFLHFEQUFPO1FBQUEsVUFDckQsUUFBUTtJQUFBLEVBQ2MsQ0FDMUI7QUFDSCxDQUFDIiwic291cmNlcyI6WyIvVXNlcnMvcm9iZXJ0LmhhbnNlbi9QbGF5QmVnL3NyYy9uZXh0anMvaW5kZXgudHN4Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@convex-dev/auth/dist/nextjs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@convex-dev/auth/dist/nextjs/server/invalidateCache.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@convex-dev/auth/dist/nextjs/server/invalidateCache.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   invalidateCache: () => (/* binding */ invalidateCache)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-action-client-wrapper */ \"(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js\");\n/* __next_internal_action_entry_do_not_use__ {\"00f913389bf07a0ce5471de0fecab54577e23d5f75\":\"invalidateCache\"} */ \nvar invalidateCache = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"00f913389bf07a0ce5471de0fecab54577e23d5f75\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"invalidateCache\");\n //# sourceMappingURL=invalidateCache.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNvbnZleC1kZXYvYXV0aC9kaXN0L25leHRqcy9zZXJ2ZXIvaW52YWxpZGF0ZUNhY2hlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztJQUlzQixlQUFlIiwic291cmNlcyI6WyIvVXNlcnMvcm9iZXJ0LmhhbnNlbi9zcmMvbmV4dGpzL3NlcnZlci9pbnZhbGlkYXRlQ2FjaGUudHMiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@convex-dev/auth/dist/nextjs/server/invalidateCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@convex-dev/auth/dist/react/client.js":
/*!************************************************************!*\
  !*** ./node_modules/@convex-dev/auth/dist/react/client.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   ConvexAuthActionsContext: () => (/* binding */ ConvexAuthActionsContext),\n/* harmony export */   ConvexAuthTokenContext: () => (/* binding */ ConvexAuthTokenContext),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var is_network_error__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! is-network-error */ \"(ssr)/./node_modules/is-network-error/index.js\");\n\n\n\n// Retry after this much time, based on the retry number.\nconst RETRY_BACKOFF = [500, 2000]; // In ms\nconst RETRY_JITTER = 100; // In ms\nconst ConvexAuthActionsContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst ConvexAuthInternalContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ConvexAuthInternalContext);\n}\nconst ConvexAuthTokenContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst VERIFIER_STORAGE_KEY = \"__convexAuthOAuthVerifier\";\nconst JWT_STORAGE_KEY = \"__convexAuthJWT\";\nconst REFRESH_TOKEN_STORAGE_KEY = \"__convexAuthRefreshToken\";\nconst SERVER_STATE_FETCH_TIME_STORAGE_KEY = \"__convexAuthServerStateFetchTime\";\nfunction AuthProvider({ client, serverState, onChange, storage, storageNamespace, replaceURL, children, }) {\n    const token = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(serverState?._state.token ?? null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(token.current === null);\n    const [tokenState, setTokenState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(token.current);\n    const verbose = client.verbose ?? false;\n    const logVerbose = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message) => {\n        if (verbose) {\n            console.debug(`${new Date().toISOString()} ${message}`);\n            client.logger?.logVerbose(message);\n        }\n    }, [verbose]);\n    const { storageSet, storageGet, storageRemove, storageKey } = useNamespacedStorage(storage, storageNamespace);\n    const [isRefreshingToken, setIsRefreshingToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const setToken = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (args) => {\n        const wasAuthenticated = token.current !== null;\n        let newToken;\n        if (args.tokens === null) {\n            token.current = null;\n            if (args.shouldStore) {\n                await storageRemove(JWT_STORAGE_KEY);\n                await storageRemove(REFRESH_TOKEN_STORAGE_KEY);\n            }\n            newToken = null;\n        }\n        else {\n            const { token: value } = args.tokens;\n            token.current = value;\n            if (args.shouldStore) {\n                const { refreshToken } = args.tokens;\n                await storageSet(JWT_STORAGE_KEY, value);\n                await storageSet(REFRESH_TOKEN_STORAGE_KEY, refreshToken);\n            }\n            newToken = value;\n        }\n        if (wasAuthenticated !== (newToken !== null)) {\n            await onChange?.();\n        }\n        setTokenState(newToken);\n        setIsLoading(false);\n    }, [storageSet, storageRemove]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n        const listener = async (e) => {\n            if (isRefreshingToken) {\n                // There are 3 different ways to trigger this pop up so just try all of\n                // them.\n                e.preventDefault();\n                // This confirmation message doesn't actually appear in most modern\n                // browsers but we tried.\n                const confirmationMessage = \"Are you sure you want to leave? Your changes may not be saved.\";\n                e.returnValue = true;\n                return confirmationMessage;\n            }\n        };\n        browserAddEventListener(\"beforeunload\", listener);\n        return () => {\n            browserRemoveEventListener(\"beforeunload\", listener);\n        };\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n        // We're listening for:\n        // 1. sibling tabs in case of localStorage\n        // 2. other frames in case of sessionStorage\n        const listener = (event) => {\n            void (async () => {\n                // TODO: Test this if statement works in iframes correctly\n                if (event.storageArea !== storage) {\n                    return;\n                }\n                // Another tab/frame set the access token, use it\n                if (event.key === storageKey(JWT_STORAGE_KEY)) {\n                    const value = event.newValue;\n                    logVerbose(`synced access token, is null: ${value === null}`);\n                    // We don't write into storage since the event came from there and\n                    // we'd trigger a loop, plus we get each key as a separate event so\n                    // we don't have the refresh key here.\n                    await setToken({\n                        shouldStore: false,\n                        tokens: value === null ? null : { token: value },\n                    });\n                }\n            })();\n        };\n        browserAddEventListener(\"storage\", listener);\n        return () => browserRemoveEventListener(\"storage\", listener);\n    }, [setToken]);\n    const verifyCode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (args) => {\n        let lastError;\n        // Retry the call if it fails due to a network error.\n        // This is especially common in mobile apps where an app is backgrounded\n        // while making a call and hits a network error, but will succeed with a\n        // retry once the app is brought to the foreground.\n        let retry = 0;\n        while (retry < RETRY_BACKOFF.length) {\n            try {\n                return await client.unauthenticatedCall(\"auth:signIn\", \"code\" in args\n                    ? { params: { code: args.code }, verifier: args.verifier }\n                    : args);\n            }\n            catch (e) {\n                lastError = e;\n                if (!(0,is_network_error__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(e)) {\n                    break;\n                }\n                const wait = RETRY_BACKOFF[retry] + RETRY_JITTER * Math.random();\n                retry++;\n                logVerbose(`verifyCode failed with network error, retry ${retry} of ${RETRY_BACKOFF.length} in ${wait}ms`);\n                await new Promise((resolve) => setTimeout(resolve, wait));\n            }\n        }\n        throw lastError;\n    }, [client]);\n    const verifyCodeAndSetToken = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (args) => {\n        const { tokens } = await verifyCode(args);\n        logVerbose(`retrieved tokens, is null: ${tokens === null}`);\n        await setToken({ shouldStore: true, tokens: tokens ?? null });\n        return tokens !== null;\n    }, [client, setToken]);\n    const signIn = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (provider, args) => {\n        const params = args instanceof FormData\n            ? Array.from(args.entries()).reduce((acc, [key, value]) => {\n                acc[key] = value;\n                return acc;\n            }, {})\n            : args ?? {};\n        const verifier = (await storageGet(VERIFIER_STORAGE_KEY)) ?? undefined;\n        await storageRemove(VERIFIER_STORAGE_KEY);\n        const result = await client.authenticatedCall(\"auth:signIn\", { provider, params, verifier });\n        if (result.redirect !== undefined) {\n            const url = new URL(result.redirect);\n            await storageSet(VERIFIER_STORAGE_KEY, result.verifier);\n            // Do not redirect in React Native\n            if (window.location !== undefined) {\n                window.location.href = url.toString();\n            }\n            return { signingIn: false, redirect: url };\n        }\n        else if (result.tokens !== undefined) {\n            const { tokens } = result;\n            logVerbose(`signed in and got tokens, is null: ${tokens === null}`);\n            await setToken({ shouldStore: true, tokens });\n            return { signingIn: result.tokens !== null };\n        }\n        return { signingIn: false };\n    }, [client, setToken, storageGet]);\n    const signOut = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async () => {\n        try {\n            await client.authenticatedCall(\"auth:signOut\");\n        }\n        catch (error) {\n            // Ignore any errors, they are usually caused by being\n            // already signed out, which is ok.\n        }\n        logVerbose(`signed out, erasing tokens`);\n        await setToken({ shouldStore: true, tokens: null });\n    }, [setToken, client]);\n    const fetchAccessToken = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ({ forceRefreshToken }) => {\n        if (forceRefreshToken) {\n            const tokenBeforeLockAquisition = token.current;\n            return await browserMutex(REFRESH_TOKEN_STORAGE_KEY, async () => {\n                const tokenAfterLockAquisition = token.current;\n                // Another tab or frame just refreshed the token, we can use it\n                // and skip another refresh.\n                if (tokenAfterLockAquisition !== tokenBeforeLockAquisition) {\n                    logVerbose(`returning synced token, is null: ${tokenAfterLockAquisition === null}`);\n                    return tokenAfterLockAquisition;\n                }\n                const refreshToken = (await storageGet(REFRESH_TOKEN_STORAGE_KEY)) ?? null;\n                if (refreshToken !== null) {\n                    setIsRefreshingToken(true);\n                    await verifyCodeAndSetToken({ refreshToken }).finally(() => {\n                        setIsRefreshingToken(false);\n                    });\n                    logVerbose(`returning retrieved token, is null: ${tokenAfterLockAquisition === null}`);\n                    return token.current;\n                }\n                else {\n                    setIsRefreshingToken(false);\n                    logVerbose(`returning null, there is no refresh token`);\n                    return null;\n                }\n            });\n        }\n        return token.current;\n    }, [verifyCodeAndSetToken, signOut, storageGet]);\n    const signingInWithCodeFromURL = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n        // Has to happen in useEffect to avoid SSR.\n        if (storage === undefined) {\n            throw new Error(\"`localStorage` is not available in this environment, \" +\n                \"set the `storage` prop on `ConvexAuthProvider`!\");\n        }\n        const readStateFromStorage = async () => {\n            const token = (await storageGet(JWT_STORAGE_KEY)) ?? null;\n            logVerbose(`retrieved token from storage, is null: ${token === null}`);\n            await setToken({\n                shouldStore: false,\n                tokens: token === null ? null : { token },\n            });\n        };\n        if (serverState !== undefined) {\n            // First check that this isn't a subsequent render\n            // with stale serverState.\n            const timeFetched = storageGet(SERVER_STATE_FETCH_TIME_STORAGE_KEY);\n            const setTokensFromServerState = (timeFetched) => {\n                if (!timeFetched || serverState._timeFetched > +timeFetched) {\n                    const { token, refreshToken } = serverState._state;\n                    const tokens = token === null || refreshToken === null\n                        ? null\n                        : { token, refreshToken };\n                    void storageSet(SERVER_STATE_FETCH_TIME_STORAGE_KEY, serverState._timeFetched.toString());\n                    void setToken({ tokens, shouldStore: true });\n                }\n                else {\n                    void readStateFromStorage();\n                }\n            };\n            // We want to avoid async if possible.\n            if (timeFetched instanceof Promise) {\n                void timeFetched.then(setTokensFromServerState);\n            }\n            else {\n                setTokensFromServerState(timeFetched);\n            }\n            return;\n        }\n        const code = typeof window?.location !== \"undefined\"\n            ? new URLSearchParams(window.location.search).get(\"code\")\n            : null;\n        // code from URL is only consumed initially,\n        // ref avoids racing in Strict mode\n        if (signingInWithCodeFromURL.current || code) {\n            if (code && !signingInWithCodeFromURL.current) {\n                signingInWithCodeFromURL.current = true;\n                const url = new URL(window.location.href);\n                url.searchParams.delete(\"code\");\n                void (async () => {\n                    await replaceURL(url.pathname + url.search + url.hash);\n                    await signIn(undefined, { code });\n                    signingInWithCodeFromURL.current = false;\n                })();\n            }\n        }\n        else {\n            void readStateFromStorage();\n        }\n    }, \n    // Explicitly chosen dependencies.\n    // This effect should mostly only run once\n    // on mount.\n    [client, storageGet]);\n    const actions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => ({ signIn, signOut }), [signIn, signOut]);\n    const isAuthenticated = tokenState !== null;\n    const authState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => ({\n        isLoading,\n        isAuthenticated,\n        fetchAccessToken,\n    }), [fetchAccessToken, isLoading, isAuthenticated]);\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ConvexAuthInternalContext.Provider, { value: authState, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ConvexAuthActionsContext.Provider, { value: actions, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ConvexAuthTokenContext.Provider, { value: tokenState, children: children }) }) }));\n}\nfunction useNamespacedStorage(peristentStorage, namespace) {\n    const inMemoryStorage = useInMemoryStorage();\n    const storage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => peristentStorage ?? inMemoryStorage(), [peristentStorage]);\n    const escapedNamespace = namespace.replace(/[^a-zA-Z0-9]/g, \"\");\n    const storageKey = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((key) => `${key}_${escapedNamespace}`, [namespace]);\n    const storageSet = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((key, value) => storage.setItem(storageKey(key), value), [storage, storageKey]);\n    const storageGet = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((key) => storage.getItem(storageKey(key)), [storage, storageKey]);\n    const storageRemove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((key) => storage.removeItem(storageKey(key)), [storage, storageKey]);\n    return { storageSet, storageGet, storageRemove, storageKey };\n}\nfunction useInMemoryStorage() {\n    const [inMemoryStorage, setInMemoryStorage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    return () => ({\n        getItem: (key) => inMemoryStorage[key],\n        setItem: (key, value) => {\n            setInMemoryStorage((prev) => ({ ...prev, [key]: value }));\n        },\n        removeItem: (key) => {\n            setInMemoryStorage((prev) => {\n                const { [key]: _, ...rest } = prev;\n                return rest;\n            });\n        },\n    });\n}\n// In the browser, executes the callback as the only tab / frame at a time.\nasync function browserMutex(key, callback) {\n    const lockManager = window?.navigator?.locks;\n    return lockManager !== undefined\n        ? await lockManager.request(key, callback)\n        : await manualMutex(key, callback);\n}\nfunction getMutexValue(key) {\n    if (globalThis.__convexAuthMutexes === undefined) {\n        globalThis.__convexAuthMutexes = {};\n    }\n    let mutex = globalThis.__convexAuthMutexes[key];\n    if (mutex === undefined) {\n        globalThis.__convexAuthMutexes[key] = {\n            currentlyRunning: null,\n            waiting: [],\n        };\n    }\n    mutex = globalThis.__convexAuthMutexes[key];\n    return mutex;\n}\nfunction setMutexValue(key, value) {\n    globalThis.__convexAuthMutexes[key] = value;\n}\nasync function enqueueCallbackForMutex(key, callback) {\n    const mutex = getMutexValue(key);\n    if (mutex.currentlyRunning === null) {\n        setMutexValue(key, {\n            currentlyRunning: callback().finally(() => {\n                const nextCb = getMutexValue(key).waiting.shift();\n                getMutexValue(key).currentlyRunning = null;\n                setMutexValue(key, {\n                    ...getMutexValue(key),\n                    currentlyRunning: nextCb === undefined ? null : enqueueCallbackForMutex(key, nextCb),\n                });\n            }),\n            waiting: [],\n        });\n    }\n    else {\n        setMutexValue(key, {\n            ...mutex,\n            waiting: [...mutex.waiting, callback],\n        });\n    }\n}\nasync function manualMutex(key, callback) {\n    const outerPromise = new Promise((resolve, reject) => {\n        const wrappedCallback = () => {\n            return callback()\n                .then((v) => resolve(v))\n                .catch((e) => reject(e));\n        };\n        void enqueueCallbackForMutex(key, wrappedCallback);\n    });\n    return outerPromise;\n}\nfunction browserAddEventListener(type, listener, options) {\n    window.addEventListener?.(type, listener, options);\n}\nfunction browserRemoveEventListener(type, listener, options) {\n    window.removeEventListener?.(type, listener, options);\n}\n//# sourceMappingURL=client.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@convex-dev/auth/dist/react/client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@convex-dev/auth/dist/react/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/@convex-dev/auth/dist/react/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConvexAuthProvider: () => (/* binding */ ConvexAuthProvider),\n/* harmony export */   useAuthActions: () => (/* binding */ useAuthActions),\n/* harmony export */   useAuthToken: () => (/* binding */ useAuthToken)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var convex_browser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/browser */ \"(ssr)/./node_modules/convex/dist/esm/browser/index-node.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! convex/react */ \"(ssr)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _client_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./client.js */ \"(ssr)/./node_modules/@convex-dev/auth/dist/react/client.js\");\n/**\n * React bindings for Convex Auth.\n *\n * @module\n */ /* __next_internal_client_entry_do_not_use__ useAuthActions,ConvexAuthProvider,useAuthToken auto */ \n\n\n\n\n/**\n * Use this hook to access the `signIn` and `signOut` methods:\n *\n * ```ts\n * import { useAuthActions } from \"@convex-dev/auth/react\";\n *\n * function SomeComponent() {\n *   const { signIn, signOut } = useAuthActions();\n *   // ...\n * }\n * ```\n */ function useAuthActions() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(_client_js__WEBPACK_IMPORTED_MODULE_4__.ConvexAuthActionsContext);\n}\n/**\n * Replace your `ConvexProvider` with this component to enable authentication.\n *\n * ```tsx\n * import { ConvexAuthProvider } from \"@convex-dev/auth/react\";\n * import { ConvexReactClient } from \"convex/react\";\n * import { ReactNode } from \"react\";\n *\n * const convex = new ConvexReactClient(/* ... *\\/);\n *\n * function RootComponent({ children }: { children: ReactNode }) {\n *   return <ConvexAuthProvider client={convex}>{children}</ConvexAuthProvider>;\n * }\n * ```\n */ function ConvexAuthProvider(props) {\n    const { client, storage, storageNamespace, replaceURL, children } = props;\n    const authClient = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)({\n        \"ConvexAuthProvider.useMemo[authClient]\": ()=>({\n                authenticatedCall (action, args) {\n                    return client.action(action, args);\n                },\n                unauthenticatedCall (action, args) {\n                    return new convex_browser__WEBPACK_IMPORTED_MODULE_1__.ConvexHttpClient(client.address, {\n                        logger: client.logger\n                    }).action(action, args);\n                },\n                verbose: client.options?.verbose,\n                logger: client.logger\n            })\n    }[\"ConvexAuthProvider.useMemo[authClient]\"], [\n        client\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_client_js__WEBPACK_IMPORTED_MODULE_4__.AuthProvider, {\n        client: authClient,\n        storage: storage ?? // Handle SSR, RN, Web, etc.\n        // Pretend we always have storage, the component checks\n        // it in first useEffect.\n        ( true ? undefined : 0),\n        storageNamespace: storageNamespace ?? client.address,\n        replaceURL: replaceURL ?? ((url)=>{\n            window.history.replaceState({}, \"\", url);\n        }),\n        children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(convex_react__WEBPACK_IMPORTED_MODULE_2__.ConvexProviderWithAuth, {\n            client: client,\n            useAuth: _client_js__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n            children: children\n        })\n    });\n}\n/**\n * Use this hook to access the JWT token on the client, for authenticating\n * your Convex HTTP actions.\n *\n * You should not pass this token to other servers (think of it\n * as an \"ID token\").\n *\n * ```ts\n * import { useAuthToken } from \"@convex-dev/auth/react\";\n *\n * function SomeComponent() {\n *   const token = useAuthToken();\n *   const onClick = async () => {\n *     await fetch(`${CONVEX_SITE_URL}/someEndpoint`, {\n *       headers: {\n *         Authorization: `Bearer ${token}`,\n *       },\n *     });\n *   };\n *   // ...\n * }\n * ```\n */ function useAuthToken() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(_client_js__WEBPACK_IMPORTED_MODULE_4__.ConvexAuthTokenContext);\n} //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNvbnZleC1kZXYvYXV0aC9kaXN0L3JlYWN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQUE7Ozs7R0FJRztBQUkrQztBQUN1QjtBQUVsQjtBQU1sQztBQUdyQjs7Ozs7Ozs7Ozs7R0FXRyxDQUNHLFNBQVUsY0FBYztJQUM1QixPQUFPLGlEQUFVLENBQUMsZ0VBQXdCLENBQUMsQ0FBQztBQUM5QyxDQUFDO0FBRUQ7Ozs7Ozs7Ozs7Ozs7O0dBY0csQ0FDRyxTQUFVLGtCQUFrQixDQUFDLEtBMENsQztJQUNDLE1BQU0sRUFBRSxNQUFNLEVBQUUsT0FBTyxFQUFFLGdCQUFnQixFQUFFLFVBQVUsRUFBRSxRQUFRLEVBQUUsR0FBRyxLQUFLLENBQUM7SUFDMUUsTUFBTSxVQUFVLEdBQUcsOENBQU87a0RBQ3hCLEdBQUcsRUFBRSxDQUNIO2dCQUNFLGlCQUFpQixFQUFDLE1BQU0sRUFBRSxJQUFJO29CQUM1QixPQUFPLE1BQU0sQ0FBQyxNQUFNLENBQUMsTUFBTSxFQUFFLElBQUksQ0FBQyxDQUFDO2dCQUNyQyxDQUFDO2dCQUNELG1CQUFtQixFQUFDLE1BQU0sRUFBRSxJQUFJO29CQUM5QixPQUFPLElBQUksNERBQWdCLENBQUUsTUFBYyxDQUFDLE9BQU8sRUFBRTt3QkFDbkQsTUFBTSxFQUFFLE1BQU0sQ0FBQyxNQUFNO3FCQUN0QixDQUFDLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxJQUFJLENBQUMsQ0FBQztnQkFDMUIsQ0FBQztnQkFDRCxPQUFPLEVBQUcsTUFBYyxDQUFDLE9BQU8sRUFBRSxPQUFPO2dCQUN6QyxNQUFNLEVBQUUsTUFBTSxDQUFDLE1BQU07Y0FDdEI7aURBQ0g7UUFBQyxNQUFNO0tBQUMsQ0FDVCxDQUFDO0lBQ0YsT0FBTyx1REFDSixvREFBWTtRQUNYLE1BQU0sRUFBRSxVQUFVO1FBQ2xCLE9BQU8sRUFDTCxPQUFPLElBQ1AsNEJBQTRCO1FBQzVCLHVEQUF1RDtRQUN2RCx5QkFBeUI7U0FDeEIsS0FBNkIsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxDQUFRLENBQVksQ0FBRTtRQUVyRSxnQkFBZ0IsRUFBRSxnQkFBZ0IsSUFBSyxNQUFjLENBQUMsT0FBTztRQUM3RCxVQUFVLEVBQ1IsVUFBVSxLQUNULENBQUMsR0FBRyxFQUFFLEVBQUU7WUFDUCxNQUFNLENBQUMsT0FBTyxDQUFDLFlBQVksQ0FBQyxFQUFFLEVBQUUsRUFBRSxFQUFFLEdBQUcsQ0FBQyxDQUFDO1NBQzNDLENBQUMsQ0FBQztRQUFBLFVBR0osdURBQUMsZ0VBQXNCO1lBQUMsTUFBTSxFQUFFLE1BQU07WUFBRSxPQUFPLEVBQUUsK0NBQU87WUFBQSxVQUNyRCxRQUFRO1FBQUEsRUFDYztJQUFBLEVBQ1osQ0FDaEI7QUFDSCxDQUFDO0FBMEdEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0dBc0JHLENBQ0csU0FBVSxZQUFZO0lBQzFCLE9BQU8saURBQVUsQ0FBQyw4REFBc0IsQ0FBQyxDQUFDO0FBQzVDLENBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yb2JlcnQuaGFuc2VuL1BsYXlCZWcvc3JjL3JlYWN0L2luZGV4LnRzeCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@convex-dev/auth/dist/react/index.js\n");

/***/ })

};
;