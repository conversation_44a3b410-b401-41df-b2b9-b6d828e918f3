(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[751],{35:(e,t)=>{"use strict";var r={H:null,A:null};function n(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var r=2;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=Array.isArray,o=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),c=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),h=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator,g=Object.prototype.hasOwnProperty,v=Object.assign;function b(e,t,r,n,i,a){return{$$typeof:o,type:e,key:t,ref:void 0!==(r=a.ref)?r:null,props:a}}function m(e){return"object"==typeof e&&null!==e&&e.$$typeof===o}var y=/\/+/g;function w(e,t){var r,n;return"object"==typeof e&&null!==e&&null!=e.key?(r=""+e.key,n={"=":"=0",":":"=2"},"$"+r.replace(/[=:]/g,function(e){return n[e]})):t.toString(36)}function _(){}function E(e,t,r){if(null==e)return e;var s=[],l=0;return!function e(t,r,s,l,u){var c,d,h,g=typeof t;("undefined"===g||"boolean"===g)&&(t=null);var v=!1;if(null===t)v=!0;else switch(g){case"bigint":case"string":case"number":v=!0;break;case"object":switch(t.$$typeof){case o:case a:v=!0;break;case f:return e((v=t._init)(t._payload),r,s,l,u)}}if(v)return u=u(t),v=""===l?"."+w(t,0):l,i(u)?(s="",null!=v&&(s=v.replace(y,"$&/")+"/"),e(u,r,s,"",function(e){return e})):null!=u&&(m(u)&&(c=u,d=s+(null==u.key||t&&t.key===u.key?"":(""+u.key).replace(y,"$&/")+"/")+v,u=b(c.type,d,void 0,void 0,void 0,c.props)),r.push(u)),1;v=0;var E=""===l?".":l+":";if(i(t))for(var x=0;x<t.length;x++)g=E+w(l=t[x],x),v+=e(l,r,s,g,u);else if("function"==typeof(x=null===(h=t)||"object"!=typeof h?null:"function"==typeof(h=p&&h[p]||h["@@iterator"])?h:null))for(t=x.call(t),x=0;!(l=t.next()).done;)g=E+w(l=l.value,x++),v+=e(l,r,s,g,u);else if("object"===g){if("function"==typeof t.then)return e(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(_,_):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(t),r,s,l,u);throw Error(n(31,"[object Object]"===(r=String(t))?"object with keys {"+Object.keys(t).join(", ")+"}":r))}return v}(e,s,"","",function(e){return t.call(r,e,l++)}),s}function x(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){(0===e._status||-1===e._status)&&(e._status=1,e._result=t)},function(t){(0===e._status||-1===e._status)&&(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}function O(){return new WeakMap}function R(){return{s:0,v:void 0,o:null,p:null}}t.Children={map:E,forEach:function(e,t,r){E(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return E(e,function(){t++}),t},toArray:function(e){return E(e,function(e){return e})||[]},only:function(e){if(!m(e))throw Error(n(143));return e}},t.Fragment=s,t.Profiler=u,t.StrictMode=l,t.Suspense=d,t.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,t.cache=function(e){return function(){var t=r.A;if(!t)return e.apply(null,arguments);var n=t.getCacheForType(O);void 0===(t=n.get(e))&&(t=R(),n.set(e,t)),n=0;for(var i=arguments.length;n<i;n++){var o=arguments[n];if("function"==typeof o||"object"==typeof o&&null!==o){var a=t.o;null===a&&(t.o=a=new WeakMap),void 0===(t=a.get(o))&&(t=R(),a.set(o,t))}else null===(a=t.p)&&(t.p=a=new Map),void 0===(t=a.get(o))&&(t=R(),a.set(o,t))}if(1===t.s)return t.v;if(2===t.s)throw t.v;try{var s=e.apply(null,arguments);return(n=t).s=1,n.v=s}catch(e){throw(s=t).s=2,s.v=e,e}}},t.captureOwnerStack=function(){return null},t.cloneElement=function(e,t,r){if(null==e)throw Error(n(267,e));var i=v({},e.props),o=e.key,a=void 0;if(null!=t)for(s in void 0!==t.ref&&(a=void 0),void 0!==t.key&&(o=""+t.key),t)g.call(t,s)&&"key"!==s&&"__self"!==s&&"__source"!==s&&("ref"!==s||void 0!==t.ref)&&(i[s]=t[s]);var s=arguments.length-2;if(1===s)i.children=r;else if(1<s){for(var l=Array(s),u=0;u<s;u++)l[u]=arguments[u+2];i.children=l}return b(e.type,o,void 0,void 0,a,i)},t.createElement=function(e,t,r){var n,i={},o=null;if(null!=t)for(n in void 0!==t.key&&(o=""+t.key),t)g.call(t,n)&&"key"!==n&&"__self"!==n&&"__source"!==n&&(i[n]=t[n]);var a=arguments.length-2;if(1===a)i.children=r;else if(1<a){for(var s=Array(a),l=0;l<a;l++)s[l]=arguments[l+2];i.children=s}if(e&&e.defaultProps)for(n in a=e.defaultProps)void 0===i[n]&&(i[n]=a[n]);return b(e,o,void 0,void 0,null,i)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=m,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:x}},t.memo=function(e,t){return{$$typeof:h,type:e,compare:void 0===t?null:t}},t.use=function(e){return r.H.use(e)},t.useCallback=function(e,t){return r.H.useCallback(e,t)},t.useDebugValue=function(){},t.useId=function(){return r.H.useId()},t.useMemo=function(e,t){return r.H.useMemo(e,t)},t.version="19.1.0-canary-029e8bd6-20250306"},171:(e,t,r)=>{"use strict";r(216)},201:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return a},withRequest:function(){return o}});let n=new(r(521)).AsyncLocalStorage;function i(e,t){let r=t.header(e,"next-test-proxy-port");if(!r)return;let n=t.url(e);return{url:n,proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function o(e,t,r){let o=i(e,t);return o?n.run(o,r):r()}function a(e,t){let r=n.getStore();return r||(e&&t?i(e,t):void 0)}},216:(e,t,r)=>{"use strict";var n=r(815),i=Symbol.for("react.transitional.element");if(Symbol.for("react.fragment"),!n.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.')},280:(e,t,r)=>{var n;(()=>{var i={226:function(i,o){!function(a,s){"use strict";var l="function",u="undefined",c="object",d="string",h="major",f="model",p="name",g="type",v="vendor",b="version",m="architecture",y="console",w="mobile",_="tablet",E="smarttv",x="wearable",O="embedded",R="Amazon",S="Apple",C="ASUS",T="BlackBerry",P="Browser",k="Chrome",N="Firefox",A="Google",I="Huawei",j="Microsoft",M="Motorola",$="Opera",L="Samsung",D="Sharp",q="Sony",U="Xiaomi",B="Zebra",V="Facebook",H="Chromium OS",F="Mac OS",X=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},G=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},z=function(e,t){return typeof e===d&&-1!==W(t).indexOf(W(e))},W=function(e){return e.toLowerCase()},J=function(e,t){if(typeof e===d)return e=e.replace(/^\s\s*/,""),typeof t===u?e:e.substring(0,350)},K=function(e,t){for(var r,n,i,o,a,u,d=0;d<t.length&&!a;){var h=t[d],f=t[d+1];for(r=n=0;r<h.length&&!a&&h[r];)if(a=h[r++].exec(e))for(i=0;i<f.length;i++)u=a[++n],typeof(o=f[i])===c&&o.length>0?2===o.length?typeof o[1]==l?this[o[0]]=o[1].call(this,u):this[o[0]]=o[1]:3===o.length?typeof o[1]!==l||o[1].exec&&o[1].test?this[o[0]]=u?u.replace(o[1],o[2]):void 0:this[o[0]]=u?o[1].call(this,u,o[2]):void 0:4===o.length&&(this[o[0]]=u?o[3].call(this,u.replace(o[1],o[2])):s):this[o]=u||s;d+=2}},Q=function(e,t){for(var r in t)if(typeof t[r]===c&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(z(t[r][n],e))return"?"===r?s:r}else if(z(t[r],e))return"?"===r?s:r;return e},Y={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Z={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[b,[p,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[b,[p,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[p,b],[/opios[\/ ]+([\w\.]+)/i],[b,[p,$+" Mini"]],[/\bopr\/([\w\.]+)/i],[b,[p,$]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[p,b],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[b,[p,"UC"+P]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[b,[p,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[b,[p,"WeChat"]],[/konqueror\/([\w\.]+)/i],[b,[p,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[b,[p,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[b,[p,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[p,/(.+)/,"$1 Secure "+P],b],[/\bfocus\/([\w\.]+)/i],[b,[p,N+" Focus"]],[/\bopt\/([\w\.]+)/i],[b,[p,$+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[b,[p,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[b,[p,"Dolphin"]],[/coast\/([\w\.]+)/i],[b,[p,$+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[b,[p,"MIUI "+P]],[/fxios\/([-\w\.]+)/i],[b,[p,N]],[/\bqihu|(qi?ho?o?|360)browser/i],[[p,"360 "+P]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[p,/(.+)/,"$1 "+P],b],[/(comodo_dragon)\/([\w\.]+)/i],[[p,/_/g," "],b],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[p,b],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[p],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[p,V],b],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[p,b],[/\bgsa\/([\w\.]+) .*safari\//i],[b,[p,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[b,[p,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[b,[p,k+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[p,k+" WebView"],b],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[b,[p,"Android "+P]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[p,b],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[b,[p,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[b,p],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[p,[b,Q,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[p,b],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[p,"Netscape"],b],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[b,[p,N+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[p,b],[/(cobalt)\/([\w\.]+)/i],[p,[b,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[m,"amd64"]],[/(ia32(?=;))/i],[[m,W]],[/((?:i[346]|x)86)[;\)]/i],[[m,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[m,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[m,"armhf"]],[/windows (ce|mobile); ppc;/i],[[m,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[m,/ower/,"",W]],[/(sun4\w)[;\)]/i],[[m,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[m,W]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[f,[v,L],[g,_]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[f,[v,L],[g,w]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[f,[v,S],[g,w]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[f,[v,S],[g,_]],[/(macintosh);/i],[f,[v,S]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[f,[v,D],[g,w]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[f,[v,I],[g,_]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[f,[v,I],[g,w]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[f,/_/g," "],[v,U],[g,w]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[f,/_/g," "],[v,U],[g,_]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[f,[v,"OPPO"],[g,w]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[f,[v,"Vivo"],[g,w]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[f,[v,"Realme"],[g,w]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[f,[v,M],[g,w]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[f,[v,M],[g,_]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[f,[v,"LG"],[g,_]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[f,[v,"LG"],[g,w]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[f,[v,"Lenovo"],[g,_]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[f,/_/g," "],[v,"Nokia"],[g,w]],[/(pixel c)\b/i],[f,[v,A],[g,_]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[f,[v,A],[g,w]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[f,[v,q],[g,w]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[f,"Xperia Tablet"],[v,q],[g,_]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[f,[v,"OnePlus"],[g,w]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[f,[v,R],[g,_]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[f,/(.+)/g,"Fire Phone $1"],[v,R],[g,w]],[/(playbook);[-\w\),; ]+(rim)/i],[f,v,[g,_]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[f,[v,T],[g,w]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[f,[v,C],[g,_]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[f,[v,C],[g,w]],[/(nexus 9)/i],[f,[v,"HTC"],[g,_]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[v,[f,/_/g," "],[g,w]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[f,[v,"Acer"],[g,_]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[f,[v,"Meizu"],[g,w]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[v,f,[g,w]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[v,f,[g,_]],[/(surface duo)/i],[f,[v,j],[g,_]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[f,[v,"Fairphone"],[g,w]],[/(u304aa)/i],[f,[v,"AT&T"],[g,w]],[/\bsie-(\w*)/i],[f,[v,"Siemens"],[g,w]],[/\b(rct\w+) b/i],[f,[v,"RCA"],[g,_]],[/\b(venue[\d ]{2,7}) b/i],[f,[v,"Dell"],[g,_]],[/\b(q(?:mv|ta)\w+) b/i],[f,[v,"Verizon"],[g,_]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[f,[v,"Barnes & Noble"],[g,_]],[/\b(tm\d{3}\w+) b/i],[f,[v,"NuVision"],[g,_]],[/\b(k88) b/i],[f,[v,"ZTE"],[g,_]],[/\b(nx\d{3}j) b/i],[f,[v,"ZTE"],[g,w]],[/\b(gen\d{3}) b.+49h/i],[f,[v,"Swiss"],[g,w]],[/\b(zur\d{3}) b/i],[f,[v,"Swiss"],[g,_]],[/\b((zeki)?tb.*\b) b/i],[f,[v,"Zeki"],[g,_]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[v,"Dragon Touch"],f,[g,_]],[/\b(ns-?\w{0,9}) b/i],[f,[v,"Insignia"],[g,_]],[/\b((nxa|next)-?\w{0,9}) b/i],[f,[v,"NextBook"],[g,_]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[v,"Voice"],f,[g,w]],[/\b(lvtel\-)?(v1[12]) b/i],[[v,"LvTel"],f,[g,w]],[/\b(ph-1) /i],[f,[v,"Essential"],[g,w]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[f,[v,"Envizen"],[g,_]],[/\b(trio[-\w\. ]+) b/i],[f,[v,"MachSpeed"],[g,_]],[/\btu_(1491) b/i],[f,[v,"Rotor"],[g,_]],[/(shield[\w ]+) b/i],[f,[v,"Nvidia"],[g,_]],[/(sprint) (\w+)/i],[v,f,[g,w]],[/(kin\.[onetw]{3})/i],[[f,/\./g," "],[v,j],[g,w]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[f,[v,B],[g,_]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[f,[v,B],[g,w]],[/smart-tv.+(samsung)/i],[v,[g,E]],[/hbbtv.+maple;(\d+)/i],[[f,/^/,"SmartTV"],[v,L],[g,E]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[v,"LG"],[g,E]],[/(apple) ?tv/i],[v,[f,S+" TV"],[g,E]],[/crkey/i],[[f,k+"cast"],[v,A],[g,E]],[/droid.+aft(\w)( bui|\))/i],[f,[v,R],[g,E]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[f,[v,D],[g,E]],[/(bravia[\w ]+)( bui|\))/i],[f,[v,q],[g,E]],[/(mitv-\w{5}) bui/i],[f,[v,U],[g,E]],[/Hbbtv.*(technisat) (.*);/i],[v,f,[g,E]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[v,J],[f,J],[g,E]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,E]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[v,f,[g,y]],[/droid.+; (shield) bui/i],[f,[v,"Nvidia"],[g,y]],[/(playstation [345portablevi]+)/i],[f,[v,q],[g,y]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[f,[v,j],[g,y]],[/((pebble))app/i],[v,f,[g,x]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[f,[v,S],[g,x]],[/droid.+; (glass) \d/i],[f,[v,A],[g,x]],[/droid.+; (wt63?0{2,3})\)/i],[f,[v,B],[g,x]],[/(quest( 2| pro)?)/i],[f,[v,V],[g,x]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[v,[g,O]],[/(aeobc)\b/i],[f,[v,R],[g,O]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[f,[g,w]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[f,[g,_]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,_]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,w]],[/(android[-\w\. ]{0,9});.+buil/i],[f,[v,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[b,[p,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[b,[p,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[p,b],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[b,p]],os:[[/microsoft (windows) (vista|xp)/i],[p,b],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[p,[b,Q,Y]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[p,"Windows"],[b,Q,Y]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[b,/_/g,"."],[p,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[p,F],[b,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[b,p],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[p,b],[/\(bb(10);/i],[b,[p,T]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[b,[p,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[b,[p,N+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[b,[p,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[b,[p,"watchOS"]],[/crkey\/([\d\.]+)/i],[b,[p,k+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[p,H],b],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[p,b],[/(sunos) ?([\w\.\d]*)/i],[[p,"Solaris"],b],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[p,b]]},ee=function(e,t){if(typeof e===c&&(t=e,e=s),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof a!==u&&a.navigator?a.navigator:s,n=e||(r&&r.userAgent?r.userAgent:""),i=r&&r.userAgentData?r.userAgentData:s,o=t?X(Z,t):Z,y=r&&r.userAgent==n;return this.getBrowser=function(){var e,t={};return t[p]=s,t[b]=s,K.call(t,n,o.browser),t[h]=typeof(e=t[b])===d?e.replace(/[^\d\.]/g,"").split(".")[0]:s,y&&r&&r.brave&&typeof r.brave.isBrave==l&&(t[p]="Brave"),t},this.getCPU=function(){var e={};return e[m]=s,K.call(e,n,o.cpu),e},this.getDevice=function(){var e={};return e[v]=s,e[f]=s,e[g]=s,K.call(e,n,o.device),y&&!e[g]&&i&&i.mobile&&(e[g]=w),y&&"Macintosh"==e[f]&&r&&typeof r.standalone!==u&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[f]="iPad",e[g]=_),e},this.getEngine=function(){var e={};return e[p]=s,e[b]=s,K.call(e,n,o.engine),e},this.getOS=function(){var e={};return e[p]=s,e[b]=s,K.call(e,n,o.os),y&&!e[p]&&i&&"Unknown"!=i.platform&&(e[p]=i.platform.replace(/chrome os/i,H).replace(/macos/i,F)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===d&&e.length>350?J(e,350):e,this},this.setUA(n),this};ee.VERSION="1.0.35",ee.BROWSER=G([p,b,h]),ee.CPU=G([m]),ee.DEVICE=G([f,v,g,y,w,E,_,x,O]),ee.ENGINE=ee.OS=G([p,b]),typeof o!==u?(i.exports&&(o=i.exports=ee),o.UAParser=ee):r.amdO?void 0!==(n=(function(){return ee}).call(t,r,t,e))&&(e.exports=n):typeof a!==u&&(a.UAParser=ee);var et=typeof a!==u&&(a.jQuery||a.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},o={};function a(e){var t=o[e];if(void 0!==t)return t.exports;var r=o[e]={exports:{}},n=!0;try{i[e].call(r.exports,r,r.exports,a),n=!1}finally{n&&delete o[e]}return r.exports}a.ab="//",e.exports=a(226)})()},356:e=>{"use strict";e.exports=require("node:buffer")},521:e=>{"use strict";e.exports=require("node:async_hooks")},552:(e,t,r)=>{"use strict";var n=r(356).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return s},interceptFetch:function(){return l},reader:function(){return o}});let i=r(201),o={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function a(e,t){let{url:r,method:i,headers:o,body:a,cache:s,credentials:l,integrity:u,mode:c,redirect:d,referrer:h,referrerPolicy:f}=t;return{testData:e,api:"fetch",request:{url:r,method:i,headers:[...Array.from(o),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:a?n.from(await t.arrayBuffer()).toString("base64"):null,cache:s,credentials:l,integrity:u,mode:c,redirect:d,referrer:h,referrerPolicy:f}}}async function s(e,t){let r=(0,i.getTestReqInfo)(t,o);if(!r)return e(t);let{testData:s,proxyPort:l}=r,u=await a(s,t),c=await e(`http://localhost:${l}`,{method:"POST",body:JSON.stringify(u),next:{internal:!0}});if(!c.ok)throw Object.defineProperty(Error(`Proxy request failed: ${c.status}`),"__NEXT_ERROR_CODE",{value:"E146",enumerable:!1,configurable:!0});let d=await c.json(),{api:h}=d;switch(h){case"continue":return e(t);case"abort":case"unhandled":throw Object.defineProperty(Error(`Proxy request aborted [${t.method} ${t.url}]`),"__NEXT_ERROR_CODE",{value:"E145",enumerable:!1,configurable:!0})}return function(e){let{status:t,headers:r,body:i}=e.response;return new Response(i?n.from(i,"base64"):null,{status:t,headers:new Headers(r)})}(d)}function l(e){return r.g.fetch=function(t,r){var n;return(null==r?void 0:null==(n=r.next)?void 0:n.internal)?e(t,r):s(e,new Request(t,r))},()=>{r.g.fetch=e}}},666:(e,t,r)=>{"use strict";let n,i;r.r(t),r.d(t,{default:()=>iO});var o,a,s={};async function l(){return"_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation}r.r(s),r.d(s,{config:()=>iw,default:()=>iy});let u=null;async function c(){if("phase-production-build"===process.env.NEXT_PHASE)return;u||(u=l());let e=await u;if(null==e?void 0:e.register)try{await e.register()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}async function d(...e){let t=await l();try{var r;await (null==t?void 0:null==(r=t.onRequestError)?void 0:r.call(t,...e))}catch(e){console.error("Error in instrumentation.onRequestError:",e)}}let h=null;function f(){return h||(h=c()),h}function p(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Object.defineProperty(Error(p(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},construct(){throw Object.defineProperty(Error(p(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},apply(r,n,i){if("function"==typeof i[0])return i[0](t);throw Object.defineProperty(Error(p(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),f();class g extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class v extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class b extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}let m={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};function y(e){var t,r,n,i,o,a=[],s=0;function l(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,o=!1;l();)if(","===(r=e.charAt(s))){for(n=s,s+=1,l(),i=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(o=!0,s=i,a.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!o||s>=e.length)&&a.push(e.substring(t,e.length))}return a}function w(e){let t={},r=[];if(e)for(let[n,i]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(...y(i)),t[n]=1===r.length?r[0]:r):t[n]=i;return t}function _(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}({...m,GROUP:{builtinReact:[m.reactServerComponents,m.actionBrowser],serverOnly:[m.reactServerComponents,m.actionBrowser,m.instrument,m.middleware],neutralTarget:[m.apiNode,m.apiEdge],clientOnly:[m.serverSideRendering,m.appPagesBrowser],bundled:[m.reactServerComponents,m.actionBrowser,m.serverSideRendering,m.appPagesBrowser,m.shared,m.instrument,m.middleware],appPages:[m.reactServerComponents,m.serverSideRendering,m.appPagesBrowser,m.actionBrowser]}});let E=Symbol("response"),x=Symbol("passThrough"),O=Symbol("waitUntil");class R{constructor(e,t){this[x]=!1,this[O]=t?{kind:"external",function:t}:{kind:"internal",promises:[]}}respondWith(e){this[E]||(this[E]=Promise.resolve(e))}passThroughOnException(){this[x]=!0}waitUntil(e){if("external"===this[O].kind)return(0,this[O].function)(e);this[O].promises.push(e)}}class S extends R{constructor(e){var t;super(e.request,null==(t=e.context)?void 0:t.waitUntil),this.sourcePage=e.page}get request(){throw Object.defineProperty(new g({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new g({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}function C(e){return e.replace(/\/$/,"")||"/"}function T(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function P(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=T(e);return""+t+r+n+i}function k(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=T(e);return""+r+t+n+i}function N(e,t){if("string"!=typeof e)return!1;let{pathname:r}=T(e);return r===t||r.startsWith(t+"/")}let A=new WeakMap;function I(e,t){let r;if(!t)return{pathname:e};let n=A.get(t);n||(n=t.map(e=>e.toLowerCase()),A.set(t,n));let i=e.split("/",2);if(!i[1])return{pathname:e};let o=i[1].toLowerCase(),a=n.indexOf(o);return a<0?{pathname:e}:(r=t[a],{pathname:e=e.slice(r.length+1)||"/",detectedLocale:r})}let j=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function M(e,t){return new URL(String(e).replace(j,"localhost"),t&&String(t).replace(j,"localhost"))}let $=Symbol("NextURLInternal");class L{constructor(e,t,r){let n,i;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,i=r||{}):i=r||t||{},this[$]={url:M(e,n??i.base),options:i,basePath:""},this.analyze()}analyze(){var e,t,r,n,i;let o=function(e,t){var r,n;let{basePath:i,i18n:o,trailingSlash:a}=null!=(r=t.nextConfig)?r:{},s={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):a};i&&N(s.pathname,i)&&(s.pathname=function(e,t){if(!N(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(s.pathname,i),s.basePath=i);let l=s.pathname;if(s.pathname.startsWith("/_next/data/")&&s.pathname.endsWith(".json")){let e=s.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");s.buildId=e[0],l="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(s.pathname=l)}if(o){let e=t.i18nProvider?t.i18nProvider.analyze(s.pathname):I(s.pathname,o.locales);s.locale=e.detectedLocale,s.pathname=null!=(n=e.pathname)?n:s.pathname,!e.detectedLocale&&s.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(l):I(l,o.locales)).detectedLocale&&(s.locale=e.detectedLocale)}return s}(this[$].url.pathname,{nextConfig:this[$].options.nextConfig,parseData:!0,i18nProvider:this[$].options.i18nProvider}),a=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[$].url,this[$].options.headers);this[$].domainLocale=this[$].options.i18nProvider?this[$].options.i18nProvider.detectDomainLocale(a):function(e,t,r){if(e)for(let o of(r&&(r=r.toLowerCase()),e)){var n,i;if(t===(null==(n=o.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===o.defaultLocale.toLowerCase()||(null==(i=o.locales)?void 0:i.some(e=>e.toLowerCase()===r)))return o}}(null==(t=this[$].options.nextConfig)?void 0:null==(e=t.i18n)?void 0:e.domains,a);let s=(null==(r=this[$].domainLocale)?void 0:r.defaultLocale)||(null==(i=this[$].options.nextConfig)?void 0:null==(n=i.i18n)?void 0:n.defaultLocale);this[$].url.pathname=o.pathname,this[$].defaultLocale=s,this[$].basePath=o.basePath??"",this[$].buildId=o.buildId,this[$].locale=o.locale??s,this[$].trailingSlash=o.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,n){if(!t||t===r)return e;let i=e.toLowerCase();return!n&&(N(i,"/api")||N(i,"/"+t.toLowerCase()))?e:P(e,"/"+t)}((e={basePath:this[$].basePath,buildId:this[$].buildId,defaultLocale:this[$].options.forceLocale?void 0:this[$].defaultLocale,locale:this[$].locale,pathname:this[$].url.pathname,trailingSlash:this[$].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=C(t)),e.buildId&&(t=k(P(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=P(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:k(t,"/"):C(t)}formatSearch(){return this[$].url.search}get buildId(){return this[$].buildId}set buildId(e){this[$].buildId=e}get locale(){return this[$].locale??""}set locale(e){var t,r;if(!this[$].locale||!(null==(r=this[$].options.nextConfig)?void 0:null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[$].locale=e}get defaultLocale(){return this[$].defaultLocale}get domainLocale(){return this[$].domainLocale}get searchParams(){return this[$].url.searchParams}get host(){return this[$].url.host}set host(e){this[$].url.host=e}get hostname(){return this[$].url.hostname}set hostname(e){this[$].url.hostname=e}get port(){return this[$].url.port}set port(e){this[$].url.port=e}get protocol(){return this[$].url.protocol}set protocol(e){this[$].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[$].url=M(e),this.analyze()}get origin(){return this[$].url.origin}get pathname(){return this[$].url.pathname}set pathname(e){this[$].url.pathname=e}get hash(){return this[$].url.hash}set hash(e){this[$].url.hash=e}get search(){return this[$].url.search}set search(e){this[$].url.search=e}get password(){return this[$].url.password}set password(e){this[$].url.password=e}get username(){return this[$].url.username}set username(e){this[$].url.username=e}get basePath(){return this[$].basePath}set basePath(e){this[$].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new L(String(this),this[$].options)}}var D=r(724);let q=Symbol("internal request");class U extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);_(r),e instanceof Request?super(e,t):super(r,t);let n=new L(r,{headers:w(this.headers),nextConfig:t.nextConfig});this[q]={cookies:new D.RequestCookies(this.headers),nextUrl:n,url:n.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[q].cookies}get nextUrl(){return this[q].nextUrl}get page(){throw new v}get ua(){throw new b}get url(){return this[q].url}}class B{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}let V=Symbol("internal response"),H=new Set([301,302,303,307,308]);function F(e,t){var r;if(null==e?void 0:null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[n,i]of e.request.headers)t.set("x-middleware-request-"+n,i),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class X extends Response{constructor(e,t={}){super(e,t);let r=this.headers,n=new Proxy(new D.ResponseCookies(r),{get(e,n,i){switch(n){case"delete":case"set":return(...i)=>{let o=Reflect.apply(e[n],e,i),a=new Headers(r);return o instanceof D.ResponseCookies&&r.set("x-middleware-set-cookie",o.getAll().map(e=>(0,D.stringifyCookie)(e)).join(",")),F(t,a),o};default:return B.get(e,n,i)}}});this[V]={cookies:n,url:t.url?new L(t.url,{headers:w(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[V].cookies}static json(e,t){let r=Response.json(e,t);return new X(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!H.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let n="object"==typeof t?t:{},i=new Headers(null==n?void 0:n.headers);return i.set("Location",_(e)),new X(null,{...n,headers:i,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",_(e)),F(t,r),new X(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),F(e,t),new X(null,{...e,headers:t})}}function G(e,t){let r="string"==typeof t?new URL(t):t,n=new URL(e,t),i=n.origin===r.origin;return{url:i?n.toString().slice(r.origin.length):n.toString(),isRelative:i}}let z="Next-Router-Prefetch",W=["RSC","Next-Router-State-Tree",z,"Next-HMR-Refresh","Next-Router-Segment-Prefetch"],J="_rsc";class K extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new K}}class Q extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,n){if("symbol"==typeof r)return B.get(t,r,n);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==o)return B.get(t,o,n)},set(t,r,n,i){if("symbol"==typeof r)return B.set(t,r,n,i);let o=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===o);return B.set(t,a??r,n,i)},has(t,r){if("symbol"==typeof r)return B.has(t,r);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0!==i&&B.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return B.deleteProperty(t,r);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0===i||B.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return K.callable;default:return B.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new Q(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}let Y=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class Z{disable(){throw Y}getStore(){}run(){throw Y}exit(){throw Y}enterWith(){throw Y}static bind(e){return e}}let ee="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function et(){return ee?new ee:new Z}let er=et(),en=et();function ei(e){let t=en.getStore();if(t){if("request"===t.type)return t;if("prerender"===t.type||"prerender-ppr"===t.type||"prerender-legacy"===t.type)throw Object.defineProperty(Error(`\`${e}\` cannot be called inside a prerender. This is a bug in Next.js.`),"__NEXT_ERROR_CODE",{value:"E401",enumerable:!1,configurable:!0});if("cache"===t.type)throw Object.defineProperty(Error(`\`${e}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E37",enumerable:!1,configurable:!0});if("unstable-cache"===t.type)throw Object.defineProperty(Error(`\`${e}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E69",enumerable:!1,configurable:!0})}throw Object.defineProperty(Error(`\`${e}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`),"__NEXT_ERROR_CODE",{value:"E251",enumerable:!1,configurable:!0})}class eo extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new eo}}class ea{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return eo.callable;default:return B.get(e,t,r)}}})}}let es=Symbol.for("next.mutated.cookies");class el{static wrap(e,t){let r=new D.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let n=[],i=new Set,o=()=>{let e=er.getStore();if(e&&(e.pathWasRevalidated=!0),n=r.getAll().filter(e=>i.has(e.name)),t){let e=[];for(let t of n){let r=new D.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},a=new Proxy(r,{get(e,t,r){switch(t){case es:return n;case"delete":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),a}finally{o()}};case"set":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),a}finally{o()}};default:return B.get(e,t,r)}}});return a}}function eu(e){return"action"===e.phase}function ec(e){if(!eu(ei(e)))throw new eo}var ed=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(ed||{}),eh=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(eh||{}),ef=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(ef||{}),ep=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(ep||{}),eg=function(e){return e.startServer="startServer.startServer",e}(eg||{}),ev=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(ev||{}),eb=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(eb||{}),em=function(e){return e.executeRoute="Router.executeRoute",e}(em||{}),ey=function(e){return e.runHandler="Node.runHandler",e}(ey||{}),ew=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(ew||{}),e_=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(e_||{}),eE=function(e){return e.execute="Middleware.execute",e}(eE||{});let ex=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],eO=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];function eR(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}let{context:eS,propagation:eC,trace:eT,SpanStatusCode:eP,SpanKind:ek,ROOT_CONTEXT:eN}=n=r(956);class eA extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}let eI=(e,t)=>{(function(e){return"object"==typeof e&&null!==e&&e instanceof eA})(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:eP.ERROR,message:null==t?void 0:t.message})),e.end()},ej=new Map,eM=n.createContextKey("next.rootSpanId"),e$=0,eL=()=>e$++,eD={set(e,t,r){e.push({key:t,value:r})}};class eq{getTracerInstance(){return eT.getTracer("next.js","0.0.1")}getContext(){return eS}getTracePropagationData(){let e=eS.active(),t=[];return eC.inject(e,t,eD),t}getActiveScopeSpan(){return eT.getSpan(null==eS?void 0:eS.active())}withPropagatedContext(e,t,r){let n=eS.active();if(eT.getSpanContext(n))return t();let i=eC.extract(n,e,r);return eS.with(i,t)}trace(...e){var t;let[r,n,i]=e,{fn:o,options:a}="function"==typeof n?{fn:n,options:{}}:{fn:i,options:{...n}},s=a.spanName??r;if(!ex.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||a.hideSpan)return o();let l=this.getSpanContext((null==a?void 0:a.parentSpan)??this.getActiveScopeSpan()),u=!1;l?(null==(t=eT.getSpanContext(l))?void 0:t.isRemote)&&(u=!0):(l=(null==eS?void 0:eS.active())??eN,u=!0);let c=eL();return a.attributes={"next.span_name":s,"next.span_type":r,...a.attributes},eS.with(l.setValue(eM,c),()=>this.getTracerInstance().startActiveSpan(s,a,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,n=()=>{ej.delete(c),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&eO.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};u&&ej.set(c,new Map(Object.entries(a.attributes??{})));try{if(o.length>1)return o(e,t=>eI(e,t));let t=o(e);if(eR(t))return t.then(t=>(e.end(),t)).catch(t=>{throw eI(e,t),t}).finally(n);return e.end(),n(),t}catch(t){throw eI(e,t),n(),t}}))}wrap(...e){let t=this,[r,n,i]=3===e.length?e:[e[0],{},e[1]];return ex.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=n;"function"==typeof e&&"function"==typeof i&&(e=e.apply(this,arguments));let o=arguments.length-1,a=arguments[o];if("function"!=typeof a)return t.trace(r,e,()=>i.apply(this,arguments));{let n=t.getContext().bind(eS.active(),a);return t.trace(r,e,(e,t)=>(arguments[o]=function(e){return null==t||t(e),n.apply(this,arguments)},i.apply(this,arguments)))}}:i}startSpan(...e){let[t,r]=e,n=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,n)}getSpanContext(e){return e?eT.setSpan(eS.active(),e):void 0}getRootSpanAttributes(){let e=eS.active().getValue(eM);return ej.get(e)}setRootSpanAttribute(e,t){let r=eS.active().getValue(eM),n=ej.get(r);n&&n.set(e,t)}}let eU=(()=>{let e=new eq;return()=>e})(),eB="__prerender_bypass";Symbol("__next_preview_data"),Symbol(eB);class eV{constructor(e,t,r,n){var i;let o=e&&function(e,t){let r=Q.from(e.headers);return{isOnDemandRevalidate:r.get("x-prerender-revalidate")===t.previewModeId,revalidateOnlyGenerated:r.has("x-prerender-revalidate-if-generated")}}(t,e).isOnDemandRevalidate,a=null==(i=r.get(eB))?void 0:i.value;this.isEnabled=!!(!o&&a&&e&&a===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=n}enable(){if(!this._previewModeId)throw Object.defineProperty(Error("Invariant: previewProps missing previewModeId this should never happen"),"__NEXT_ERROR_CODE",{value:"E93",enumerable:!1,configurable:!0});this._mutableCookies.set({name:eB,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"})}disable(){this._mutableCookies.set({name:eB,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)})}}function eH(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],n=new Headers;for(let e of y(r))n.append("set-cookie",e);for(let e of new D.ResponseCookies(n).getAll())t.set(e)}}var eF=r(802),eX=r.n(eF);class eG extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}async function ez(e,t){if(!e)return t();let r=eW(e);try{return await t()}finally{let t=function(e,t){let r=new Set(e.revalidatedTags),n=new Set(e.pendingRevalidateWrites);return{revalidatedTags:t.revalidatedTags.filter(e=>!r.has(e)),pendingRevalidates:Object.fromEntries(Object.entries(t.pendingRevalidates).filter(([t])=>!(t in e.pendingRevalidates))),pendingRevalidateWrites:t.pendingRevalidateWrites.filter(e=>!n.has(e))}}(r,eW(e));await eJ(e,t)}}function eW(e){return{revalidatedTags:e.revalidatedTags?[...e.revalidatedTags]:[],pendingRevalidates:{...e.pendingRevalidates},pendingRevalidateWrites:e.pendingRevalidateWrites?[...e.pendingRevalidateWrites]:[]}}async function eJ(e,{revalidatedTags:t,pendingRevalidates:r,pendingRevalidateWrites:n}){var i;return Promise.all([null==(i=e.incrementalCache)?void 0:i.revalidateTag(t),...Object.values(r),...n])}let eK=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class eQ{disable(){throw eK}getStore(){}run(){throw eK}exit(){throw eK}enterWith(){throw eK}static bind(e){return e}}let eY="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage,eZ=eY?new eY:new eQ;class e0{constructor({waitUntil:e,onClose:t,onTaskError:r}){this.workUnitStores=new Set,this.waitUntil=e,this.onClose=t,this.onTaskError=r,this.callbackQueue=new(eX()),this.callbackQueue.pause()}after(e){if(eR(e))this.waitUntil||e1(),this.waitUntil(e.catch(e=>this.reportTaskError("promise",e)));else if("function"==typeof e)this.addCallback(e);else throw Object.defineProperty(Error("`after()`: Argument must be a promise or a function"),"__NEXT_ERROR_CODE",{value:"E50",enumerable:!1,configurable:!0})}addCallback(e){var t;this.waitUntil||e1();let r=en.getStore();r&&this.workUnitStores.add(r);let n=eZ.getStore(),i=n?n.rootTaskSpawnPhase:null==r?void 0:r.phase;this.runCallbacksOnClosePromise||(this.runCallbacksOnClosePromise=this.runCallbacksOnClose(),this.waitUntil(this.runCallbacksOnClosePromise));let o=(t=async()=>{try{await eZ.run({rootTaskSpawnPhase:i},()=>e())}catch(e){this.reportTaskError("function",e)}},eY?eY.bind(t):eQ.bind(t));this.callbackQueue.add(o)}async runCallbacksOnClose(){return await new Promise(e=>this.onClose(e)),this.runCallbacks()}async runCallbacks(){if(0===this.callbackQueue.size)return;for(let e of this.workUnitStores)e.phase="after";let e=er.getStore();if(!e)throw Object.defineProperty(new eG("Missing workStore in AfterContext.runCallbacks"),"__NEXT_ERROR_CODE",{value:"E547",enumerable:!1,configurable:!0});return ez(e,()=>(this.callbackQueue.start(),this.callbackQueue.onIdle()))}reportTaskError(e,t){if(console.error("promise"===e?"A promise passed to `after()` rejected:":"An error occurred in a function passed to `after()`:",t),this.onTaskError)try{null==this.onTaskError||this.onTaskError.call(this,t)}catch(e){console.error(Object.defineProperty(new eG("`onTaskError` threw while handling an error thrown from an `after` task",{cause:e}),"__NEXT_ERROR_CODE",{value:"E569",enumerable:!1,configurable:!0}))}}}function e1(){throw Object.defineProperty(Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."),"__NEXT_ERROR_CODE",{value:"E91",enumerable:!1,configurable:!0})}class e2{onClose(e){if(this.isClosed)throw Object.defineProperty(Error("Cannot subscribe to a closed CloseController"),"__NEXT_ERROR_CODE",{value:"E365",enumerable:!1,configurable:!0});this.target.addEventListener("close",e),this.listeners++}dispatchClose(){if(this.isClosed)throw Object.defineProperty(Error("Cannot close a CloseController multiple times"),"__NEXT_ERROR_CODE",{value:"E229",enumerable:!1,configurable:!0});this.listeners>0&&this.target.dispatchEvent(new Event("close")),this.isClosed=!0}constructor(){this.target=new EventTarget,this.listeners=0,this.isClosed=!1}}function e6(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}let e3=Symbol.for("@next/request-context");class e4 extends U{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw Object.defineProperty(new g({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new g({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}waitUntil(){throw Object.defineProperty(new g({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}let e5={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},e9=(e,t)=>eU().withPropagatedContext(e.headers,t,e5),e8=!1;async function e7(e){var t;let n,i;!function(){if(!e8&&(e8=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(905);e(),e9=t(e9)}}(),await f();let o=void 0!==globalThis.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let a=new L(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...a.searchParams.keys()]){let t=a.searchParams.getAll(e),r=function(e){for(let t of["nxtP","nxtI"])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}(e);if(r){for(let e of(a.searchParams.delete(r),t))a.searchParams.append(r,e);a.searchParams.delete(e)}}let s=a.buildId;a.buildId="";let l=function(e){let t=new Headers;for(let[r,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}(e.request.headers),u=l.has("x-nextjs-data"),c="1"===l.get("RSC");u&&"/index"===a.pathname&&(a.pathname="/");let d=new Map;if(!o)for(let e of W){let t=e.toLowerCase(),r=l.get(t);null!==r&&(d.set(t,r),l.delete(t))}let h=new e4({page:e.page,input:(function(e){let t="string"==typeof e,r=t?new URL(e):e;return r.searchParams.delete(J),t?r.toString():r})(a).toString(),init:{body:e.request.body,headers:l,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});u&&Object.defineProperty(h,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCache&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:e6()})}));let p=e.request.waitUntil??(null==(t=function(){let e=globalThis[e3];return null==e?void 0:e.get()}())?void 0:t.waitUntil),g=new S({request:h,page:e.page,context:p?{waitUntil:p}:void 0});if((n=await e9(h,()=>{if("/middleware"===e.page||"/src/middleware"===e.page){let t=g.waitUntil.bind(g),r=new e2;return eU().trace(eE.execute,{spanName:`middleware ${h.method} ${h.nextUrl.pathname}`,attributes:{"http.target":h.nextUrl.pathname,"http.method":h.method}},async()=>{try{var n,o,a,l,u;let c=e6(),d=(u=h.nextUrl,function(e,t,r,n,i,o,a,s,l,u,c){function d(e){r&&r.setHeader("Set-Cookie",e)}let h={};return{type:"request",phase:e,implicitTags:o??[],url:{pathname:n.pathname,search:n.search??""},rootParams:i,get headers(){return h.headers||(h.headers=function(e){let t=Q.from(e);for(let e of W)t.delete(e.toLowerCase());return Q.seal(t)}(t.headers)),h.headers},get cookies(){if(!h.cookies){let e=new D.RequestCookies(Q.from(t.headers));eH(t,e),h.cookies=ea.seal(e)}return h.cookies},set cookies(value){h.cookies=value},get mutableCookies(){if(!h.mutableCookies){let e=function(e,t){let r=new D.RequestCookies(Q.from(e));return el.wrap(r,t)}(t.headers,a||(r?d:void 0));eH(t,e),h.mutableCookies=e}return h.mutableCookies},get userspaceMutableCookies(){return h.userspaceMutableCookies||(h.userspaceMutableCookies=function(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return ec("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return ec("cookies().set"),e.set(...r),t};default:return B.get(e,r,n)}}});return t}(this.mutableCookies)),h.userspaceMutableCookies},get draftMode(){return h.draftMode||(h.draftMode=new eV(l,t,this.cookies,this.mutableCookies)),h.draftMode},renderResumeDataCache:s??null,isHmrRefresh:u,serverComponentsHmrCache:c||globalThis.__serverComponentsHmrCache}}("action",h,void 0,u,{},void 0,e=>{i=e},void 0,c,!1,void 0)),f=function({page:e,fallbackRouteParams:t,renderOpts:r,requestEndedState:n,isPrefetchRequest:i,buildId:o}){var a;let s={isStaticGeneration:!r.shouldWaitOnAllReady&&!r.supportsDynamicResponse&&!r.isDraftMode&&!r.isServerAction,page:e,fallbackRouteParams:t,route:(a=e.split("/").reduce((e,t,r,n)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t:e,"")).startsWith("/")?a:"/"+a,incrementalCache:r.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:r.cacheLifeProfiles,isRevalidate:r.isRevalidate,isPrerendering:r.nextExport,fetchCache:r.fetchCache,isOnDemandRevalidate:r.isOnDemandRevalidate,isDraftMode:r.isDraftMode,requestEndedState:n,isPrefetchRequest:i,buildId:o,reactLoadableManifest:(null==r?void 0:r.reactLoadableManifest)||{},assetPrefix:(null==r?void 0:r.assetPrefix)||"",afterContext:function(e){let{waitUntil:t,onClose:r,onAfterTaskError:n}=e;return new e0({waitUntil:t,onClose:r,onTaskError:n})}(r),dynamicIOEnabled:r.experimental.dynamicIO,dev:r.dev??!1};return r.store=s,s}({page:"/",fallbackRouteParams:null,renderOpts:{cacheLifeProfiles:null==(o=e.request.nextConfig)?void 0:null==(n=o.experimental)?void 0:n.cacheLife,experimental:{isRoutePPREnabled:!1,dynamicIO:!1,authInterrupts:!!(null==(l=e.request.nextConfig)?void 0:null==(a=l.experimental)?void 0:a.authInterrupts)},supportsDynamicResponse:!0,waitUntil:t,onClose:r.onClose.bind(r),onAfterTaskError:void 0},requestEndedState:{ended:!1},isPrefetchRequest:h.headers.has(z),buildId:s??""});return await er.run(f,()=>en.run(d,e.handler,h,g))}finally{setTimeout(()=>{r.dispatchClose()},0)}})}return e.handler(h,g)}))&&!(n instanceof Response))throw Object.defineProperty(TypeError("Expected an instance of Response to be returned"),"__NEXT_ERROR_CODE",{value:"E567",enumerable:!1,configurable:!0});n&&i&&n.headers.set("set-cookie",i);let v=null==n?void 0:n.headers.get("x-middleware-rewrite");if(n&&v&&(c||!o)){let t=new L(v,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});o||t.host!==h.nextUrl.host||(t.buildId=s||t.buildId,n.headers.set("x-middleware-rewrite",String(t)));let{url:r,isRelative:i}=G(t.toString(),a.toString());!o&&u&&n.headers.set("x-nextjs-rewrite",r),c&&i&&(a.pathname!==t.pathname&&n.headers.set("x-nextjs-rewritten-path",t.pathname),a.search!==t.search&&n.headers.set("x-nextjs-rewritten-query",t.search.slice(1)))}let b=null==n?void 0:n.headers.get("Location");if(n&&b&&!o){let t=new L(b,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});n=new Response(n.body,n),t.host===a.host&&(t.buildId=s||t.buildId,n.headers.set("Location",t.toString())),u&&(n.headers.delete("Location"),n.headers.set("x-nextjs-redirect",G(t.toString(),a.toString()).url))}let m=n||X.next(),y=m.headers.get("x-middleware-override-headers"),w=[];if(y){for(let[e,t]of d)m.headers.set(`x-middleware-request-${e}`,t),w.push(e);w.length>0&&m.headers.set("x-middleware-override-headers",y+","+w.join(","))}return{response:m,waitUntil:("internal"===g[O].kind?Promise.all(g[O].promises).then(()=>{}):void 0)??Promise.resolve(),fetchMetrics:h.fetchMetrics}}function te(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function tt(e){return e&&e.sensitive?"":"i"}let tr=e=>{if("function"==typeof e)return t=>e(t);let t=tn([e||""].flat().filter(Boolean));return e=>t.some(t=>t.test(e.nextUrl.pathname))},tn=e=>e.map(e=>e instanceof RegExp?e:function(e){try{return function e(t,r,n){var i;return t instanceof RegExp?function(e,t){if(!t)return e;for(var r=/\((?:\?<(.*?)>)?(?!\?)/g,n=0,i=r.exec(e.source);i;)t.push({name:i[1]||n++,prefix:"",suffix:"",modifier:"",pattern:""}),i=r.exec(e.source);return e}(t,r):Array.isArray(t)?(i=t.map(function(t){return e(t,r,n).source}),new RegExp("(?:".concat(i.join("|"),")"),tt(n))):function(e,t,r){void 0===r&&(r={});for(var n=r.strict,i=void 0!==n&&n,o=r.start,a=r.end,s=r.encode,l=void 0===s?function(e){return e}:s,u=r.delimiter,c=r.endsWith,d="[".concat(te(void 0===c?"":c),"]|$"),h="[".concat(te(void 0===u?"/#?":u),"]"),f=void 0===o||o?"^":"",p=0;p<e.length;p++){var g=e[p];if("string"==typeof g)f+=te(l(g));else{var v=te(l(g.prefix)),b=te(l(g.suffix));if(g.pattern){if(t&&t.push(g),v||b){if("+"===g.modifier||"*"===g.modifier){var m="*"===g.modifier?"?":"";f+="(?:".concat(v,"((?:").concat(g.pattern,")(?:").concat(b).concat(v,"(?:").concat(g.pattern,"))*)").concat(b,")").concat(m)}else f+="(?:".concat(v,"(").concat(g.pattern,")").concat(b,")").concat(g.modifier)}else{if("+"===g.modifier||"*"===g.modifier)throw TypeError('Can not repeat "'.concat(g.name,'" without a prefix and suffix'));f+="(".concat(g.pattern,")").concat(g.modifier)}}else f+="(?:".concat(v).concat(b,")").concat(g.modifier)}}if(void 0===a||a)i||(f+="".concat(h,"?")),f+=r.endsWith?"(?=".concat(d,")"):"$";else{var y=e[e.length-1],w="string"==typeof y?h.indexOf(y[y.length-1])>-1:void 0===y;i||(f+="(?:".concat(h,"(?=").concat(d,"))?")),w||(f+="(?=".concat(h,"|").concat(d,")"))}return new RegExp(f,tt(r))}(function(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",o=r+1;o<e.length;){var a=e.charCodeAt(o);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){i+=e[o++];continue}break}if(!i)throw TypeError("Missing parameter name at ".concat(r));t.push({type:"NAME",index:r,value:i}),r=o;continue}if("("===n){var s=1,l="",o=r+1;if("?"===e[o])throw TypeError('Pattern cannot start with "?" at '.concat(o));for(;o<e.length;){if("\\"===e[o]){l+=e[o++]+e[o++];continue}if(")"===e[o]){if(0==--s){o++;break}}else if("("===e[o]&&(s++,"?"!==e[o+1]))throw TypeError("Capturing groups are not allowed at ".concat(o));l+=e[o++]}if(s)throw TypeError("Unbalanced pattern at ".concat(r));if(!l)throw TypeError("Missing pattern at ".concat(r));t.push({type:"PATTERN",index:r,value:l}),r=o;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,i=void 0===n?"./":n,o=t.delimiter,a=void 0===o?"/#?":o,s=[],l=0,u=0,c="",d=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},h=function(e){var t=d(e);if(void 0!==t)return t;var n=r[u],i=n.type,o=n.index;throw TypeError("Unexpected ".concat(i," at ").concat(o,", expected ").concat(e))},f=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t},p=function(e){for(var t=0;t<a.length;t++){var r=a[t];if(e.indexOf(r)>-1)return!0}return!1},g=function(e){var t=s[s.length-1],r=e||(t&&"string"==typeof t?t:"");if(t&&!r)throw TypeError('Must have text between two parameters, missing text after "'.concat(t.name,'"'));return!r||p(r)?"[^".concat(te(a),"]+?"):"(?:(?!".concat(te(r),")[^").concat(te(a),"])+?")};u<r.length;){var v=d("CHAR"),b=d("NAME"),m=d("PATTERN");if(b||m){var y=v||"";-1===i.indexOf(y)&&(c+=y,y=""),c&&(s.push(c),c=""),s.push({name:b||l++,prefix:y,suffix:"",pattern:m||g(y),modifier:d("MODIFIER")||""});continue}var w=v||d("ESCAPED_CHAR");if(w){c+=w;continue}if(c&&(s.push(c),c=""),d("OPEN")){var y=f(),_=d("NAME")||"",E=d("PATTERN")||"",x=f();h("CLOSE"),s.push({name:_||(E?l++:""),pattern:_&&!E?g(y):E,prefix:y,suffix:x,modifier:d("MODIFIER")||""});continue}h("END")}return s}(t,n),r,n)}(e)}catch(t){throw Error(`Invalid path: ${e}.
Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp
${t.message}`)}}(e));r(171);for(var ti=[],to=[],ta=Uint8Array,ts="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",tl=0,tu=ts.length;tl<tu;++tl)ti[tl]=ts[tl],to[ts.charCodeAt(tl)]=tl;function tc(e){var t,r,n=function(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var n=r===t?0:4-r%4;return[r,n]}(e),i=n[0],o=n[1],a=new ta((i+o)*3/4-o),s=0,l=o>0?i-4:i;for(r=0;r<l;r+=4)t=to[e.charCodeAt(r)]<<18|to[e.charCodeAt(r+1)]<<12|to[e.charCodeAt(r+2)]<<6|to[e.charCodeAt(r+3)],a[s++]=t>>16&255,a[s++]=t>>8&255,a[s++]=255&t;return 2===o&&(t=to[e.charCodeAt(r)]<<2|to[e.charCodeAt(r+1)]>>4,a[s++]=255&t),1===o&&(t=to[e.charCodeAt(r)]<<10|to[e.charCodeAt(r+1)]<<4|to[e.charCodeAt(r+2)]>>2,a[s++]=t>>8&255,a[s++]=255&t),a}function td(e){for(var t,r=e.length,n=r%3,i=[],o=0,a=r-n;o<a;o+=16383)i.push(function(e,t,r){for(var n,i=[],o=t;o<r;o+=3)n=(e[o]<<16&0xff0000)+(e[o+1]<<8&65280)+(255&e[o+2]),i.push(ti[n>>18&63]+ti[n>>12&63]+ti[n>>6&63]+ti[63&n]);return i.join("")}(e,o,o+16383>a?a:o+16383));return 1===n?i.push(ti[(t=e[r-1])>>2]+ti[t<<4&63]+"=="):2===n&&i.push(ti[(t=(e[r-2]<<8)+e[r-1])>>10]+ti[t>>4&63]+ti[t<<2&63]+"="),i.join("")}function th(e){if(void 0===e)return{};if(!tp(e))throw Error(`The arguments to a Convex function must be an object. Received: ${e}`);return e}function tf(e){if(void 0===e)throw Error("Client created with undefined deployment address. If you used an environment variable, check that it's set.");if("string"!=typeof e)throw Error(`Invalid deployment address: found ${e}".`);if(!(e.startsWith("http:")||e.startsWith("https:")))throw Error(`Invalid deployment address: Must start with "https://" or "http://". Found "${e}".`);try{new URL(e)}catch{throw Error(`Invalid deployment address: "${e}" is not a valid URL. If you believe this URL is correct, use the \`skipConvexDeploymentUrlCheck\` option to bypass this.`)}if(e.endsWith(".convex.site"))throw Error(`Invalid deployment address: "${e}" ends with .convex.site, which is used for HTTP Actions. Convex deployment URLs typically end with .convex.cloud? If you believe this URL is correct, use the \`skipConvexDeploymentUrlCheck\` option to bypass this.`)}function tp(e){let t="object"==typeof e,r=Object.getPrototypeOf(e),n=null===r||r===Object.prototype||r?.constructor?.name==="Object";return t&&n}to["-".charCodeAt(0)]=62,to["_".charCodeAt(0)]=63;let tg=BigInt("-9223372036854775808"),tv=BigInt("9223372036854775807"),tb=BigInt("0"),tm=BigInt("8"),ty=BigInt("256");function tw(e){return Number.isNaN(e)||!Number.isFinite(e)||Object.is(e,-0)}let t_=DataView.prototype.setBigInt64?function(e){if(e<tg||tv<e)throw Error(`BigInt ${e} does not fit into a 64-bit signed integer.`);let t=new ArrayBuffer(8);return new DataView(t).setBigInt64(0,e,!0),td(new Uint8Array(t))}:function(e){e<tb&&(e-=tg+tg);let t=e.toString(16);t.length%2==1&&(t="0"+t);let r=new Uint8Array(new ArrayBuffer(8)),n=0;for(let i of t.match(/.{2}/g).reverse())r.set([parseInt(i,16)],n++),e>>=tm;return td(r)},tE=DataView.prototype.getBigInt64?function(e){let t=tc(e);if(8!==t.byteLength)throw Error(`Received ${t.byteLength} bytes, expected 8 for $integer`);return new DataView(t.buffer).getBigInt64(0,!0)}:function(e){let t=tc(e);if(8!==t.byteLength)throw Error(`Received ${t.byteLength} bytes, expected 8 for $integer`);let r=tb,n=tb;for(let e of t)r+=BigInt(e)*ty**n,n++;return r>tv&&(r+=tg+tg),r};function tx(e){if(e.length>1024)throw Error(`Field name ${e} exceeds maximum field name length 1024.`);if(e.startsWith("$"))throw Error(`Field name ${e} starts with a '$', which is reserved.`);for(let t=0;t<e.length;t+=1){let r=e.charCodeAt(t);if(r<32||r>=127)throw Error(`Field name ${e} has invalid character '${e[t]}': Field names can only contain non-control ASCII characters`)}}function tO(e){if(null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e)return e;if(Array.isArray(e))return e.map(e=>tO(e));if("object"!=typeof e)throw Error(`Unexpected type of ${e}`);let t=Object.entries(e);if(1===t.length){let r=t[0][0];if("$bytes"===r){if("string"!=typeof e.$bytes)throw Error(`Malformed $bytes field on ${e}`);return tc(e.$bytes).buffer}if("$integer"===r){if("string"!=typeof e.$integer)throw Error(`Malformed $integer field on ${e}`);return tE(e.$integer)}if("$float"===r){if("string"!=typeof e.$float)throw Error(`Malformed $float field on ${e}`);let t=tc(e.$float);if(8!==t.byteLength)throw Error(`Received ${t.byteLength} bytes, expected 8 for $float`);let r=new DataView(t.buffer).getFloat64(0,!0);if(!tw(r))throw Error(`Float ${r} should be encoded as a number`);return r}if("$set"===r)throw Error("Received a Set which is no longer supported as a Convex type.");if("$map"===r)throw Error("Received a Map which is no longer supported as a Convex type.")}let r={};for(let[t,n]of Object.entries(e))tx(t),r[t]=tO(n);return r}function tR(e){return JSON.stringify(e,(e,t)=>void 0===t?"undefined":"bigint"==typeof t?`${t.toString()}n`:t)}function tS(e,t,r,n){if(void 0===e){let e=r&&` (present at path ${r} in original object ${tR(t)})`;throw Error(`undefined is not a valid Convex value${e}. To learn about Convex's supported types, see https://docs.convex.dev/using/types.`)}if(null===e)return e;if("bigint"==typeof e){if(e<tg||tv<e)throw Error(`BigInt ${e} does not fit into a 64-bit signed integer.`);return{$integer:t_(e)}}if("number"==typeof e){if(!tw(e))return e;{let t=new ArrayBuffer(8);return new DataView(t).setFloat64(0,e,!0),{$float:td(new Uint8Array(t))}}}if("boolean"==typeof e||"string"==typeof e)return e;if(e instanceof ArrayBuffer)return{$bytes:td(new Uint8Array(e))};if(Array.isArray(e))return e.map((e,n)=>tS(e,t,r+`[${n}]`,!1));if(e instanceof Set)throw Error(tC(r,"Set",[...e],t));if(e instanceof Map)throw Error(tC(r,"Map",[...e],t));if(!tp(e)){let n=e?.constructor?.name;throw Error(tC(r,n?`${n} `:"",e,t))}let i={},o=Object.entries(e);for(let[e,a]of(o.sort(([e,t],[r,n])=>e===r?0:e<r?-1:1),o))void 0!==a?(tx(e),i[e]=tS(a,t,r+`.${e}`,!1)):n&&(tx(e),i[e]=tT(a,t,r+`.${e}`));return i}function tC(e,t,r,n){return e?`${t}${tR(r)} is not a supported Convex type (present at path ${e} in original object ${tR(n)}). To learn about Convex's supported types, see https://docs.convex.dev/using/types.`:`${t}${tR(r)} is not a supported Convex type.`}function tT(e,t,r){if(void 0===e)return{$undefined:null};if(void 0===t)throw Error(`Programming error. Current value is ${tR(e)} but original value is undefined`);return tS(e,t,r,!1)}function tP(e){return tS(e,e,"",!1)}function tk(e){return tT(e,e,"")}var tN=Object.defineProperty,tA=(e,t,r)=>t in e?tN(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,tI=(e,t,r)=>tA(e,"symbol"!=typeof t?t+"":t,r);class tj{constructor({isOptional:e}){tI(this,"type"),tI(this,"fieldPaths"),tI(this,"isOptional"),tI(this,"isConvexValidator"),this.isOptional=e,this.isConvexValidator=!0}get optional(){return"optional"===this.isOptional}}class tM extends tj{constructor({isOptional:e,tableName:t}){super({isOptional:e}),tI(this,"tableName"),tI(this,"kind","id"),this.tableName=t}get json(){return{type:"id",tableName:this.tableName}}asOptional(){return new tM({isOptional:"optional",tableName:this.tableName})}}class t$ extends tj{constructor(){super(...arguments),tI(this,"kind","float64")}get json(){return{type:"number"}}asOptional(){return new t$({isOptional:"optional"})}}class tL extends tj{constructor(){super(...arguments),tI(this,"kind","int64")}get json(){return{type:"bigint"}}asOptional(){return new tL({isOptional:"optional"})}}class tD extends tj{constructor(){super(...arguments),tI(this,"kind","boolean")}get json(){return{type:this.kind}}asOptional(){return new tD({isOptional:"optional"})}}class tq extends tj{constructor(){super(...arguments),tI(this,"kind","bytes")}get json(){return{type:this.kind}}asOptional(){return new tq({isOptional:"optional"})}}class tU extends tj{constructor(){super(...arguments),tI(this,"kind","string")}get json(){return{type:this.kind}}asOptional(){return new tU({isOptional:"optional"})}}class tB extends tj{constructor(){super(...arguments),tI(this,"kind","null")}get json(){return{type:this.kind}}asOptional(){return new tB({isOptional:"optional"})}}class tV extends tj{constructor(){super(...arguments),tI(this,"kind","any")}get json(){return{type:this.kind}}asOptional(){return new tV({isOptional:"optional"})}}class tH extends tj{constructor({isOptional:e,fields:t}){super({isOptional:e}),tI(this,"fields"),tI(this,"kind","object"),this.fields=t}get json(){return{type:this.kind,value:globalThis.Object.fromEntries(globalThis.Object.entries(this.fields).map(([e,t])=>[e,{fieldType:t.json,optional:"optional"===t.isOptional}]))}}asOptional(){return new tH({isOptional:"optional",fields:this.fields})}}class tF extends tj{constructor({isOptional:e,value:t}){super({isOptional:e}),tI(this,"value"),tI(this,"kind","literal"),this.value=t}get json(){return{type:this.kind,value:tP(this.value)}}asOptional(){return new tF({isOptional:"optional",value:this.value})}}class tX extends tj{constructor({isOptional:e,element:t}){super({isOptional:e}),tI(this,"element"),tI(this,"kind","array"),this.element=t}get json(){return{type:this.kind,value:this.element.json}}asOptional(){return new tX({isOptional:"optional",element:this.element})}}class tG extends tj{constructor({isOptional:e,key:t,value:r}){if(super({isOptional:e}),tI(this,"key"),tI(this,"value"),tI(this,"kind","record"),"optional"===t.isOptional)throw Error("Record validator cannot have optional keys");if("optional"===r.isOptional)throw Error("Record validator cannot have optional values");this.key=t,this.value=r}get json(){return{type:this.kind,keys:this.key.json,values:{fieldType:this.value.json,optional:!1}}}asOptional(){return new tG({isOptional:"optional",key:this.key,value:this.value})}}class tz extends tj{constructor({isOptional:e,members:t}){super({isOptional:e}),tI(this,"members"),tI(this,"kind","union"),this.members=t}get json(){return{type:this.kind,value:this.members.map(e=>e.json)}}asOptional(){return new tz({isOptional:"optional",members:this.members})}}let tW={null:()=>new tB({isOptional:"required"}),number:()=>new t$({isOptional:"required"}),float64:()=>new t$({isOptional:"required"}),string:()=>new tU({isOptional:"required"}),literal:e=>new tF({isOptional:"required",value:e}),array:e=>new tX({isOptional:"required",element:e}),object:e=>new tH({isOptional:"required",fields:e}),union:(...e)=>new tz({isOptional:"required",members:e}),any:()=>new tV({isOptional:"required"}),optional:e=>e.asOptional()};var tJ=Object.defineProperty,tK=(e,t,r)=>t in e?tJ(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,tQ=(e,t,r)=>tK(e,"symbol"!=typeof t?t+"":t,r);let tY=Symbol.for("ConvexError");class tZ extends(a=Error,o=tY,a){constructor(e){super("string"==typeof e?e:tR(e)),tQ(this,"name","ConvexError"),tQ(this,"data"),tQ(this,o,!0),this.data=e}}let t0=()=>Array.from({length:4},()=>0),t1=t0(),t2=t0();function t6(e,t){let r,n;if(e<128)return t[0]=e,1;if(e<=2047)r=1,n=192;else if(e<=65535)r=2,n=224;else if(e<=1114111)r=3,n=240;else throw Error("Invalid code point");t[0]=(e>>6*r)+n;let i=1;for(;r>0;r--){let n=e>>6*(r-1);t[i++]=128|63&n}return i}function t3(e){return void 0===e?[0,void 0]:null===e?[1,null]:"bigint"==typeof e?[2,e]:"number"==typeof e?[3,e]:"boolean"==typeof e?[4,e]:"string"==typeof e?[5,e]:e instanceof ArrayBuffer?[6,Array.from(new Uint8Array(e)).map(t3)]:Array.isArray(e)?[7,e.map(t3)]:[8,Object.keys(e).sort().map(t=>[t,e[t]]).map(t3)]}var t4=Object.defineProperty,t5=(e,t,r)=>t in e?t4(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,t9=(e,t,r)=>t5(e,"symbol"!=typeof t?t+"":t,r);class t8{constructor(e){t9(this,"_onLogLineFuncs"),t9(this,"_verbose"),this._onLogLineFuncs={},this._verbose=e.verbose}addLogLineListener(e){let t=Math.random().toString(36).substring(2,15);for(let e=0;e<10&&void 0!==this._onLogLineFuncs[t];e++)t=Math.random().toString(36).substring(2,15);return this._onLogLineFuncs[t]=e,()=>{delete this._onLogLineFuncs[t]}}logVerbose(...e){if(this._verbose)for(let t of Object.values(this._onLogLineFuncs))t("debug",`${new Date().toISOString()}`,...e)}log(...e){for(let t of Object.values(this._onLogLineFuncs))t("info",...e)}warn(...e){for(let t of Object.values(this._onLogLineFuncs))t("warn",...e)}error(...e){for(let t of Object.values(this._onLogLineFuncs))t("error",...e)}}function t7(e,t,r,n,i){let o=function(e){switch(e){case"query":return"Q";case"mutation":return"M";case"action":return"A";case"any":return"?"}}(r);if("object"==typeof i&&(i=`ConvexError ${JSON.stringify(i.errorData,null,2)}`),"info"===t){let t=i.match(/^\[.*?\] /);if(null===t){e.error(`[CONVEX ${o}(${n})] Could not parse console.log`);return}let r=i.slice(1,t[0].length-2),a=i.slice(t[0].length);e.log(`%c[CONVEX ${o}(${n})] [${r}]`,"color:rgb(0, 145, 255)",a)}else e.error(`[CONVEX ${o}(${n})] ${i}`)}var re=Object.defineProperty,rt=Object.defineProperty;let rr=Symbol.for("functionName"),rn=Symbol.for("toReferencePath");function ri(e){let t=function(e){let t;if("string"==typeof e)t=e.startsWith("function://")?{functionHandle:e}:{name:e};else if(e[rr])t={name:e[rr]};else{let r=e[rn]??null;if(!r)throw Error(`${e} is not a functionReference`);t={reference:r}}return t}(e);if(void 0===t.name){if(void 0!==t.functionHandle)throw Error(`Expected function reference like "api.file.func" or "internal.file.func", but received function handle ${t.functionHandle}`);if(void 0!==t.reference)throw Error(`Expected function reference in the current component like "api.file.func" or "internal.file.func", but received reference ${t.reference}`);throw Error(`Expected function reference like "api.file.func" or "internal.file.func", but received ${JSON.stringify(t)}`)}if("string"==typeof e)return e;let r=e[rr];if(!r)throw Error(`${e} is not a functionReference`);return r}!function e(t=[]){return new Proxy({},{get(r,n){if("string"==typeof n)return e([...t,n]);if(n===rr){if(t.length<2){let e=["api",...t].join(".");throw Error(`API path is expected to be of the form \`api.moduleName.functionName\`. Found: \`${e}\``)}let e=t.slice(0,-1).join("/"),r=t[t.length-1];return"default"===r?e:e+":"+r}return n===Symbol.toStringTag?"FunctionReference":void 0}})}();var ro=Object.defineProperty,ra=Object.defineProperty,rs=(e,t,r)=>t in e?ra(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,rl=(e,t,r)=>rs(e,"symbol"!=typeof t?t+"":t,r);class ru{constructor(e,t){rl(this,"low"),rl(this,"high"),rl(this,"__isUnsignedLong__"),this.low=0|e,this.high=0|t,this.__isUnsignedLong__=!0}static isLong(e){return!0===(e&&e.__isUnsignedLong__)}static fromBytesLE(e){return new ru(e[0]|e[1]<<8|e[2]<<16|e[3]<<24,e[4]|e[5]<<8|e[6]<<16|e[7]<<24)}toBytesLE(){let e=this.high,t=this.low;return[255&t,t>>>8&255,t>>>16&255,t>>>24,255&e,e>>>8&255,e>>>16&255,e>>>24]}static fromNumber(e){return isNaN(e)||e<0?rc:e>=rh?rf:new ru(e%rd|0,e/rd|0)}toString(){return(BigInt(this.high)*BigInt(rd)+BigInt(this.low)).toString()}equals(e){return ru.isLong(e)||(e=ru.fromValue(e)),(this.high>>>31!=1||e.high>>>31!=1)&&this.high===e.high&&this.low===e.low}notEquals(e){return!this.equals(e)}comp(e){return(ru.isLong(e)||(e=ru.fromValue(e)),this.equals(e))?0:e.high>>>0>this.high>>>0||e.high===this.high&&e.low>>>0>this.low>>>0?-1:1}lessThanOrEqual(e){return 0>=this.comp(e)}static fromValue(e){return"number"==typeof e?ru.fromNumber(e):new ru(e.low,e.high)}}let rc=new ru(0,0),rd=0x100000000,rh=0xffffffffffffffff,rf=new ru(-1,-1);var rp=Object.defineProperty,rg=Object.defineProperty;class rv extends Error{}rv.prototype.name="InvalidTokenError";var rb=Object.defineProperty,rm=Object.defineProperty,ry=Object.defineProperty;let rw="1.24.8";var r_=Object.defineProperty,rE=(e,t,r)=>t in e?r_(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,rx=(e,t,r)=>rE(e,"symbol"!=typeof t?t+"":t,r);class rO{constructor(e,t){if(rx(this,"address"),rx(this,"auth"),rx(this,"adminAuth"),rx(this,"encodedTsPromise"),rx(this,"debug"),rx(this,"fetchOptions"),rx(this,"logger"),"boolean"==typeof t)throw Error("skipConvexDeploymentUrlCheck as the second argument is no longer supported. Please pass an options object, `{ skipConvexDeploymentUrlCheck: true }`.");!0!==(t??{}).skipConvexDeploymentUrlCheck&&tf(e),this.logger=t?.logger===!1?new t8({verbose:!1}):t?.logger!==!0&&t?.logger?t.logger:function(e){let t=new t8(e);return t.addLogLineListener((e,...t)=>{switch(e){case"debug":console.debug(...t);break;case"info":default:console.log(...t);break;case"warn":console.warn(...t);break;case"error":console.error(...t)}}),t}({verbose:!1}),this.address=e,this.debug=!0}backendUrl(){return`${this.address}/api`}get url(){return this.address}setAuth(e){this.clearAuth(),this.auth=e}setAdminAuth(e,t){if(this.clearAuth(),void 0!==t){let r=btoa(String.fromCodePoint(...new TextEncoder().encode(JSON.stringify(t))));this.adminAuth=`${e}:${r}`}else this.adminAuth=e}clearAuth(){this.auth=void 0,this.adminAuth=void 0}setDebug(e){this.debug=e}setFetchOptions(e){this.fetchOptions=e}async consistentQuery(e,...t){let r=th(t[0]),n=this.getTimestamp();return await this.queryInner(e,r,{timestampPromise:n})}async getTimestamp(){return this.encodedTsPromise?this.encodedTsPromise:this.encodedTsPromise=this.getTimestampInner()}async getTimestampInner(){let e=i||fetch,t={"Content-Type":"application/json","Convex-Client":`npm-${rw}`},r=await e(`${this.address}/api/query_ts`,{...this.fetchOptions,method:"POST",headers:t});if(!r.ok)throw Error(await r.text());let{ts:n}=await r.json();return n}async query(e,...t){let r=th(t[0]);return await this.queryInner(e,r,{})}async queryInner(e,t,r){let n=ri(e),o=[tP(t)],a={"Content-Type":"application/json","Convex-Client":`npm-${rw}`};this.adminAuth?a.Authorization=`Convex ${this.adminAuth}`:this.auth&&(a.Authorization=`Bearer ${this.auth}`);let s=i||fetch,l=r.timestampPromise?await r.timestampPromise:void 0,u=JSON.stringify({path:n,format:"convex_encoded_json",args:o,...l?{ts:l}:{}}),c=l?`${this.address}/api/query_at_ts`:`${this.address}/api/query`,d=await s(c,{...this.fetchOptions,body:u,method:"POST",headers:a});if(!d.ok&&560!==d.status)throw Error(await d.text());let h=await d.json();if(this.debug)for(let e of h.logLines??[])t7(this.logger,"info","query",n,e);switch(h.status){case"success":return tO(h.value);case"error":if(void 0!==h.errorData)throw rR(h.errorData,new tZ(h.errorMessage));throw Error(h.errorMessage);default:throw Error(`Invalid response: ${JSON.stringify(h)}`)}}async mutation(e,...t){let r=th(t[0]),n=ri(e),o=JSON.stringify({path:n,format:"convex_encoded_json",args:[tP(r)]}),a={"Content-Type":"application/json","Convex-Client":`npm-${rw}`};this.adminAuth?a.Authorization=`Convex ${this.adminAuth}`:this.auth&&(a.Authorization=`Bearer ${this.auth}`);let s=i||fetch,l=await s(`${this.address}/api/mutation`,{...this.fetchOptions,body:o,method:"POST",headers:a});if(!l.ok&&560!==l.status)throw Error(await l.text());let u=await l.json();if(this.debug)for(let e of u.logLines??[])t7(this.logger,"info","mutation",n,e);switch(u.status){case"success":return tO(u.value);case"error":if(void 0!==u.errorData)throw rR(u.errorData,new tZ(u.errorMessage));throw Error(u.errorMessage);default:throw Error(`Invalid response: ${JSON.stringify(u)}`)}}async action(e,...t){let r=th(t[0]),n=ri(e),o=JSON.stringify({path:n,format:"convex_encoded_json",args:[tP(r)]}),a={"Content-Type":"application/json","Convex-Client":`npm-${rw}`};this.adminAuth?a.Authorization=`Convex ${this.adminAuth}`:this.auth&&(a.Authorization=`Bearer ${this.auth}`);let s=i||fetch,l=await s(`${this.address}/api/action`,{...this.fetchOptions,body:o,method:"POST",headers:a});if(!l.ok&&560!==l.status)throw Error(await l.text());let u=await l.json();if(this.debug)for(let e of u.logLines??[])t7(this.logger,"info","action",n,e);switch(u.status){case"success":return tO(u.value);case"error":if(void 0!==u.errorData)throw rR(u.errorData,new tZ(u.errorMessage));throw Error(u.errorMessage);default:throw Error(`Invalid response: ${JSON.stringify(u)}`)}}async function(e,t,...r){let n=th(r[0]),o="string"==typeof e?e:ri(e),a=JSON.stringify({componentPath:t,path:o,format:"convex_encoded_json",args:tP(n)}),s={"Content-Type":"application/json","Convex-Client":`npm-${rw}`};this.adminAuth?s.Authorization=`Convex ${this.adminAuth}`:this.auth&&(s.Authorization=`Bearer ${this.auth}`);let l=i||fetch,u=await l(`${this.address}/api/function`,{...this.fetchOptions,body:a,method:"POST",headers:s});if(!u.ok&&560!==u.status)throw Error(await u.text());let c=await u.json();if(this.debug)for(let e of c.logLines??[])t7(this.logger,"info","any",o,e);switch(c.status){case"success":return tO(c.value);case"error":if(void 0!==c.errorData)throw rR(c.errorData,new tZ(c.errorMessage));throw Error(c.errorMessage);default:throw Error(`Invalid response: ${JSON.stringify(c)}`)}}}function rR(e,t){return t.data=tO(e),t}function rS(e,t){if("undefined"==typeof Convex||void 0===Convex.syscall)throw Error("The Convex database and auth objects are being used outside of a Convex backend. Did you mean to use `useQuery` or `useMutation` to call a Convex function?");return JSON.parse(Convex.syscall(e,JSON.stringify(t)))}async function rC(e,t){let r;if("undefined"==typeof Convex||void 0===Convex.asyncSyscall)throw Error("The Convex database and auth objects are being used outside of a Convex backend. Did you mean to use `useQuery` or `useMutation` to call a Convex function?");try{r=await Convex.asyncSyscall(e,JSON.stringify(t))}catch(e){if(void 0!==e.data){let t=new tZ(e.message);throw t.data=tO(e.data),t}throw Error(e.message)}return JSON.parse(r)}var rT=Object.defineProperty,rP=(e,t,r)=>t in e?rT(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,rk=(e,t,r)=>rP(e,"symbol"!=typeof t?t+"":t,r);class rN{constructor(){rk(this,"_isExpression"),rk(this,"_value")}}var rA=Object.defineProperty,rI=(e,t,r)=>t in e?rA(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,rj=(e,t,r)=>rI(e,"symbol"!=typeof t?t+"":t,r);class rM extends rN{constructor(e){super(),rj(this,"inner"),this.inner=e}serialize(){return this.inner}}function r$(e){return e instanceof rM?e.serialize():{$literal:tk(e)}}let rL={eq(e,t){if("string"!=typeof e)throw Error("The first argument to `q.eq` must be a field name.");return new rM({$eq:[r$(new rM({$field:e})),r$(t)]})},or:(...e)=>new rM({$or:e.map(r$)})};var rD=Object.defineProperty,rq=(e,t,r)=>t in e?rD(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,rU=(e,t,r)=>rq(e,"symbol"!=typeof t?t+"":t,r);class rB{constructor(){rU(this,"_isExpression"),rU(this,"_value")}}var rV=Object.defineProperty,rH=(e,t,r)=>t in e?rV(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,rF=(e,t,r)=>rH(e,"symbol"!=typeof t?t+"":t,r);class rX extends rB{constructor(e){super(),rF(this,"inner"),this.inner=e}serialize(){return this.inner}}function rG(e){return e instanceof rX?e.serialize():{$literal:tk(e)}}let rz={eq:(e,t)=>new rX({$eq:[rG(e),rG(t)]}),neq:(e,t)=>new rX({$neq:[rG(e),rG(t)]}),lt:(e,t)=>new rX({$lt:[rG(e),rG(t)]}),lte:(e,t)=>new rX({$lte:[rG(e),rG(t)]}),gt:(e,t)=>new rX({$gt:[rG(e),rG(t)]}),gte:(e,t)=>new rX({$gte:[rG(e),rG(t)]}),add:(e,t)=>new rX({$add:[rG(e),rG(t)]}),sub:(e,t)=>new rX({$sub:[rG(e),rG(t)]}),mul:(e,t)=>new rX({$mul:[rG(e),rG(t)]}),div:(e,t)=>new rX({$div:[rG(e),rG(t)]}),mod:(e,t)=>new rX({$mod:[rG(e),rG(t)]}),neg:e=>new rX({$neg:rG(e)}),and:(...e)=>new rX({$and:e.map(rG)}),or:(...e)=>new rX({$or:e.map(rG)}),not:e=>new rX({$not:rG(e)}),field:e=>new rX({$field:e})};var rW=Object.defineProperty,rJ=(e,t,r)=>t in e?rW(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,rK=(e,t,r)=>rJ(e,"symbol"!=typeof t?t+"":t,r);class rQ{constructor(){rK(this,"_isIndexRange")}}var rY=Object.defineProperty,rZ=(e,t,r)=>t in e?rY(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,r0=(e,t,r)=>rZ(e,"symbol"!=typeof t?t+"":t,r);class r1 extends rQ{constructor(e){super(),r0(this,"rangeExpressions"),r0(this,"isConsumed"),this.rangeExpressions=e,this.isConsumed=!1}static new(){return new r1([])}consume(){if(this.isConsumed)throw Error("IndexRangeBuilder has already been used! Chain your method calls like `q => q.eq(...).eq(...)`. See https://docs.convex.dev/using/indexes");this.isConsumed=!0}eq(e,t){return this.consume(),new r1(this.rangeExpressions.concat({type:"Eq",fieldPath:e,value:tk(t)}))}gt(e,t){return this.consume(),new r1(this.rangeExpressions.concat({type:"Gt",fieldPath:e,value:tP(t)}))}gte(e,t){return this.consume(),new r1(this.rangeExpressions.concat({type:"Gte",fieldPath:e,value:tP(t)}))}lt(e,t){return this.consume(),new r1(this.rangeExpressions.concat({type:"Lt",fieldPath:e,value:tP(t)}))}lte(e,t){return this.consume(),new r1(this.rangeExpressions.concat({type:"Lte",fieldPath:e,value:tP(t)}))}export(){return this.consume(),this.rangeExpressions}}var r2=Object.defineProperty,r6=(e,t,r)=>t in e?r2(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,r3=(e,t,r)=>r6(e,"symbol"!=typeof t?t+"":t,r);class r4{constructor(){r3(this,"_isSearchFilter")}}function r5(e,t,r,n){if(void 0===e)throw TypeError(`Must provide arg ${t} \`${n}\` to \`${r}\``)}var r9=Object.defineProperty,r8=(e,t,r)=>t in e?r9(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,r7=(e,t,r)=>r8(e,"symbol"!=typeof t?t+"":t,r);class ne extends r4{constructor(e){super(),r7(this,"filters"),r7(this,"isConsumed"),this.filters=e,this.isConsumed=!1}static new(){return new ne([])}consume(){if(this.isConsumed)throw Error("SearchFilterBuilder has already been used! Chain your method calls like `q => q.search(...).eq(...)`.");this.isConsumed=!0}search(e,t){return r5(e,1,"search","fieldName"),r5(t,2,"search","query"),this.consume(),new ne(this.filters.concat({type:"Search",fieldPath:e,value:t}))}eq(e,t){return r5(e,1,"eq","fieldName"),2!=arguments.length&&r5(t,2,"search","value"),this.consume(),new ne(this.filters.concat({type:"Eq",fieldPath:e,value:tk(t)}))}export(){return this.consume(),this.filters}}var nt=Object.defineProperty,nr=(e,t,r)=>t in e?nt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,nn=(e,t,r)=>nr(e,"symbol"!=typeof t?t+"":t,r);function ni(e){throw Error("consumed"===e?"This query is closed and can't emit any more values.":"This query has been chained with another operator and can't be reused.")}Symbol.asyncIterator;class no{constructor(e){nn(this,"state"),this.state={type:"preparing",query:e}}takeQuery(){if("preparing"!==this.state.type)throw Error("A query can only be chained once and can't be chained after iteration begins.");let e=this.state.query;return this.state={type:"closed"},e}startQuery(){if("executing"===this.state.type)throw Error("Iteration can only begin on a query once.");("closed"===this.state.type||"consumed"===this.state.type)&&ni(this.state.type);let{queryId:e}=rS("1.0/queryStream",{query:this.state.query,version:rw});return this.state={type:"executing",queryId:e},e}closeQuery(){"executing"===this.state.type&&rS("1.0/queryCleanup",{queryId:this.state.queryId}),this.state={type:"consumed"}}order(e){r5(e,1,"order","order");let t=this.takeQuery();if("Search"===t.source.type)throw Error("Search queries must always be in relevance order. Can not set order manually.");if(null!==t.source.order)throw Error("Queries may only specify order at most once");return t.source.order=e,new no(t)}filter(e){r5(e,1,"filter","predicate");let t=this.takeQuery();if(t.operators.length>=256)throw Error("Can't construct query with more than 256 operators");return t.operators.push({filter:rG(e(rz))}),new no(t)}limit(e){r5(e,1,"limit","n");let t=this.takeQuery();return t.operators.push({limit:e}),new no(t)}[Symbol.asyncIterator](){return this.startQuery(),this}async next(){("closed"===this.state.type||"consumed"===this.state.type)&&ni(this.state.type);let e="preparing"===this.state.type?this.startQuery():this.state.queryId,{value:t,done:r}=await rC("1.0/queryStreamNext",{queryId:e});return r&&this.closeQuery(),{value:tO(t),done:r}}return(){return this.closeQuery(),Promise.resolve({done:!0,value:void 0})}async paginate(e){if(r5(e,1,"paginate","options"),"number"!=typeof e?.numItems||e.numItems<0)throw Error(`\`options.numItems\` must be a positive number. Received \`${e?.numItems}\`.`);let t=this.takeQuery(),r=e.numItems,n=e.cursor,i=e?.endCursor??null,o=e.maximumRowsRead??null,{page:a,isDone:s,continueCursor:l,splitCursor:u,pageStatus:c}=await rC("1.0/queryPage",{query:t,cursor:n,endCursor:i,pageSize:r,maximumRowsRead:o,maximumBytesRead:e.maximumBytesRead,version:rw});return{page:a.map(e=>tO(e)),isDone:s,continueCursor:l,splitCursor:u,pageStatus:c}}async collect(){let e=[];for await(let t of this)e.push(t);return e}async take(e){return r5(e,1,"take","n"),!function(e,t,r,n){if(!Number.isInteger(e)||e<0)throw TypeError(`Arg 1 \`n\` to \`${r}\` must be a non-negative integer`)}(e,0,"take",0),this.limit(e).collect()}async first(){let e=await this.take(1);return 0===e.length?null:e[0]}async unique(){let e=await this.take(2);if(0===e.length)return null;if(2===e.length)throw Error(`unique() query returned more than one result: 
 [${e[0]._id}, ${e[1]._id}, ...]`);return e[0]}}async function na(e,t){if(validateArg(e,1,"get","id"),"string"!=typeof e)throw Error(`Invalid argument \`id\` for \`db.get\`, expected string but got '${typeof e}': ${e}`);let r={id:convexToJson(e),isSystem:t,version};return jsonToConvex(await performAsyncSyscall("1.0/get",r))}async function ns(e,t){if(e.startsWith("_"))throw Error("System tables (prefixed with `_`) are read-only.");return validateArg(e,1,"insert","table"),validateArg(t,2,"insert","value"),jsonToConvex(await performAsyncSyscall("1.0/insert",{table:e,value:convexToJson(t)}))._id}async function nl(e,t){validateArg(e,1,"patch","id"),validateArg(t,2,"patch","value"),await performAsyncSyscall("1.0/shallowMerge",{id:convexToJson(e),value:patchValueToJson(t)})}async function nu(e,t){validateArg(e,1,"replace","id"),validateArg(t,2,"replace","value"),await performAsyncSyscall("1.0/replace",{id:convexToJson(e),value:convexToJson(t)})}async function nc(e){validateArg(e,1,"delete","id"),await performAsyncSyscall("1.0/remove",{id:convexToJson(e)})}tW.object({numItems:tW.number(),cursor:tW.union(tW.string(),tW.null()),endCursor:tW.optional(tW.union(tW.string(),tW.null())),id:tW.optional(tW.number()),maximumRowsRead:tW.optional(tW.number()),maximumBytesRead:tW.optional(tW.number())});var nd=Object.defineProperty,nh=Object.defineProperty,nf=Object.defineProperty,np=(e,t,r)=>t in e?nf(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,ng=(e,t,r)=>np(e,"symbol"!=typeof t?t+"":t,r);class nv{constructor(e,t){ng(this,"_definition"),ng(this,"_name"),this._definition=e,this._name=t,setReferencePath(this,`_reference/childComponent/${t}`)}get exports(){return function e(t,r){return new Proxy({},{get(n,i){if("string"==typeof i)return e(t,[...r,i]);if(i===toReferencePath){let e=`_reference/childComponent/${t}`;for(let t of r)e+=`/${t}`;return e}}})}(this._name,[])}}function nb(e){let t=[];for(let[r,n]of Object.entries(e)){let e;e="string"==typeof n?{type:"leaf",leaf:n}:nb(n),t.push([r,e])}return{type:"branch",branch:t}}function nm(e){return e.map(([e,t,r])=>{let n=null;if(null!==r)for(let[e,t]of(n=[],Object.entries(r)))void 0!==t&&n.push([e,{type:"value",value:JSON.stringify(convexToJson(t))}]);let i=t.componentDefinitionPath;if(!i)throw Error("no .componentPath for component definition "+JSON.stringify(t,null,2));return{name:e,path:i,args:n}})}var ny=Object.defineProperty,nw=(e,t,r)=>t in e?ny(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,n_=(e,t,r)=>nw(e,"symbol"!=typeof t?t+"":t,r);class nE{constructor(e){n_(this,"indexes"),n_(this,"searchIndexes"),n_(this,"vectorIndexes"),n_(this,"validator"),this.indexes=[],this.searchIndexes=[],this.vectorIndexes=[],this.validator=e}index(e,t){return this.indexes.push({indexDescriptor:e,fields:t}),this}searchIndex(e,t){return this.searchIndexes.push({indexDescriptor:e,searchField:t.searchField,filterFields:t.filterFields||[]}),this}vectorIndex(e,t){return this.vectorIndexes.push({indexDescriptor:e,vectorField:t.vectorField,dimensions:t.dimensions,filterFields:t.filterFields||[]}),this}self(){return this}export(){let e=this.validator.json;if("object"!=typeof e)throw Error("Invalid validator: please make sure that the parameter of `defineTable` is valid (see https://docs.convex.dev/database/schemas)");return{indexes:this.indexes,searchIndexes:this.searchIndexes,vectorIndexes:this.vectorIndexes,documentType:e}}}function nx(e){return new nE(e.isConvexValidator?e:tW.object(e))}class nO{constructor(e,t){n_(this,"tables"),n_(this,"strictTableNameTypes"),n_(this,"schemaValidation"),this.tables=e,this.schemaValidation=t?.schemaValidation===void 0||t.schemaValidation}export(){return JSON.stringify({tables:Object.entries(this.tables).map(([e,t])=>{let{indexes:r,searchIndexes:n,vectorIndexes:i,documentType:o}=t.export();return{tableName:e,indexes:r,searchIndexes:n,vectorIndexes:i,documentType:o}}),schemaValidation:this.schemaValidation})}}async function nR(e,...t){let[r,n]=t;return nC(n??{}).query(e,r)}async function nS(e,...t){let[r,n]=t;return nC(n??{}).action(e,r)}function nC(e){let t=new rO(function(e,t){let r=e??"https://lovely-cormorant-474.convex.cloud";if("string"!=typeof r)throw Error(void 0===e?"Environment variable NEXT_PUBLIC_CONVEX_URL is not set.":"Convex function called with invalid deployment address.");return t||tf(r),r}(e.url,e.skipConvexDeploymentUrlCheck??!1));return void 0!==e.token&&t.setAuth(e.token),void 0!==e.adminToken&&t.setAdminAuth(e.adminToken),t.setFetchOptions({cache:"no-store"}),t}new nO({_scheduled_functions:nx({name:tW.string(),args:tW.array(tW.any()),scheduledTime:tW.float64(),completedTime:tW.optional(tW.float64()),state:tW.union(tW.object({kind:tW.literal("pending")}),tW.object({kind:tW.literal("inProgress")}),tW.object({kind:tW.literal("success")}),tW.object({kind:tW.literal("failed"),error:tW.string()}),tW.object({kind:tW.literal("canceled")}))}),_storage:nx({sha256:tW.string(),size:tW.float64(),contentType:tW.optional(tW.string())})},void 0),r(280),"undefined"==typeof URLPattern||URLPattern;var nT=r(815);class nP extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest="DYNAMIC_SERVER_USAGE"}}class nk extends Error{constructor(...e){super(...e),this.code="NEXT_STATIC_GEN_BAILOUT"}}class nN extends Error{constructor(e){super(`During prerendering, ${e} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${e} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`),this.expression=e,this.digest="HANGING_PROMISE_REJECTION"}}function nA(e,t){let r=new Promise((r,n)=>{e.addEventListener("abort",()=>{n(new nN(t))},{once:!0})});return r.catch(nI),r}function nI(){}let nj="function"==typeof nT.unstable_postpone;function nM(e,t,r){let n=Object.defineProperty(new nP(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw r.revalidate=0,t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}function n$(e,t){t&&"cache"!==t.type&&"unstable-cache"!==t.type&&("prerender"===t.type||"prerender-legacy"===t.type)&&(t.revalidate=0)}function nL(e,t,r,n){let i=n.dynamicTracking;throw i&&null===i.syncDynamicErrorWithStack&&(i.syncDynamicExpression=t,i.syncDynamicErrorWithStack=r,!0===n.validating&&(i.syncDynamicLogged=!0)),function(e,t,r){let n=nU(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`);r.controller.abort(n);let i=r.dynamicTracking;i&&i.dynamicAccesses.push({stack:i.isDebugDynamicAccesses?Error().stack:void 0,expression:t})}(e,t,n),nU(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`)}function nD(e,t,r){(function(){if(!nj)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})})(),r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:t}),nT.unstable_postpone(nq(e,t))}function nq(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}if(!1===function(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}(nq("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});function nU(e){let t=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return t.digest="NEXT_PRERENDER_INTERRUPTED",t}function nB(){let e=eZ.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`),new WeakMap;let nV={current:null},nH="function"==typeof nT.cache?nT.cache:e=>e,nF=console.warn;function nX(e){return function(...t){nF(e(...t))}}nH(e=>{try{nF(nV.current)}finally{nV.current=null}});let nG=new WeakMap;function nz(e){let t=nG.get(e);if(t)return t;let r=Promise.resolve(e);return nG.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):nQ.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):nY.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function nW(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let nJ=nX(nK);function nK(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function nQ(){return this.getAll().map(e=>[e.name,e]).values()}function nY(e){for(let e of this.getAll())this.delete(e.name);return e}let nZ=new WeakMap;function n0(e){let t=nZ.get(e);if(t)return t;let r=Promise.resolve(e);return nZ.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function n1(e){return"string"==typeof e?`'${e}'`:"..."}let n2=nX(n6);function n6(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}new WeakMap;class n3{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){n5("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){n5("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let n4=nX(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function n5(e){let t=workAsyncStorage.getStore(),r=workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});abortAndThrowOnSynchronousRequestDataAccess(t.route,e,n,r)}else if("prerender-ppr"===r.type)postponeWithTracking(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}let n9=function(){let e="cookies",t=er.getStore(),r=en.getStore();if(t){if(r&&"after"===r.phase&&!nB())throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(t.forceStatic)return nz(ea.seal(new D.RequestCookies(new Headers({}))));if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new nk(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type)return function(e,t){let r=nG.get(t);if(r)return r;let n=nA(t.renderSignal,"`cookies()`");return nG.set(t,n),Object.defineProperties(n,{[Symbol.iterator]:{value:function(){let r="`cookies()[Symbol.iterator]()`",n=nK(e,r);nL(e,r,n,t)}},size:{get(){let r="`cookies().size`",n=nK(e,r);nL(e,r,n,t)}},get:{value:function(){let r;r=0==arguments.length?"`cookies().get()`":`\`cookies().get(${nW(arguments[0])})\``;let n=nK(e,r);nL(e,r,n,t)}},getAll:{value:function(){let r;r=0==arguments.length?"`cookies().getAll()`":`\`cookies().getAll(${nW(arguments[0])})\``;let n=nK(e,r);nL(e,r,n,t)}},has:{value:function(){let r;r=0==arguments.length?"`cookies().has()`":`\`cookies().has(${nW(arguments[0])})\``;let n=nK(e,r);nL(e,r,n,t)}},set:{value:function(){let r;if(0==arguments.length)r="`cookies().set()`";else{let e=arguments[0];r=e?`\`cookies().set(${nW(e)}, ...)\``:"`cookies().set(...)`"}let n=nK(e,r);nL(e,r,n,t)}},delete:{value:function(){let r;r=0==arguments.length?"`cookies().delete()`":1==arguments.length?`\`cookies().delete(${nW(arguments[0])})\``:`\`cookies().delete(${nW(arguments[0])}, ...)\``;let n=nK(e,r);nL(e,r,n,t)}},clear:{value:function(){let r="`cookies().clear()`",n=nK(e,r);nL(e,r,n,t)}},toString:{value:function(){let r="`cookies().toString()`",n=nK(e,r);nL(e,r,n,t)}}}),n}(t.route,r);"prerender-ppr"===r.type?nD(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&nM(e,t,r)}n$(t,r)}let n=ei(e);return nz(eu(n)?n.userspaceMutableCookies:n.cookies)},n8=function(){let e=er.getStore(),t=en.getStore();if(e){if(t&&"after"===t.phase&&!nB())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return n0(Q.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new nk(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t){if("prerender"===t.type)return function(e,t){let r=nZ.get(t);if(r)return r;let n=nA(t.renderSignal,"`headers()`");return nZ.set(t,n),Object.defineProperties(n,{append:{value:function(){let r=`\`headers().append(${n1(arguments[0])}, ...)\``,n=n6(e,r);nL(e,r,n,t)}},delete:{value:function(){let r=`\`headers().delete(${n1(arguments[0])})\``,n=n6(e,r);nL(e,r,n,t)}},get:{value:function(){let r=`\`headers().get(${n1(arguments[0])})\``,n=n6(e,r);nL(e,r,n,t)}},has:{value:function(){let r=`\`headers().has(${n1(arguments[0])})\``,n=n6(e,r);nL(e,r,n,t)}},set:{value:function(){let r=`\`headers().set(${n1(arguments[0])}, ...)\``,n=n6(e,r);nL(e,r,n,t)}},getSetCookie:{value:function(){let r="`headers().getSetCookie()`",n=n6(e,r);nL(e,r,n,t)}},forEach:{value:function(){let r="`headers().forEach(...)`",n=n6(e,r);nL(e,r,n,t)}},keys:{value:function(){let r="`headers().keys()`",n=n6(e,r);nL(e,r,n,t)}},values:{value:function(){let r="`headers().values()`",n=n6(e,r);nL(e,r,n,t)}},entries:{value:function(){let r="`headers().entries()`",n=n6(e,r);nL(e,r,n,t)}},[Symbol.iterator]:{value:function(){let r="`headers()[Symbol.iterator]()`",n=n6(e,r);nL(e,r,n,t)}}}),n}(e.route,t);"prerender-ppr"===t.type?nD(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&nM("headers",e,t)}n$(e,t)}return n0(ei("headers").headers)};async function n7(){return ir(await n8(),await n9(),{maxAge:null})}async function ie(e){return ir(await n8(),e.cookies,{maxAge:null})}async function it(e,t){return ir(await n8(),e.cookies,t)}function ir(e,t,r){var n;let i=(n=e.get("Host")??"",/(localhost|127\.0\.0\.1):\d+/.test(n??"")),o=i?"":"__Host-",a=o+"__convexAuthJWT",s=o+"__convexAuthRefreshToken",l=o+"__convexAuthOAuthVerifier";function u(e){return t.get(e)?.value??null}let c={secure:!i,httpOnly:!0,sameSite:"lax",path:"/",maxAge:r.maxAge??void 0};function d(e,r){null===r?"size"in t?t.delete(e):t.set(e,"",{...c,maxAge:void 0,expires:0}):t.set(e,r,c)}return{get token(){return u(a)},set token(value){d(a,value)},get refreshToken(){return u(s)},set refreshToken(value){d(s,value)},get verifier(){return u(l)},set verifier(value){d(l,value)}}}function ii(e){return new X(JSON.stringify(e),{headers:{"Content-Type":"application/json"}})}async function io(e,t,r){let n=await it(e,r);null===t?(n.token=null,n.refreshToken=null):(n.token=t.token,n.refreshToken=t.refreshToken),n.verifier=null}async function ia(e,t){let r=await ie(e);null===t?(r.token=null,r.refreshToken=null):(r.token=t.token,r.refreshToken=t.refreshToken)}function is(e){let t=e.headers.get("Origin"),r=t?new URL(t):null;return null!==r&&(r.host!==e.headers.get("Host")||r.protocol!==new URL(e.url).protocol)}function il(e,t){t&&console.debug(`[verbose] ${new Date().toISOString()} [ConvexAuthNextjs] ${e}`)}function iu(e){return Object.hasOwn(e,"convexUrl")?{url:e.convexUrl}:{}}async function ic(e,t){var r;let n;let i=t?.cookieConfig??{maxAge:null},o=t?.verbose??!1;if("POST"!==e.method)return new Response("Invalid method",{status:405});if(is(e))return new Response("Invalid origin",{status:403});let{action:a,args:s}=await e.json();if("auth:signIn"!==a&&"auth:signOut"!==a)return il(`Invalid action ${a}, returning 400`,o),new Response("Invalid action",{status:400});if("auth:signIn"===a&&void 0!==s.refreshToken){let e=(await n7()).refreshToken;if(null===e)return console.error("Convex Auth: Unexpected missing refreshToken cookie during client refresh"),new Response(JSON.stringify({tokens:null}));s.refreshToken=e}else n=(await n7()).token??void 0;if(il(`Fetching action ${a} with args ${JSON.stringify({...s,refreshToken:(r=s?.refreshToken??"").length<10?"<redacted>":r.substring(0,5)+"<redacted>"+r.substring(r.length-5)})}`,o),"auth:signIn"===a){let e;let r=void 0!==s.refreshToken||s.params?.code!==void 0?{}:{token:n};try{e=await nS(a,s,{...iu(t),...r})}catch(t){console.error("Hit error while running `auth:signIn`:"),console.error(t),il("Clearing auth cookies",o);let e=ii(null);return await io(e,null,i),e}if(void 0!==e.redirect){let{redirect:t}=e,r=ii({redirect:t});return(await it(r,i)).verifier=e.verifier,il(`Redirecting to ${t}`,o),r}if(void 0!==e.tokens){il(null===e.tokens?"No tokens returned, clearing auth cookies":"Setting auth cookies with returned tokens",o);let t=ii({tokens:null!==e.tokens?{token:e.tokens.token,refreshToken:"dummy"}:null});return await io(t,e.tokens,i),t}return ii(e)}{try{await nS(a,s,{...iu(t),token:n})}catch(e){console.error("Hit error while running `auth:signOut`:"),console.error(e)}il("Clearing auth cookies",o);let e=ii(null);return await io(e,null,i),e}}async function id(e,t){let r=t.verbose??!1,n=t.cookieConfig??{maxAge:null};il("Begin handleAuthenticationInRequest",r);let i=new URL(e.url);await ih(e);let o=await ip(t),a=i.searchParams.get("code");if(a&&"GET"===e.method&&e.headers.get("accept")?.includes("text/html")){il("Handling code exchange for OAuth or magic link",r);let e=(await n7()).verifier??void 0,o=new URL(i);o.searchParams.delete("code");try{let i=await nS("auth:signIn",{params:{code:a},verifier:e},iu(t));if(void 0===i.tokens)throw Error("Invalid `signIn` action result for code exchange");let s=X.redirect(o);return await io(s,i.tokens,n),il(`Successfully validated code, redirecting to ${o.toString()} with auth cookies`,r),{kind:"redirect",response:s}}catch(t){console.error(t),il(`Error validating code, redirecting to ${o.toString()} and clearing auth cookies`,r);let e=X.redirect(o);return await io(e,null,n),{kind:"redirect",response:e}}}return{kind:"refreshTokens",refreshTokens:o}}async function ih(e){if(is(e)){let t=await ie(e);t.token=null,t.refreshToken=null,t.verifier=null}}async function ip(e){let t=e.verbose??!1,{token:r,refreshToken:n}=await n7();if(null===n&&null===r){il("No tokens to refresh, returning undefined",t);return}if(null===n||null===r)return il(`Refresh token null? ${null===n}, token null? ${null===r}, returning null`,t),null;let i=function(e){try{return function(e,t){let r;if("string"!=typeof e)throw new rv("Invalid token specified: must be a string");t||(t={});let n=+(!0!==t.header),i=e.split(".")[n];if("string"!=typeof i)throw new rv(`Invalid token specified: missing part #${n+1}`);try{r=function(e){let t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw Error("base64 string is not of the correct length")}try{var r;return r=t,decodeURIComponent(atob(r).replace(/(.)/g,(e,t)=>{let r=t.charCodeAt(0).toString(16).toUpperCase();return r.length<2&&(r="0"+r),"%"+r}))}catch(e){return atob(t)}}(i)}catch(e){throw new rv(`Invalid token specified: invalid base64 for part #${n+1} (${e.message})`)}try{return JSON.parse(r)}catch(e){throw new rv(`Invalid token specified: invalid json for part #${n+1} (${e.message})`)}}(e)}catch(e){return null}}(r);if(null===i)return il("Failed to decode token, returning null",t),null;let o=1e3*i.exp-1e3*i.iat,a=Date.now()+Math.min(6e4,Math.max(1e4,o/10));if(1e3*i.exp>a){il("Token expires far enough in the future, no need to refresh, returning undefined",t);return}try{let r=await nS("auth:signIn",{refreshToken:n},iu(e));if(void 0===r.tokens)throw Error("Invalid `signIn` action result for token refresh");return il(`Successfully refreshed tokens: is null? ${null===r.tokens}`,t),r.tokens}catch(e){return console.error(e),il("Failed to refresh tokens, returning null",t),null}}function ig(e,t){let r=e.nextUrl.clone();return r.pathname=t,X.redirect(r)}async function iv(e,t){if(!e)return!1;try{return await nR("auth:isAuthenticated",{},{...iu(t),token:e})}catch(e){if(e.message.includes("Could not find public function"))throw Error("Server Error: could not find api.auth.isAuthenticated. convex-auth 0.0.76 introduced a new export in convex/auth.ts. Add `isAuthenticated` to the list of functions returned from convexAuth(). See convex-auth changelog for more https://github.com/get-convex/convex-auth/blob/main/CHANGELOG.md");return console.log("Returning false from isAuthenticated because",e),!1}}let ib=tr(["/signin"]),im=tr(["/","/server"]),iy=function(e,t={}){return async(r,n)=>{let i=t.verbose??!1,o=t.cookieConfig??{maxAge:null};if(null!==o.maxAge&&o.maxAge<=0)throw Error("cookieConfig.maxAge must be null or a positive number of seconds");il(`Begin middleware for request with URL ${r.url}`,i);let a=new URL(r.url),s=t?.apiRoute??"/api/auth";if(function(e,t){let r=new URL(e.url);return t.endsWith("/")?r.pathname===t||r.pathname===t.slice(0,-1):r.pathname===t||r.pathname===t+"/"}(r,s))return il(`Proxying auth action to Convex, path matches ${s} with or without trailing slash`,i),await ic(r,t);il(`Not proxying auth action to Convex, path ${a.pathname} does not match ${s}`,i);let l=await id(r,t);if("redirect"===l.kind)return il(`Redirecting to ${l.response.headers.get("Location")}`,i),l.response;let u=null;if("refreshTokens"===l.kind&&void 0!==l.refreshTokens&&(il("Forwarding cookies to request",i),await ia(r,l.refreshTokens)),void 0===e?(il("No custom handler",i),u=X.next({request:{headers:r.headers}})):(il("Calling custom handler",i),u=await e(r,{event:n,convexAuth:{getToken:async()=>(await ie(r)).token??void 0,isAuthenticated:async()=>iv((await ie(r)).token,t)}})??X.next({request:{headers:r.headers}})),"refreshTokens"===l.kind&&void 0!==l.refreshTokens){let e=X.next(u);return await io(e,l.refreshTokens,o),e}return u}}(async(e,{convexAuth:t})=>ib(e)&&await t.isAuthenticated()?ig(e,"/"):im(e)&&!await t.isAuthenticated()?ig(e,"/signin"):void 0),iw={matcher:["/((?!.*\\..*|_next).*)","/","/(api|trpc)(.*)"]};Object.values({NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401});let i_={...s},iE=i_.middleware||i_.default,ix="/middleware";if("function"!=typeof iE)throw Object.defineProperty(Error(`The Middleware "${ix}" must export a \`middleware\` or a \`default\` function`),"__NEXT_ERROR_CODE",{value:"E120",enumerable:!1,configurable:!0});function iO(e){return e7({...e,page:ix,handler:async(...e)=>{try{return await iE(...e)}catch(i){let t=e[0],r=new URL(t.url),n=r.pathname+r.search;throw await d(i,{path:n,method:t.method,headers:Object.fromEntries(t.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),i}}})}},724:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o={};function a(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function s(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,i]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=i?i:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[n,i],...o]=s(e),{domain:a,expires:l,httponly:d,maxage:h,path:f,samesite:p,secure:g,partitioned:v,priority:b}=Object.fromEntries(o.map(([e,t])=>[e.toLowerCase().replace(/-/g,""),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:n,value:decodeURIComponent(i),domain:a,...l&&{expires:new Date(l)},...d&&{httpOnly:!0},..."string"==typeof h&&{maxAge:Number(h)},path:f,...p&&{sameSite:u.includes(t=(t=p).toLowerCase())?t:void 0},...g&&{secure:!0},...b&&{priority:c.includes(r=(r=b).toLowerCase())?r:void 0},...v&&{partitioned:!0}})}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(o,{RequestCookies:()=>d,ResponseCookies:()=>h,parseCookie:()=>s,parseSetCookie:()=>l,stringifyCookie:()=>a}),e.exports=((e,o,a,s)=>{if(o&&"object"==typeof o||"function"==typeof o)for(let l of n(o))i.call(e,l)||l===a||t(e,l,{get:()=>o[l],enumerable:!(s=r(o,l))||s.enumerable});return e})(t({},"__esModule",{value:!0}),o);var u=["strict","lax","none"],c=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of s(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},h=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let i=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(i)?i:function(e){if(!e)return[];var t,r,n,i,o,a=[],s=0;function l(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,o=!1;l();)if(","===(r=e.charAt(s))){for(n=s,s+=1,l(),i=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(o=!0,s=i,a.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!o||s>=e.length)&&a.push(e.substring(t,e.length))}return a}(i)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,i=this._parsed;return i.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=a(r);t.append("set-cookie",e)}}(i,this._headers),this}delete(...e){let[t,r]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set({...r,name:t,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},802:e=>{(()=>{"use strict";var t={993:e=>{var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function o(e,t,n,o,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var s=new i(n,o||e,a),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],s]:e._events[l].push(s):(e._events[l]=s,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function s(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),s.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},s.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,o=n.length,a=Array(o);i<o;i++)a[i]=n[i].fn;return a},s.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},s.prototype.emit=function(e,t,n,i,o,a){var s=r?r+e:e;if(!this._events[s])return!1;var l,u,c=this._events[s],d=arguments.length;if(c.fn){switch(c.once&&this.removeListener(e,c.fn,void 0,!0),d){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,t),!0;case 3:return c.fn.call(c.context,t,n),!0;case 4:return c.fn.call(c.context,t,n,i),!0;case 5:return c.fn.call(c.context,t,n,i,o),!0;case 6:return c.fn.call(c.context,t,n,i,o,a),!0}for(u=1,l=Array(d-1);u<d;u++)l[u-1]=arguments[u];c.fn.apply(c.context,l)}else{var h,f=c.length;for(u=0;u<f;u++)switch(c[u].once&&this.removeListener(e,c[u].fn,void 0,!0),d){case 1:c[u].fn.call(c[u].context);break;case 2:c[u].fn.call(c[u].context,t);break;case 3:c[u].fn.call(c[u].context,t,n);break;case 4:c[u].fn.call(c[u].context,t,n,i);break;default:if(!l)for(h=1,l=Array(d-1);h<d;h++)l[h-1]=arguments[h];c[u].fn.apply(c[u].context,l)}}return!0},s.prototype.on=function(e,t,r){return o(this,e,t,r,!1)},s.prototype.once=function(e,t,r){return o(this,e,t,r,!0)},s.prototype.removeListener=function(e,t,n,i){var o=r?r+e:e;if(!this._events[o])return this;if(!t)return a(this,o),this;var s=this._events[o];if(s.fn)s.fn!==t||i&&!s.once||n&&s.context!==n||a(this,o);else{for(var l=0,u=[],c=s.length;l<c;l++)(s[l].fn!==t||i&&!s[l].once||n&&s[l].context!==n)&&u.push(s[l]);u.length?this._events[o]=1===u.length?u[0]:u:a(this,o)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=r,s.EventEmitter=s,e.exports=s},213:e=>{e.exports=(e,t)=>(t=t||(()=>{}),e.then(e=>new Promise(e=>{e(t())}).then(()=>e),e=>new Promise(e=>{e(t())}).then(()=>{throw e})))},574:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){let n=0,i=e.length;for(;i>0;){let o=i/2|0,a=n+o;0>=r(e[a],t)?(n=++a,i-=o+1):i=o}return n}},821:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});let n=r(574);class i{constructor(){this._queue=[]}enqueue(e,t){let r={priority:(t=Object.assign({priority:0},t)).priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority){this._queue.push(r);return}let i=n.default(this._queue,r,(e,t)=>t.priority-e.priority);this._queue.splice(i,0,r)}dequeue(){let e=this._queue.shift();return null==e?void 0:e.run}filter(e){return this._queue.filter(t=>t.priority===e.priority).map(e=>e.run)}get size(){return this._queue.length}}t.default=i},816:(e,t,r)=>{let n=r(213);class i extends Error{constructor(e){super(e),this.name="TimeoutError"}}let o=(e,t,r)=>new Promise((o,a)=>{if("number"!=typeof t||t<0)throw TypeError("Expected `milliseconds` to be a positive number");if(t===1/0){o(e);return}let s=setTimeout(()=>{if("function"==typeof r){try{o(r())}catch(e){a(e)}return}let n="string"==typeof r?r:`Promise timed out after ${t} milliseconds`,s=r instanceof Error?r:new i(n);"function"==typeof e.cancel&&e.cancel(),a(s)},t);n(e.then(o,a),()=>{clearTimeout(s)})});e.exports=o,e.exports.default=o,e.exports.TimeoutError=i}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var o=r[e]={exports:{}},a=!0;try{t[e](o,o.exports,n),a=!1}finally{a&&delete r[e]}return o.exports}n.ab="//";var i={};(()=>{Object.defineProperty(i,"__esModule",{value:!0});let e=n(993),t=n(816),r=n(821),o=()=>{},a=new t.TimeoutError;class s extends e{constructor(e){var t,n,i,a;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=o,this._resolveIdle=o,!("number"==typeof(e=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:r.default},e)).intervalCap&&e.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!==(n=null===(t=e.intervalCap)||void 0===t?void 0:t.toString())&&void 0!==n?n:""}\` (${typeof e.intervalCap})`);if(void 0===e.interval||!(Number.isFinite(e.interval)&&e.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!==(a=null===(i=e.interval)||void 0===i?void 0:i.toString())&&void 0!==a?a:""}\` (${typeof e.interval})`);this._carryoverConcurrencyCount=e.carryoverConcurrencyCount,this._isIntervalIgnored=e.intervalCap===1/0||0===e.interval,this._intervalCap=e.intervalCap,this._interval=e.interval,this._queue=new e.queueClass,this._queueClass=e.queueClass,this.concurrency=e.concurrency,this._timeout=e.timeout,this._throwOnTimeout=!0===e.throwOnTimeout,this._isPaused=!1===e.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=o,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=o,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let e=Date.now();if(void 0===this._intervalId){let t=this._intervalEnd-e;if(!(t<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},t)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let t=this._queue.dequeue();return!!t&&(this.emit("active"),t(),e&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){!this._isIntervalIgnored&&void 0===this._intervalId&&(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(e){if(!("number"==typeof e&&e>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);this._concurrency=e,this._processQueue()}async add(e,r={}){return new Promise((n,i)=>{let o=async()=>{this._pendingCount++,this._intervalCount++;try{let o=void 0===this._timeout&&void 0===r.timeout?e():t.default(Promise.resolve(e()),void 0===r.timeout?this._timeout:r.timeout,()=>{(void 0===r.throwOnTimeout?this._throwOnTimeout:r.throwOnTimeout)&&i(a)});n(await o)}catch(e){i(e)}this._next()};this._queue.enqueue(o,r),this._tryToStartAnother(),this.emit("add")})}async addAll(e,t){return Promise.all(e.map(async e=>this.add(e,t)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(e=>{let t=this._resolveEmpty;this._resolveEmpty=()=>{t(),e()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(e=>{let t=this._resolveIdle;this._resolveIdle=()=>{t(),e()}})}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}i.default=s})(),e.exports=i})()},815:(e,t,r)=>{"use strict";e.exports=r(35)},890:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},o=t.split(n),a=(r||{}).decode||e,s=0;s<o.length;s++){var l=o[s],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),d=l.substr(++u,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[c]&&(i[c]=function(e,t){try{return t(e)}catch(t){return e}}(d,a))}}return i},t.serialize=function(e,t,n){var o=n||{},a=o.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var s=a(t);if(s&&!i.test(s))throw TypeError("argument val is invalid");var l=e+"="+s;if(null!=o.maxAge){var u=o.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(o.domain){if(!i.test(o.domain))throw TypeError("option domain is invalid");l+="; Domain="+o.domain}if(o.path){if(!i.test(o.path))throw TypeError("option path is invalid");l+="; Path="+o.path}if(o.expires){if("function"!=typeof o.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+o.expires.toUTCString()}if(o.httpOnly&&(l+="; HttpOnly"),o.secure&&(l+="; Secure"),o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},905:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return o},wrapRequestHandler:function(){return a}});let n=r(201),i=r(552);function o(){return(0,i.interceptFetch)(r.g.fetch)}function a(e){return(t,r)=>(0,n.withRequest)(t,i.reader,()=>e(t,r))}},956:(e,t,r)=>{(()=>{"use strict";var t={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let n=r(223),i=r(172),o=r(930),a="context",s=new n.NoopContextManager;class l{constructor(){}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalContextManager(e){return(0,i.registerGlobal)(a,e,o.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...n){return this._getContextManager().with(e,t,r,...n)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,i.getGlobal)(a)||s}disable(){this._getContextManager().disable(),(0,i.unregisterGlobal)(a,o.DiagAPI.instance())}}t.ContextAPI=l},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let n=r(56),i=r(912),o=r(957),a=r(172);class s{constructor(){function e(e){return function(...t){let r=(0,a.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:o.DiagLogLevel.INFO})=>{var n,s,l;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!==(n=e.stack)&&void 0!==n?n:e.message),!1}"number"==typeof r&&(r={logLevel:r});let u=(0,a.getGlobal)("diag"),c=(0,i.createLogLevelDiagLogger)(null!==(s=r.logLevel)&&void 0!==s?s:o.DiagLogLevel.INFO,e);if(u&&!r.suppressOverrideMessage){let e=null!==(l=Error().stack)&&void 0!==l?l:"<failed to generate stacktrace>";u.warn(`Current logger will be overwritten from ${e}`),c.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,a.registerGlobal)("diag",c,t,!0)},t.disable=()=>{(0,a.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new n.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new s),this._instance}}t.DiagAPI=s},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let n=r(660),i=r(172),o=r(930),a="metrics";class s{constructor(){}static getInstance(){return this._instance||(this._instance=new s),this._instance}setGlobalMeterProvider(e){return(0,i.registerGlobal)(a,e,o.DiagAPI.instance())}getMeterProvider(){return(0,i.getGlobal)(a)||n.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,i.unregisterGlobal)(a,o.DiagAPI.instance())}}t.MetricsAPI=s},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let n=r(172),i=r(874),o=r(194),a=r(277),s=r(369),l=r(930),u="propagation",c=new i.NoopTextMapPropagator;class d{constructor(){this.createBaggage=s.createBaggage,this.getBaggage=a.getBaggage,this.getActiveBaggage=a.getActiveBaggage,this.setBaggage=a.setBaggage,this.deleteBaggage=a.deleteBaggage}static getInstance(){return this._instance||(this._instance=new d),this._instance}setGlobalPropagator(e){return(0,n.registerGlobal)(u,e,l.DiagAPI.instance())}inject(e,t,r=o.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=o.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,n.unregisterGlobal)(u,l.DiagAPI.instance())}_getGlobalPropagator(){return(0,n.getGlobal)(u)||c}}t.PropagationAPI=d},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let n=r(172),i=r(846),o=r(139),a=r(607),s=r(930),l="trace";class u{constructor(){this._proxyTracerProvider=new i.ProxyTracerProvider,this.wrapSpanContext=o.wrapSpanContext,this.isSpanContextValid=o.isSpanContextValid,this.deleteSpan=a.deleteSpan,this.getSpan=a.getSpan,this.getActiveSpan=a.getActiveSpan,this.getSpanContext=a.getSpanContext,this.setSpan=a.setSpan,this.setSpanContext=a.setSpanContext}static getInstance(){return this._instance||(this._instance=new u),this._instance}setGlobalTracerProvider(e){let t=(0,n.registerGlobal)(l,this._proxyTracerProvider,s.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,n.getGlobal)(l)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,n.unregisterGlobal)(l,s.DiagAPI.instance()),this._proxyTracerProvider=new i.ProxyTracerProvider}}t.TraceAPI=u},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let n=r(491),i=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function o(e){return e.getValue(i)||void 0}t.getBaggage=o,t.getActiveBaggage=function(){return o(n.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(i,t)},t.deleteBaggage=function(e){return e.deleteValue(i)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let n=new r(this._entries);return n._entries.set(e,t),n}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let n=r(930),i=r(993),o=r(830),a=n.DiagAPI.instance();t.createBaggage=function(e={}){return new i.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(a.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:o.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0,t.context=r(491).ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let n=r(780);class i{active(){return n.ROOT_CONTEXT}with(e,t,r,...n){return t.call(r,...n)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=i},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,n)=>{let i=new r(t._currentContext);return i._currentContext.set(e,n),i},t.deleteValue=e=>{let n=new r(t._currentContext);return n._currentContext.delete(e),n}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0,t.diag=r(930).DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let n=r(172);class i{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return o("debug",this._namespace,e)}error(...e){return o("error",this._namespace,e)}info(...e){return o("info",this._namespace,e)}warn(...e){return o("warn",this._namespace,e)}verbose(...e){return o("verbose",this._namespace,e)}}function o(e,t,r){let i=(0,n.getGlobal)("diag");if(i)return r.unshift(t),i[e](...r)}t.DiagComponentLogger=i},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class n{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}t.DiagConsoleLogger=n},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let n=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,n){let i=t[r];return"function"==typeof i&&e>=n?i.bind(t):function(){}}return e<n.DiagLogLevel.NONE?e=n.DiagLogLevel.NONE:e>n.DiagLogLevel.ALL&&(e=n.DiagLogLevel.ALL),t=t||{},{error:r("error",n.DiagLogLevel.ERROR),warn:r("warn",n.DiagLogLevel.WARN),info:r("info",n.DiagLogLevel.INFO),debug:r("debug",n.DiagLogLevel.DEBUG),verbose:r("verbose",n.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let n=r(200),i=r(521),o=r(130),a=i.VERSION.split(".")[0],s=Symbol.for(`opentelemetry.js.api.${a}`),l=n._globalThis;t.registerGlobal=function(e,t,r,n=!1){var o;let a=l[s]=null!==(o=l[s])&&void 0!==o?o:{version:i.VERSION};if(!n&&a[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(a.version!==i.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${a.version} for ${e} does not match previously registered API v${i.VERSION}`);return r.error(t.stack||t.message),!1}return a[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${i.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let n=null===(t=l[s])||void 0===t?void 0:t.version;if(n&&(0,o.isCompatible)(n))return null===(r=l[s])||void 0===r?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${i.VERSION}.`);let r=l[s];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let n=r(521),i=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function o(e){let t=new Set([e]),r=new Set,n=e.match(i);if(!n)return()=>!1;let o={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=o.prerelease)return function(t){return t===e};function a(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let n=e.match(i);if(!n)return a(e);let s={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=s.prerelease||o.major!==s.major)return a(e);if(0===o.major)return o.minor===s.minor&&o.patch<=s.patch?(t.add(e),!0):a(e);return o.minor<=s.minor?(t.add(e),!0):a(e)}}t._makeCompatibilityCheck=o,t.isCompatible=o(n.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0,t.metrics=r(653).MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class n{}t.NoopMetric=n;class i extends n{add(e,t){}}t.NoopCounterMetric=i;class o extends n{add(e,t){}}t.NoopUpDownCounterMetric=o;class a extends n{record(e,t){}}t.NoopHistogramMetric=a;class s{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=s;class l extends s{}t.NoopObservableCounterMetric=l;class u extends s{}t.NoopObservableGaugeMetric=u;class c extends s{}t.NoopObservableUpDownCounterMetric=c,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new i,t.NOOP_HISTOGRAM_METRIC=new a,t.NOOP_UP_DOWN_COUNTER_METRIC=new o,t.NOOP_OBSERVABLE_COUNTER_METRIC=new l,t.NOOP_OBSERVABLE_GAUGE_METRIC=new u,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new c,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let n=r(102);class i{getMeter(e,t,r){return n.NOOP_METER}}t.NoopMeterProvider=i,t.NOOP_METER_PROVIDER=new i},200:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:r.g},46:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0,t.propagation=r(181).PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class r{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=r},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0,t.trace=r(997).TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let n=r(476);class i{constructor(e=n.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=i},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let n=r(491),i=r(607),o=r(403),a=r(139),s=n.ContextAPI.getInstance();class l{startSpan(e,t,r=s.active()){var n;if(null==t?void 0:t.root)return new o.NonRecordingSpan;let l=r&&(0,i.getSpanContext)(r);return"object"==typeof(n=l)&&"string"==typeof n.spanId&&"string"==typeof n.traceId&&"number"==typeof n.traceFlags&&(0,a.isSpanContextValid)(l)?new o.NonRecordingSpan(l):new o.NonRecordingSpan}startActiveSpan(e,t,r,n){let o,a,l;if(arguments.length<2)return;2==arguments.length?l=t:3==arguments.length?(o=t,l=r):(o=t,a=r,l=n);let u=null!=a?a:s.active(),c=this.startSpan(e,o,u),d=(0,i.setSpan)(u,c);return s.with(d,l,void 0,c)}}t.NoopTracer=l},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let n=r(614);class i{getTracer(e,t,r){return new n.NoopTracer}}t.NoopTracerProvider=i},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let n=new(r(614)).NoopTracer;class i{constructor(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,n){let i=this._getTracer();return Reflect.apply(i.startActiveSpan,i,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):n}}t.ProxyTracer=i},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let n=r(125),i=new(r(124)).NoopTracerProvider;class o{getTracer(e,t,r){var i;return null!==(i=this.getDelegateTracer(e,t,r))&&void 0!==i?i:new n.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!==(e=this._delegate)&&void 0!==e?e:i}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var n;return null===(n=this._delegate)||void 0===n?void 0:n.getTracer(e,t,r)}}t.ProxyTracerProvider=o},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let n=r(780),i=r(403),o=r(491),a=(0,n.createContextKey)("OpenTelemetry Context Key SPAN");function s(e){return e.getValue(a)||void 0}function l(e,t){return e.setValue(a,t)}t.getSpan=s,t.getActiveSpan=function(){return s(o.ContextAPI.getInstance().active())},t.setSpan=l,t.deleteSpan=function(e){return e.deleteValue(a)},t.setSpanContext=function(e,t){return l(e,new i.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null===(t=s(e))||void 0===t?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let n=r(564);class i{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),i=r.indexOf("=");if(-1!==i){let o=r.slice(0,i),a=r.slice(i+1,t.length);(0,n.validateKey)(o)&&(0,n.validateValue)(a)&&e.set(o,a)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new i;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=i},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",n=`[a-z]${r}{0,255}`,i=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,o=RegExp(`^(?:${n}|${i})$`),a=/^[ -~]{0,255}[!-~]$/,s=/,|=/;t.validateKey=function(e){return o.test(e)},t.validateValue=function(e){return a.test(e)&&!s.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let n=r(325);t.createTraceState=function(e){return new n.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let n=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:n.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let n=r(476),i=r(403),o=/^([0-9a-f]{32})$/i,a=/^[0-9a-f]{16}$/i;function s(e){return o.test(e)&&e!==n.INVALID_TRACEID}function l(e){return a.test(e)&&e!==n.INVALID_SPANID}t.isValidTraceId=s,t.isValidSpanId=l,t.isSpanContextValid=function(e){return s(e.traceId)&&l(e.spanId)},t.wrapSpanContext=function(e){return new i.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},n={};function i(e){var r=n[e];if(void 0!==r)return r.exports;var o=n[e]={exports:{}},a=!0;try{t[e].call(o.exports,o,o.exports,i),a=!1}finally{a&&delete n[e]}return o.exports}i.ab="//";var o={};(()=>{Object.defineProperty(o,"__esModule",{value:!0}),o.trace=o.propagation=o.metrics=o.diag=o.context=o.INVALID_SPAN_CONTEXT=o.INVALID_TRACEID=o.INVALID_SPANID=o.isValidSpanId=o.isValidTraceId=o.isSpanContextValid=o.createTraceState=o.TraceFlags=o.SpanStatusCode=o.SpanKind=o.SamplingDecision=o.ProxyTracerProvider=o.ProxyTracer=o.defaultTextMapSetter=o.defaultTextMapGetter=o.ValueType=o.createNoopMeter=o.DiagLogLevel=o.DiagConsoleLogger=o.ROOT_CONTEXT=o.createContextKey=o.baggageEntryMetadataFromString=void 0;var e=i(369);Object.defineProperty(o,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=i(780);Object.defineProperty(o,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(o,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=i(972);Object.defineProperty(o,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var n=i(957);Object.defineProperty(o,"DiagLogLevel",{enumerable:!0,get:function(){return n.DiagLogLevel}});var a=i(102);Object.defineProperty(o,"createNoopMeter",{enumerable:!0,get:function(){return a.createNoopMeter}});var s=i(901);Object.defineProperty(o,"ValueType",{enumerable:!0,get:function(){return s.ValueType}});var l=i(194);Object.defineProperty(o,"defaultTextMapGetter",{enumerable:!0,get:function(){return l.defaultTextMapGetter}}),Object.defineProperty(o,"defaultTextMapSetter",{enumerable:!0,get:function(){return l.defaultTextMapSetter}});var u=i(125);Object.defineProperty(o,"ProxyTracer",{enumerable:!0,get:function(){return u.ProxyTracer}});var c=i(846);Object.defineProperty(o,"ProxyTracerProvider",{enumerable:!0,get:function(){return c.ProxyTracerProvider}});var d=i(996);Object.defineProperty(o,"SamplingDecision",{enumerable:!0,get:function(){return d.SamplingDecision}});var h=i(357);Object.defineProperty(o,"SpanKind",{enumerable:!0,get:function(){return h.SpanKind}});var f=i(847);Object.defineProperty(o,"SpanStatusCode",{enumerable:!0,get:function(){return f.SpanStatusCode}});var p=i(475);Object.defineProperty(o,"TraceFlags",{enumerable:!0,get:function(){return p.TraceFlags}});var g=i(98);Object.defineProperty(o,"createTraceState",{enumerable:!0,get:function(){return g.createTraceState}});var v=i(139);Object.defineProperty(o,"isSpanContextValid",{enumerable:!0,get:function(){return v.isSpanContextValid}}),Object.defineProperty(o,"isValidTraceId",{enumerable:!0,get:function(){return v.isValidTraceId}}),Object.defineProperty(o,"isValidSpanId",{enumerable:!0,get:function(){return v.isValidSpanId}});var b=i(476);Object.defineProperty(o,"INVALID_SPANID",{enumerable:!0,get:function(){return b.INVALID_SPANID}}),Object.defineProperty(o,"INVALID_TRACEID",{enumerable:!0,get:function(){return b.INVALID_TRACEID}}),Object.defineProperty(o,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return b.INVALID_SPAN_CONTEXT}});let m=i(67);Object.defineProperty(o,"context",{enumerable:!0,get:function(){return m.context}});let y=i(506);Object.defineProperty(o,"diag",{enumerable:!0,get:function(){return y.diag}});let w=i(886);Object.defineProperty(o,"metrics",{enumerable:!0,get:function(){return w.metrics}});let _=i(939);Object.defineProperty(o,"propagation",{enumerable:!0,get:function(){return _.propagation}});let E=i(845);Object.defineProperty(o,"trace",{enumerable:!0,get:function(){return E.trace}}),o.default={context:m.context,diag:y.diag,metrics:w.metrics,propagation:_.propagation,trace:E.trace}})(),e.exports=o})()}},e=>{var t=e(e.s=666);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES).middleware_middleware=t}]);
//# sourceMappingURL=middleware.js.map