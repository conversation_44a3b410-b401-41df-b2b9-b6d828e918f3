"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-server.edge.development.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-server.edge.development.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/**\n * @license React\n * react-server-dom-webpack-server.edge.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ \n true && function() {\n    function voidHandler() {}\n    function getIteratorFn(maybeIterable) {\n        if (null === maybeIterable || \"object\" !== typeof maybeIterable) return null;\n        maybeIterable = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[\"@@iterator\"];\n        return \"function\" === typeof maybeIterable ? maybeIterable : null;\n    }\n    function _defineProperty(obj, key, value) {\n        key in obj ? Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: !0,\n            configurable: !0,\n            writable: !0\n        }) : obj[key] = value;\n        return obj;\n    }\n    function handleErrorInNextTick(error) {\n        setTimeoutOrImmediate(function() {\n            throw error;\n        });\n    }\n    function writeChunkAndReturn(destination, chunk) {\n        if (0 !== chunk.byteLength) if (2048 < chunk.byteLength) 0 < writtenBytes && (destination.enqueue(new Uint8Array(currentView.buffer, 0, writtenBytes)), currentView = new Uint8Array(2048), writtenBytes = 0), destination.enqueue(chunk);\n        else {\n            var allowableBytes = currentView.length - writtenBytes;\n            allowableBytes < chunk.byteLength && (0 === allowableBytes ? destination.enqueue(currentView) : (currentView.set(chunk.subarray(0, allowableBytes), writtenBytes), destination.enqueue(currentView), chunk = chunk.subarray(allowableBytes)), currentView = new Uint8Array(2048), writtenBytes = 0);\n            currentView.set(chunk, writtenBytes);\n            writtenBytes += chunk.byteLength;\n        }\n        return !0;\n    }\n    function stringToChunk(content) {\n        return textEncoder.encode(content);\n    }\n    function byteLengthOfChunk(chunk) {\n        return chunk.byteLength;\n    }\n    function closeWithError(destination, error) {\n        \"function\" === typeof destination.error ? destination.error(error) : destination.close();\n    }\n    function isClientReference(reference) {\n        return reference.$$typeof === CLIENT_REFERENCE_TAG$1;\n    }\n    function registerClientReferenceImpl(proxyImplementation, id, async) {\n        return Object.defineProperties(proxyImplementation, {\n            $$typeof: {\n                value: CLIENT_REFERENCE_TAG$1\n            },\n            $$id: {\n                value: id\n            },\n            $$async: {\n                value: async\n            }\n        });\n    }\n    function bind() {\n        var newFn = FunctionBind.apply(this, arguments);\n        if (this.$$typeof === SERVER_REFERENCE_TAG) {\n            null != arguments[0] && console.error('Cannot bind \"this\" of a Server Action. Pass null or undefined as the first argument to .bind().');\n            var args = ArraySlice.call(arguments, 1), $$typeof = {\n                value: SERVER_REFERENCE_TAG\n            }, $$id = {\n                value: this.$$id\n            };\n            args = {\n                value: this.$$bound ? this.$$bound.concat(args) : args\n            };\n            return Object.defineProperties(newFn, {\n                $$typeof: $$typeof,\n                $$id: $$id,\n                $$bound: args,\n                $$location: {\n                    value: this.$$location,\n                    configurable: !0\n                },\n                bind: {\n                    value: bind,\n                    configurable: !0\n                }\n            });\n        }\n        return newFn;\n    }\n    function getReference(target, name) {\n        switch(name){\n            case \"$$typeof\":\n                return target.$$typeof;\n            case \"$$id\":\n                return target.$$id;\n            case \"$$async\":\n                return target.$$async;\n            case \"name\":\n                return target.name;\n            case \"defaultProps\":\n                return;\n            case \"toJSON\":\n                return;\n            case Symbol.toPrimitive:\n                return Object.prototype[Symbol.toPrimitive];\n            case Symbol.toStringTag:\n                return Object.prototype[Symbol.toStringTag];\n            case \"__esModule\":\n                var moduleId = target.$$id;\n                target.default = registerClientReferenceImpl(function() {\n                    throw Error(\"Attempted to call the default export of \" + moduleId + \" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\");\n                }, target.$$id + \"#\", target.$$async);\n                return !0;\n            case \"then\":\n                if (target.then) return target.then;\n                if (target.$$async) return;\n                var clientReference = registerClientReferenceImpl({}, target.$$id, !0), proxy = new Proxy(clientReference, proxyHandlers$1);\n                target.status = \"fulfilled\";\n                target.value = proxy;\n                return target.then = registerClientReferenceImpl(function(resolve) {\n                    return Promise.resolve(resolve(proxy));\n                }, target.$$id + \"#then\", !1);\n        }\n        if (\"symbol\" === typeof name) throw Error(\"Cannot read Symbol exports. Only named exports are supported on a client module imported on the server.\");\n        clientReference = target[name];\n        clientReference || (clientReference = registerClientReferenceImpl(function() {\n            throw Error(\"Attempted to call \" + String(name) + \"() from the server but \" + String(name) + \" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\");\n        }, target.$$id + \"#\" + name, target.$$async), Object.defineProperty(clientReference, \"name\", {\n            value: name\n        }), clientReference = target[name] = new Proxy(clientReference, deepProxyHandlers));\n        return clientReference;\n    }\n    function trimOptions(options) {\n        if (null == options) return null;\n        var hasProperties = !1, trimmed = {}, key;\n        for(key in options)null != options[key] && (hasProperties = !0, trimmed[key] = options[key]);\n        return hasProperties ? trimmed : null;\n    }\n    function prepareStackTrace(error, structuredStackTrace) {\n        error = (error.name || \"Error\") + \": \" + (error.message || \"\");\n        for(var i = 0; i < structuredStackTrace.length; i++)error += \"\\n    at \" + structuredStackTrace[i].toString();\n        return error;\n    }\n    function parseStackTrace(error, skipFrames) {\n        a: {\n            var previousPrepare = Error.prepareStackTrace;\n            Error.prepareStackTrace = prepareStackTrace;\n            try {\n                var stack = String(error.stack);\n                break a;\n            } finally{\n                Error.prepareStackTrace = previousPrepare;\n            }\n            stack = void 0;\n        }\n        stack.startsWith(\"Error: react-stack-top-frame\\n\") && (stack = stack.slice(29));\n        error = stack.indexOf(\"react-stack-bottom-frame\");\n        -1 !== error && (error = stack.lastIndexOf(\"\\n\", error));\n        -1 !== error && (stack = stack.slice(0, error));\n        stack = stack.split(\"\\n\");\n        for(error = []; skipFrames < stack.length; skipFrames++)if (previousPrepare = frameRegExp.exec(stack[skipFrames])) {\n            var name = previousPrepare[1] || \"\";\n            \"<anonymous>\" === name && (name = \"\");\n            var filename = previousPrepare[2] || previousPrepare[5] || \"\";\n            \"<anonymous>\" === filename && (filename = \"\");\n            error.push([\n                name,\n                filename,\n                +(previousPrepare[3] || previousPrepare[6]),\n                +(previousPrepare[4] || previousPrepare[7])\n            ]);\n        }\n        return error;\n    }\n    function createTemporaryReference(temporaryReferences, id) {\n        var reference = Object.defineProperties(function() {\n            throw Error(\"Attempted to call a temporary Client Reference from the server but it is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\");\n        }, {\n            $$typeof: {\n                value: TEMPORARY_REFERENCE_TAG\n            }\n        });\n        reference = new Proxy(reference, proxyHandlers);\n        temporaryReferences.set(reference, id);\n        return reference;\n    }\n    function noop$1() {}\n    function trackUsedThenable(thenableState, thenable, index) {\n        index = thenableState[index];\n        void 0 === index ? thenableState.push(thenable) : index !== thenable && (thenable.then(noop$1, noop$1), thenable = index);\n        switch(thenable.status){\n            case \"fulfilled\":\n                return thenable.value;\n            case \"rejected\":\n                throw thenable.reason;\n            default:\n                \"string\" === typeof thenable.status ? thenable.then(noop$1, noop$1) : (thenableState = thenable, thenableState.status = \"pending\", thenableState.then(function(fulfilledValue) {\n                    if (\"pending\" === thenable.status) {\n                        var fulfilledThenable = thenable;\n                        fulfilledThenable.status = \"fulfilled\";\n                        fulfilledThenable.value = fulfilledValue;\n                    }\n                }, function(error) {\n                    if (\"pending\" === thenable.status) {\n                        var rejectedThenable = thenable;\n                        rejectedThenable.status = \"rejected\";\n                        rejectedThenable.reason = error;\n                    }\n                }));\n                switch(thenable.status){\n                    case \"fulfilled\":\n                        return thenable.value;\n                    case \"rejected\":\n                        throw thenable.reason;\n                }\n                suspendedThenable = thenable;\n                throw SuspenseException;\n        }\n    }\n    function getSuspendedThenable() {\n        if (null === suspendedThenable) throw Error(\"Expected a suspended thenable. This is a bug in React. Please file an issue.\");\n        var thenable = suspendedThenable;\n        suspendedThenable = null;\n        return thenable;\n    }\n    function getThenableStateAfterSuspending() {\n        var state = thenableState || [];\n        state._componentDebugInfo = currentComponentDebugInfo;\n        thenableState = currentComponentDebugInfo = null;\n        return state;\n    }\n    function unsupportedHook() {\n        throw Error(\"This Hook is not supported in Server Components.\");\n    }\n    function unsupportedRefresh() {\n        throw Error(\"Refreshing the cache is not supported in Server Components.\");\n    }\n    function unsupportedContext() {\n        throw Error(\"Cannot read a Client Context from a Server Component.\");\n    }\n    function resolveOwner() {\n        if (currentOwner) return currentOwner;\n        if (supportsComponentStorage) {\n            var owner = componentStorage.getStore();\n            if (owner) return owner;\n        }\n        return null;\n    }\n    function isObjectPrototype(object) {\n        if (!object) return !1;\n        var ObjectPrototype = Object.prototype;\n        if (object === ObjectPrototype) return !0;\n        if (getPrototypeOf(object)) return !1;\n        object = Object.getOwnPropertyNames(object);\n        for(var i = 0; i < object.length; i++)if (!(object[i] in ObjectPrototype)) return !1;\n        return !0;\n    }\n    function isSimpleObject(object) {\n        if (!isObjectPrototype(getPrototypeOf(object))) return !1;\n        for(var names = Object.getOwnPropertyNames(object), i = 0; i < names.length; i++){\n            var descriptor = Object.getOwnPropertyDescriptor(object, names[i]);\n            if (!descriptor || !descriptor.enumerable && (\"key\" !== names[i] && \"ref\" !== names[i] || \"function\" !== typeof descriptor.get)) return !1;\n        }\n        return !0;\n    }\n    function objectName(object) {\n        return Object.prototype.toString.call(object).replace(/^\\[object (.*)\\]$/, function(m, p0) {\n            return p0;\n        });\n    }\n    function describeKeyForErrorMessage(key) {\n        var encodedKey = JSON.stringify(key);\n        return '\"' + key + '\"' === encodedKey ? key : encodedKey;\n    }\n    function describeValueForErrorMessage(value) {\n        switch(typeof value){\n            case \"string\":\n                return JSON.stringify(10 >= value.length ? value : value.slice(0, 10) + \"...\");\n            case \"object\":\n                if (isArrayImpl(value)) return \"[...]\";\n                if (null !== value && value.$$typeof === CLIENT_REFERENCE_TAG) return \"client\";\n                value = objectName(value);\n                return \"Object\" === value ? \"{...}\" : value;\n            case \"function\":\n                return value.$$typeof === CLIENT_REFERENCE_TAG ? \"client\" : (value = value.displayName || value.name) ? \"function \" + value : \"function\";\n            default:\n                return String(value);\n        }\n    }\n    function describeElementType(type) {\n        if (\"string\" === typeof type) return type;\n        switch(type){\n            case REACT_SUSPENSE_TYPE:\n                return \"Suspense\";\n            case REACT_SUSPENSE_LIST_TYPE:\n                return \"SuspenseList\";\n        }\n        if (\"object\" === typeof type) switch(type.$$typeof){\n            case REACT_FORWARD_REF_TYPE:\n                return describeElementType(type.render);\n            case REACT_MEMO_TYPE:\n                return describeElementType(type.type);\n            case REACT_LAZY_TYPE:\n                var payload = type._payload;\n                type = type._init;\n                try {\n                    return describeElementType(type(payload));\n                } catch (x) {}\n        }\n        return \"\";\n    }\n    function describeObjectForErrorMessage(objectOrArray, expandedName) {\n        var objKind = objectName(objectOrArray);\n        if (\"Object\" !== objKind && \"Array\" !== objKind) return objKind;\n        var start = -1, length = 0;\n        if (isArrayImpl(objectOrArray)) if (jsxChildrenParents.has(objectOrArray)) {\n            var type = jsxChildrenParents.get(objectOrArray);\n            objKind = \"<\" + describeElementType(type) + \">\";\n            for(var i = 0; i < objectOrArray.length; i++){\n                var value = objectOrArray[i];\n                value = \"string\" === typeof value ? value : \"object\" === typeof value && null !== value ? \"{\" + describeObjectForErrorMessage(value) + \"}\" : \"{\" + describeValueForErrorMessage(value) + \"}\";\n                \"\" + i === expandedName ? (start = objKind.length, length = value.length, objKind += value) : objKind = 15 > value.length && 40 > objKind.length + value.length ? objKind + value : objKind + \"{...}\";\n            }\n            objKind += \"</\" + describeElementType(type) + \">\";\n        } else {\n            objKind = \"[\";\n            for(type = 0; type < objectOrArray.length; type++)0 < type && (objKind += \", \"), i = objectOrArray[type], i = \"object\" === typeof i && null !== i ? describeObjectForErrorMessage(i) : describeValueForErrorMessage(i), \"\" + type === expandedName ? (start = objKind.length, length = i.length, objKind += i) : objKind = 10 > i.length && 40 > objKind.length + i.length ? objKind + i : objKind + \"...\";\n            objKind += \"]\";\n        }\n        else if (objectOrArray.$$typeof === REACT_ELEMENT_TYPE) objKind = \"<\" + describeElementType(objectOrArray.type) + \"/>\";\n        else {\n            if (objectOrArray.$$typeof === CLIENT_REFERENCE_TAG) return \"client\";\n            if (jsxPropsParents.has(objectOrArray)) {\n                objKind = jsxPropsParents.get(objectOrArray);\n                objKind = \"<\" + (describeElementType(objKind) || \"...\");\n                type = Object.keys(objectOrArray);\n                for(i = 0; i < type.length; i++){\n                    objKind += \" \";\n                    value = type[i];\n                    objKind += describeKeyForErrorMessage(value) + \"=\";\n                    var _value2 = objectOrArray[value];\n                    var _substr2 = value === expandedName && \"object\" === typeof _value2 && null !== _value2 ? describeObjectForErrorMessage(_value2) : describeValueForErrorMessage(_value2);\n                    \"string\" !== typeof _value2 && (_substr2 = \"{\" + _substr2 + \"}\");\n                    value === expandedName ? (start = objKind.length, length = _substr2.length, objKind += _substr2) : objKind = 10 > _substr2.length && 40 > objKind.length + _substr2.length ? objKind + _substr2 : objKind + \"...\";\n                }\n                objKind += \">\";\n            } else {\n                objKind = \"{\";\n                type = Object.keys(objectOrArray);\n                for(i = 0; i < type.length; i++)0 < i && (objKind += \", \"), value = type[i], objKind += describeKeyForErrorMessage(value) + \": \", _value2 = objectOrArray[value], _value2 = \"object\" === typeof _value2 && null !== _value2 ? describeObjectForErrorMessage(_value2) : describeValueForErrorMessage(_value2), value === expandedName ? (start = objKind.length, length = _value2.length, objKind += _value2) : objKind = 10 > _value2.length && 40 > objKind.length + _value2.length ? objKind + _value2 : objKind + \"...\";\n                objKind += \"}\";\n            }\n        }\n        return void 0 === expandedName ? objKind : -1 < start && 0 < length ? (objectOrArray = \" \".repeat(start) + \"^\".repeat(length), \"\\n  \" + objKind + \"\\n  \" + objectOrArray) : \"\\n  \" + objKind;\n    }\n    function defaultFilterStackFrame(filename) {\n        return \"\" !== filename && !filename.startsWith(\"node:\") && !filename.includes(\"node_modules\");\n    }\n    function filterStackTrace(request, error, skipFrames) {\n        request = request.filterStackFrame;\n        error = parseStackTrace(error, skipFrames);\n        for(skipFrames = 0; skipFrames < error.length; skipFrames++){\n            var callsite = error[skipFrames], functionName = callsite[0], url = callsite[1];\n            if (url.startsWith(\"rsc://React/\")) {\n                var envIdx = url.indexOf(\"/\", 12), suffixIdx = url.lastIndexOf(\"?\");\n                -1 < envIdx && -1 < suffixIdx && (url = callsite[1] = url.slice(envIdx + 1, suffixIdx));\n            }\n            request(url, functionName) || (error.splice(skipFrames, 1), skipFrames--);\n        }\n        return error;\n    }\n    function patchConsole(consoleInst, methodName) {\n        var descriptor = Object.getOwnPropertyDescriptor(consoleInst, methodName);\n        if (descriptor && (descriptor.configurable || descriptor.writable) && \"function\" === typeof descriptor.value) {\n            var originalMethod = descriptor.value;\n            descriptor = Object.getOwnPropertyDescriptor(originalMethod, \"name\");\n            var wrapperMethod = function() {\n                var request = resolveRequest();\n                if ((\"assert\" !== methodName || !arguments[0]) && null !== request) {\n                    var stack = filterStackTrace(request, Error(\"react-stack-top-frame\"), 1);\n                    request.pendingChunks++;\n                    var owner = resolveOwner();\n                    emitConsoleChunk(request, methodName, owner, stack, arguments);\n                }\n                return originalMethod.apply(this, arguments);\n            };\n            descriptor && Object.defineProperty(wrapperMethod, \"name\", descriptor);\n            Object.defineProperty(consoleInst, methodName, {\n                value: wrapperMethod\n            });\n        }\n    }\n    function getCurrentStackInDEV() {\n        var owner = resolveOwner();\n        if (null === owner) return \"\";\n        try {\n            var info = \"\";\n            if (owner.owner || \"string\" !== typeof owner.name) {\n                for(; owner;){\n                    var ownerStack = owner.debugStack;\n                    if (null != ownerStack) {\n                        if (owner = owner.owner) {\n                            var JSCompiler_temp_const = info;\n                            var error = ownerStack, prevPrepareStackTrace = Error.prepareStackTrace;\n                            Error.prepareStackTrace = prepareStackTrace;\n                            var stack = error.stack;\n                            Error.prepareStackTrace = prevPrepareStackTrace;\n                            stack.startsWith(\"Error: react-stack-top-frame\\n\") && (stack = stack.slice(29));\n                            var idx = stack.indexOf(\"\\n\");\n                            -1 !== idx && (stack = stack.slice(idx + 1));\n                            idx = stack.indexOf(\"react-stack-bottom-frame\");\n                            -1 !== idx && (idx = stack.lastIndexOf(\"\\n\", idx));\n                            var JSCompiler_inline_result = -1 !== idx ? stack = stack.slice(0, idx) : \"\";\n                            info = JSCompiler_temp_const + (\"\\n\" + JSCompiler_inline_result);\n                        }\n                    } else break;\n                }\n                var JSCompiler_inline_result$jscomp$0 = info;\n            } else {\n                JSCompiler_temp_const = owner.name;\n                if (void 0 === prefix) try {\n                    throw Error();\n                } catch (x) {\n                    prefix = (error = x.stack.trim().match(/\\n( *(at )?)/)) && error[1] || \"\", suffix = -1 < x.stack.indexOf(\"\\n    at\") ? \" (<anonymous>)\" : -1 < x.stack.indexOf(\"@\") ? \"@unknown:0:0\" : \"\";\n                }\n                JSCompiler_inline_result$jscomp$0 = \"\\n\" + prefix + JSCompiler_temp_const + suffix;\n            }\n        } catch (x) {\n            JSCompiler_inline_result$jscomp$0 = \"\\nError generating stack: \" + x.message + \"\\n\" + x.stack;\n        }\n        return JSCompiler_inline_result$jscomp$0;\n    }\n    function defaultErrorHandler(error) {\n        console.error(error);\n    }\n    function defaultPostponeHandler() {}\n    function RequestInstance(type, model, bundlerConfig, onError, identifierPrefix, onPostpone, temporaryReferences, environmentName, filterStackFrame, onAllReady, onFatalError) {\n        if (null !== ReactSharedInternalsServer.A && ReactSharedInternalsServer.A !== DefaultAsyncDispatcher) throw Error(\"Currently React only supports one RSC renderer at a time.\");\n        ReactSharedInternalsServer.A = DefaultAsyncDispatcher;\n        ReactSharedInternalsServer.getCurrentStack = getCurrentStackInDEV;\n        var abortSet = new Set(), pingedTasks = [], hints = new Set();\n        this.type = type;\n        this.status = OPENING;\n        this.flushScheduled = !1;\n        this.destination = this.fatalError = null;\n        this.bundlerConfig = bundlerConfig;\n        this.cache = new Map();\n        this.pendingChunks = this.nextChunkId = 0;\n        this.hints = hints;\n        this.abortListeners = new Set();\n        this.abortableTasks = abortSet;\n        this.pingedTasks = pingedTasks;\n        this.completedImportChunks = [];\n        this.completedHintChunks = [];\n        this.completedRegularChunks = [];\n        this.completedErrorChunks = [];\n        this.writtenSymbols = new Map();\n        this.writtenClientReferences = new Map();\n        this.writtenServerReferences = new Map();\n        this.writtenObjects = new WeakMap();\n        this.temporaryReferences = temporaryReferences;\n        this.identifierPrefix = identifierPrefix || \"\";\n        this.identifierCount = 1;\n        this.taintCleanupQueue = [];\n        this.onError = void 0 === onError ? defaultErrorHandler : onError;\n        this.onPostpone = void 0 === onPostpone ? defaultPostponeHandler : onPostpone;\n        this.onAllReady = onAllReady;\n        this.onFatalError = onFatalError;\n        this.environmentName = void 0 === environmentName ? function() {\n            return \"Server\";\n        } : \"function\" !== typeof environmentName ? function() {\n            return environmentName;\n        } : environmentName;\n        this.filterStackFrame = void 0 === filterStackFrame ? defaultFilterStackFrame : filterStackFrame;\n        this.didWarnForKey = null;\n        type = createTask(this, model, null, !1, abortSet, null, null, null);\n        pingedTasks.push(type);\n    }\n    function noop() {}\n    function resolveRequest() {\n        if (currentRequest) return currentRequest;\n        if (supportsRequestStorage) {\n            var store = requestStorage.getStore();\n            if (store) return store;\n        }\n        return null;\n    }\n    function serializeThenable(request, task, thenable) {\n        var newTask = createTask(request, null, task.keyPath, task.implicitSlot, request.abortableTasks, task.debugOwner, task.debugStack, task.debugTask);\n        (task = thenable._debugInfo) && forwardDebugInfo(request, newTask.id, task);\n        switch(thenable.status){\n            case \"fulfilled\":\n                return newTask.model = thenable.value, pingTask(request, newTask), newTask.id;\n            case \"rejected\":\n                return erroredTask(request, newTask, thenable.reason), newTask.id;\n            default:\n                if (request.status === ABORTING) return request.abortableTasks.delete(newTask), newTask.status = ABORTED, task = stringify(serializeByValueID(request.fatalError)), emitModelChunk(request, newTask.id, task), newTask.id;\n                \"string\" !== typeof thenable.status && (thenable.status = \"pending\", thenable.then(function(fulfilledValue) {\n                    \"pending\" === thenable.status && (thenable.status = \"fulfilled\", thenable.value = fulfilledValue);\n                }, function(error) {\n                    \"pending\" === thenable.status && (thenable.status = \"rejected\", thenable.reason = error);\n                }));\n        }\n        thenable.then(function(value) {\n            newTask.model = value;\n            pingTask(request, newTask);\n        }, function(reason) {\n            newTask.status === PENDING$1 && (erroredTask(request, newTask, reason), enqueueFlush(request));\n        });\n        return newTask.id;\n    }\n    function serializeReadableStream(request, task, stream) {\n        function progress(entry) {\n            if (!aborted) if (entry.done) request.abortListeners.delete(abortStream), entry = streamTask.id.toString(16) + \":C\\n\", request.completedRegularChunks.push(stringToChunk(entry)), enqueueFlush(request), aborted = !0;\n            else try {\n                streamTask.model = entry.value, request.pendingChunks++, tryStreamTask(request, streamTask), enqueueFlush(request), reader.read().then(progress, error);\n            } catch (x$0) {\n                error(x$0);\n            }\n        }\n        function error(reason) {\n            aborted || (aborted = !0, request.abortListeners.delete(abortStream), erroredTask(request, streamTask, reason), enqueueFlush(request), reader.cancel(reason).then(error, error));\n        }\n        function abortStream(reason) {\n            aborted || (aborted = !0, request.abortListeners.delete(abortStream), erroredTask(request, streamTask, reason), enqueueFlush(request), reader.cancel(reason).then(error, error));\n        }\n        var supportsBYOB = stream.supportsBYOB;\n        if (void 0 === supportsBYOB) try {\n            stream.getReader({\n                mode: \"byob\"\n            }).releaseLock(), supportsBYOB = !0;\n        } catch (x) {\n            supportsBYOB = !1;\n        }\n        var reader = stream.getReader(), streamTask = createTask(request, task.model, task.keyPath, task.implicitSlot, request.abortableTasks, task.debugOwner, task.debugStack, task.debugTask);\n        request.abortableTasks.delete(streamTask);\n        request.pendingChunks++;\n        task = streamTask.id.toString(16) + \":\" + (supportsBYOB ? \"r\" : \"R\") + \"\\n\";\n        request.completedRegularChunks.push(stringToChunk(task));\n        var aborted = !1;\n        request.abortListeners.add(abortStream);\n        reader.read().then(progress, error);\n        return serializeByValueID(streamTask.id);\n    }\n    function serializeAsyncIterable(request, task, iterable, iterator) {\n        function progress(entry) {\n            if (!aborted) if (entry.done) {\n                request.abortListeners.delete(abortIterable);\n                if (void 0 === entry.value) var endStreamRow = streamTask.id.toString(16) + \":C\\n\";\n                else try {\n                    var chunkId = outlineModel(request, entry.value);\n                    endStreamRow = streamTask.id.toString(16) + \":C\" + stringify(serializeByValueID(chunkId)) + \"\\n\";\n                } catch (x) {\n                    error(x);\n                    return;\n                }\n                request.completedRegularChunks.push(stringToChunk(endStreamRow));\n                enqueueFlush(request);\n                aborted = !0;\n            } else try {\n                streamTask.model = entry.value, request.pendingChunks++, tryStreamTask(request, streamTask), enqueueFlush(request), callIteratorInDEV(iterator, progress, error);\n            } catch (x$1) {\n                error(x$1);\n            }\n        }\n        function error(reason) {\n            aborted || (aborted = !0, request.abortListeners.delete(abortIterable), erroredTask(request, streamTask, reason), enqueueFlush(request), \"function\" === typeof iterator.throw && iterator.throw(reason).then(error, error));\n        }\n        function abortIterable(reason) {\n            aborted || (aborted = !0, request.abortListeners.delete(abortIterable), erroredTask(request, streamTask, reason), enqueueFlush(request), \"function\" === typeof iterator.throw && iterator.throw(reason).then(error, error));\n        }\n        var isIterator = iterable === iterator, streamTask = createTask(request, task.model, task.keyPath, task.implicitSlot, request.abortableTasks, task.debugOwner, task.debugStack, task.debugTask);\n        request.abortableTasks.delete(streamTask);\n        request.pendingChunks++;\n        task = streamTask.id.toString(16) + \":\" + (isIterator ? \"x\" : \"X\") + \"\\n\";\n        request.completedRegularChunks.push(stringToChunk(task));\n        (iterable = iterable._debugInfo) && forwardDebugInfo(request, streamTask.id, iterable);\n        var aborted = !1;\n        request.abortListeners.add(abortIterable);\n        callIteratorInDEV(iterator, progress, error);\n        return serializeByValueID(streamTask.id);\n    }\n    function emitHint(request, code, model) {\n        model = stringify(model);\n        code = stringToChunk(\":H\" + code + model + \"\\n\");\n        request.completedHintChunks.push(code);\n        enqueueFlush(request);\n    }\n    function readThenable(thenable) {\n        if (\"fulfilled\" === thenable.status) return thenable.value;\n        if (\"rejected\" === thenable.status) throw thenable.reason;\n        throw thenable;\n    }\n    function createLazyWrapperAroundWakeable(wakeable) {\n        switch(wakeable.status){\n            case \"fulfilled\":\n            case \"rejected\":\n                break;\n            default:\n                \"string\" !== typeof wakeable.status && (wakeable.status = \"pending\", wakeable.then(function(fulfilledValue) {\n                    \"pending\" === wakeable.status && (wakeable.status = \"fulfilled\", wakeable.value = fulfilledValue);\n                }, function(error) {\n                    \"pending\" === wakeable.status && (wakeable.status = \"rejected\", wakeable.reason = error);\n                }));\n        }\n        var lazyType = {\n            $$typeof: REACT_LAZY_TYPE,\n            _payload: wakeable,\n            _init: readThenable\n        };\n        lazyType._debugInfo = wakeable._debugInfo || [];\n        return lazyType;\n    }\n    function callWithDebugContextInDEV(request, task, callback, arg) {\n        var componentDebugInfo = {\n            name: \"\",\n            env: task.environmentName,\n            key: null,\n            owner: task.debugOwner\n        };\n        componentDebugInfo.stack = null === task.debugStack ? null : filterStackTrace(request, task.debugStack, 1);\n        componentDebugInfo.debugStack = task.debugStack;\n        request = componentDebugInfo.debugTask = task.debugTask;\n        currentOwner = componentDebugInfo;\n        try {\n            return request ? request.run(callback.bind(null, arg)) : callback(arg);\n        } finally{\n            currentOwner = null;\n        }\n    }\n    function processServerComponentReturnValue(request, task, Component, result) {\n        if (\"object\" !== typeof result || null === result || isClientReference(result)) return result;\n        if (\"function\" === typeof result.then) return result.then(function(resolvedValue) {\n            \"object\" === typeof resolvedValue && null !== resolvedValue && resolvedValue.$$typeof === REACT_ELEMENT_TYPE && (resolvedValue._store.validated = 1);\n        }, voidHandler), \"fulfilled\" === result.status ? result.value : createLazyWrapperAroundWakeable(result);\n        result.$$typeof === REACT_ELEMENT_TYPE && (result._store.validated = 1);\n        var iteratorFn = getIteratorFn(result);\n        if (iteratorFn) {\n            var multiShot = _defineProperty({}, Symbol.iterator, function() {\n                var iterator = iteratorFn.call(result);\n                iterator !== result || \"[object GeneratorFunction]\" === Object.prototype.toString.call(Component) && \"[object Generator]\" === Object.prototype.toString.call(result) || callWithDebugContextInDEV(request, task, function() {\n                    console.error(\"Returning an Iterator from a Server Component is not supported since it cannot be looped over more than once. \");\n                });\n                return iterator;\n            });\n            multiShot._debugInfo = result._debugInfo;\n            return multiShot;\n        }\n        return \"function\" !== typeof result[ASYNC_ITERATOR] || \"function\" === typeof ReadableStream && result instanceof ReadableStream ? result : (multiShot = _defineProperty({}, ASYNC_ITERATOR, function() {\n            var iterator = result[ASYNC_ITERATOR]();\n            iterator !== result || \"[object AsyncGeneratorFunction]\" === Object.prototype.toString.call(Component) && \"[object AsyncGenerator]\" === Object.prototype.toString.call(result) || callWithDebugContextInDEV(request, task, function() {\n                console.error(\"Returning an AsyncIterator from a Server Component is not supported since it cannot be looped over more than once. \");\n            });\n            return iterator;\n        }), multiShot._debugInfo = result._debugInfo, multiShot);\n    }\n    function renderFunctionComponent(request, task, key, Component, props, validated) {\n        var prevThenableState = task.thenableState;\n        task.thenableState = null;\n        if (null === debugID) return outlineTask(request, task);\n        if (null !== prevThenableState) var componentDebugInfo = prevThenableState._componentDebugInfo;\n        else {\n            var componentDebugID = debugID;\n            componentDebugInfo = Component.displayName || Component.name || \"\";\n            var componentEnv = (0, request.environmentName)();\n            request.pendingChunks++;\n            componentDebugInfo = {\n                name: componentDebugInfo,\n                env: componentEnv,\n                key: key,\n                owner: task.debugOwner\n            };\n            componentDebugInfo.stack = null === task.debugStack ? null : filterStackTrace(request, task.debugStack, 1);\n            componentDebugInfo.props = props;\n            componentDebugInfo.debugStack = task.debugStack;\n            componentDebugInfo.debugTask = task.debugTask;\n            outlineComponentInfo(request, componentDebugInfo);\n            emitDebugChunk(request, componentDebugID, componentDebugInfo);\n            task.environmentName = componentEnv;\n            2 === validated && warnForMissingKey(request, key, componentDebugInfo, task.debugTask);\n        }\n        thenableIndexCounter = 0;\n        thenableState = prevThenableState;\n        currentComponentDebugInfo = componentDebugInfo;\n        props = supportsComponentStorage ? task.debugTask ? task.debugTask.run(componentStorage.run.bind(componentStorage, componentDebugInfo, callComponentInDEV, Component, props, componentDebugInfo)) : componentStorage.run(componentDebugInfo, callComponentInDEV, Component, props, componentDebugInfo) : task.debugTask ? task.debugTask.run(callComponentInDEV.bind(null, Component, props, componentDebugInfo)) : callComponentInDEV(Component, props, componentDebugInfo);\n        if (request.status === ABORTING) throw \"object\" !== typeof props || null === props || \"function\" !== typeof props.then || isClientReference(props) || props.then(voidHandler, voidHandler), null;\n        props = processServerComponentReturnValue(request, task, Component, props);\n        Component = task.keyPath;\n        validated = task.implicitSlot;\n        null !== key ? task.keyPath = null === Component ? key : Component + \",\" + key : null === Component && (task.implicitSlot = !0);\n        request = renderModelDestructive(request, task, emptyRoot, \"\", props);\n        task.keyPath = Component;\n        task.implicitSlot = validated;\n        return request;\n    }\n    function warnForMissingKey(request, key, componentDebugInfo, debugTask) {\n        function logKeyError() {\n            console.error('Each child in a list should have a unique \"key\" prop.%s%s See https://react.dev/link/warning-keys for more information.', \"\", \"\");\n        }\n        key = request.didWarnForKey;\n        null == key && (key = request.didWarnForKey = new WeakSet());\n        request = componentDebugInfo.owner;\n        if (null != request) {\n            if (key.has(request)) return;\n            key.add(request);\n        }\n        supportsComponentStorage ? debugTask ? debugTask.run(componentStorage.run.bind(componentStorage, componentDebugInfo, callComponentInDEV, logKeyError, null, componentDebugInfo)) : componentStorage.run(componentDebugInfo, callComponentInDEV, logKeyError, null, componentDebugInfo) : debugTask ? debugTask.run(callComponentInDEV.bind(null, logKeyError, null, componentDebugInfo)) : callComponentInDEV(logKeyError, null, componentDebugInfo);\n    }\n    function renderFragment(request, task, children) {\n        for(var i = 0; i < children.length; i++){\n            var child = children[i];\n            null === child || \"object\" !== typeof child || child.$$typeof !== REACT_ELEMENT_TYPE || null !== child.key || child._store.validated || (child._store.validated = 2);\n        }\n        if (null !== task.keyPath) return request = [\n            REACT_ELEMENT_TYPE,\n            REACT_FRAGMENT_TYPE,\n            task.keyPath,\n            {\n                children: children\n            },\n            null,\n            null,\n            0\n        ], task.implicitSlot ? [\n            request\n        ] : request;\n        if (i = children._debugInfo) {\n            if (null === debugID) return outlineTask(request, task);\n            forwardDebugInfo(request, debugID, i);\n            children = Array.from(children);\n        }\n        return children;\n    }\n    function renderAsyncFragment(request, task, children, getAsyncIterator) {\n        if (null !== task.keyPath) return request = [\n            REACT_ELEMENT_TYPE,\n            REACT_FRAGMENT_TYPE,\n            task.keyPath,\n            {\n                children: children\n            },\n            null,\n            null,\n            0\n        ], task.implicitSlot ? [\n            request\n        ] : request;\n        getAsyncIterator = getAsyncIterator.call(children);\n        return serializeAsyncIterable(request, task, children, getAsyncIterator);\n    }\n    function outlineTask(request, task) {\n        task = createTask(request, task.model, task.keyPath, task.implicitSlot, request.abortableTasks, task.debugOwner, task.debugStack, task.debugTask);\n        retryTask(request, task);\n        return task.status === COMPLETED ? serializeByValueID(task.id) : \"$L\" + task.id.toString(16);\n    }\n    function renderElement(request, task, type, key, ref, props, validated) {\n        if (null !== ref && void 0 !== ref) throw Error(\"Refs cannot be used in Server Components, nor passed to Client Components.\");\n        jsxPropsParents.set(props, type);\n        \"object\" === typeof props.children && null !== props.children && jsxChildrenParents.set(props.children, type);\n        if (\"function\" !== typeof type || isClientReference(type) || type.$$typeof === TEMPORARY_REFERENCE_TAG) {\n            if (type === REACT_FRAGMENT_TYPE && null === key) return 2 === validated && (validated = {\n                name: \"Fragment\",\n                env: (0, request.environmentName)(),\n                key: key,\n                owner: task.debugOwner,\n                stack: null === task.debugStack ? null : filterStackTrace(request, task.debugStack, 1),\n                props: props,\n                debugStack: task.debugStack,\n                debugTask: task.debugTask\n            }, warnForMissingKey(request, key, validated, task.debugTask)), validated = task.implicitSlot, null === task.keyPath && (task.implicitSlot = !0), request = renderModelDestructive(request, task, emptyRoot, \"\", props.children), task.implicitSlot = validated, request;\n            if (null != type && \"object\" === typeof type && !isClientReference(type)) switch(type.$$typeof){\n                case REACT_LAZY_TYPE:\n                    type = callLazyInitInDEV(type);\n                    if (request.status === ABORTING) throw null;\n                    return renderElement(request, task, type, key, ref, props, validated);\n                case REACT_FORWARD_REF_TYPE:\n                    return renderFunctionComponent(request, task, key, type.render, props, validated);\n                case REACT_MEMO_TYPE:\n                    return renderElement(request, task, type.type, key, ref, props, validated);\n                case REACT_ELEMENT_TYPE:\n                    type._store.validated = 1;\n            }\n        } else return renderFunctionComponent(request, task, key, type, props, validated);\n        ref = task.keyPath;\n        null === key ? key = ref : null !== ref && (key = ref + \",\" + key);\n        null !== task.debugOwner && outlineComponentInfo(request, task.debugOwner);\n        request = [\n            REACT_ELEMENT_TYPE,\n            type,\n            key,\n            props,\n            task.debugOwner,\n            null === task.debugStack ? null : filterStackTrace(request, task.debugStack, 1),\n            validated\n        ];\n        task = task.implicitSlot && null !== key ? [\n            request\n        ] : request;\n        return task;\n    }\n    function pingTask(request, task) {\n        var pingedTasks = request.pingedTasks;\n        pingedTasks.push(task);\n        1 === pingedTasks.length && (request.flushScheduled = null !== request.destination, request.type === PRERENDER || request.status === OPENING ? scheduleMicrotask(function() {\n            return performWork(request);\n        }) : setTimeoutOrImmediate(function() {\n            return performWork(request);\n        }, 0));\n    }\n    function createTask(request, model, keyPath, implicitSlot, abortSet, debugOwner, debugStack, debugTask) {\n        request.pendingChunks++;\n        var id = request.nextChunkId++;\n        \"object\" !== typeof model || null === model || null !== keyPath || implicitSlot || request.writtenObjects.set(model, serializeByValueID(id));\n        var task = {\n            id: id,\n            status: PENDING$1,\n            model: model,\n            keyPath: keyPath,\n            implicitSlot: implicitSlot,\n            ping: function() {\n                return pingTask(request, task);\n            },\n            toJSON: function(parentPropertyName, value) {\n                var parent = this, originalValue = parent[parentPropertyName];\n                \"object\" !== typeof originalValue || originalValue === value || originalValue instanceof Date || callWithDebugContextInDEV(request, task, function() {\n                    \"Object\" !== objectName(originalValue) ? \"string\" === typeof jsxChildrenParents.get(parent) ? console.error(\"%s objects cannot be rendered as text children. Try formatting it using toString().%s\", objectName(originalValue), describeObjectForErrorMessage(parent, parentPropertyName)) : console.error(\"Only plain objects can be passed to Client Components from Server Components. %s objects are not supported.%s\", objectName(originalValue), describeObjectForErrorMessage(parent, parentPropertyName)) : console.error(\"Only plain objects can be passed to Client Components from Server Components. Objects with toJSON methods are not supported. Convert it manually to a simple value before passing it to props.%s\", describeObjectForErrorMessage(parent, parentPropertyName));\n                });\n                return renderModel(request, task, parent, parentPropertyName, value);\n            },\n            thenableState: null\n        };\n        task.environmentName = request.environmentName();\n        task.debugOwner = debugOwner;\n        task.debugStack = debugStack;\n        task.debugTask = debugTask;\n        abortSet.add(task);\n        return task;\n    }\n    function serializeByValueID(id) {\n        return \"$\" + id.toString(16);\n    }\n    function serializeNumber(number) {\n        return Number.isFinite(number) ? 0 === number && -Infinity === 1 / number ? \"$-0\" : number : Infinity === number ? \"$Infinity\" : -Infinity === number ? \"$-Infinity\" : \"$NaN\";\n    }\n    function encodeReferenceChunk(request, id, reference) {\n        request = stringify(reference);\n        id = id.toString(16) + \":\" + request + \"\\n\";\n        return stringToChunk(id);\n    }\n    function serializeClientReference(request, parent, parentPropertyName, clientReference) {\n        var clientReferenceKey = clientReference.$$async ? clientReference.$$id + \"#async\" : clientReference.$$id, writtenClientReferences = request.writtenClientReferences, existingId = writtenClientReferences.get(clientReferenceKey);\n        if (void 0 !== existingId) return parent[0] === REACT_ELEMENT_TYPE && \"1\" === parentPropertyName ? \"$L\" + existingId.toString(16) : serializeByValueID(existingId);\n        try {\n            var config = request.bundlerConfig, modulePath = clientReference.$$id;\n            existingId = \"\";\n            var resolvedModuleData = config[modulePath];\n            if (resolvedModuleData) existingId = resolvedModuleData.name;\n            else {\n                var idx = modulePath.lastIndexOf(\"#\");\n                -1 !== idx && (existingId = modulePath.slice(idx + 1), resolvedModuleData = config[modulePath.slice(0, idx)]);\n                if (!resolvedModuleData) throw Error('Could not find the module \"' + modulePath + '\" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');\n            }\n            if (!0 === resolvedModuleData.async && !0 === clientReference.$$async) throw Error('The module \"' + modulePath + '\" is marked as an async ESM module but was loaded as a CJS proxy. This is probably a bug in the React Server Components bundler.');\n            var clientReferenceMetadata = !0 === resolvedModuleData.async || !0 === clientReference.$$async ? [\n                resolvedModuleData.id,\n                resolvedModuleData.chunks,\n                existingId,\n                1\n            ] : [\n                resolvedModuleData.id,\n                resolvedModuleData.chunks,\n                existingId\n            ];\n            request.pendingChunks++;\n            var importId = request.nextChunkId++, json = stringify(clientReferenceMetadata), row = importId.toString(16) + \":I\" + json + \"\\n\", processedChunk = stringToChunk(row);\n            request.completedImportChunks.push(processedChunk);\n            writtenClientReferences.set(clientReferenceKey, importId);\n            return parent[0] === REACT_ELEMENT_TYPE && \"1\" === parentPropertyName ? \"$L\" + importId.toString(16) : serializeByValueID(importId);\n        } catch (x) {\n            return request.pendingChunks++, parent = request.nextChunkId++, parentPropertyName = logRecoverableError(request, x, null), emitErrorChunk(request, parent, parentPropertyName, x), serializeByValueID(parent);\n        }\n    }\n    function outlineModel(request, value) {\n        value = createTask(request, value, null, !1, request.abortableTasks, null, null, null);\n        retryTask(request, value);\n        return value.id;\n    }\n    function serializeServerReference(request, serverReference) {\n        var writtenServerReferences = request.writtenServerReferences, existingId = writtenServerReferences.get(serverReference);\n        if (void 0 !== existingId) return \"$F\" + existingId.toString(16);\n        existingId = serverReference.$$bound;\n        existingId = null === existingId ? null : Promise.resolve(existingId);\n        var id = serverReference.$$id, location = null, error = serverReference.$$location;\n        error && (error = parseStackTrace(error, 1), 0 < error.length && (location = error[0]));\n        existingId = null !== location ? {\n            id: id,\n            bound: existingId,\n            name: \"function\" === typeof serverReference ? serverReference.name : \"\",\n            env: (0, request.environmentName)(),\n            location: location\n        } : {\n            id: id,\n            bound: existingId\n        };\n        request = outlineModel(request, existingId);\n        writtenServerReferences.set(serverReference, request);\n        return \"$F\" + request.toString(16);\n    }\n    function serializeLargeTextString(request, text) {\n        request.pendingChunks++;\n        var textId = request.nextChunkId++;\n        emitTextChunk(request, textId, text);\n        return serializeByValueID(textId);\n    }\n    function serializeMap(request, map) {\n        map = Array.from(map);\n        return \"$Q\" + outlineModel(request, map).toString(16);\n    }\n    function serializeFormData(request, formData) {\n        formData = Array.from(formData.entries());\n        return \"$K\" + outlineModel(request, formData).toString(16);\n    }\n    function serializeSet(request, set) {\n        set = Array.from(set);\n        return \"$W\" + outlineModel(request, set).toString(16);\n    }\n    function serializeTypedArray(request, tag, typedArray) {\n        request.pendingChunks++;\n        var bufferId = request.nextChunkId++;\n        emitTypedArrayChunk(request, bufferId, tag, typedArray);\n        return serializeByValueID(bufferId);\n    }\n    function serializeBlob(request, blob) {\n        function progress(entry) {\n            if (!aborted) if (entry.done) request.abortListeners.delete(abortBlob), aborted = !0, pingTask(request, newTask);\n            else return model.push(entry.value), reader.read().then(progress).catch(error);\n        }\n        function error(reason) {\n            aborted || (aborted = !0, request.abortListeners.delete(abortBlob), erroredTask(request, newTask, reason), enqueueFlush(request), reader.cancel(reason).then(error, error));\n        }\n        function abortBlob(reason) {\n            aborted || (aborted = !0, request.abortListeners.delete(abortBlob), erroredTask(request, newTask, reason), enqueueFlush(request), reader.cancel(reason).then(error, error));\n        }\n        var model = [\n            blob.type\n        ], newTask = createTask(request, model, null, !1, request.abortableTasks, null, null, null), reader = blob.stream().getReader(), aborted = !1;\n        request.abortListeners.add(abortBlob);\n        reader.read().then(progress).catch(error);\n        return \"$B\" + newTask.id.toString(16);\n    }\n    function renderModel(request, task, parent, key, value) {\n        var prevKeyPath = task.keyPath, prevImplicitSlot = task.implicitSlot;\n        try {\n            return renderModelDestructive(request, task, parent, key, value);\n        } catch (thrownValue) {\n            parent = task.model;\n            parent = \"object\" === typeof parent && null !== parent && (parent.$$typeof === REACT_ELEMENT_TYPE || parent.$$typeof === REACT_LAZY_TYPE);\n            if (request.status === ABORTING) return task.status = ABORTED, task = request.fatalError, parent ? \"$L\" + task.toString(16) : serializeByValueID(task);\n            key = thrownValue === SuspenseException ? getSuspendedThenable() : thrownValue;\n            if (\"object\" === typeof key && null !== key && \"function\" === typeof key.then) return request = createTask(request, task.model, task.keyPath, task.implicitSlot, request.abortableTasks, task.debugOwner, task.debugStack, task.debugTask), value = request.ping, key.then(value, value), request.thenableState = getThenableStateAfterSuspending(), task.keyPath = prevKeyPath, task.implicitSlot = prevImplicitSlot, parent ? \"$L\" + request.id.toString(16) : serializeByValueID(request.id);\n            task.keyPath = prevKeyPath;\n            task.implicitSlot = prevImplicitSlot;\n            request.pendingChunks++;\n            prevKeyPath = request.nextChunkId++;\n            task = logRecoverableError(request, key, task);\n            emitErrorChunk(request, prevKeyPath, task, key);\n            return parent ? \"$L\" + prevKeyPath.toString(16) : serializeByValueID(prevKeyPath);\n        }\n    }\n    function renderModelDestructive(request, task, parent, parentPropertyName, value) {\n        task.model = value;\n        if (value === REACT_ELEMENT_TYPE) return \"$\";\n        if (null === value) return null;\n        if (\"object\" === typeof value) {\n            switch(value.$$typeof){\n                case REACT_ELEMENT_TYPE:\n                    var elementReference = null, _writtenObjects = request.writtenObjects;\n                    if (null === task.keyPath && !task.implicitSlot) {\n                        var _existingReference = _writtenObjects.get(value);\n                        if (void 0 !== _existingReference) if (modelRoot === value) modelRoot = null;\n                        else return _existingReference;\n                        else -1 === parentPropertyName.indexOf(\":\") && (_existingReference = _writtenObjects.get(parent), void 0 !== _existingReference && (elementReference = _existingReference + \":\" + parentPropertyName, _writtenObjects.set(value, elementReference)));\n                    }\n                    if (_existingReference = value._debugInfo) {\n                        if (null === debugID) return outlineTask(request, task);\n                        forwardDebugInfo(request, debugID, _existingReference);\n                    }\n                    _existingReference = value.props;\n                    var refProp = _existingReference.ref;\n                    task.debugOwner = value._owner;\n                    task.debugStack = value._debugStack;\n                    task.debugTask = value._debugTask;\n                    request = renderElement(request, task, value.type, value.key, void 0 !== refProp ? refProp : null, _existingReference, value._store.validated);\n                    \"object\" === typeof request && null !== request && null !== elementReference && (_writtenObjects.has(request) || _writtenObjects.set(request, elementReference));\n                    return request;\n                case REACT_LAZY_TYPE:\n                    task.thenableState = null;\n                    elementReference = callLazyInitInDEV(value);\n                    if (request.status === ABORTING) throw null;\n                    if (_writtenObjects = value._debugInfo) {\n                        if (null === debugID) return outlineTask(request, task);\n                        forwardDebugInfo(request, debugID, _writtenObjects);\n                    }\n                    return renderModelDestructive(request, task, emptyRoot, \"\", elementReference);\n                case REACT_LEGACY_ELEMENT_TYPE:\n                    throw Error('A React Element from an older version of React was rendered. This is not supported. It can happen if:\\n- Multiple copies of the \"react\" package is used.\\n- A library pre-bundled an old copy of \"react\" or \"react/jsx-runtime\".\\n- A compiler tries to \"inline\" JSX instead of using the runtime.');\n            }\n            if (isClientReference(value)) return serializeClientReference(request, parent, parentPropertyName, value);\n            if (void 0 !== request.temporaryReferences && (elementReference = request.temporaryReferences.get(value), void 0 !== elementReference)) return \"$T\" + elementReference;\n            elementReference = request.writtenObjects;\n            _writtenObjects = elementReference.get(value);\n            if (\"function\" === typeof value.then) {\n                if (void 0 !== _writtenObjects) {\n                    if (null !== task.keyPath || task.implicitSlot) return \"$@\" + serializeThenable(request, task, value).toString(16);\n                    if (modelRoot === value) modelRoot = null;\n                    else return _writtenObjects;\n                }\n                request = \"$@\" + serializeThenable(request, task, value).toString(16);\n                elementReference.set(value, request);\n                return request;\n            }\n            if (void 0 !== _writtenObjects) if (modelRoot === value) modelRoot = null;\n            else return _writtenObjects;\n            else if (-1 === parentPropertyName.indexOf(\":\") && (_writtenObjects = elementReference.get(parent), void 0 !== _writtenObjects)) {\n                _existingReference = parentPropertyName;\n                if (isArrayImpl(parent) && parent[0] === REACT_ELEMENT_TYPE) switch(parentPropertyName){\n                    case \"1\":\n                        _existingReference = \"type\";\n                        break;\n                    case \"2\":\n                        _existingReference = \"key\";\n                        break;\n                    case \"3\":\n                        _existingReference = \"props\";\n                        break;\n                    case \"4\":\n                        _existingReference = \"_owner\";\n                }\n                elementReference.set(value, _writtenObjects + \":\" + _existingReference);\n            }\n            if (isArrayImpl(value)) return renderFragment(request, task, value);\n            if (value instanceof Map) return serializeMap(request, value);\n            if (value instanceof Set) return serializeSet(request, value);\n            if (\"function\" === typeof FormData && value instanceof FormData) return serializeFormData(request, value);\n            if (value instanceof Error) return serializeErrorValue(request, value);\n            if (value instanceof ArrayBuffer) return serializeTypedArray(request, \"A\", new Uint8Array(value));\n            if (value instanceof Int8Array) return serializeTypedArray(request, \"O\", value);\n            if (value instanceof Uint8Array) return serializeTypedArray(request, \"o\", value);\n            if (value instanceof Uint8ClampedArray) return serializeTypedArray(request, \"U\", value);\n            if (value instanceof Int16Array) return serializeTypedArray(request, \"S\", value);\n            if (value instanceof Uint16Array) return serializeTypedArray(request, \"s\", value);\n            if (value instanceof Int32Array) return serializeTypedArray(request, \"L\", value);\n            if (value instanceof Uint32Array) return serializeTypedArray(request, \"l\", value);\n            if (value instanceof Float32Array) return serializeTypedArray(request, \"G\", value);\n            if (value instanceof Float64Array) return serializeTypedArray(request, \"g\", value);\n            if (value instanceof BigInt64Array) return serializeTypedArray(request, \"M\", value);\n            if (value instanceof BigUint64Array) return serializeTypedArray(request, \"m\", value);\n            if (value instanceof DataView) return serializeTypedArray(request, \"V\", value);\n            if (\"function\" === typeof Blob && value instanceof Blob) return serializeBlob(request, value);\n            if (elementReference = getIteratorFn(value)) return elementReference = elementReference.call(value), elementReference === value ? \"$i\" + outlineModel(request, Array.from(elementReference)).toString(16) : renderFragment(request, task, Array.from(elementReference));\n            if (\"function\" === typeof ReadableStream && value instanceof ReadableStream) return serializeReadableStream(request, task, value);\n            elementReference = value[ASYNC_ITERATOR];\n            if (\"function\" === typeof elementReference) return renderAsyncFragment(request, task, value, elementReference);\n            if (value instanceof Date) return \"$D\" + value.toJSON();\n            elementReference = getPrototypeOf(value);\n            if (elementReference !== ObjectPrototype && (null === elementReference || null !== getPrototypeOf(elementReference))) throw Error(\"Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported.\" + describeObjectForErrorMessage(parent, parentPropertyName));\n            if (\"Object\" !== objectName(value)) callWithDebugContextInDEV(request, task, function() {\n                console.error(\"Only plain objects can be passed to Client Components from Server Components. %s objects are not supported.%s\", objectName(value), describeObjectForErrorMessage(parent, parentPropertyName));\n            });\n            else if (!isSimpleObject(value)) callWithDebugContextInDEV(request, task, function() {\n                console.error(\"Only plain objects can be passed to Client Components from Server Components. Classes or other objects with methods are not supported.%s\", describeObjectForErrorMessage(parent, parentPropertyName));\n            });\n            else if (Object.getOwnPropertySymbols) {\n                var symbols = Object.getOwnPropertySymbols(value);\n                0 < symbols.length && callWithDebugContextInDEV(request, task, function() {\n                    console.error(\"Only plain objects can be passed to Client Components from Server Components. Objects with symbol properties like %s are not supported.%s\", symbols[0].description, describeObjectForErrorMessage(parent, parentPropertyName));\n                });\n            }\n            return value;\n        }\n        if (\"string\" === typeof value) return \"Z\" === value[value.length - 1] && parent[parentPropertyName] instanceof Date ? \"$D\" + value : 1024 <= value.length && null !== byteLengthOfChunk ? serializeLargeTextString(request, value) : \"$\" === value[0] ? \"$\" + value : value;\n        if (\"boolean\" === typeof value) return value;\n        if (\"number\" === typeof value) return serializeNumber(value);\n        if (\"undefined\" === typeof value) return \"$undefined\";\n        if (\"function\" === typeof value) {\n            if (isClientReference(value)) return serializeClientReference(request, parent, parentPropertyName, value);\n            if (value.$$typeof === SERVER_REFERENCE_TAG) return serializeServerReference(request, value);\n            if (void 0 !== request.temporaryReferences && (request = request.temporaryReferences.get(value), void 0 !== request)) return \"$T\" + request;\n            if (value.$$typeof === TEMPORARY_REFERENCE_TAG) throw Error(\"Could not reference an opaque temporary reference. This is likely due to misconfiguring the temporaryReferences options on the server.\");\n            if (/^on[A-Z]/.test(parentPropertyName)) throw Error(\"Event handlers cannot be passed to Client Component props.\" + describeObjectForErrorMessage(parent, parentPropertyName) + \"\\nIf you need interactivity, consider converting part of this to a Client Component.\");\n            if (jsxChildrenParents.has(parent) || jsxPropsParents.has(parent) && \"children\" === parentPropertyName) throw request = value.displayName || value.name || \"Component\", Error(\"Functions are not valid as a child of Client Components. This may happen if you return \" + request + \" instead of <\" + request + \" /> from render. Or maybe you meant to call this function rather than return it.\" + describeObjectForErrorMessage(parent, parentPropertyName));\n            throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with \"use server\". Or maybe you meant to call this function rather than return it.' + describeObjectForErrorMessage(parent, parentPropertyName));\n        }\n        if (\"symbol\" === typeof value) {\n            task = request.writtenSymbols;\n            elementReference = task.get(value);\n            if (void 0 !== elementReference) return serializeByValueID(elementReference);\n            elementReference = value.description;\n            if (Symbol.for(elementReference) !== value) throw Error(\"Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for(\" + (value.description + \") cannot be found among global symbols.\") + describeObjectForErrorMessage(parent, parentPropertyName));\n            request.pendingChunks++;\n            _writtenObjects = request.nextChunkId++;\n            emitSymbolChunk(request, _writtenObjects, elementReference);\n            task.set(value, _writtenObjects);\n            return serializeByValueID(_writtenObjects);\n        }\n        if (\"bigint\" === typeof value) return \"$n\" + value.toString(10);\n        throw Error(\"Type \" + typeof value + \" is not supported in Client Component props.\" + describeObjectForErrorMessage(parent, parentPropertyName));\n    }\n    function logRecoverableError(request, error, task) {\n        var prevRequest = currentRequest;\n        currentRequest = null;\n        try {\n            var onError = request.onError;\n            var errorDigest = null !== task ? supportsRequestStorage ? requestStorage.run(void 0, callWithDebugContextInDEV, request, task, onError, error) : callWithDebugContextInDEV(request, task, onError, error) : supportsRequestStorage ? requestStorage.run(void 0, onError, error) : onError(error);\n        } finally{\n            currentRequest = prevRequest;\n        }\n        if (null != errorDigest && \"string\" !== typeof errorDigest) throw Error('onError returned something with a type other than \"string\". onError should return a string and may return null or undefined but must not return anything else. It received something of type \"' + typeof errorDigest + '\" instead');\n        return errorDigest || \"\";\n    }\n    function fatalError(request, error) {\n        var onFatalError = request.onFatalError;\n        onFatalError(error);\n        null !== request.destination ? (request.status = CLOSED, closeWithError(request.destination, error)) : (request.status = CLOSING, request.fatalError = error);\n    }\n    function serializeErrorValue(request, error) {\n        var name = \"Error\", env = (0, request.environmentName)();\n        try {\n            name = error.name;\n            var message = String(error.message);\n            var stack = filterStackTrace(request, error, 0);\n            var errorEnv = error.environmentName;\n            \"string\" === typeof errorEnv && (env = errorEnv);\n        } catch (x) {\n            message = \"An error occurred but serializing the error message failed.\", stack = [];\n        }\n        return \"$Z\" + outlineModel(request, {\n            name: name,\n            message: message,\n            stack: stack,\n            env: env\n        }).toString(16);\n    }\n    function emitErrorChunk(request, id, digest, error) {\n        var name = \"Error\", env = (0, request.environmentName)();\n        try {\n            if (error instanceof Error) {\n                name = error.name;\n                var message = String(error.message);\n                var stack = filterStackTrace(request, error, 0);\n                var errorEnv = error.environmentName;\n                \"string\" === typeof errorEnv && (env = errorEnv);\n            } else message = \"object\" === typeof error && null !== error ? describeObjectForErrorMessage(error) : String(error), stack = [];\n        } catch (x) {\n            message = \"An error occurred but serializing the error message failed.\", stack = [];\n        }\n        digest = {\n            digest: digest,\n            name: name,\n            message: message,\n            stack: stack,\n            env: env\n        };\n        id = id.toString(16) + \":E\" + stringify(digest) + \"\\n\";\n        id = stringToChunk(id);\n        request.completedErrorChunks.push(id);\n    }\n    function emitSymbolChunk(request, id, name) {\n        id = encodeReferenceChunk(request, id, \"$S\" + name);\n        request.completedImportChunks.push(id);\n    }\n    function emitModelChunk(request, id, json) {\n        id = id.toString(16) + \":\" + json + \"\\n\";\n        id = stringToChunk(id);\n        request.completedRegularChunks.push(id);\n    }\n    function emitDebugChunk(request, id, debugInfo) {\n        var counter = {\n            objectLimit: 500\n        };\n        debugInfo = stringify(debugInfo, function(parentPropertyName, value) {\n            return renderConsoleValue(request, counter, this, parentPropertyName, value);\n        });\n        id = id.toString(16) + \":D\" + debugInfo + \"\\n\";\n        id = stringToChunk(id);\n        request.completedRegularChunks.push(id);\n    }\n    function outlineComponentInfo(request, componentInfo) {\n        if (!request.writtenObjects.has(componentInfo)) {\n            null != componentInfo.owner && outlineComponentInfo(request, componentInfo.owner);\n            var objectLimit = 10;\n            null != componentInfo.stack && (objectLimit += componentInfo.stack.length);\n            objectLimit = {\n                objectLimit: objectLimit\n            };\n            var componentDebugInfo = {\n                name: componentInfo.name,\n                env: componentInfo.env,\n                key: componentInfo.key,\n                owner: componentInfo.owner\n            };\n            componentDebugInfo.stack = componentInfo.stack;\n            componentDebugInfo.props = componentInfo.props;\n            objectLimit = outlineConsoleValue(request, objectLimit, componentDebugInfo);\n            request.writtenObjects.set(componentInfo, serializeByValueID(objectLimit));\n        }\n    }\n    function emitTypedArrayChunk(request, id, tag, typedArray) {\n        request.pendingChunks++;\n        var buffer = new Uint8Array(typedArray.buffer, typedArray.byteOffset, typedArray.byteLength);\n        typedArray = 2048 < typedArray.byteLength ? buffer.slice() : buffer;\n        buffer = typedArray.byteLength;\n        id = id.toString(16) + \":\" + tag + buffer.toString(16) + \",\";\n        id = stringToChunk(id);\n        request.completedRegularChunks.push(id, typedArray);\n    }\n    function emitTextChunk(request, id, text) {\n        if (null === byteLengthOfChunk) throw Error(\"Existence of byteLengthOfChunk should have already been checked. This is a bug in React.\");\n        request.pendingChunks++;\n        text = stringToChunk(text);\n        var binaryLength = text.byteLength;\n        id = id.toString(16) + \":T\" + binaryLength.toString(16) + \",\";\n        id = stringToChunk(id);\n        request.completedRegularChunks.push(id, text);\n    }\n    function renderConsoleValue(request, counter, parent, parentPropertyName, value) {\n        if (null === value) return null;\n        if (value === REACT_ELEMENT_TYPE) return \"$\";\n        if (\"object\" === typeof value) {\n            if (isClientReference(value)) return serializeClientReference(request, parent, parentPropertyName, value);\n            if (void 0 !== request.temporaryReferences && (parent = request.temporaryReferences.get(value), void 0 !== parent)) return \"$T\" + parent;\n            parent = request.writtenObjects.get(value);\n            if (void 0 !== parent) return parent;\n            if (0 >= counter.objectLimit && !doNotLimit.has(value)) return \"$Y\";\n            counter.objectLimit--;\n            switch(value.$$typeof){\n                case REACT_ELEMENT_TYPE:\n                    null != value._owner && outlineComponentInfo(request, value._owner);\n                    \"object\" === typeof value.type && null !== value.type && doNotLimit.add(value.type);\n                    \"object\" === typeof value.key && null !== value.key && doNotLimit.add(value.key);\n                    doNotLimit.add(value.props);\n                    null !== value._owner && doNotLimit.add(value._owner);\n                    counter = null;\n                    if (null != value._debugStack) for(counter = filterStackTrace(request, value._debugStack, 1), doNotLimit.add(counter), request = 0; request < counter.length; request++)doNotLimit.add(counter[request]);\n                    return [\n                        REACT_ELEMENT_TYPE,\n                        value.type,\n                        value.key,\n                        value.props,\n                        value._owner,\n                        counter,\n                        value._store.validated\n                    ];\n            }\n            if (\"function\" === typeof value.then) {\n                switch(value.status){\n                    case \"fulfilled\":\n                        return \"$@\" + outlineConsoleValue(request, counter, value.value).toString(16);\n                    case \"rejected\":\n                        return counter = value.reason, request.pendingChunks++, value = request.nextChunkId++, emitErrorChunk(request, value, \"\", counter), \"$@\" + value.toString(16);\n                }\n                return \"$@\";\n            }\n            if (isArrayImpl(value)) return value;\n            if (value instanceof Map) {\n                value = Array.from(value);\n                counter.objectLimit++;\n                for(parent = 0; parent < value.length; parent++){\n                    var entry = value[parent];\n                    doNotLimit.add(entry);\n                    parentPropertyName = entry[0];\n                    entry = entry[1];\n                    \"object\" === typeof parentPropertyName && null !== parentPropertyName && doNotLimit.add(parentPropertyName);\n                    \"object\" === typeof entry && null !== entry && doNotLimit.add(entry);\n                }\n                return \"$Q\" + outlineConsoleValue(request, counter, value).toString(16);\n            }\n            if (value instanceof Set) {\n                value = Array.from(value);\n                counter.objectLimit++;\n                for(parent = 0; parent < value.length; parent++)parentPropertyName = value[parent], \"object\" === typeof parentPropertyName && null !== parentPropertyName && doNotLimit.add(parentPropertyName);\n                return \"$W\" + outlineConsoleValue(request, counter, value).toString(16);\n            }\n            return \"function\" === typeof FormData && value instanceof FormData ? serializeFormData(request, value) : value instanceof Error ? serializeErrorValue(request, value) : value instanceof ArrayBuffer ? serializeTypedArray(request, \"A\", new Uint8Array(value)) : value instanceof Int8Array ? serializeTypedArray(request, \"O\", value) : value instanceof Uint8Array ? serializeTypedArray(request, \"o\", value) : value instanceof Uint8ClampedArray ? serializeTypedArray(request, \"U\", value) : value instanceof Int16Array ? serializeTypedArray(request, \"S\", value) : value instanceof Uint16Array ? serializeTypedArray(request, \"s\", value) : value instanceof Int32Array ? serializeTypedArray(request, \"L\", value) : value instanceof Uint32Array ? serializeTypedArray(request, \"l\", value) : value instanceof Float32Array ? serializeTypedArray(request, \"G\", value) : value instanceof Float64Array ? serializeTypedArray(request, \"g\", value) : value instanceof BigInt64Array ? serializeTypedArray(request, \"M\", value) : value instanceof BigUint64Array ? serializeTypedArray(request, \"m\", value) : value instanceof DataView ? serializeTypedArray(request, \"V\", value) : \"function\" === typeof Blob && value instanceof Blob ? serializeBlob(request, value) : getIteratorFn(value) ? Array.from(value) : value;\n        }\n        if (\"string\" === typeof value) return \"Z\" === value[value.length - 1] && parent[parentPropertyName] instanceof Date ? \"$D\" + value : 1024 <= value.length ? serializeLargeTextString(request, value) : \"$\" === value[0] ? \"$\" + value : value;\n        if (\"boolean\" === typeof value) return value;\n        if (\"number\" === typeof value) return serializeNumber(value);\n        if (\"undefined\" === typeof value) return \"$undefined\";\n        if (\"function\" === typeof value) return isClientReference(value) ? serializeClientReference(request, parent, parentPropertyName, value) : void 0 !== request.temporaryReferences && (request = request.temporaryReferences.get(value), void 0 !== request) ? \"$T\" + request : \"$E(\" + (Function.prototype.toString.call(value) + \")\");\n        if (\"symbol\" === typeof value) {\n            counter = request.writtenSymbols.get(value);\n            if (void 0 !== counter) return serializeByValueID(counter);\n            counter = value.description;\n            request.pendingChunks++;\n            value = request.nextChunkId++;\n            emitSymbolChunk(request, value, counter);\n            return serializeByValueID(value);\n        }\n        return \"bigint\" === typeof value ? \"$n\" + value.toString(10) : value instanceof Date ? \"$D\" + value.toJSON() : \"unknown type \" + typeof value;\n    }\n    function outlineConsoleValue(request, counter, model) {\n        function replacer(parentPropertyName, value) {\n            try {\n                return renderConsoleValue(request, counter, this, parentPropertyName, value);\n            } catch (x) {\n                return \"Unknown Value: React could not send it from the server.\\n\" + x.message;\n            }\n        }\n        \"object\" === typeof model && null !== model && doNotLimit.add(model);\n        try {\n            var json = stringify(model, replacer);\n        } catch (x) {\n            json = stringify(\"Unknown Value: React could not send it from the server.\\n\" + x.message);\n        }\n        request.pendingChunks++;\n        model = request.nextChunkId++;\n        json = model.toString(16) + \":\" + json + \"\\n\";\n        json = stringToChunk(json);\n        request.completedRegularChunks.push(json);\n        return model;\n    }\n    function emitConsoleChunk(request, methodName, owner, stackTrace, args) {\n        function replacer(parentPropertyName, value) {\n            try {\n                return renderConsoleValue(request, counter, this, parentPropertyName, value);\n            } catch (x) {\n                return \"Unknown Value: React could not send it from the server.\\n\" + x.message;\n            }\n        }\n        var counter = {\n            objectLimit: 500\n        };\n        null != owner && outlineComponentInfo(request, owner);\n        var env = (0, request.environmentName)(), payload = [\n            methodName,\n            stackTrace,\n            owner,\n            env\n        ];\n        payload.push.apply(payload, args);\n        try {\n            var json = stringify(payload, replacer);\n        } catch (x) {\n            json = stringify([\n                methodName,\n                stackTrace,\n                owner,\n                env,\n                \"Unknown Value: React could not send it from the server.\",\n                x\n            ], replacer);\n        }\n        methodName = stringToChunk(\":W\" + json + \"\\n\");\n        request.completedRegularChunks.push(methodName);\n    }\n    function forwardDebugInfo(request, id, debugInfo) {\n        for(var i = 0; i < debugInfo.length; i++)\"number\" !== typeof debugInfo[i].time && (request.pendingChunks++, \"string\" === typeof debugInfo[i].name && outlineComponentInfo(request, debugInfo[i]), emitDebugChunk(request, id, debugInfo[i]));\n    }\n    function emitChunk(request, task, value) {\n        var id = task.id;\n        \"string\" === typeof value && null !== byteLengthOfChunk ? emitTextChunk(request, id, value) : value instanceof ArrayBuffer ? emitTypedArrayChunk(request, id, \"A\", new Uint8Array(value)) : value instanceof Int8Array ? emitTypedArrayChunk(request, id, \"O\", value) : value instanceof Uint8Array ? emitTypedArrayChunk(request, id, \"o\", value) : value instanceof Uint8ClampedArray ? emitTypedArrayChunk(request, id, \"U\", value) : value instanceof Int16Array ? emitTypedArrayChunk(request, id, \"S\", value) : value instanceof Uint16Array ? emitTypedArrayChunk(request, id, \"s\", value) : value instanceof Int32Array ? emitTypedArrayChunk(request, id, \"L\", value) : value instanceof Uint32Array ? emitTypedArrayChunk(request, id, \"l\", value) : value instanceof Float32Array ? emitTypedArrayChunk(request, id, \"G\", value) : value instanceof Float64Array ? emitTypedArrayChunk(request, id, \"g\", value) : value instanceof BigInt64Array ? emitTypedArrayChunk(request, id, \"M\", value) : value instanceof BigUint64Array ? emitTypedArrayChunk(request, id, \"m\", value) : value instanceof DataView ? emitTypedArrayChunk(request, id, \"V\", value) : (value = stringify(value, task.toJSON), emitModelChunk(request, task.id, value));\n    }\n    function erroredTask(request, task, error) {\n        request.abortableTasks.delete(task);\n        task.status = ERRORED$1;\n        var digest = logRecoverableError(request, error, task);\n        emitErrorChunk(request, task.id, digest, error);\n    }\n    function retryTask(request, task) {\n        if (task.status === PENDING$1) {\n            var prevDebugID = debugID;\n            task.status = RENDERING;\n            try {\n                modelRoot = task.model;\n                debugID = task.id;\n                var resolvedModel = renderModelDestructive(request, task, emptyRoot, \"\", task.model);\n                debugID = null;\n                modelRoot = resolvedModel;\n                task.keyPath = null;\n                task.implicitSlot = !1;\n                var currentEnv = (0, request.environmentName)();\n                currentEnv !== task.environmentName && (request.pendingChunks++, emitDebugChunk(request, task.id, {\n                    env: currentEnv\n                }));\n                if (\"object\" === typeof resolvedModel && null !== resolvedModel) request.writtenObjects.set(resolvedModel, serializeByValueID(task.id)), emitChunk(request, task, resolvedModel);\n                else {\n                    var json = stringify(resolvedModel);\n                    emitModelChunk(request, task.id, json);\n                }\n                request.abortableTasks.delete(task);\n                task.status = COMPLETED;\n            } catch (thrownValue) {\n                if (request.status === ABORTING) {\n                    request.abortableTasks.delete(task);\n                    task.status = ABORTED;\n                    var model = stringify(serializeByValueID(request.fatalError));\n                    emitModelChunk(request, task.id, model);\n                } else {\n                    var x = thrownValue === SuspenseException ? getSuspendedThenable() : thrownValue;\n                    if (\"object\" === typeof x && null !== x && \"function\" === typeof x.then) {\n                        task.status = PENDING$1;\n                        task.thenableState = getThenableStateAfterSuspending();\n                        var ping = task.ping;\n                        x.then(ping, ping);\n                    } else erroredTask(request, task, x);\n                }\n            } finally{\n                debugID = prevDebugID;\n            }\n        }\n    }\n    function tryStreamTask(request, task) {\n        var prevDebugID = debugID;\n        debugID = null;\n        try {\n            emitChunk(request, task, task.model);\n        } finally{\n            debugID = prevDebugID;\n        }\n    }\n    function performWork(request) {\n        var prevDispatcher = ReactSharedInternalsServer.H;\n        ReactSharedInternalsServer.H = HooksDispatcher;\n        var prevRequest = currentRequest;\n        currentRequest$1 = currentRequest = request;\n        var hadAbortableTasks = 0 < request.abortableTasks.size;\n        try {\n            var pingedTasks = request.pingedTasks;\n            request.pingedTasks = [];\n            for(var i = 0; i < pingedTasks.length; i++)retryTask(request, pingedTasks[i]);\n            null !== request.destination && flushCompletedChunks(request, request.destination);\n            if (hadAbortableTasks && 0 === request.abortableTasks.size) {\n                var onAllReady = request.onAllReady;\n                onAllReady();\n            }\n        } catch (error) {\n            logRecoverableError(request, error, null), fatalError(request, error);\n        } finally{\n            ReactSharedInternalsServer.H = prevDispatcher, currentRequest$1 = null, currentRequest = prevRequest;\n        }\n    }\n    function flushCompletedChunks(request, destination) {\n        currentView = new Uint8Array(2048);\n        writtenBytes = 0;\n        try {\n            for(var importsChunks = request.completedImportChunks, i = 0; i < importsChunks.length; i++)if (request.pendingChunks--, !writeChunkAndReturn(destination, importsChunks[i])) {\n                request.destination = null;\n                i++;\n                break;\n            }\n            importsChunks.splice(0, i);\n            var hintChunks = request.completedHintChunks;\n            for(i = 0; i < hintChunks.length; i++)if (!writeChunkAndReturn(destination, hintChunks[i])) {\n                request.destination = null;\n                i++;\n                break;\n            }\n            hintChunks.splice(0, i);\n            var regularChunks = request.completedRegularChunks;\n            for(i = 0; i < regularChunks.length; i++)if (request.pendingChunks--, !writeChunkAndReturn(destination, regularChunks[i])) {\n                request.destination = null;\n                i++;\n                break;\n            }\n            regularChunks.splice(0, i);\n            var errorChunks = request.completedErrorChunks;\n            for(i = 0; i < errorChunks.length; i++)if (request.pendingChunks--, !writeChunkAndReturn(destination, errorChunks[i])) {\n                request.destination = null;\n                i++;\n                break;\n            }\n            errorChunks.splice(0, i);\n        } finally{\n            request.flushScheduled = !1, currentView && 0 < writtenBytes && (destination.enqueue(new Uint8Array(currentView.buffer, 0, writtenBytes)), currentView = null, writtenBytes = 0);\n        }\n        0 === request.pendingChunks && (request.status = CLOSED, destination.close(), request.destination = null);\n    }\n    function startWork(request) {\n        request.flushScheduled = null !== request.destination;\n        supportsRequestStorage ? scheduleMicrotask(function() {\n            requestStorage.run(request, performWork, request);\n        }) : scheduleMicrotask(function() {\n            return performWork(request);\n        });\n        setTimeoutOrImmediate(function() {\n            request.status === OPENING && (request.status = 11);\n        }, 0);\n    }\n    function enqueueFlush(request) {\n        !1 === request.flushScheduled && 0 === request.pingedTasks.length && null !== request.destination && (request.flushScheduled = !0, setTimeoutOrImmediate(function() {\n            request.flushScheduled = !1;\n            var destination = request.destination;\n            destination && flushCompletedChunks(request, destination);\n        }, 0));\n    }\n    function startFlowing(request, destination) {\n        if (request.status === CLOSING) request.status = CLOSED, closeWithError(destination, request.fatalError);\n        else if (request.status !== CLOSED && null === request.destination) {\n            request.destination = destination;\n            try {\n                flushCompletedChunks(request, destination);\n            } catch (error) {\n                logRecoverableError(request, error, null), fatalError(request, error);\n            }\n        }\n    }\n    function abort(request, reason) {\n        try {\n            11 >= request.status && (request.status = ABORTING);\n            var abortableTasks = request.abortableTasks;\n            if (0 < abortableTasks.size) {\n                var error = void 0 === reason ? Error(\"The render was aborted by the server without a reason.\") : \"object\" === typeof reason && null !== reason && \"function\" === typeof reason.then ? Error(\"The render was aborted by the server with a promise.\") : reason, digest = logRecoverableError(request, error, null), _errorId2 = request.nextChunkId++;\n                request.fatalError = _errorId2;\n                request.pendingChunks++;\n                emitErrorChunk(request, _errorId2, digest, error);\n                abortableTasks.forEach(function(task) {\n                    if (task.status !== RENDERING) {\n                        task.status = ABORTED;\n                        var ref = serializeByValueID(_errorId2);\n                        task = encodeReferenceChunk(request, task.id, ref);\n                        request.completedErrorChunks.push(task);\n                    }\n                });\n                abortableTasks.clear();\n                var onAllReady = request.onAllReady;\n                onAllReady();\n            }\n            var abortListeners = request.abortListeners;\n            if (0 < abortListeners.size) {\n                var _error = void 0 === reason ? Error(\"The render was aborted by the server without a reason.\") : \"object\" === typeof reason && null !== reason && \"function\" === typeof reason.then ? Error(\"The render was aborted by the server with a promise.\") : reason;\n                abortListeners.forEach(function(callback) {\n                    return callback(_error);\n                });\n                abortListeners.clear();\n            }\n            null !== request.destination && flushCompletedChunks(request, request.destination);\n        } catch (error$2) {\n            logRecoverableError(request, error$2, null), fatalError(request, error$2);\n        }\n    }\n    function resolveServerReference(bundlerConfig, id) {\n        var name = \"\", resolvedModuleData = bundlerConfig[id];\n        if (resolvedModuleData) name = resolvedModuleData.name;\n        else {\n            var idx = id.lastIndexOf(\"#\");\n            -1 !== idx && (name = id.slice(idx + 1), resolvedModuleData = bundlerConfig[id.slice(0, idx)]);\n            if (!resolvedModuleData) throw Error('Could not find the module \"' + id + '\" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');\n        }\n        return resolvedModuleData.async ? [\n            resolvedModuleData.id,\n            resolvedModuleData.chunks,\n            name,\n            1\n        ] : [\n            resolvedModuleData.id,\n            resolvedModuleData.chunks,\n            name\n        ];\n    }\n    function requireAsyncModule(id) {\n        var promise = globalThis.__next_require__(id);\n        if (\"function\" !== typeof promise.then || \"fulfilled\" === promise.status) return null;\n        promise.then(function(value) {\n            promise.status = \"fulfilled\";\n            promise.value = value;\n        }, function(reason) {\n            promise.status = \"rejected\";\n            promise.reason = reason;\n        });\n        return promise;\n    }\n    function ignoreReject() {}\n    function preloadModule(metadata) {\n        for(var chunks = metadata[1], promises = [], i = 0; i < chunks.length;){\n            var chunkId = chunks[i++];\n            chunks[i++];\n            var entry = chunkCache.get(chunkId);\n            if (void 0 === entry) {\n                entry = __webpack_require__.e(chunkId);\n                promises.push(entry);\n                var resolve = chunkCache.set.bind(chunkCache, chunkId, null);\n                entry.then(resolve, ignoreReject);\n                chunkCache.set(chunkId, entry);\n            } else null !== entry && promises.push(entry);\n        }\n        return 4 === metadata.length ? 0 === promises.length ? requireAsyncModule(metadata[0]) : Promise.all(promises).then(function() {\n            return requireAsyncModule(metadata[0]);\n        }) : 0 < promises.length ? Promise.all(promises) : null;\n    }\n    function requireModule(metadata) {\n        var moduleExports = globalThis.__next_require__(metadata[0]);\n        if (4 === metadata.length && \"function\" === typeof moduleExports.then) if (\"fulfilled\" === moduleExports.status) moduleExports = moduleExports.value;\n        else throw moduleExports.reason;\n        return \"*\" === metadata[2] ? moduleExports : \"\" === metadata[2] ? moduleExports.__esModule ? moduleExports.default : moduleExports : moduleExports[metadata[2]];\n    }\n    function Chunk(status, value, reason, response) {\n        this.status = status;\n        this.value = value;\n        this.reason = reason;\n        this._response = response;\n    }\n    function createPendingChunk(response) {\n        return new Chunk(\"pending\", null, null, response);\n    }\n    function wakeChunk(listeners, value) {\n        for(var i = 0; i < listeners.length; i++)(0, listeners[i])(value);\n    }\n    function triggerErrorOnChunk(chunk, error) {\n        if (\"pending\" !== chunk.status && \"blocked\" !== chunk.status) chunk.reason.error(error);\n        else {\n            var listeners = chunk.reason;\n            chunk.status = \"rejected\";\n            chunk.reason = error;\n            null !== listeners && wakeChunk(listeners, error);\n        }\n    }\n    function resolveModelChunk(chunk, value, id) {\n        if (\"pending\" !== chunk.status) chunk = chunk.reason, \"C\" === value[0] ? chunk.close(\"C\" === value ? '\"$undefined\"' : value.slice(1)) : chunk.enqueueModel(value);\n        else {\n            var resolveListeners = chunk.value, rejectListeners = chunk.reason;\n            chunk.status = \"resolved_model\";\n            chunk.value = value;\n            chunk.reason = id;\n            if (null !== resolveListeners) switch(initializeModelChunk(chunk), chunk.status){\n                case \"fulfilled\":\n                    wakeChunk(resolveListeners, chunk.value);\n                    break;\n                case \"pending\":\n                case \"blocked\":\n                case \"cyclic\":\n                    if (chunk.value) for(value = 0; value < resolveListeners.length; value++)chunk.value.push(resolveListeners[value]);\n                    else chunk.value = resolveListeners;\n                    if (chunk.reason) {\n                        if (rejectListeners) for(value = 0; value < rejectListeners.length; value++)chunk.reason.push(rejectListeners[value]);\n                    } else chunk.reason = rejectListeners;\n                    break;\n                case \"rejected\":\n                    rejectListeners && wakeChunk(rejectListeners, chunk.reason);\n            }\n        }\n    }\n    function createResolvedIteratorResultChunk(response, value, done) {\n        return new Chunk(\"resolved_model\", (done ? '{\"done\":true,\"value\":' : '{\"done\":false,\"value\":') + value + \"}\", -1, response);\n    }\n    function resolveIteratorResultChunk(chunk, value, done) {\n        resolveModelChunk(chunk, (done ? '{\"done\":true,\"value\":' : '{\"done\":false,\"value\":') + value + \"}\", -1);\n    }\n    function loadServerReference$1(response, id, bound, parentChunk, parentObject, key) {\n        var serverReference = resolveServerReference(response._bundlerConfig, id);\n        id = preloadModule(serverReference);\n        if (bound) bound = Promise.all([\n            bound,\n            id\n        ]).then(function(_ref) {\n            _ref = _ref[0];\n            var fn = requireModule(serverReference);\n            return fn.bind.apply(fn, [\n                null\n            ].concat(_ref));\n        });\n        else if (id) bound = Promise.resolve(id).then(function() {\n            return requireModule(serverReference);\n        });\n        else return requireModule(serverReference);\n        bound.then(createModelResolver(parentChunk, parentObject, key, !1, response, createModel, []), createModelReject(parentChunk));\n        return null;\n    }\n    function reviveModel(response, parentObj, parentKey, value, reference) {\n        if (\"string\" === typeof value) return parseModelString(response, parentObj, parentKey, value, reference);\n        if (\"object\" === typeof value && null !== value) if (void 0 !== reference && void 0 !== response._temporaryReferences && response._temporaryReferences.set(value, reference), Array.isArray(value)) for(var i = 0; i < value.length; i++)value[i] = reviveModel(response, value, \"\" + i, value[i], void 0 !== reference ? reference + \":\" + i : void 0);\n        else for(i in value)hasOwnProperty.call(value, i) && (parentObj = void 0 !== reference && -1 === i.indexOf(\":\") ? reference + \":\" + i : void 0, parentObj = reviveModel(response, value, i, value[i], parentObj), void 0 !== parentObj ? value[i] = parentObj : delete value[i]);\n        return value;\n    }\n    function initializeModelChunk(chunk) {\n        var prevChunk = initializingChunk, prevBlocked = initializingChunkBlockedModel;\n        initializingChunk = chunk;\n        initializingChunkBlockedModel = null;\n        var rootReference = -1 === chunk.reason ? void 0 : chunk.reason.toString(16), resolvedModel = chunk.value;\n        chunk.status = \"cyclic\";\n        chunk.value = null;\n        chunk.reason = null;\n        try {\n            var rawModel = JSON.parse(resolvedModel), value = reviveModel(chunk._response, {\n                \"\": rawModel\n            }, \"\", rawModel, rootReference);\n            if (null !== initializingChunkBlockedModel && 0 < initializingChunkBlockedModel.deps) initializingChunkBlockedModel.value = value, chunk.status = \"blocked\";\n            else {\n                var resolveListeners = chunk.value;\n                chunk.status = \"fulfilled\";\n                chunk.value = value;\n                null !== resolveListeners && wakeChunk(resolveListeners, value);\n            }\n        } catch (error) {\n            chunk.status = \"rejected\", chunk.reason = error;\n        } finally{\n            initializingChunk = prevChunk, initializingChunkBlockedModel = prevBlocked;\n        }\n    }\n    function reportGlobalError(response, error) {\n        response._closed = !0;\n        response._closedReason = error;\n        response._chunks.forEach(function(chunk) {\n            \"pending\" === chunk.status && triggerErrorOnChunk(chunk, error);\n        });\n    }\n    function getChunk(response, id) {\n        var chunks = response._chunks, chunk = chunks.get(id);\n        chunk || (chunk = response._formData.get(response._prefix + id), chunk = null != chunk ? new Chunk(\"resolved_model\", chunk, id, response) : response._closed ? new Chunk(\"rejected\", null, response._closedReason, response) : createPendingChunk(response), chunks.set(id, chunk));\n        return chunk;\n    }\n    function createModelResolver(chunk, parentObject, key, cyclic, response, map, path) {\n        if (initializingChunkBlockedModel) {\n            var blocked = initializingChunkBlockedModel;\n            cyclic || blocked.deps++;\n        } else blocked = initializingChunkBlockedModel = {\n            deps: cyclic ? 0 : 1,\n            value: null\n        };\n        return function(value) {\n            for(var i = 1; i < path.length; i++)value = value[path[i]];\n            parentObject[key] = map(response, value);\n            \"\" === key && null === blocked.value && (blocked.value = parentObject[key]);\n            blocked.deps--;\n            0 === blocked.deps && \"blocked\" === chunk.status && (value = chunk.value, chunk.status = \"fulfilled\", chunk.value = blocked.value, null !== value && wakeChunk(value, blocked.value));\n        };\n    }\n    function createModelReject(chunk) {\n        return function(error) {\n            return triggerErrorOnChunk(chunk, error);\n        };\n    }\n    function getOutlinedModel(response, reference, parentObject, key, map) {\n        reference = reference.split(\":\");\n        var id = parseInt(reference[0], 16);\n        id = getChunk(response, id);\n        switch(id.status){\n            case \"resolved_model\":\n                initializeModelChunk(id);\n        }\n        switch(id.status){\n            case \"fulfilled\":\n                parentObject = id.value;\n                for(key = 1; key < reference.length; key++)parentObject = parentObject[reference[key]];\n                return map(response, parentObject);\n            case \"pending\":\n            case \"blocked\":\n            case \"cyclic\":\n                var parentChunk = initializingChunk;\n                id.then(createModelResolver(parentChunk, parentObject, key, \"cyclic\" === id.status, response, map, reference), createModelReject(parentChunk));\n                return null;\n            default:\n                throw id.reason;\n        }\n    }\n    function createMap(response, model) {\n        return new Map(model);\n    }\n    function createSet(response, model) {\n        return new Set(model);\n    }\n    function extractIterator(response, model) {\n        return model[Symbol.iterator]();\n    }\n    function createModel(response, model) {\n        return model;\n    }\n    function parseTypedArray(response, reference, constructor, bytesPerElement, parentObject, parentKey) {\n        reference = parseInt(reference.slice(2), 16);\n        reference = response._formData.get(response._prefix + reference);\n        reference = constructor === ArrayBuffer ? reference.arrayBuffer() : reference.arrayBuffer().then(function(buffer) {\n            return new constructor(buffer);\n        });\n        bytesPerElement = initializingChunk;\n        reference.then(createModelResolver(bytesPerElement, parentObject, parentKey, !1, response, createModel, []), createModelReject(bytesPerElement));\n        return null;\n    }\n    function resolveStream(response, id, stream, controller) {\n        var chunks = response._chunks;\n        stream = new Chunk(\"fulfilled\", stream, controller, response);\n        chunks.set(id, stream);\n        response = response._formData.getAll(response._prefix + id);\n        for(id = 0; id < response.length; id++)chunks = response[id], \"C\" === chunks[0] ? controller.close(\"C\" === chunks ? '\"$undefined\"' : chunks.slice(1)) : controller.enqueueModel(chunks);\n    }\n    function parseReadableStream(response, reference, type) {\n        reference = parseInt(reference.slice(2), 16);\n        var controller = null;\n        type = new ReadableStream({\n            type: type,\n            start: function(c) {\n                controller = c;\n            }\n        });\n        var previousBlockedChunk = null;\n        resolveStream(response, reference, type, {\n            enqueueModel: function(json) {\n                if (null === previousBlockedChunk) {\n                    var chunk = new Chunk(\"resolved_model\", json, -1, response);\n                    initializeModelChunk(chunk);\n                    \"fulfilled\" === chunk.status ? controller.enqueue(chunk.value) : (chunk.then(function(v) {\n                        return controller.enqueue(v);\n                    }, function(e) {\n                        return controller.error(e);\n                    }), previousBlockedChunk = chunk);\n                } else {\n                    chunk = previousBlockedChunk;\n                    var _chunk = createPendingChunk(response);\n                    _chunk.then(function(v) {\n                        return controller.enqueue(v);\n                    }, function(e) {\n                        return controller.error(e);\n                    });\n                    previousBlockedChunk = _chunk;\n                    chunk.then(function() {\n                        previousBlockedChunk === _chunk && (previousBlockedChunk = null);\n                        resolveModelChunk(_chunk, json, -1);\n                    });\n                }\n            },\n            close: function() {\n                if (null === previousBlockedChunk) controller.close();\n                else {\n                    var blockedChunk = previousBlockedChunk;\n                    previousBlockedChunk = null;\n                    blockedChunk.then(function() {\n                        return controller.close();\n                    });\n                }\n            },\n            error: function(error) {\n                if (null === previousBlockedChunk) controller.error(error);\n                else {\n                    var blockedChunk = previousBlockedChunk;\n                    previousBlockedChunk = null;\n                    blockedChunk.then(function() {\n                        return controller.error(error);\n                    });\n                }\n            }\n        });\n        return type;\n    }\n    function asyncIterator() {\n        return this;\n    }\n    function createIterator(next) {\n        next = {\n            next: next\n        };\n        next[ASYNC_ITERATOR] = asyncIterator;\n        return next;\n    }\n    function parseAsyncIterable(response, reference, iterator) {\n        reference = parseInt(reference.slice(2), 16);\n        var buffer = [], closed = !1, nextWriteIndex = 0, iterable = _defineProperty({}, ASYNC_ITERATOR, function() {\n            var nextReadIndex = 0;\n            return createIterator(function(arg) {\n                if (void 0 !== arg) throw Error(\"Values cannot be passed to next() of AsyncIterables passed to Client Components.\");\n                if (nextReadIndex === buffer.length) {\n                    if (closed) return new Chunk(\"fulfilled\", {\n                        done: !0,\n                        value: void 0\n                    }, null, response);\n                    buffer[nextReadIndex] = createPendingChunk(response);\n                }\n                return buffer[nextReadIndex++];\n            });\n        });\n        iterator = iterator ? iterable[ASYNC_ITERATOR]() : iterable;\n        resolveStream(response, reference, iterator, {\n            enqueueModel: function(value) {\n                nextWriteIndex === buffer.length ? buffer[nextWriteIndex] = createResolvedIteratorResultChunk(response, value, !1) : resolveIteratorResultChunk(buffer[nextWriteIndex], value, !1);\n                nextWriteIndex++;\n            },\n            close: function(value) {\n                closed = !0;\n                nextWriteIndex === buffer.length ? buffer[nextWriteIndex] = createResolvedIteratorResultChunk(response, value, !0) : resolveIteratorResultChunk(buffer[nextWriteIndex], value, !0);\n                for(nextWriteIndex++; nextWriteIndex < buffer.length;)resolveIteratorResultChunk(buffer[nextWriteIndex++], '\"$undefined\"', !0);\n            },\n            error: function(error) {\n                closed = !0;\n                for(nextWriteIndex === buffer.length && (buffer[nextWriteIndex] = createPendingChunk(response)); nextWriteIndex < buffer.length;)triggerErrorOnChunk(buffer[nextWriteIndex++], error);\n            }\n        });\n        return iterator;\n    }\n    function parseModelString(response, obj, key, value, reference) {\n        if (\"$\" === value[0]) {\n            switch(value[1]){\n                case \"$\":\n                    return value.slice(1);\n                case \"@\":\n                    return obj = parseInt(value.slice(2), 16), getChunk(response, obj);\n                case \"F\":\n                    return value = value.slice(2), value = getOutlinedModel(response, value, obj, key, createModel), loadServerReference$1(response, value.id, value.bound, initializingChunk, obj, key);\n                case \"T\":\n                    if (void 0 === reference || void 0 === response._temporaryReferences) throw Error(\"Could not reference an opaque temporary reference. This is likely due to misconfiguring the temporaryReferences options on the server.\");\n                    return createTemporaryReference(response._temporaryReferences, reference);\n                case \"Q\":\n                    return value = value.slice(2), getOutlinedModel(response, value, obj, key, createMap);\n                case \"W\":\n                    return value = value.slice(2), getOutlinedModel(response, value, obj, key, createSet);\n                case \"K\":\n                    obj = value.slice(2);\n                    var formPrefix = response._prefix + obj + \"_\", data = new FormData();\n                    response._formData.forEach(function(entry, entryKey) {\n                        entryKey.startsWith(formPrefix) && data.append(entryKey.slice(formPrefix.length), entry);\n                    });\n                    return data;\n                case \"i\":\n                    return value = value.slice(2), getOutlinedModel(response, value, obj, key, extractIterator);\n                case \"I\":\n                    return Infinity;\n                case \"-\":\n                    return \"$-0\" === value ? -0 : -Infinity;\n                case \"N\":\n                    return NaN;\n                case \"u\":\n                    return;\n                case \"D\":\n                    return new Date(Date.parse(value.slice(2)));\n                case \"n\":\n                    return BigInt(value.slice(2));\n            }\n            switch(value[1]){\n                case \"A\":\n                    return parseTypedArray(response, value, ArrayBuffer, 1, obj, key);\n                case \"O\":\n                    return parseTypedArray(response, value, Int8Array, 1, obj, key);\n                case \"o\":\n                    return parseTypedArray(response, value, Uint8Array, 1, obj, key);\n                case \"U\":\n                    return parseTypedArray(response, value, Uint8ClampedArray, 1, obj, key);\n                case \"S\":\n                    return parseTypedArray(response, value, Int16Array, 2, obj, key);\n                case \"s\":\n                    return parseTypedArray(response, value, Uint16Array, 2, obj, key);\n                case \"L\":\n                    return parseTypedArray(response, value, Int32Array, 4, obj, key);\n                case \"l\":\n                    return parseTypedArray(response, value, Uint32Array, 4, obj, key);\n                case \"G\":\n                    return parseTypedArray(response, value, Float32Array, 4, obj, key);\n                case \"g\":\n                    return parseTypedArray(response, value, Float64Array, 8, obj, key);\n                case \"M\":\n                    return parseTypedArray(response, value, BigInt64Array, 8, obj, key);\n                case \"m\":\n                    return parseTypedArray(response, value, BigUint64Array, 8, obj, key);\n                case \"V\":\n                    return parseTypedArray(response, value, DataView, 1, obj, key);\n                case \"B\":\n                    return obj = parseInt(value.slice(2), 16), response._formData.get(response._prefix + obj);\n            }\n            switch(value[1]){\n                case \"R\":\n                    return parseReadableStream(response, value, void 0);\n                case \"r\":\n                    return parseReadableStream(response, value, \"bytes\");\n                case \"X\":\n                    return parseAsyncIterable(response, value, !1);\n                case \"x\":\n                    return parseAsyncIterable(response, value, !0);\n            }\n            value = value.slice(1);\n            return getOutlinedModel(response, value, obj, key, createModel);\n        }\n        return value;\n    }\n    function createResponse(bundlerConfig, formFieldPrefix, temporaryReferences) {\n        var backingFormData = 3 < arguments.length && void 0 !== arguments[3] ? arguments[3] : new FormData(), chunks = new Map();\n        return {\n            _bundlerConfig: bundlerConfig,\n            _prefix: formFieldPrefix,\n            _formData: backingFormData,\n            _chunks: chunks,\n            _closed: !1,\n            _closedReason: null,\n            _temporaryReferences: temporaryReferences\n        };\n    }\n    function close(response) {\n        reportGlobalError(response, Error(\"Connection closed.\"));\n    }\n    function loadServerReference(bundlerConfig, id, bound) {\n        var serverReference = resolveServerReference(bundlerConfig, id);\n        bundlerConfig = preloadModule(serverReference);\n        return bound ? Promise.all([\n            bound,\n            bundlerConfig\n        ]).then(function(_ref) {\n            _ref = _ref[0];\n            var fn = requireModule(serverReference);\n            return fn.bind.apply(fn, [\n                null\n            ].concat(_ref));\n        }) : bundlerConfig ? Promise.resolve(bundlerConfig).then(function() {\n            return requireModule(serverReference);\n        }) : Promise.resolve(requireModule(serverReference));\n    }\n    function decodeBoundActionMetaData(body, serverManifest, formFieldPrefix) {\n        body = createResponse(serverManifest, formFieldPrefix, void 0, body);\n        close(body);\n        body = getChunk(body, 0);\n        body.then(function() {});\n        if (\"fulfilled\" !== body.status) throw body.reason;\n        return body.value;\n    }\n    var ReactDOM = __webpack_require__(/*! react-dom */ \"(middleware)/./node_modules/next/dist/compiled/react-dom/react-dom.react-server.js\"), React = __webpack_require__(/*! react */ \"(middleware)/./node_modules/next/dist/compiled/react/react.react-server.js\"), REACT_LEGACY_ELEMENT_TYPE = Symbol.for(\"react.element\"), REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"), REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"), REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"), REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"), REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"), REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"), REACT_MEMO_TYPE = Symbol.for(\"react.memo\"), REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"), REACT_MEMO_CACHE_SENTINEL = Symbol.for(\"react.memo_cache_sentinel\");\n    Symbol.for(\"react.postpone\");\n    var MAYBE_ITERATOR_SYMBOL = Symbol.iterator, ASYNC_ITERATOR = Symbol.asyncIterator, LocalPromise = Promise, scheduleMicrotask = \"function\" === typeof queueMicrotask ? queueMicrotask : function(callback) {\n        LocalPromise.resolve(null).then(callback).catch(handleErrorInNextTick);\n    }, currentView = null, writtenBytes = 0, textEncoder = new TextEncoder(), CLIENT_REFERENCE_TAG$1 = Symbol.for(\"react.client.reference\"), SERVER_REFERENCE_TAG = Symbol.for(\"react.server.reference\"), FunctionBind = Function.prototype.bind, ArraySlice = Array.prototype.slice, PROMISE_PROTOTYPE = Promise.prototype, deepProxyHandlers = {\n        get: function(target, name) {\n            switch(name){\n                case \"$$typeof\":\n                    return target.$$typeof;\n                case \"$$id\":\n                    return target.$$id;\n                case \"$$async\":\n                    return target.$$async;\n                case \"name\":\n                    return target.name;\n                case \"displayName\":\n                    return;\n                case \"defaultProps\":\n                    return;\n                case \"toJSON\":\n                    return;\n                case Symbol.toPrimitive:\n                    return Object.prototype[Symbol.toPrimitive];\n                case Symbol.toStringTag:\n                    return Object.prototype[Symbol.toStringTag];\n                case \"Provider\":\n                    throw Error(\"Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.\");\n                case \"then\":\n                    throw Error(\"Cannot await or return from a thenable. You cannot await a client module from a server component.\");\n            }\n            throw Error(\"Cannot access \" + (String(target.name) + \".\" + String(name)) + \" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.\");\n        },\n        set: function() {\n            throw Error(\"Cannot assign to a client module from a server module.\");\n        }\n    }, proxyHandlers$1 = {\n        get: function(target, name) {\n            return getReference(target, name);\n        },\n        getOwnPropertyDescriptor: function(target, name) {\n            var descriptor = Object.getOwnPropertyDescriptor(target, name);\n            descriptor || (descriptor = {\n                value: getReference(target, name),\n                writable: !1,\n                configurable: !1,\n                enumerable: !1\n            }, Object.defineProperty(target, name, descriptor));\n            return descriptor;\n        },\n        getPrototypeOf: function() {\n            return PROMISE_PROTOTYPE;\n        },\n        set: function() {\n            throw Error(\"Cannot assign to a client module from a server module.\");\n        }\n    }, ReactDOMSharedInternals = ReactDOM.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, previousDispatcher = ReactDOMSharedInternals.d;\n    ReactDOMSharedInternals.d = {\n        f: previousDispatcher.f,\n        r: previousDispatcher.r,\n        D: function(href) {\n            if (\"string\" === typeof href && href) {\n                var request = resolveRequest();\n                if (request) {\n                    var hints = request.hints, key = \"D|\" + href;\n                    hints.has(key) || (hints.add(key), emitHint(request, \"D\", href));\n                } else previousDispatcher.D(href);\n            }\n        },\n        C: function(href, crossOrigin) {\n            if (\"string\" === typeof href) {\n                var request = resolveRequest();\n                if (request) {\n                    var hints = request.hints, key = \"C|\" + (null == crossOrigin ? \"null\" : crossOrigin) + \"|\" + href;\n                    hints.has(key) || (hints.add(key), \"string\" === typeof crossOrigin ? emitHint(request, \"C\", [\n                        href,\n                        crossOrigin\n                    ]) : emitHint(request, \"C\", href));\n                } else previousDispatcher.C(href, crossOrigin);\n            }\n        },\n        L: function(href, as, options) {\n            if (\"string\" === typeof href) {\n                var request = resolveRequest();\n                if (request) {\n                    var hints = request.hints, key = \"L\";\n                    if (\"image\" === as && options) {\n                        var imageSrcSet = options.imageSrcSet, imageSizes = options.imageSizes, uniquePart = \"\";\n                        \"string\" === typeof imageSrcSet && \"\" !== imageSrcSet ? (uniquePart += \"[\" + imageSrcSet + \"]\", \"string\" === typeof imageSizes && (uniquePart += \"[\" + imageSizes + \"]\")) : uniquePart += \"[][]\" + href;\n                        key += \"[image]\" + uniquePart;\n                    } else key += \"[\" + as + \"]\" + href;\n                    hints.has(key) || (hints.add(key), (options = trimOptions(options)) ? emitHint(request, \"L\", [\n                        href,\n                        as,\n                        options\n                    ]) : emitHint(request, \"L\", [\n                        href,\n                        as\n                    ]));\n                } else previousDispatcher.L(href, as, options);\n            }\n        },\n        m: function(href, options) {\n            if (\"string\" === typeof href) {\n                var request = resolveRequest();\n                if (request) {\n                    var hints = request.hints, key = \"m|\" + href;\n                    if (hints.has(key)) return;\n                    hints.add(key);\n                    return (options = trimOptions(options)) ? emitHint(request, \"m\", [\n                        href,\n                        options\n                    ]) : emitHint(request, \"m\", href);\n                }\n                previousDispatcher.m(href, options);\n            }\n        },\n        X: function(src, options) {\n            if (\"string\" === typeof src) {\n                var request = resolveRequest();\n                if (request) {\n                    var hints = request.hints, key = \"X|\" + src;\n                    if (hints.has(key)) return;\n                    hints.add(key);\n                    return (options = trimOptions(options)) ? emitHint(request, \"X\", [\n                        src,\n                        options\n                    ]) : emitHint(request, \"X\", src);\n                }\n                previousDispatcher.X(src, options);\n            }\n        },\n        S: function(href, precedence, options) {\n            if (\"string\" === typeof href) {\n                var request = resolveRequest();\n                if (request) {\n                    var hints = request.hints, key = \"S|\" + href;\n                    if (hints.has(key)) return;\n                    hints.add(key);\n                    return (options = trimOptions(options)) ? emitHint(request, \"S\", [\n                        href,\n                        \"string\" === typeof precedence ? precedence : 0,\n                        options\n                    ]) : \"string\" === typeof precedence ? emitHint(request, \"S\", [\n                        href,\n                        precedence\n                    ]) : emitHint(request, \"S\", href);\n                }\n                previousDispatcher.S(href, precedence, options);\n            }\n        },\n        M: function(src, options) {\n            if (\"string\" === typeof src) {\n                var request = resolveRequest();\n                if (request) {\n                    var hints = request.hints, key = \"M|\" + src;\n                    if (hints.has(key)) return;\n                    hints.add(key);\n                    return (options = trimOptions(options)) ? emitHint(request, \"M\", [\n                        src,\n                        options\n                    ]) : emitHint(request, \"M\", src);\n                }\n                previousDispatcher.M(src, options);\n            }\n        }\n    };\n    var frameRegExp = /^ {3} at (?:(.+) \\((?:(.+):(\\d+):(\\d+)|<anonymous>)\\)|(?:async )?(.+):(\\d+):(\\d+)|<anonymous>)$/, supportsRequestStorage = \"function\" === typeof AsyncLocalStorage, requestStorage = supportsRequestStorage ? new AsyncLocalStorage() : null, supportsComponentStorage = supportsRequestStorage, componentStorage = supportsComponentStorage ? new AsyncLocalStorage() : null;\n    \"object\" === typeof async_hooks ? async_hooks.createHook : function() {\n        return {\n            enable: function() {},\n            disable: function() {}\n        };\n    };\n    \"object\" === typeof async_hooks ? async_hooks.executionAsyncId : null;\n    var TEMPORARY_REFERENCE_TAG = Symbol.for(\"react.temporary.reference\"), proxyHandlers = {\n        get: function(target, name) {\n            switch(name){\n                case \"$$typeof\":\n                    return target.$$typeof;\n                case \"name\":\n                    return;\n                case \"displayName\":\n                    return;\n                case \"defaultProps\":\n                    return;\n                case \"toJSON\":\n                    return;\n                case Symbol.toPrimitive:\n                    return Object.prototype[Symbol.toPrimitive];\n                case Symbol.toStringTag:\n                    return Object.prototype[Symbol.toStringTag];\n                case \"Provider\":\n                    throw Error(\"Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.\");\n            }\n            throw Error(\"Cannot access \" + String(name) + \" on the server. You cannot dot into a temporary client reference from a server component. You can only pass the value through to the client.\");\n        },\n        set: function() {\n            throw Error(\"Cannot assign to a temporary client reference from a server module.\");\n        }\n    }, SuspenseException = Error(\"Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\\n\\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`.\"), suspendedThenable = null, currentRequest$1 = null, thenableIndexCounter = 0, thenableState = null, currentComponentDebugInfo = null, HooksDispatcher = {\n        readContext: unsupportedContext,\n        use: function(usable) {\n            if (null !== usable && \"object\" === typeof usable || \"function\" === typeof usable) {\n                if (\"function\" === typeof usable.then) {\n                    var index = thenableIndexCounter;\n                    thenableIndexCounter += 1;\n                    null === thenableState && (thenableState = []);\n                    return trackUsedThenable(thenableState, usable, index);\n                }\n                usable.$$typeof === REACT_CONTEXT_TYPE && unsupportedContext();\n            }\n            if (isClientReference(usable)) {\n                if (null != usable.value && usable.value.$$typeof === REACT_CONTEXT_TYPE) throw Error(\"Cannot read a Client Context from a Server Component.\");\n                throw Error(\"Cannot use() an already resolved Client Reference.\");\n            }\n            throw Error(\"An unsupported type was passed to use(): \" + String(usable));\n        },\n        useCallback: function(callback) {\n            return callback;\n        },\n        useContext: unsupportedContext,\n        useEffect: unsupportedHook,\n        useImperativeHandle: unsupportedHook,\n        useLayoutEffect: unsupportedHook,\n        useInsertionEffect: unsupportedHook,\n        useMemo: function(nextCreate) {\n            return nextCreate();\n        },\n        useReducer: unsupportedHook,\n        useRef: unsupportedHook,\n        useState: unsupportedHook,\n        useDebugValue: function() {},\n        useDeferredValue: unsupportedHook,\n        useTransition: unsupportedHook,\n        useSyncExternalStore: unsupportedHook,\n        useId: function() {\n            if (null === currentRequest$1) throw Error(\"useId can only be used while React is rendering\");\n            var id = currentRequest$1.identifierCount++;\n            return \":\" + currentRequest$1.identifierPrefix + \"S\" + id.toString(32) + \":\";\n        },\n        useHostTransitionStatus: unsupportedHook,\n        useFormState: unsupportedHook,\n        useActionState: unsupportedHook,\n        useOptimistic: unsupportedHook,\n        useMemoCache: function(size) {\n            for(var data = Array(size), i = 0; i < size; i++)data[i] = REACT_MEMO_CACHE_SENTINEL;\n            return data;\n        },\n        useCacheRefresh: function() {\n            return unsupportedRefresh;\n        }\n    }, currentOwner = null, DefaultAsyncDispatcher = {\n        getCacheForType: function(resourceType) {\n            var cache = (cache = resolveRequest()) ? cache.cache : new Map();\n            var entry = cache.get(resourceType);\n            void 0 === entry && (entry = resourceType(), cache.set(resourceType, entry));\n            return entry;\n        }\n    };\n    DefaultAsyncDispatcher.getOwner = resolveOwner;\n    var ReactSharedInternalsServer = React.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;\n    if (!ReactSharedInternalsServer) throw Error('The \"react\" package in this environment is not configured correctly. The \"react-server\" condition must be enabled in any environment that runs React Server Components.');\n    var prefix, suffix;\n    new (\"function\" === typeof WeakMap ? WeakMap : Map)();\n    var callComponent = {\n        \"react-stack-bottom-frame\": function(Component, props, componentDebugInfo) {\n            currentOwner = componentDebugInfo;\n            try {\n                return Component(props, void 0);\n            } finally{\n                currentOwner = null;\n            }\n        }\n    }, callComponentInDEV = callComponent[\"react-stack-bottom-frame\"].bind(callComponent), callLazyInit = {\n        \"react-stack-bottom-frame\": function(lazy) {\n            var init = lazy._init;\n            return init(lazy._payload);\n        }\n    }, callLazyInitInDEV = callLazyInit[\"react-stack-bottom-frame\"].bind(callLazyInit), callIterator = {\n        \"react-stack-bottom-frame\": function(iterator, progress, error) {\n            iterator.next().then(progress, error);\n        }\n    }, callIteratorInDEV = callIterator[\"react-stack-bottom-frame\"].bind(callIterator), isArrayImpl = Array.isArray, getPrototypeOf = Object.getPrototypeOf, jsxPropsParents = new WeakMap(), jsxChildrenParents = new WeakMap(), CLIENT_REFERENCE_TAG = Symbol.for(\"react.client.reference\"), doNotLimit = new WeakSet();\n    \"object\" === typeof console && null !== console && (patchConsole(console, \"assert\"), patchConsole(console, \"debug\"), patchConsole(console, \"dir\"), patchConsole(console, \"dirxml\"), patchConsole(console, \"error\"), patchConsole(console, \"group\"), patchConsole(console, \"groupCollapsed\"), patchConsole(console, \"groupEnd\"), patchConsole(console, \"info\"), patchConsole(console, \"log\"), patchConsole(console, \"table\"), patchConsole(console, \"trace\"), patchConsole(console, \"warn\"));\n    var ObjectPrototype = Object.prototype, stringify = JSON.stringify, PENDING$1 = 0, COMPLETED = 1, ABORTED = 3, ERRORED$1 = 4, RENDERING = 5, OPENING = 10, ABORTING = 12, CLOSING = 13, CLOSED = 14, PRERENDER = 21, currentRequest = null, debugID = null, modelRoot = !1, emptyRoot = {}, chunkCache = new Map(), hasOwnProperty = Object.prototype.hasOwnProperty;\n    Chunk.prototype = Object.create(Promise.prototype);\n    Chunk.prototype.then = function(resolve, reject) {\n        switch(this.status){\n            case \"resolved_model\":\n                initializeModelChunk(this);\n        }\n        switch(this.status){\n            case \"fulfilled\":\n                resolve(this.value);\n                break;\n            case \"pending\":\n            case \"blocked\":\n            case \"cyclic\":\n                resolve && (null === this.value && (this.value = []), this.value.push(resolve));\n                reject && (null === this.reason && (this.reason = []), this.reason.push(reject));\n                break;\n            default:\n                reject(this.reason);\n        }\n    };\n    var initializingChunk = null, initializingChunkBlockedModel = null;\n    exports.createClientModuleProxy = function(moduleId) {\n        moduleId = registerClientReferenceImpl({}, moduleId, !1);\n        return new Proxy(moduleId, proxyHandlers$1);\n    };\n    exports.createTemporaryReferenceSet = function() {\n        return new WeakMap();\n    };\n    exports.decodeAction = function(body, serverManifest) {\n        var formData = new FormData(), action = null;\n        body.forEach(function(value, key) {\n            key.startsWith(\"$ACTION_\") ? key.startsWith(\"$ACTION_REF_\") ? (value = \"$ACTION_\" + key.slice(12) + \":\", value = decodeBoundActionMetaData(body, serverManifest, value), action = loadServerReference(serverManifest, value.id, value.bound)) : key.startsWith(\"$ACTION_ID_\") && (value = key.slice(11), action = loadServerReference(serverManifest, value, null)) : formData.append(key, value);\n        });\n        return null === action ? null : action.then(function(fn) {\n            return fn.bind(null, formData);\n        });\n    };\n    exports.decodeFormState = function(actionResult, body, serverManifest) {\n        var keyPath = body.get(\"$ACTION_KEY\");\n        if (\"string\" !== typeof keyPath) return Promise.resolve(null);\n        var metaData = null;\n        body.forEach(function(value, key) {\n            key.startsWith(\"$ACTION_REF_\") && (value = \"$ACTION_\" + key.slice(12) + \":\", metaData = decodeBoundActionMetaData(body, serverManifest, value));\n        });\n        if (null === metaData) return Promise.resolve(null);\n        var referenceId = metaData.id;\n        return Promise.resolve(metaData.bound).then(function(bound) {\n            return null === bound ? null : [\n                actionResult,\n                keyPath,\n                referenceId,\n                bound.length - 1\n            ];\n        });\n    };\n    exports.decodeReply = function(body, webpackMap, options) {\n        if (\"string\" === typeof body) {\n            var form = new FormData();\n            form.append(\"0\", body);\n            body = form;\n        }\n        body = createResponse(webpackMap, \"\", options ? options.temporaryReferences : void 0, body);\n        webpackMap = getChunk(body, 0);\n        close(body);\n        return webpackMap;\n    };\n    exports.decodeReplyFromAsyncIterable = function(iterable, webpackMap, options) {\n        function progress(entry) {\n            if (entry.done) close(response$jscomp$0);\n            else {\n                entry = entry.value;\n                var name = entry[0];\n                entry = entry[1];\n                if (\"string\" === typeof entry) {\n                    var response = response$jscomp$0;\n                    response._formData.append(name, entry);\n                    var prefix = response._prefix;\n                    name.startsWith(prefix) && (response = response._chunks, name = +name.slice(prefix.length), (prefix = response.get(name)) && resolveModelChunk(prefix, entry, name));\n                } else response$jscomp$0._formData.append(name, entry);\n                iterator.next().then(progress, error);\n            }\n        }\n        function error(reason) {\n            reportGlobalError(response$jscomp$0, reason);\n            \"function\" === typeof iterator.throw && iterator.throw(reason).then(error, error);\n        }\n        var iterator = iterable[ASYNC_ITERATOR](), response$jscomp$0 = createResponse(webpackMap, \"\", options ? options.temporaryReferences : void 0);\n        iterator.next().then(progress, error);\n        return getChunk(response$jscomp$0, 0);\n    };\n    exports.registerClientReference = function(proxyImplementation, id, exportName) {\n        return registerClientReferenceImpl(proxyImplementation, id + \"#\" + exportName, !1);\n    };\n    exports.registerServerReference = function(reference, id, exportName) {\n        return Object.defineProperties(reference, {\n            $$typeof: {\n                value: SERVER_REFERENCE_TAG\n            },\n            $$id: {\n                value: null === exportName ? id : id + \"#\" + exportName,\n                configurable: !0\n            },\n            $$bound: {\n                value: null,\n                configurable: !0\n            },\n            $$location: {\n                value: Error(\"react-stack-top-frame\"),\n                configurable: !0\n            },\n            bind: {\n                value: bind,\n                configurable: !0\n            }\n        });\n    };\n    // This is a patch added by Next.js\n    const setTimeoutOrImmediate = typeof globalThis['set' + 'Immediate'] === 'function' && // edge runtime sandbox defines a stub for setImmediate\n    // (see 'addStub' in packages/next/src/server/web/sandbox/context.ts)\n    // but it's made non-enumerable, so we can detect it\n    globalThis.propertyIsEnumerable('setImmediate') ? globalThis['set' + 'Immediate'] : setTimeout;\n    exports.renderToReadableStream = function(model, webpackMap, options) {\n        var request = new RequestInstance(20, model, webpackMap, options ? options.onError : void 0, options ? options.identifierPrefix : void 0, options ? options.onPostpone : void 0, options ? options.temporaryReferences : void 0, options ? options.environmentName : void 0, options ? options.filterStackFrame : void 0, noop, noop);\n        if (options && options.signal) {\n            var signal = options.signal;\n            if (signal.aborted) abort(request, signal.reason);\n            else {\n                var listener = function() {\n                    abort(request, signal.reason);\n                    signal.removeEventListener(\"abort\", listener);\n                };\n                signal.addEventListener(\"abort\", listener);\n            }\n        }\n        return new ReadableStream({\n            type: \"bytes\",\n            start: function() {\n                startWork(request);\n            },\n            pull: function(controller) {\n                startFlowing(request, controller);\n            },\n            cancel: function(reason) {\n                request.destination = null;\n                abort(request, reason);\n            }\n        }, {\n            highWaterMark: 0\n        });\n    };\n    exports.unstable_prerender = function(model, webpackMap, options) {\n        return new Promise(function(resolve, reject) {\n            var request = new RequestInstance(PRERENDER, model, webpackMap, options ? options.onError : void 0, options ? options.identifierPrefix : void 0, options ? options.onPostpone : void 0, options ? options.temporaryReferences : void 0, options ? options.environmentName : void 0, options ? options.filterStackFrame : void 0, function() {\n                var stream = new ReadableStream({\n                    type: \"bytes\",\n                    start: function() {\n                        startWork(request);\n                    },\n                    pull: function(controller) {\n                        startFlowing(request, controller);\n                    },\n                    cancel: function(reason) {\n                        request.destination = null;\n                        abort(request, reason);\n                    }\n                }, {\n                    highWaterMark: 0\n                });\n                resolve({\n                    prelude: stream\n                });\n            }, reject);\n            if (options && options.signal) {\n                var signal = options.signal;\n                if (signal.aborted) abort(request, signal.reason);\n                else {\n                    var listener = function() {\n                        abort(request, signal.reason);\n                        signal.removeEventListener(\"abort\", listener);\n                    };\n                    signal.addEventListener(\"abort\", listener);\n                }\n            }\n            startWork(request);\n        });\n    };\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-server.edge.development.js\n");

/***/ })

});