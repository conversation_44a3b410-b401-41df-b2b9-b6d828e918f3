"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _convex_dev_auth_nextjs_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @convex-dev/auth/nextjs/server */ \"(middleware)/./node_modules/@convex-dev/auth/dist/nextjs/server/routeMatcher.js\");\n/* harmony import */ var _convex_dev_auth_nextjs_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @convex-dev/auth/nextjs/server */ \"(middleware)/./node_modules/@convex-dev/auth/dist/nextjs/server/index.js\");\n\nconst isSignInPage = (0,_convex_dev_auth_nextjs_server__WEBPACK_IMPORTED_MODULE_0__.createRouteMatcher)([\n    \"/signin\"\n]);\nconst isProtectedRoute = (0,_convex_dev_auth_nextjs_server__WEBPACK_IMPORTED_MODULE_0__.createRouteMatcher)([\n    \"/dashboard\",\n    \"/server\",\n    \"/test-upload\"\n]);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_convex_dev_auth_nextjs_server__WEBPACK_IMPORTED_MODULE_1__.convexAuthNextjsMiddleware)(async (request, { convexAuth })=>{\n    if (isSignInPage(request) && await convexAuth.isAuthenticated()) {\n        return (0,_convex_dev_auth_nextjs_server__WEBPACK_IMPORTED_MODULE_1__.nextjsMiddlewareRedirect)(request, \"/dashboard\");\n    }\n    if (isProtectedRoute(request) && !await convexAuth.isAuthenticated()) {\n        return (0,_convex_dev_auth_nextjs_server__WEBPACK_IMPORTED_MODULE_1__.nextjsMiddlewareRedirect)(request, \"/signin\");\n    }\n}));\nconst config = {\n    // The following matcher runs middleware on all routes\n    // except static assets.\n    matcher: [\n        \"/((?!.*\\\\..*|_next).*)\",\n        \"/\",\n        \"/(api|trpc)(.*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});