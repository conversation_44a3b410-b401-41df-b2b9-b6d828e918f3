{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!.*\\..*|_next).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!.*\\..*|_next).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "2QkCbBmQuABmmVswGVmxW", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "3sdKLMEKp4NqJJKQb1ZBNsrGgLkHO8+ywddIvkLPjfo=", "__NEXT_PREVIEW_MODE_ID": "6dbc591f027ab6178ec21c4da341bafa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "14651f8f9eaa09021cda0715d1ef3a6501448c7da986a54e50cec22d78c6ec6e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1c3dd66c56ad0f06e9574721d3aece7f8d806ffd1627804df49a3ffd5ec36a4d"}}}, "functions": {}, "sortedMiddleware": ["/"]}