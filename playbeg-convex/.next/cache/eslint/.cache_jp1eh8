[{"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx": "1", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/layout.tsx": "2", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/page.tsx": "3", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/server/inner.tsx": "4", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/server/page.tsx": "5", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx": "6", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/test-upload/page.tsx": "7", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ConvexClientProvider.tsx": "8", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/EnhancedSessionControls.tsx": "9", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SessionAnalyticsDashboard.tsx": "10", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SessionCollaboration.tsx": "11", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SessionScheduling.tsx": "12", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SessionTemplates.tsx": "13", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/AudienceEngagement.tsx": "14", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx": "15", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/FileManager.tsx": "16", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/LiveAnalytics.tsx": "17", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/NotificationCenter.tsx": "18", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx": "19", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/badge.tsx": "20", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/button.tsx": "21", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/card.tsx": "22", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/file-upload.tsx": "23", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/form.tsx": "24", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/input.tsx": "25", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/label.tsx": "26", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/progress.tsx": "27", "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/lib/utils.ts": "28"}, {"size": 5383, "mtime": 1750680599828, "results": "29", "hashOfConfig": "30"}, {"size": 1014, "mtime": 1749085239000, "results": "31", "hashOfConfig": "30"}, {"size": 759, "mtime": 1750676087688, "results": "32", "hashOfConfig": "30"}, {"size": 1439, "mtime": 1750680748828, "results": "33", "hashOfConfig": "30"}, {"size": 833, "mtime": 1750680687507, "results": "34", "hashOfConfig": "30"}, {"size": 6845, "mtime": 1750675982331, "results": "35", "hashOfConfig": "30"}, {"size": 2471, "mtime": 1750678915454, "results": "36", "hashOfConfig": "30"}, {"size": 451, "mtime": 1749085239000, "results": "37", "hashOfConfig": "30"}, {"size": 28074, "mtime": 1750680558641, "results": "38", "hashOfConfig": "30"}, {"size": 15833, "mtime": 1750680383609, "results": "39", "hashOfConfig": "30"}, {"size": 21659, "mtime": 1750680461234, "results": "40", "hashOfConfig": "30"}, {"size": 17955, "mtime": 1750680309880, "results": "41", "hashOfConfig": "30"}, {"size": 18466, "mtime": 1750680242428, "results": "42", "hashOfConfig": "30"}, {"size": 10393, "mtime": 1750679408497, "results": "43", "hashOfConfig": "30"}, {"size": 9791, "mtime": 1750678822293, "results": "44", "hashOfConfig": "30"}, {"size": 9422, "mtime": 1750678755150, "results": "45", "hashOfConfig": "30"}, {"size": 11505, "mtime": 1750679314127, "results": "46", "hashOfConfig": "30"}, {"size": 11235, "mtime": 1750679361111, "results": "47", "hashOfConfig": "30"}, {"size": 8711, "mtime": 1750678212499, "results": "48", "hashOfConfig": "30"}, {"size": 1127, "mtime": 1750678297908, "results": "49", "hashOfConfig": "30"}, {"size": 2003, "mtime": 1750675891702, "results": "50", "hashOfConfig": "30"}, {"size": 1848, "mtime": 1750678287539, "results": "51", "hashOfConfig": "30"}, {"size": 9295, "mtime": 1750678705974, "results": "52", "hashOfConfig": "30"}, {"size": 4085, "mtime": 1750675928092, "results": "53", "hashOfConfig": "30"}, {"size": 791, "mtime": 1750675903435, "results": "54", "hashOfConfig": "30"}, {"size": 710, "mtime": 1750675942983, "results": "55", "hashOfConfig": "30"}, {"size": 790, "mtime": 1750678716720, "results": "56", "hashOfConfig": "30"}, {"size": 166, "mtime": 1750675874260, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1t2t0bq", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx", ["142"], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/layout.tsx", [], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/page.tsx", [], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/server/inner.tsx", [], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/server/page.tsx", [], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx", ["143", "144"], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/test-upload/page.tsx", [], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ConvexClientProvider.tsx", [], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/EnhancedSessionControls.tsx", ["145", "146", "147", "148", "149", "150", "151", "152", "153", "154"], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SessionAnalyticsDashboard.tsx", ["155", "156", "157", "158", "159", "160", "161", "162"], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SessionCollaboration.tsx", ["163", "164", "165", "166", "167", "168", "169"], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SessionScheduling.tsx", ["170", "171", "172", "173"], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SessionTemplates.tsx", ["174", "175", "176"], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/AudienceEngagement.tsx", ["177", "178", "179", "180", "181", "182", "183", "184", "185"], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx", ["186", "187", "188", "189"], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/FileManager.tsx", ["190", "191", "192", "193"], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/LiveAnalytics.tsx", ["194", "195", "196", "197", "198", "199"], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/NotificationCenter.tsx", ["200", "201", "202", "203", "204"], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx", [], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/badge.tsx", [], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/button.tsx", [], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/card.tsx", [], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/file-upload.tsx", ["205", "206"], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/form.tsx", [], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/input.tsx", [], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/label.tsx", [], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/progress.tsx", [], [], "/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/lib/utils.ts", [], [], {"ruleId": "207", "severity": 2, "message": "208", "line": 21, "column": 9, "nodeType": null, "messageId": "209", "endLine": 21, "endColumn": 18}, {"ruleId": "210", "severity": 2, "message": "211", "line": 56, "column": 21, "nodeType": "212", "messageId": "213", "endLine": 56, "endColumn": 24, "suggestions": "214"}, {"ruleId": "215", "severity": 2, "message": "216", "line": 73, "column": 56, "nodeType": "217", "messageId": "218", "suggestions": "219"}, {"ruleId": "207", "severity": 2, "message": "220", "line": 12, "column": 11, "nodeType": null, "messageId": "209", "endLine": 12, "endColumn": 26}, {"ruleId": "207", "severity": 2, "message": "221", "line": 63, "column": 9, "nodeType": null, "messageId": "209", "endLine": 63, "endColumn": 17}, {"ruleId": "207", "severity": 2, "message": "222", "line": 94, "column": 9, "nodeType": null, "messageId": "209", "endLine": 94, "endColumn": 20}, {"ruleId": "210", "severity": 2, "message": "211", "line": 324, "column": 20, "nodeType": "212", "messageId": "213", "endLine": 324, "endColumn": 23, "suggestions": "223"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 324, "column": 36, "nodeType": "212", "messageId": "213", "endLine": 324, "endColumn": 39, "suggestions": "224"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 441, "column": 20, "nodeType": "212", "messageId": "213", "endLine": 441, "endColumn": 23, "suggestions": "225"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 441, "column": 36, "nodeType": "212", "messageId": "213", "endLine": 441, "endColumn": 39, "suggestions": "226"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 494, "column": 54, "nodeType": "212", "messageId": "213", "endLine": 494, "endColumn": 57, "suggestions": "227"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 575, "column": 20, "nodeType": "212", "messageId": "213", "endLine": 575, "endColumn": 23, "suggestions": "228"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 575, "column": 36, "nodeType": "212", "messageId": "213", "endLine": 575, "endColumn": 39, "suggestions": "229"}, {"ruleId": "207", "severity": 2, "message": "230", "line": 14, "column": 10, "nodeType": null, "messageId": "209", "endLine": 14, "endColumn": 24}, {"ruleId": "207", "severity": 2, "message": "231", "line": 14, "column": 26, "nodeType": null, "messageId": "209", "endLine": 14, "endColumn": 43}, {"ruleId": "232", "severity": 2, "message": "233", "line": 22, "column": 37, "nodeType": "234", "endLine": 22, "endColumn": 45}, {"ruleId": "210", "severity": 2, "message": "211", "line": 59, "column": 61, "nodeType": "212", "messageId": "213", "endLine": 59, "endColumn": 64, "suggestions": "235"}, {"ruleId": "207", "severity": 2, "message": "236", "line": 115, "column": 59, "nodeType": null, "messageId": "209", "endLine": 115, "endColumn": 64}, {"ruleId": "207", "severity": 2, "message": "236", "line": 151, "column": 62, "nodeType": null, "messageId": "209", "endLine": 151, "endColumn": 67}, {"ruleId": "207", "severity": 2, "message": "236", "line": 173, "column": 60, "nodeType": null, "messageId": "209", "endLine": 173, "endColumn": 65}, {"ruleId": "207", "severity": 2, "message": "236", "line": 292, "column": 70, "nodeType": null, "messageId": "209", "endLine": 292, "endColumn": 75}, {"ruleId": "210", "severity": 2, "message": "211", "line": 55, "column": 55, "nodeType": "212", "messageId": "213", "endLine": 55, "endColumn": 58, "suggestions": "237"}, {"ruleId": "207", "severity": 2, "message": "238", "line": 68, "column": 9, "nodeType": null, "messageId": "209", "endLine": 68, "endColumn": 30}, {"ruleId": "210", "severity": 2, "message": "211", "line": 80, "column": 96, "nodeType": "212", "messageId": "213", "endLine": 80, "endColumn": 99, "suggestions": "239"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 293, "column": 20, "nodeType": "212", "messageId": "213", "endLine": 293, "endColumn": 23, "suggestions": "240"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 349, "column": 21, "nodeType": "212", "messageId": "213", "endLine": 349, "endColumn": 24, "suggestions": "241"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 477, "column": 23, "nodeType": "212", "messageId": "213", "endLine": 477, "endColumn": 26, "suggestions": "242"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 514, "column": 90, "nodeType": "212", "messageId": "213", "endLine": 514, "endColumn": 93, "suggestions": "243"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 35, "column": 70, "nodeType": "212", "messageId": "213", "endLine": 35, "endColumn": 73, "suggestions": "244"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 54, "column": 53, "nodeType": "212", "messageId": "213", "endLine": 54, "endColumn": 56, "suggestions": "245"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 277, "column": 20, "nodeType": "212", "messageId": "213", "endLine": 277, "endColumn": 23, "suggestions": "246"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 278, "column": 14, "nodeType": "212", "messageId": "213", "endLine": 278, "endColumn": 17, "suggestions": "247"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 12, "column": 19, "nodeType": "212", "messageId": "213", "endLine": 12, "endColumn": 22, "suggestions": "248"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 57, "column": 53, "nodeType": "212", "messageId": "213", "endLine": 57, "endColumn": 56, "suggestions": "249"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 300, "column": 20, "nodeType": "212", "messageId": "213", "endLine": 300, "endColumn": 23, "suggestions": "250"}, {"ruleId": "207", "severity": 2, "message": "251", "line": 11, "column": 3, "nodeType": null, "messageId": "209", "endLine": 11, "endColumn": 11}, {"ruleId": "207", "severity": 2, "message": "252", "line": 12, "column": 3, "nodeType": null, "messageId": "209", "endLine": 12, "endColumn": 13}, {"ruleId": "207", "severity": 2, "message": "253", "line": 17, "column": 20, "nodeType": null, "messageId": "209", "endLine": 17, "endColumn": 29}, {"ruleId": "210", "severity": 2, "message": "211", "line": 30, "column": 29, "nodeType": "212", "messageId": "213", "endLine": 30, "endColumn": 32, "suggestions": "254"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 36, "column": 29, "nodeType": "212", "messageId": "213", "endLine": 36, "endColumn": 32, "suggestions": "255"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 42, "column": 29, "nodeType": "212", "messageId": "213", "endLine": 42, "endColumn": 32, "suggestions": "256"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 55, "column": 33, "nodeType": "212", "messageId": "213", "endLine": 55, "endColumn": 36, "suggestions": "257"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 56, "column": 39, "nodeType": "212", "messageId": "213", "endLine": 56, "endColumn": 42, "suggestions": "258"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 71, "column": 33, "nodeType": "212", "messageId": "213", "endLine": 71, "endColumn": 36, "suggestions": "259"}, {"ruleId": "207", "severity": 2, "message": "260", "line": 9, "column": 23, "nodeType": null, "messageId": "209", "endLine": 9, "endColumn": 31}, {"ruleId": "207", "severity": 2, "message": "261", "line": 44, "column": 61, "nodeType": null, "messageId": "209", "endLine": 44, "endColumn": 64}, {"ruleId": "210", "severity": 2, "message": "211", "line": 46, "column": 57, "nodeType": "212", "messageId": "213", "endLine": 46, "endColumn": 60, "suggestions": "262"}, {"ruleId": "263", "severity": 1, "message": "264", "line": 127, "column": 19, "nodeType": "265", "endLine": 131, "endColumn": 21}, {"ruleId": "207", "severity": 2, "message": "266", "line": 12, "column": 3, "nodeType": null, "messageId": "209", "endLine": 12, "endColumn": 11}, {"ruleId": "210", "severity": 2, "message": "211", "line": 151, "column": 62, "nodeType": "212", "messageId": "213", "endLine": 151, "endColumn": 65, "suggestions": "267"}, {"ruleId": "263", "severity": 1, "message": "264", "line": 195, "column": 25, "nodeType": "265", "endLine": 199, "endColumn": 27}, {"ruleId": "268", "severity": 1, "message": "269", "line": 201, "column": 25, "nodeType": "265", "endLine": 201, "endColumn": 68}, {"ruleId": "207", "severity": 2, "message": "270", "line": 14, "column": 3, "nodeType": null, "messageId": "209", "endLine": 14, "endColumn": 16}, {"ruleId": "207", "severity": 2, "message": "251", "line": 15, "column": 3, "nodeType": null, "messageId": "209", "endLine": 15, "endColumn": 11}, {"ruleId": "207", "severity": 2, "message": "271", "line": 26, "column": 10, "nodeType": null, "messageId": "209", "endLine": 26, "endColumn": 20}, {"ruleId": "210", "severity": 2, "message": "211", "line": 30, "column": 29, "nodeType": "212", "messageId": "213", "endLine": 30, "endColumn": 32, "suggestions": "272"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 36, "column": 29, "nodeType": "212", "messageId": "213", "endLine": 36, "endColumn": 32, "suggestions": "273"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 42, "column": 29, "nodeType": "212", "messageId": "213", "endLine": 42, "endColumn": 32, "suggestions": "274"}, {"ruleId": "207", "severity": 2, "message": "253", "line": 19, "column": 20, "nodeType": null, "messageId": "209", "endLine": 19, "endColumn": 29}, {"ruleId": "232", "severity": 2, "message": "233", "line": 44, "column": 44, "nodeType": "234", "endLine": 44, "endColumn": 52}, {"ruleId": "210", "severity": 2, "message": "211", "line": 45, "column": 29, "nodeType": "212", "messageId": "213", "endLine": 45, "endColumn": 32, "suggestions": "275"}, {"ruleId": "210", "severity": 2, "message": "211", "line": 55, "column": 60, "nodeType": "212", "messageId": "213", "endLine": 55, "endColumn": 63, "suggestions": "276"}, {"ruleId": "215", "severity": 2, "message": "216", "line": 247, "column": 24, "nodeType": "217", "messageId": "218", "suggestions": "277"}, {"ruleId": "207", "severity": 2, "message": "278", "line": 117, "column": 22, "nodeType": null, "messageId": "209", "endLine": 117, "endColumn": 27}, {"ruleId": "268", "severity": 1, "message": "269", "line": 169, "column": 14, "nodeType": "265", "endLine": 169, "endColumn": 43}, "@typescript-eslint/no-unused-vars", "'djProfile' is assigned a value but never used.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["279", "280"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["281", "282", "283", "284"], "'SessionControls' is defined but never used.", "'kickUser' is assigned a value but never used.", "'canModerate' is assigned a value but never used.", ["285", "286"], ["287", "288"], ["289", "290"], ["291", "292"], ["293", "294"], ["295", "296"], ["297", "298"], "'selectedMetric' is assigned a value but never used.", "'setSelectedMetric' is assigned a value but never used.", "react-hooks/rules-of-hooks", "React Hook \"useQuery\" is called conditionally. React Hooks must be called in the exact same order in every component render.", "Identifier", ["299", "300"], "'index' is defined but never used.", ["301", "302"], "'handleRespondToInvite' is assigned a value but never used.", ["303", "304"], ["305", "306"], ["307", "308"], ["309", "310"], ["311", "312"], ["313", "314"], ["315", "316"], ["317", "318"], ["319", "320"], ["321", "322"], ["323", "324"], ["325", "326"], "'ThumbsUp' is defined but never used.", "'ThumbsDown' is defined but never used.", "'useEffect' is defined but never used.", ["327", "328"], ["329", "330"], ["331", "332"], ["333", "334"], ["335", "336"], ["337", "338"], "'Calendar' is defined but never used.", "'url' is defined but never used.", ["339", "340"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'Download' is defined but never used.", ["341", "342"], "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'MessageCircle' is defined but never used.", "'lastUpdate' is assigned a value but never used.", ["343", "344"], ["345", "346"], ["347", "348"], ["349", "350"], ["351", "352"], ["353", "354", "355", "356"], "'error' is defined but never used.", {"messageId": "357", "fix": "358", "desc": "359"}, {"messageId": "360", "fix": "361", "desc": "362"}, {"messageId": "363", "data": "364", "fix": "365", "desc": "366"}, {"messageId": "363", "data": "367", "fix": "368", "desc": "369"}, {"messageId": "363", "data": "370", "fix": "371", "desc": "372"}, {"messageId": "363", "data": "373", "fix": "374", "desc": "375"}, {"messageId": "357", "fix": "376", "desc": "359"}, {"messageId": "360", "fix": "377", "desc": "362"}, {"messageId": "357", "fix": "378", "desc": "359"}, {"messageId": "360", "fix": "379", "desc": "362"}, {"messageId": "357", "fix": "380", "desc": "359"}, {"messageId": "360", "fix": "381", "desc": "362"}, {"messageId": "357", "fix": "382", "desc": "359"}, {"messageId": "360", "fix": "383", "desc": "362"}, {"messageId": "357", "fix": "384", "desc": "359"}, {"messageId": "360", "fix": "385", "desc": "362"}, {"messageId": "357", "fix": "386", "desc": "359"}, {"messageId": "360", "fix": "387", "desc": "362"}, {"messageId": "357", "fix": "388", "desc": "359"}, {"messageId": "360", "fix": "389", "desc": "362"}, {"messageId": "357", "fix": "390", "desc": "359"}, {"messageId": "360", "fix": "391", "desc": "362"}, {"messageId": "357", "fix": "392", "desc": "359"}, {"messageId": "360", "fix": "393", "desc": "362"}, {"messageId": "357", "fix": "394", "desc": "359"}, {"messageId": "360", "fix": "395", "desc": "362"}, {"messageId": "357", "fix": "396", "desc": "359"}, {"messageId": "360", "fix": "397", "desc": "362"}, {"messageId": "357", "fix": "398", "desc": "359"}, {"messageId": "360", "fix": "399", "desc": "362"}, {"messageId": "357", "fix": "400", "desc": "359"}, {"messageId": "360", "fix": "401", "desc": "362"}, {"messageId": "357", "fix": "402", "desc": "359"}, {"messageId": "360", "fix": "403", "desc": "362"}, {"messageId": "357", "fix": "404", "desc": "359"}, {"messageId": "360", "fix": "405", "desc": "362"}, {"messageId": "357", "fix": "406", "desc": "359"}, {"messageId": "360", "fix": "407", "desc": "362"}, {"messageId": "357", "fix": "408", "desc": "359"}, {"messageId": "360", "fix": "409", "desc": "362"}, {"messageId": "357", "fix": "410", "desc": "359"}, {"messageId": "360", "fix": "411", "desc": "362"}, {"messageId": "357", "fix": "412", "desc": "359"}, {"messageId": "360", "fix": "413", "desc": "362"}, {"messageId": "357", "fix": "414", "desc": "359"}, {"messageId": "360", "fix": "415", "desc": "362"}, {"messageId": "357", "fix": "416", "desc": "359"}, {"messageId": "360", "fix": "417", "desc": "362"}, {"messageId": "357", "fix": "418", "desc": "359"}, {"messageId": "360", "fix": "419", "desc": "362"}, {"messageId": "357", "fix": "420", "desc": "359"}, {"messageId": "360", "fix": "421", "desc": "362"}, {"messageId": "357", "fix": "422", "desc": "359"}, {"messageId": "360", "fix": "423", "desc": "362"}, {"messageId": "357", "fix": "424", "desc": "359"}, {"messageId": "360", "fix": "425", "desc": "362"}, {"messageId": "357", "fix": "426", "desc": "359"}, {"messageId": "360", "fix": "427", "desc": "362"}, {"messageId": "357", "fix": "428", "desc": "359"}, {"messageId": "360", "fix": "429", "desc": "362"}, {"messageId": "357", "fix": "430", "desc": "359"}, {"messageId": "360", "fix": "431", "desc": "362"}, {"messageId": "357", "fix": "432", "desc": "359"}, {"messageId": "360", "fix": "433", "desc": "362"}, {"messageId": "357", "fix": "434", "desc": "359"}, {"messageId": "360", "fix": "435", "desc": "362"}, {"messageId": "357", "fix": "436", "desc": "359"}, {"messageId": "360", "fix": "437", "desc": "362"}, {"messageId": "357", "fix": "438", "desc": "359"}, {"messageId": "360", "fix": "439", "desc": "362"}, {"messageId": "357", "fix": "440", "desc": "359"}, {"messageId": "360", "fix": "441", "desc": "362"}, {"messageId": "357", "fix": "442", "desc": "359"}, {"messageId": "360", "fix": "443", "desc": "362"}, {"messageId": "363", "data": "444", "fix": "445", "desc": "366"}, {"messageId": "363", "data": "446", "fix": "447", "desc": "369"}, {"messageId": "363", "data": "448", "fix": "449", "desc": "372"}, {"messageId": "363", "data": "450", "fix": "451", "desc": "375"}, "suggestUnknown", {"range": "452", "text": "453"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "454", "text": "455"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "replaceWithAlt", {"alt": "456"}, {"range": "457", "text": "458"}, "Replace with `&apos;`.", {"alt": "459"}, {"range": "460", "text": "461"}, "Replace with `&lsquo;`.", {"alt": "462"}, {"range": "463", "text": "464"}, "Replace with `&#39;`.", {"alt": "465"}, {"range": "466", "text": "467"}, "Replace with `&rsquo;`.", {"range": "468", "text": "453"}, {"range": "469", "text": "455"}, {"range": "470", "text": "453"}, {"range": "471", "text": "455"}, {"range": "472", "text": "453"}, {"range": "473", "text": "455"}, {"range": "474", "text": "453"}, {"range": "475", "text": "455"}, {"range": "476", "text": "453"}, {"range": "477", "text": "455"}, {"range": "478", "text": "453"}, {"range": "479", "text": "455"}, {"range": "480", "text": "453"}, {"range": "481", "text": "455"}, {"range": "482", "text": "453"}, {"range": "483", "text": "455"}, {"range": "484", "text": "453"}, {"range": "485", "text": "455"}, {"range": "486", "text": "453"}, {"range": "487", "text": "455"}, {"range": "488", "text": "453"}, {"range": "489", "text": "455"}, {"range": "490", "text": "453"}, {"range": "491", "text": "455"}, {"range": "492", "text": "453"}, {"range": "493", "text": "455"}, {"range": "494", "text": "453"}, {"range": "495", "text": "455"}, {"range": "496", "text": "453"}, {"range": "497", "text": "455"}, {"range": "498", "text": "453"}, {"range": "499", "text": "455"}, {"range": "500", "text": "453"}, {"range": "501", "text": "455"}, {"range": "502", "text": "453"}, {"range": "503", "text": "455"}, {"range": "504", "text": "453"}, {"range": "505", "text": "455"}, {"range": "506", "text": "453"}, {"range": "507", "text": "455"}, {"range": "508", "text": "453"}, {"range": "509", "text": "455"}, {"range": "510", "text": "453"}, {"range": "511", "text": "455"}, {"range": "512", "text": "453"}, {"range": "513", "text": "455"}, {"range": "514", "text": "453"}, {"range": "515", "text": "455"}, {"range": "516", "text": "453"}, {"range": "517", "text": "455"}, {"range": "518", "text": "453"}, {"range": "519", "text": "455"}, {"range": "520", "text": "453"}, {"range": "521", "text": "455"}, {"range": "522", "text": "453"}, {"range": "523", "text": "455"}, {"range": "524", "text": "453"}, {"range": "525", "text": "455"}, {"range": "526", "text": "453"}, {"range": "527", "text": "455"}, {"range": "528", "text": "453"}, {"range": "529", "text": "455"}, {"range": "530", "text": "453"}, {"range": "531", "text": "455"}, {"range": "532", "text": "453"}, {"range": "533", "text": "455"}, {"range": "534", "text": "453"}, {"range": "535", "text": "455"}, {"alt": "456"}, {"range": "536", "text": "537"}, {"alt": "459"}, {"range": "538", "text": "539"}, {"alt": "462"}, {"range": "540", "text": "541"}, {"alt": "465"}, {"range": "542", "text": "543"}, [1553, 1556], "unknown", [1553, 1556], "never", "&apos;", [2481, 2511], "The DJ&apos;s song request platform", "&lsquo;", [2481, 2511], "The DJ&lsquo;s song request platform", "&#39;", [2481, 2511], "The DJ&#39;s song request platform", "&rsquo;", [2481, 2511], "The DJ&rsquo;s song request platform", [12642, 12645], [12642, 12645], [12658, 12661], [12658, 12661], [17058, 17061], [17058, 17061], [17074, 17077], [17074, 17077], [19113, 19116], [19113, 19116], [22255, 22258], [22255, 22258], [22271, 22274], [22271, 22274], [1914, 1917], [1914, 1917], [1767, 1770], [1767, 1770], [2563, 2566], [2563, 2566], [10903, 10906], [10903, 10906], [12410, 12413], [12410, 12413], [17643, 17646], [17643, 17646], [18961, 18964], [18961, 18964], [1141, 1144], [1141, 1144], [1724, 1727], [1724, 1727], [10513, 10516], [10513, 10516], [10540, 10543], [10540, 10543], [329, 332], [329, 332], [1932, 1935], [1932, 1935], [10015, 10018], [10015, 10018], [964, 967], [964, 967], [1116, 1119], [1116, 1119], [1277, 1280], [1277, 1280], [1682, 1685], [1682, 1685], [1725, 1728], [1725, 1728], [2103, 2106], [2103, 2106], [1642, 1645], [1642, 1645], [5337, 5340], [5337, 5340], [822, 825], [822, 825], [988, 991], [988, 991], [1152, 1155], [1152, 1155], [1222, 1225], [1222, 1225], [1558, 1561], [1558, 1561], [9376, 9437], "\n                    You&apos;re all caught up!\n                  ", [9376, 9437], "\n                    You&lsquo;re all caught up!\n                  ", [9376, 9437], "\n                    You&#39;re all caught up!\n                  ", [9376, 9437], "\n                    You&rsquo;re all caught up!\n                  "]