"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/signin/page",{

/***/ "(app-pages-browser)/./app/signin/page.tsx":
/*!*****************************!*\
  !*** ./app/signin/page.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignIn)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @convex-dev/auth/react */ \"(app-pages-browser)/./node_modules/@convex-dev/auth/dist/react/index.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Music_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Music_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_3__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().email(\"Invalid email address\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().min(8, \"Password must be at least 8 characters\").regex(/[A-Z]/, \"Password must contain at least one uppercase letter\").regex(/[a-z]/, \"Password must contain at least one lowercase letter\").regex(/[0-9]/, \"Password must contain at least one number\")\n});\nfunction SignIn() {\n    _s();\n    const { signIn } = (0,_convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_9__.useAuthActions)();\n    const { isAuthenticated, isLoading } = (0,convex_react__WEBPACK_IMPORTED_MODULE_5__.useConvexAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [flow, setFlow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"signIn\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Redirect if already authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SignIn.useEffect\": ()=>{\n            if (!isLoading && isAuthenticated) {\n                console.log(\"User is already authenticated, redirecting to dashboard\");\n                router.push(\"/dashboard\");\n            }\n        }\n    }[\"SignIn.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(loginSchema),\n        defaultValues: {\n            email: \"\",\n            password: \"\"\n        }\n    });\n    const onSubmit = async (data)=>{\n        setIsSubmitting(true);\n        setError(null);\n        try {\n            const formData = new FormData();\n            formData.set(\"email\", data.email);\n            formData.set(\"password\", data.password);\n            formData.set(\"flow\", flow);\n            console.log(\"Attempting authentication with flow:\", flow);\n            // Use a different approach - don't rely on return value\n            await signIn(\"password\", formData);\n            // If we get here without an error, authentication was successful\n            console.log(\"Authentication successful, redirecting to dashboard\");\n            // Small delay to ensure auth state is updated\n            setTimeout(()=>{\n                router.push(\"/dashboard\");\n            }, 100);\n        } catch (error) {\n            console.error(\"Authentication error:\", error);\n            // Handle specific error messages\n            let errorMessage = \"Authentication failed\";\n            if (error.message) {\n                if (error.message.includes(\"Invalid password\")) {\n                    errorMessage = flow === \"signUp\" ? \"Password must be at least 8 characters with uppercase, lowercase, and number\" : \"Invalid email or password\";\n                } else if (error.message.includes(\"User not found\")) {\n                    errorMessage = \"No account found with this email address\";\n                } else if (error.message.includes(\"User already exists\")) {\n                    errorMessage = \"An account with this email already exists\";\n                } else if (error.message.includes(\"Cannot read properties of null\")) {\n                    errorMessage = \"Authentication service error. Please try again.\";\n                } else {\n                    errorMessage = error.message;\n                }\n            }\n            setError(errorMessage);\n            setIsSubmitting(false);\n        }\n    };\n    // Show loading while checking authentication\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-lg\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render signin form if already authenticated\n    if (isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-lg\",\n                children: \"Redirecting to dashboard...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                lineNumber: 121,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"md:mx-auto md:max-w-md md:p-8 md:rounded-2xl md:bg-gray-900/50 md:border md:border-purple-500/20 md:backdrop-blur-md md:shadow-xl w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Music_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-8 h-8 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-white\",\n                                children: \"Welcome to PlayBeg\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"The DJ's song request platform\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-500 to-cyan-400 bg-clip-text text-transparent\",\n                                    children: flow === \"signIn\" ? \"Sign In to PlayBeg\" : \"Join PlayBeg\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-base mt-2\",\n                                    children: flow === \"signIn\" ? \"Access your DJ dashboard\" : \"Create your DJ account\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.Form, {\n                            ...form,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: form.handleSubmit(onSubmit),\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                control: form.control,\n                                                name: \"email\",\n                                                render: (param)=>{\n                                                    let { field } = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                className: \"block text-sm font-medium text-white mb-2\",\n                                                                children: \"Email Address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                lineNumber: 158,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"email\",\n                                                                    placeholder: \"<EMAIL>\",\n                                                                    autoComplete: \"email\",\n                                                                    ...field,\n                                                                    className: \"w-full h-12 px-3 rounded-lg bg-gray-800 border border-gray-700 text-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500/50 focus:outline-none transition-colors\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                lineNumber: 161,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 21\n                                                    }, void 0);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                control: form.control,\n                                                name: \"password\",\n                                                render: (param)=>{\n                                                    let { field } = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                className: \"block text-sm font-medium text-white mb-2\",\n                                                                children: \"Password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"password\",\n                                                                    placeholder: \"••••••••\",\n                                                                    autoComplete: flow === \"signIn\" ? \"current-password\" : \"new-password\",\n                                                                    ...field,\n                                                                    className: \"w-full h-12 px-3 rounded-lg bg-gray-800 border border-gray-700 text-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500/50 focus:outline-none transition-colors\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                    lineNumber: 185,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            flow === \"signUp\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 text-xs text-gray-400\",\n                                                                children: [\n                                                                    \"Password must contain:\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"list-disc list-inside mt-1 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"At least 8 characters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                                lineNumber: 199,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"One uppercase letter\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                                lineNumber: 200,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"One lowercase letter\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                                lineNumber: 201,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"One number\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                                lineNumber: 202,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                        lineNumber: 198,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, void 0);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        type: \"submit\",\n                                        className: \"w-full h-12 text-lg font-semibold tracking-wide rounded-lg bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white transition-transform duration-200 flex items-center justify-center\",\n                                        disabled: isSubmitting,\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Music_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 21\n                                                }, this),\n                                                flow === \"signIn\" ? \"Signing in...\" : \"Creating account...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: flow === \"signIn\" ? \"Sign In\" : \"Sign Up\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-500/20 border border-red-500/50 rounded-lg p-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-200 text-sm text-center\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mt-6 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400\",\n                                    children: flow === \"signIn\" ? \"Don't have an account?\" : \"Already have an account?\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setFlow(flow === \"signIn\" ? \"signUp\" : \"signIn\"),\n                                    className: \"ml-2 text-purple-400 hover:text-purple-300 transition-colors font-medium\",\n                                    children: flow === \"signIn\" ? \"Sign up\" : \"Sign in\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n            lineNumber: 128,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, this);\n}\n_s(SignIn, \"ssxca98QbiXbbfrH0gvVUDigQ3E=\", false, function() {\n    return [\n        _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_9__.useAuthActions,\n        convex_react__WEBPACK_IMPORTED_MODULE_5__.useConvexAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm\n    ];\n});\n_c = SignIn;\nvar _c;\n$RefreshReg$(_c, \"SignIn\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/signin/page.tsx\n"));

/***/ })

});