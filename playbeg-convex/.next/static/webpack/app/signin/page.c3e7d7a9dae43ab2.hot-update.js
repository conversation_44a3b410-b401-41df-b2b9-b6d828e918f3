"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/signin/page",{

/***/ "(app-pages-browser)/./app/signin/page.tsx":
/*!*****************************!*\
  !*** ./app/signin/page.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignIn)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @convex-dev/auth/react */ \"(app-pages-browser)/./node_modules/@convex-dev/auth/dist/react/index.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Music_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Music_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_3__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().email(\"Invalid email address\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().min(8, \"Password must be at least 8 characters\").regex(/[A-Z]/, \"Password must contain at least one uppercase letter\").regex(/[a-z]/, \"Password must contain at least one lowercase letter\").regex(/[0-9]/, \"Password must contain at least one number\")\n});\nfunction SignIn() {\n    _s();\n    const { signIn } = (0,_convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_9__.useAuthActions)();\n    const { isAuthenticated, isLoading } = (0,convex_react__WEBPACK_IMPORTED_MODULE_5__.useConvexAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [flow, setFlow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"signIn\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Redirect if already authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SignIn.useEffect\": ()=>{\n            if (!isLoading && isAuthenticated) {\n                console.log(\"User is already authenticated, redirecting to dashboard\");\n                router.push(\"/dashboard\");\n            }\n        }\n    }[\"SignIn.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(loginSchema),\n        defaultValues: {\n            email: \"\",\n            password: \"\"\n        }\n    });\n    const onSubmit = async (data)=>{\n        setIsSubmitting(true);\n        setError(null);\n        try {\n            const formData = new FormData();\n            formData.set(\"email\", data.email);\n            formData.set(\"password\", data.password);\n            formData.set(\"flow\", flow);\n            console.log(\"Attempting authentication with flow:\", flow);\n            // Use a different approach - don't rely on return value\n            await signIn(\"password\", formData);\n            // If we get here without an error, authentication was successful\n            console.log(\"Authentication successful, redirecting to dashboard\");\n            // Small delay to ensure auth state is updated\n            setTimeout(()=>{\n                router.push(\"/dashboard\");\n            }, 100);\n        } catch (error) {\n            console.error(\"Authentication error:\", error);\n            // Handle specific error messages\n            let errorMessage = \"Authentication failed\";\n            if (error.message) {\n                if (error.message.includes(\"Invalid password\")) {\n                    errorMessage = flow === \"signUp\" ? \"Password must be at least 8 characters with uppercase, lowercase, and number\" : \"Invalid email or password\";\n                } else if (error.message.includes(\"User not found\")) {\n                    errorMessage = \"No account found with this email address\";\n                } else if (error.message.includes(\"User already exists\")) {\n                    errorMessage = \"An account with this email already exists\";\n                } else if (error.message.includes(\"Cannot read properties of null\")) {\n                    errorMessage = \"Authentication service error. Please try again.\";\n                } else {\n                    errorMessage = error.message;\n                }\n            }\n            setError(errorMessage);\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"md:mx-auto md:max-w-md md:p-8 md:rounded-2xl md:bg-gray-900/50 md:border md:border-purple-500/20 md:backdrop-blur-md md:shadow-xl w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Music_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-8 h-8 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-white\",\n                                children: \"Welcome to PlayBeg\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"The DJ's song request platform\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-500 to-cyan-400 bg-clip-text text-transparent\",\n                                    children: flow === \"signIn\" ? \"Sign In to PlayBeg\" : \"Join PlayBeg\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-base mt-2\",\n                                    children: flow === \"signIn\" ? \"Access your DJ dashboard\" : \"Create your DJ account\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.Form, {\n                            ...form,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: form.handleSubmit(onSubmit),\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                control: form.control,\n                                                name: \"email\",\n                                                render: (param)=>{\n                                                    let { field } = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                className: \"block text-sm font-medium text-white mb-2\",\n                                                                children: \"Email Address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"email\",\n                                                                    placeholder: \"<EMAIL>\",\n                                                                    autoComplete: \"email\",\n                                                                    ...field,\n                                                                    className: \"w-full h-12 px-3 rounded-lg bg-gray-800 border border-gray-700 text-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500/50 focus:outline-none transition-colors\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                    lineNumber: 144,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                lineNumber: 143,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 21\n                                                    }, void 0);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                control: form.control,\n                                                name: \"password\",\n                                                render: (param)=>{\n                                                    let { field } = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                className: \"block text-sm font-medium text-white mb-2\",\n                                                                children: \"Password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: \"password\",\n                                                                    placeholder: \"••••••••\",\n                                                                    autoComplete: flow === \"signIn\" ? \"current-password\" : \"new-password\",\n                                                                    ...field,\n                                                                    className: \"w-full h-12 px-3 rounded-lg bg-gray-800 border border-gray-700 text-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500/50 focus:outline-none transition-colors\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                    lineNumber: 167,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            flow === \"signUp\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 text-xs text-gray-400\",\n                                                                children: [\n                                                                    \"Password must contain:\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"list-disc list-inside mt-1 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"At least 8 characters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                                lineNumber: 181,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"One uppercase letter\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                                lineNumber: 182,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"One lowercase letter\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                                lineNumber: 183,\n                                                                                columnNumber: 29\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"One number\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                                lineNumber: 184,\n                                                                                columnNumber: 29\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                        lineNumber: 180,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 21\n                                                    }, void 0);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        type: \"submit\",\n                                        className: \"w-full h-12 text-lg font-semibold tracking-wide rounded-lg bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white transition-transform duration-200 flex items-center justify-center\",\n                                        disabled: isSubmitting,\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Music_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 21\n                                                }, this),\n                                                flow === \"signIn\" ? \"Signing in...\" : \"Creating account...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: flow === \"signIn\" ? \"Sign In\" : \"Sign Up\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-500/20 border border-red-500/50 rounded-lg p-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-200 text-sm text-center\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mt-6 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400\",\n                                    children: flow === \"signIn\" ? \"Don't have an account?\" : \"Already have an account?\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setFlow(flow === \"signIn\" ? \"signUp\" : \"signIn\"),\n                                    className: \"ml-2 text-purple-400 hover:text-purple-300 transition-colors font-medium\",\n                                    children: flow === \"signIn\" ? \"Sign up\" : \"Sign in\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n            lineNumber: 110,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/signin/page.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, this);\n}\n_s(SignIn, \"ssxca98QbiXbbfrH0gvVUDigQ3E=\", false, function() {\n    return [\n        _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_9__.useAuthActions,\n        convex_react__WEBPACK_IMPORTED_MODULE_5__.useConvexAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm\n    ];\n});\n_c = SignIn;\nvar _c;\n$RefreshReg$(_c, \"SignIn\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/signin/page.tsx\n"));

/***/ })

});