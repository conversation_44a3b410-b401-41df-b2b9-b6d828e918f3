"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/session/[sessionId]/page",{

/***/ "(app-pages-browser)/./app/session/[sessionId]/page.tsx":
/*!******************************************!*\
  !*** ./app/session/[sessionId]/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PublicSessionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var qrcode_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! qrcode.react */ \"(app-pages-browser)/./node_modules/qrcode.react/lib/esm/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction PublicSessionPage() {\n    var _session_weddingCoupleNames;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const sessionId = params.sessionId;\n    // Validate session ID format\n    const isValidSessionId = sessionId && sessionId.length > 0 && !sessionId.includes('test');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        songTitle: '',\n        artistName: '',\n        requesterName: '',\n        message: ''\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitMessage, setSubmitMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showQR, setShowQR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Queries with proper error handling\n    const session = (0,convex_react__WEBPACK_IMPORTED_MODULE_3__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.sessions.getPublicSession, isValidSessionId ? {\n        sessionId: sessionId\n    } : \"skip\");\n    const requests = (0,convex_react__WEBPACK_IMPORTED_MODULE_3__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.songRequests.getSessionRequests, session && isValidSessionId ? {\n        sessionId: sessionId,\n        status: \"approved\",\n        limit: 10\n    } : \"skip\");\n    // Mutations\n    const createRequest = (0,convex_react__WEBPACK_IMPORTED_MODULE_3__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.songRequests.createSongRequest);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!session || !session.acceptRequests) return;\n        setIsSubmitting(true);\n        setSubmitMessage(null);\n        try {\n            const result = await createRequest({\n                sessionId,\n                songTitle: formData.songTitle.trim(),\n                artistName: formData.artistName.trim(),\n                requesterName: formData.requesterName.trim()\n            });\n            setSubmitMessage({\n                type: 'success',\n                text: result.message || 'Request submitted successfully!'\n            });\n            // Clear form\n            setFormData({\n                songTitle: '',\n                artistName: '',\n                requesterName: '',\n                message: ''\n            });\n        } catch (error) {\n            setSubmitMessage({\n                type: 'error',\n                text: error.message || 'Failed to submit request'\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    // Loading state\n    if (session === undefined) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-lg\",\n                children: \"Loading session...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this);\n    }\n    // Session not found or inactive\n    if (!session) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-8 text-center max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83C\\uDFB5\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-white mb-4\",\n                        children: \"Session Not Found\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/80 mb-6\",\n                        children: \"This session is either inactive or doesn't exist. Please check the link and try again.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"bg-purple-600 hover:bg-purple-700 text-white\",\n                        children: \"Go to Home\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, this);\n    }\n    const currentUrl =  true ? window.location.href : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-black/20 backdrop-blur-md border-b border-white/10 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: session.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                session.weddingModeEnabled && session.weddingCoupleNames && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/80\",\n                                    children: [\n                                        session.weddingCoupleNames.join(' & '),\n                                        \"'s Wedding\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            onClick: ()=>setShowQR(!showQR),\n                            variant: \"outline\",\n                            className: \"text-white border-white/30 hover:bg-white/10\",\n                            children: showQR ? 'Hide QR' : 'Share QR'\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto p-4 space-y-8\",\n                children: [\n                    showQR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"bg-white/10 backdrop-blur-md border-white/20 p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"Share This Session\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-4 rounded-lg inline-block mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(qrcode_react__WEBPACK_IMPORTED_MODULE_9__.QRCodeSVG, {\n                                    value: currentUrl,\n                                    size: 200\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 text-sm\",\n                                children: \"Scan this QR code to join the session\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this),\n                    session.weddingModeEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"bg-white/10 backdrop-blur-md border-white/20 p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-4xl mb-4\",\n                                children: \"\\uD83D\\uDC92\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: [\n                                    (_session_weddingCoupleNames = session.weddingCoupleNames) === null || _session_weddingCoupleNames === void 0 ? void 0 : _session_weddingCoupleNames.join(' & '),\n                                    \"'s Wedding\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this),\n                            session.weddingDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 mb-2\",\n                                children: session.weddingDate\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 15\n                            }, this),\n                            session.weddingHashtag && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-purple-300\",\n                                children: [\n                                    \"#\",\n                                    session.weddingHashtag\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this),\n                            session.weddingCustomMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/90 mt-4 italic\",\n                                children: [\n                                    '\"',\n                                    session.weddingCustomMessage,\n                                    '\"'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this),\n                    session.sponsorHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"bg-white/10 backdrop-blur-md border-white/20 p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/90\",\n                                children: session.sponsorHeader\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this),\n                            session.sponsorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/70 text-sm mt-2\",\n                                children: session.sponsorMessage\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-6 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-3\",\n                                                children: \"\\uD83C\\uDFB5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Request a Song\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this),\n                                    !session.acceptRequests ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: \"⏸️\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/80\",\n                                                children: \"This session is not currently accepting requests.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-white mb-2\",\n                                                        children: \"Song Title *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        type: \"text\",\n                                                        value: formData.songTitle,\n                                                        onChange: (e)=>handleInputChange('songTitle', e.target.value),\n                                                        placeholder: \"Enter song title\",\n                                                        className: \"w-full bg-white/20 border-white/30 text-white placeholder-white/60\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-white mb-2\",\n                                                        children: \"Artist Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        type: \"text\",\n                                                        value: formData.artistName,\n                                                        onChange: (e)=>handleInputChange('artistName', e.target.value),\n                                                        placeholder: \"Enter artist name\",\n                                                        className: \"w-full bg-white/20 border-white/30 text-white placeholder-white/60\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-white mb-2\",\n                                                        children: \"Your Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        type: \"text\",\n                                                        value: formData.requesterName,\n                                                        onChange: (e)=>handleInputChange('requesterName', e.target.value),\n                                                        placeholder: \"Enter your name\",\n                                                        className: \"w-full bg-white/20 border-white/30 text-white placeholder-white/60\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-white mb-2\",\n                                                        children: \"Message (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                        value: formData.message,\n                                                        onChange: (e)=>handleInputChange('message', e.target.value),\n                                                        placeholder: \"Add a message with your request...\",\n                                                        className: \"w-full bg-white/20 border-white/30 text-white placeholder-white/60\",\n                                                        rows: 3\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            submitMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg \".concat(submitMessage.type === 'success' ? 'bg-green-600/20 border border-green-500/30 text-green-200' : 'bg-red-600/20 border border-red-500/30 text-red-200'),\n                                                children: submitMessage.text\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                type: \"submit\",\n                                                disabled: isSubmitting || !formData.songTitle.trim() || !formData.artistName.trim() || !formData.requesterName.trim(),\n                                                className: \"w-full bg-purple-600 hover:bg-purple-700 text-white py-3\",\n                                                children: isSubmitting ? 'Submitting...' : 'Submit Request'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-6 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-3\",\n                                                children: \"✅\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Recent Requests\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 13\n                                    }, this),\n                                    requests && requests.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: requests.map((request)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/10 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold text-white\",\n                                                        children: request.songTitle\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white/80 text-sm\",\n                                                        children: [\n                                                            \"by \",\n                                                            request.artistName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white/60 text-xs mt-1\",\n                                                        children: [\n                                                            \"Requested by \",\n                                                            request.requesterName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, request._id, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: \"\\uD83C\\uDFB6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/80\",\n                                                children: \"No approved requests yet. Be the first to request a song!\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\n_s(PublicSessionPage, \"9U43j0mNDREa5DSxGBSXxgY1yrY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        convex_react__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_3__.useMutation\n    ];\n});\n_c = PublicSessionPage;\nvar _c;\n$RefreshReg$(_c, \"PublicSessionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/session/[sessionId]/page.tsx\n"));

/***/ })

});