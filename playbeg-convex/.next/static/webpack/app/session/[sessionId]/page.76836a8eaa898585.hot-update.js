"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/session/[sessionId]/page",{

/***/ "(app-pages-browser)/./app/session/[sessionId]/page.tsx":
/*!******************************************!*\
  !*** ./app/session/[sessionId]/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PublicSessionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var qrcode_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! qrcode.react */ \"(app-pages-browser)/./node_modules/qrcode.react/lib/esm/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction PublicSessionPage() {\n    var _session_weddingCoupleNames;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const sessionId = params.sessionId;\n    // Validate session ID format\n    const isValidSessionId = sessionId && sessionId.length > 0 && !sessionId.includes('test');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        songTitle: '',\n        artistName: '',\n        requesterName: '',\n        message: ''\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitMessage, setSubmitMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showQR, setShowQR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Queries with proper error handling\n    const session = (0,convex_react__WEBPACK_IMPORTED_MODULE_3__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.sessions.getPublicSession, isValidSessionId ? {\n        sessionId: sessionId\n    } : \"skip\");\n    const requests = (0,convex_react__WEBPACK_IMPORTED_MODULE_3__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.songRequests.getSessionRequests, session && isValidSessionId ? {\n        sessionId: sessionId,\n        status: \"approved\",\n        limit: 10\n    } : \"skip\");\n    // Mutations\n    const createRequest = (0,convex_react__WEBPACK_IMPORTED_MODULE_3__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.songRequests.createSongRequest);\n    const createValidatedRequest = (0,convex_react__WEBPACK_IMPORTED_MODULE_3__.useAction)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.spotify.createValidatedSongRequest);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!session || !session.acceptRequests) return;\n        setIsSubmitting(true);\n        setSubmitMessage(null);\n        try {\n            // Use Spotify-enhanced request creation if we have Spotify data\n            let result;\n            if (formData.spotifyId) {\n                result = await createValidatedRequest({\n                    sessionId: sessionId,\n                    songTitle: formData.songTitle.trim(),\n                    artistName: formData.artistName.trim(),\n                    requesterName: formData.requesterName.trim(),\n                    spotifyTrackId: formData.spotifyId\n                });\n                if (!result.success) {\n                    throw new Error(result.error);\n                }\n                result = result.request;\n            } else {\n                // Fallback to regular request creation\n                result = await createRequest({\n                    sessionId: sessionId,\n                    songTitle: formData.songTitle.trim(),\n                    artistName: formData.artistName.trim(),\n                    requesterName: formData.requesterName.trim(),\n                    albumArtwork: formData.albumArt\n                });\n            }\n            setSubmitMessage({\n                type: 'success',\n                text: result.message || 'Request submitted successfully!'\n            });\n            // Clear form\n            setFormData({\n                songTitle: '',\n                artistName: '',\n                requesterName: '',\n                message: ''\n            });\n        } catch (error) {\n            setSubmitMessage({\n                type: 'error',\n                text: error.message || 'Failed to submit request'\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    // Invalid session ID\n    if (!isValidSessionId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-8 text-center max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"❌\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-white mb-4\",\n                        children: \"Invalid Session\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/80 mb-6\",\n                        children: \"The session ID provided is not valid. Please check the link and try again.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"bg-purple-600 hover:bg-purple-700 text-white\",\n                        children: \"Go to Home\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this);\n    }\n    // Loading state\n    if (session === undefined) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-lg\",\n                children: \"Loading session...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n            lineNumber: 143,\n            columnNumber: 7\n        }, this);\n    }\n    // Session not found or inactive\n    if (!session) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-8 text-center max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83C\\uDFB5\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-white mb-4\",\n                        children: \"Session Not Found\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/80 mb-6\",\n                        children: \"This session is either inactive or doesn't exist. Please check the link and try again.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"bg-purple-600 hover:bg-purple-700 text-white\",\n                        children: \"Go to Home\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, this);\n    }\n    const currentUrl =  true ? window.location.href : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-black/20 backdrop-blur-md border-b border-white/10 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: session.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                session.weddingModeEnabled && session.weddingCoupleNames && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/80\",\n                                    children: [\n                                        session.weddingCoupleNames.join(' & '),\n                                        \"'s Wedding\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            onClick: ()=>setShowQR(!showQR),\n                            variant: \"outline\",\n                            className: \"text-white border-white/30 hover:bg-white/10\",\n                            children: showQR ? 'Hide QR' : 'Share QR'\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto p-4 space-y-8\",\n                children: [\n                    showQR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"bg-white/10 backdrop-blur-md border-white/20 p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"Share This Session\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-4 rounded-lg inline-block mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(qrcode_react__WEBPACK_IMPORTED_MODULE_9__.QRCodeSVG, {\n                                    value: currentUrl,\n                                    size: 200\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 text-sm\",\n                                children: \"Scan this QR code to join the session\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this),\n                    session.weddingModeEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"bg-white/10 backdrop-blur-md border-white/20 p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-4xl mb-4\",\n                                children: \"\\uD83D\\uDC92\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: [\n                                    (_session_weddingCoupleNames = session.weddingCoupleNames) === null || _session_weddingCoupleNames === void 0 ? void 0 : _session_weddingCoupleNames.join(' & '),\n                                    \"'s Wedding\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this),\n                            session.weddingDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 mb-2\",\n                                children: session.weddingDate\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 15\n                            }, this),\n                            session.weddingHashtag && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-purple-300\",\n                                children: [\n                                    \"#\",\n                                    session.weddingHashtag\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, this),\n                            session.weddingCustomMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/90 mt-4 italic\",\n                                children: [\n                                    '\"',\n                                    session.weddingCustomMessage,\n                                    '\"'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this),\n                    session.sponsorHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"bg-white/10 backdrop-blur-md border-white/20 p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/90\",\n                                children: session.sponsorHeader\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, this),\n                            session.sponsorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/70 text-sm mt-2\",\n                                children: session.sponsorMessage\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-6 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-3\",\n                                                children: \"\\uD83C\\uDFB5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Request a Song\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, this),\n                                    !session.acceptRequests ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: \"⏸️\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/80\",\n                                                children: \"This session is not currently accepting requests.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-white mb-2\",\n                                                        children: \"Song Title *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        type: \"text\",\n                                                        value: formData.songTitle,\n                                                        onChange: (e)=>handleInputChange('songTitle', e.target.value),\n                                                        placeholder: \"Enter song title\",\n                                                        className: \"w-full bg-white/20 border-white/30 text-white placeholder-white/60\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-white mb-2\",\n                                                        children: \"Artist Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        type: \"text\",\n                                                        value: formData.artistName,\n                                                        onChange: (e)=>handleInputChange('artistName', e.target.value),\n                                                        placeholder: \"Enter artist name\",\n                                                        className: \"w-full bg-white/20 border-white/30 text-white placeholder-white/60\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-white mb-2\",\n                                                        children: \"Your Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        type: \"text\",\n                                                        value: formData.requesterName,\n                                                        onChange: (e)=>handleInputChange('requesterName', e.target.value),\n                                                        placeholder: \"Enter your name\",\n                                                        className: \"w-full bg-white/20 border-white/30 text-white placeholder-white/60\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-white mb-2\",\n                                                        children: \"Message (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                        value: formData.message,\n                                                        onChange: (e)=>handleInputChange('message', e.target.value),\n                                                        placeholder: \"Add a message with your request...\",\n                                                        className: \"w-full bg-white/20 border-white/30 text-white placeholder-white/60\",\n                                                        rows: 3\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this),\n                                            submitMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg \".concat(submitMessage.type === 'success' ? 'bg-green-600/20 border border-green-500/30 text-green-200' : 'bg-red-600/20 border border-red-500/30 text-red-200'),\n                                                children: submitMessage.text\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                type: \"submit\",\n                                                disabled: isSubmitting || !formData.songTitle.trim() || !formData.artistName.trim() || !formData.requesterName.trim(),\n                                                className: \"w-full bg-purple-600 hover:bg-purple-700 text-white py-3\",\n                                                children: isSubmitting ? 'Submitting...' : 'Submit Request'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-6 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-3\",\n                                                children: \"✅\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Recent Requests\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, this),\n                                    requests && requests.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: requests.map((request)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/10 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold text-white\",\n                                                        children: request.songTitle\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white/80 text-sm\",\n                                                        children: [\n                                                            \"by \",\n                                                            request.artistName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white/60 text-xs mt-1\",\n                                                        children: [\n                                                            \"Requested by \",\n                                                            request.requesterName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, request._id, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: \"\\uD83C\\uDFB6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/80\",\n                                                children: \"No approved requests yet. Be the first to request a song!\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, this);\n}\n_s(PublicSessionPage, \"OtMR4eckLhTkJpBcSdKd5CsPcrQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        convex_react__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_3__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_3__.useAction\n    ];\n});\n_c = PublicSessionPage;\nvar _c;\n$RefreshReg$(_c, \"PublicSessionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/session/[sessionId]/page.tsx\n"));

/***/ })

});