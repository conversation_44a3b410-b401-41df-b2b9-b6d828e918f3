"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/session/[sessionId]/page",{

/***/ "(app-pages-browser)/./app/session/[sessionId]/page.tsx":
/*!******************************************!*\
  !*** ./app/session/[sessionId]/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PublicSessionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var qrcode_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! qrcode.react */ \"(app-pages-browser)/./node_modules/qrcode.react/lib/esm/index.js\");\n/* harmony import */ var _components_SongSearchInput__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../../components/SongSearchInput */ \"(app-pages-browser)/./components/SongSearchInput.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction PublicSessionPage() {\n    var _session_weddingCoupleNames;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const sessionId = params.sessionId;\n    // Validate session ID format\n    const isValidSessionId = sessionId && sessionId.length > 0 && !sessionId.includes('test');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        songTitle: '',\n        artistName: '',\n        requesterName: '',\n        message: ''\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitMessage, setSubmitMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showQR, setShowQR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Queries with proper error handling\n    const session = (0,convex_react__WEBPACK_IMPORTED_MODULE_3__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.sessions.getPublicSession, isValidSessionId ? {\n        sessionId: sessionId\n    } : \"skip\");\n    const requests = (0,convex_react__WEBPACK_IMPORTED_MODULE_3__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.songRequests.getSessionRequests, session && isValidSessionId ? {\n        sessionId: sessionId,\n        status: \"approved\",\n        limit: 10\n    } : \"skip\");\n    // Mutations\n    const createRequest = (0,convex_react__WEBPACK_IMPORTED_MODULE_3__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.songRequests.createSongRequest);\n    const createValidatedRequest = (0,convex_react__WEBPACK_IMPORTED_MODULE_3__.useAction)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.spotify.createValidatedSongRequest);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!session || !session.acceptRequests) return;\n        setIsSubmitting(true);\n        setSubmitMessage(null);\n        try {\n            // Use Spotify-enhanced request creation if we have Spotify data\n            let result;\n            if (formData.spotifyId) {\n                result = await createValidatedRequest({\n                    sessionId: sessionId,\n                    songTitle: formData.songTitle.trim(),\n                    artistName: formData.artistName.trim(),\n                    requesterName: formData.requesterName.trim(),\n                    spotifyTrackId: formData.spotifyId\n                });\n                if (!result.success) {\n                    throw new Error(result.error);\n                }\n                result = result.request;\n            } else {\n                // Fallback to regular request creation\n                result = await createRequest({\n                    sessionId: sessionId,\n                    songTitle: formData.songTitle.trim(),\n                    artistName: formData.artistName.trim(),\n                    requesterName: formData.requesterName.trim(),\n                    albumArtwork: formData.albumArt\n                });\n            }\n            setSubmitMessage({\n                type: 'success',\n                text: result.message || 'Request submitted successfully!'\n            });\n            // Clear form\n            setFormData({\n                songTitle: '',\n                artistName: '',\n                requesterName: '',\n                message: ''\n            });\n        } catch (error) {\n            setSubmitMessage({\n                type: 'error',\n                text: error.message || 'Failed to submit request'\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSongSelect = (song)=>{\n        setFormData((prev)=>({\n                ...prev,\n                songTitle: song.title,\n                artistName: song.artist,\n                spotifyId: song.spotifyId,\n                albumArt: song.albumArt\n            }));\n    };\n    // Invalid session ID\n    if (!isValidSessionId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-8 text-center max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"❌\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-white mb-4\",\n                        children: \"Invalid Session\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/80 mb-6\",\n                        children: \"The session ID provided is not valid. Please check the link and try again.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"bg-purple-600 hover:bg-purple-700 text-white\",\n                        children: \"Go to Home\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, this);\n    }\n    // Loading state\n    if (session === undefined) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-lg\",\n                children: \"Loading session...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this);\n    }\n    // Session not found or inactive\n    if (!session) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-8 text-center max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83C\\uDFB5\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-white mb-4\",\n                        children: \"Session Not Found\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/80 mb-6\",\n                        children: \"This session is either inactive or doesn't exist. Please check the link and try again.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"bg-purple-600 hover:bg-purple-700 text-white\",\n                        children: \"Go to Home\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                lineNumber: 163,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this);\n    }\n    const currentUrl =  true ? window.location.href : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-black/20 backdrop-blur-md border-b border-white/10 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: session.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                session.weddingModeEnabled && session.weddingCoupleNames && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/80\",\n                                    children: [\n                                        session.weddingCoupleNames.join(' & '),\n                                        \"'s Wedding\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            onClick: ()=>setShowQR(!showQR),\n                            variant: \"outline\",\n                            className: \"text-white border-white/30 hover:bg-white/10\",\n                            children: showQR ? 'Hide QR' : 'Share QR'\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto p-4 space-y-8\",\n                children: [\n                    showQR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"bg-white/10 backdrop-blur-md border-white/20 p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"Share This Session\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-4 rounded-lg inline-block mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(qrcode_react__WEBPACK_IMPORTED_MODULE_9__.QRCodeSVG, {\n                                    value: currentUrl,\n                                    size: 200\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 text-sm\",\n                                children: \"Scan this QR code to join the session\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this),\n                    session.weddingModeEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"bg-white/10 backdrop-blur-md border-white/20 p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-4xl mb-4\",\n                                children: \"\\uD83D\\uDC92\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: [\n                                    (_session_weddingCoupleNames = session.weddingCoupleNames) === null || _session_weddingCoupleNames === void 0 ? void 0 : _session_weddingCoupleNames.join(' & '),\n                                    \"'s Wedding\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this),\n                            session.weddingDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/80 mb-2\",\n                                children: session.weddingDate\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, this),\n                            session.weddingHashtag && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-purple-300\",\n                                children: [\n                                    \"#\",\n                                    session.weddingHashtag\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, this),\n                            session.weddingCustomMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/90 mt-4 italic\",\n                                children: [\n                                    '\"',\n                                    session.weddingCustomMessage,\n                                    '\"'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, this),\n                    session.sponsorHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        className: \"bg-white/10 backdrop-blur-md border-white/20 p-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/90\",\n                                children: session.sponsorHeader\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this),\n                            session.sponsorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/70 text-sm mt-2\",\n                                children: session.sponsorMessage\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-6 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-3\",\n                                                children: \"\\uD83C\\uDFB5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Request a Song\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, this),\n                                    !session.acceptRequests ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: \"⏸️\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/80\",\n                                                children: \"This session is not currently accepting requests.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SongSearchInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                onSongSelect: handleSongSelect,\n                                                placeholder: \"Search for a song or artist...\",\n                                                initialTitle: formData.songTitle,\n                                                initialArtist: formData.artistName\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            (formData.songTitle || formData.artistName) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/10 rounded-lg p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        formData.albumArt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: formData.albumArt,\n                                                            alt: \"Album artwork\",\n                                                            className: \"w-12 h-12 rounded object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-semibold text-white\",\n                                                                    children: [\n                                                                        formData.songTitle,\n                                                                        formData.spotifyId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-2 text-xs bg-green-600 text-white px-2 py-1 rounded\",\n                                                                            children: \"♪ Verified\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                                            lineNumber: 287,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                                    lineNumber: 284,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white/80 text-sm\",\n                                                                    children: formData.artistName\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            type: \"button\",\n                                                            onClick: ()=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        songTitle: '',\n                                                                        artistName: '',\n                                                                        spotifyId: undefined,\n                                                                        albumArt: undefined\n                                                                    })),\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"text-white border-white/30 hover:bg-white/10\",\n                                                            children: \"Change\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-white mb-2\",\n                                                        children: \"Your Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        type: \"text\",\n                                                        value: formData.requesterName,\n                                                        onChange: (e)=>handleInputChange('requesterName', e.target.value),\n                                                        placeholder: \"Enter your name\",\n                                                        className: \"w-full bg-white/20 border-white/30 text-white placeholder-white/60\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-white mb-2\",\n                                                        children: \"Message (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                        value: formData.message,\n                                                        onChange: (e)=>handleInputChange('message', e.target.value),\n                                                        placeholder: \"Add a message with your request...\",\n                                                        className: \"w-full bg-white/20 border-white/30 text-white placeholder-white/60\",\n                                                        rows: 3\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, this),\n                                            submitMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg \".concat(submitMessage.type === 'success' ? 'bg-green-600/20 border border-green-500/30 text-green-200' : 'bg-red-600/20 border border-red-500/30 text-red-200'),\n                                                children: submitMessage.text\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                type: \"submit\",\n                                                disabled: isSubmitting || !formData.songTitle.trim() || !formData.artistName.trim() || !formData.requesterName.trim(),\n                                                className: \"w-full bg-purple-600 hover:bg-purple-700 text-white py-3\",\n                                                children: isSubmitting ? 'Submitting...' : 'Submit Request'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-6 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-3\",\n                                                children: \"✅\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Recent Requests\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 13\n                                    }, this),\n                                    requests && requests.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: requests.map((request)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/10 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold text-white\",\n                                                        children: request.songTitle\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white/80 text-sm\",\n                                                        children: [\n                                                            \"by \",\n                                                            request.artistName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white/60 text-xs mt-1\",\n                                                        children: [\n                                                            \"Requested by \",\n                                                            request.requesterName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, request._id, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: \"\\uD83C\\uDFB6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/80\",\n                                                children: \"No approved requests yet. Be the first to request a song!\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/session/[sessionId]/page.tsx\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, this);\n}\n_s(PublicSessionPage, \"OtMR4eckLhTkJpBcSdKd5CsPcrQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        convex_react__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_3__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_3__.useAction\n    ];\n});\n_c = PublicSessionPage;\nvar _c;\n$RefreshReg$(_c, \"PublicSessionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/session/[sessionId]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/SongSearchInput.tsx":
/*!****************************************!*\
  !*** ./components/SongSearchInput.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SongSearchInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction SongSearchInput(param) {\n    let { onSongSelect, placeholder = \"Search for a song...\", className = \"\", initialTitle = \"\", initialArtist = \"\" } = param;\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSearching, setIsSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showResults, setShowResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIndex, setSelectedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [manualEntry, setManualEntry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [manualTitle, setManualTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTitle);\n    const [manualArtist, setManualArtist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialArtist);\n    const searchRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const searchTracks = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useAction)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.spotify.searchTracks);\n    // Debounced search\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SongSearchInput.useEffect\": ()=>{\n            if (searchQuery.length < 2) {\n                setSearchResults([]);\n                setShowResults(false);\n                return;\n            }\n            const timeoutId = setTimeout({\n                \"SongSearchInput.useEffect.timeoutId\": async ()=>{\n                    setIsSearching(true);\n                    try {\n                        const result = await searchTracks({\n                            query: searchQuery,\n                            limit: 8\n                        });\n                        if (result.success) {\n                            setSearchResults(result.tracks);\n                            setShowResults(true);\n                        } else {\n                            setSearchResults([]);\n                            setShowResults(false);\n                        }\n                    } catch (error) {\n                        console.error('Search error:', error);\n                        setSearchResults([]);\n                        setShowResults(false);\n                    } finally{\n                        setIsSearching(false);\n                    }\n                }\n            }[\"SongSearchInput.useEffect.timeoutId\"], 300);\n            return ({\n                \"SongSearchInput.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"SongSearchInput.useEffect\"];\n        }\n    }[\"SongSearchInput.useEffect\"], [\n        searchQuery,\n        searchTracks\n    ]);\n    // Handle keyboard navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SongSearchInput.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"SongSearchInput.useEffect.handleKeyDown\": (e)=>{\n                    if (!showResults) return;\n                    switch(e.key){\n                        case 'ArrowDown':\n                            e.preventDefault();\n                            setSelectedIndex({\n                                \"SongSearchInput.useEffect.handleKeyDown\": (prev)=>prev < searchResults.length - 1 ? prev + 1 : prev\n                            }[\"SongSearchInput.useEffect.handleKeyDown\"]);\n                            break;\n                        case 'ArrowUp':\n                            e.preventDefault();\n                            setSelectedIndex({\n                                \"SongSearchInput.useEffect.handleKeyDown\": (prev)=>prev > 0 ? prev - 1 : -1\n                            }[\"SongSearchInput.useEffect.handleKeyDown\"]);\n                            break;\n                        case 'Enter':\n                            e.preventDefault();\n                            if (selectedIndex >= 0 && selectedIndex < searchResults.length) {\n                                handleSongSelect(searchResults[selectedIndex]);\n                            }\n                            break;\n                        case 'Escape':\n                            setShowResults(false);\n                            setSelectedIndex(-1);\n                            break;\n                    }\n                }\n            }[\"SongSearchInput.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"SongSearchInput.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"SongSearchInput.useEffect\"];\n        }\n    }[\"SongSearchInput.useEffect\"], [\n        showResults,\n        selectedIndex,\n        searchResults\n    ]);\n    // Handle click outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SongSearchInput.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"SongSearchInput.useEffect.handleClickOutside\": (event)=>{\n                    if (searchRef.current && !searchRef.current.contains(event.target)) {\n                        setShowResults(false);\n                        setSelectedIndex(-1);\n                    }\n                }\n            }[\"SongSearchInput.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"SongSearchInput.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"SongSearchInput.useEffect\"];\n        }\n    }[\"SongSearchInput.useEffect\"], []);\n    const handleSongSelect = (track)=>{\n        onSongSelect({\n            title: track.name,\n            artist: track.artists[0],\n            spotifyId: track.id,\n            albumArt: track.albumArt || undefined\n        });\n        setSearchQuery(\"\".concat(track.name, \" - \").concat(track.artists[0]));\n        setShowResults(false);\n        setSelectedIndex(-1);\n    };\n    const handleManualSubmit = ()=>{\n        if (manualTitle.trim() && manualArtist.trim()) {\n            onSongSelect({\n                title: manualTitle.trim(),\n                artist: manualArtist.trim()\n            });\n            setManualEntry(false);\n        }\n    };\n    const formatDuration = (ms)=>{\n        const minutes = Math.floor(ms / 60000);\n        const seconds = Math.floor(ms % 60000 / 1000);\n        return \"\".concat(minutes, \":\").concat(seconds.toString().padStart(2, '0'));\n    };\n    if (manualEntry) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-white\",\n                            children: \"Manual Entry\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            onClick: ()=>setManualEntry(false),\n                            variant: \"outline\",\n                            size: \"sm\",\n                            className: \"text-white border-white/30 hover:bg-white/10\",\n                            children: \"Back to Search\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-white mb-2\",\n                                    children: \"Song Title *\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    type: \"text\",\n                                    value: manualTitle,\n                                    onChange: (e)=>setManualTitle(e.target.value),\n                                    placeholder: \"Enter song title\",\n                                    className: \"w-full bg-white/20 border-white/30 text-white placeholder-white/60\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-white mb-2\",\n                                    children: \"Artist Name *\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    type: \"text\",\n                                    value: manualArtist,\n                                    onChange: (e)=>setManualArtist(e.target.value),\n                                    placeholder: \"Enter artist name\",\n                                    className: \"w-full bg-white/20 border-white/30 text-white placeholder-white/60\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            onClick: handleManualSubmit,\n                            disabled: !manualTitle.trim() || !manualArtist.trim(),\n                            className: \"w-full bg-purple-600 hover:bg-purple-700 text-white\",\n                            children: \"Use This Song\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n            lineNumber: 157,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: searchRef,\n        className: \"relative \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-white mb-2\",\n                                children: \"Search for a Song\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                type: \"text\",\n                                value: searchQuery,\n                                onChange: (e)=>setSearchQuery(e.target.value),\n                                placeholder: placeholder,\n                                className: \"w-full bg-white/20 border-white/30 text-white placeholder-white/60\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this),\n                            isSearching && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/60 text-sm mt-1\",\n                                children: \"Searching...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: ()=>setManualEntry(true),\n                        variant: \"outline\",\n                        size: \"sm\",\n                        className: \"text-white border-white/30 hover:bg-white/10\",\n                        children: \"Enter Song Manually\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            showResults && searchResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                className: \"absolute top-full left-0 right-0 mt-2 bg-white/10 backdrop-blur-md border-white/20 max-h-96 overflow-y-auto z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-white/80 text-sm mb-2 px-2\",\n                            children: [\n                                \"Found \",\n                                searchResults.length,\n                                \" results\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 13\n                        }, this),\n                        searchResults.map((track, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>handleSongSelect(track),\n                                className: \"flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors \".concat(index === selectedIndex ? 'bg-purple-600/30' : 'hover:bg-white/10'),\n                                children: [\n                                    track.albumArt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: track.albumArt,\n                                        alt: \"\".concat(track.album, \" cover\"),\n                                        className: \"w-12 h-12 rounded object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-semibold text-white truncate\",\n                                                children: [\n                                                    track.name,\n                                                    track.explicit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-xs bg-red-600 text-white px-1 rounded\",\n                                                        children: \"E\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white/80 text-sm truncate\",\n                                                children: track.artists.join(', ')\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white/60 text-xs\",\n                                                children: [\n                                                    track.album,\n                                                    \" • \",\n                                                    formatDuration(track.duration)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white/60 text-xs\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400\",\n                                                    children: \"♪\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        track.popularity,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, track.id, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 15\n                            }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                lineNumber: 240,\n                columnNumber: 9\n            }, this),\n            showResults && searchResults.length === 0 && !isSearching && searchQuery.length >= 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                className: \"absolute top-full left-0 right-0 mt-2 bg-white/10 backdrop-blur-md border-white/20 p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-white/80 mb-2\",\n                            children: \"No songs found\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-white/60 text-sm mb-3\",\n                            children: \"Try a different search or enter the song manually\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            onClick: ()=>setManualEntry(true),\n                            size: \"sm\",\n                            className: \"bg-purple-600 hover:bg-purple-700 text-white\",\n                            children: \"Enter Manually\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n                lineNumber: 290,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/SongSearchInput.tsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, this);\n}\n_s(SongSearchInput, \"JCG7aHIMnofIUWpV3E2H9zSMcU4=\", false, function() {\n    return [\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useAction\n    ];\n});\n_c = SongSearchInput;\nvar _c;\n$RefreshReg$(_c, \"SongSearchInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/SongSearchInput.tsx\n"));

/***/ })

});