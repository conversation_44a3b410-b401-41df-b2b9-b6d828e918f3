"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/dashboard/DJProfileCard.tsx":
/*!************************************************!*\
  !*** ./components/dashboard/DJProfileCard.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DJProfileCard: () => (/* binding */ DJProfileCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_file_upload__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/file-upload */ \"(app-pages-browser)/./components/ui/file-upload.tsx\");\n/* harmony import */ var _barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Music,Settings,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Music,Settings,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Music,Settings,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Music,Settings,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Music,Settings,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ DJProfileCard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction DJProfileCard() {\n    _s();\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [isCompletingSetup, setIsCompletingSetup] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [showProfilePictureUpload, setShowProfilePictureUpload] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    // Get current user with profile\n    const userWithProfile = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.users.getCurrentUserWithProfile);\n    const userStatus = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.users.checkUserStatus);\n    // Get user's profile pictures\n    const profilePictures = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.fileStorage.getUserFiles, {\n        purpose: \"profile_picture\",\n        limit: 1\n    });\n    // Mutations\n    const initializeSetup = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.users.initializeUserSetup);\n    const updateProfilePicture = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.fileStorage.updateProfilePicture);\n    const completeOnboarding = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.users.completeOnboarding);\n    const handleCreateDJProfile = async ()=>{\n        setIsCreating(true);\n        try {\n            var _userWithProfile_user;\n            await initializeSetup({\n                createDjProfile: true,\n                displayName: (userWithProfile === null || userWithProfile === void 0 ? void 0 : (_userWithProfile_user = userWithProfile.user) === null || _userWithProfile_user === void 0 ? void 0 : _userWithProfile_user.name) || \"New DJ\"\n            });\n        } catch (error) {\n            console.error(\"Failed to create DJ profile:\", error);\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    const handleProfilePictureUpload = async (fileId, url)=>{\n        try {\n            await updateProfilePicture({\n                storageId: fileId\n            });\n            setShowProfilePictureUpload(false);\n        } catch (error) {\n            console.error(\"Failed to update profile picture:\", error);\n        }\n    };\n    const handleEditProfile = ()=>{\n        // Navigate to profile settings or open edit modal\n        console.log(\"Edit profile clicked\");\n        // For now, we'll just show an alert - in a real app this would open a modal or navigate\n        alert(\"Profile editing functionality would be implemented here. This could open a modal with editable fields for display name, bio, etc.\");\n    };\n    const handleCompleteSetup = async ()=>{\n        setIsCompletingSetup(true);\n        try {\n            const result = await completeOnboarding();\n            console.log(\"Onboarding completed:\", result);\n            if (result.alreadyCompleted) {\n                alert(\"Setup is already complete!\");\n            } else {\n                alert(\"Setup completed successfully! Welcome to PlayBeg!\");\n            }\n        } catch (error) {\n            console.error(\"Failed to complete onboarding:\", error);\n            alert(\"Failed to complete setup. Please try again.\");\n        } finally{\n            setIsCompletingSetup(false);\n        }\n    };\n    const currentProfilePicture = profilePictures === null || profilePictures === void 0 ? void 0 : profilePictures[0];\n    if (!userWithProfile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"bg-gray-800/50 border-purple-500/20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-700 rounded w-3/4 mb-2\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-700 rounded w-1/2\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this);\n    }\n    const { user, djProfile, hasDjProfile, onboardingComplete } = userWithProfile;\n    if (!hasDjProfile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"bg-gray-800/50 border-purple-500/20\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"text-white flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                \"Create DJ Profile\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                            className: \"text-gray-400\",\n                            children: \"Set up your DJ profile to start creating sessions and receiving requests\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 bg-purple-500/20 border border-purple-500/30 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-white font-medium mb-2\",\n                                    children: [\n                                        \"Welcome, \",\n                                        (user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.email),\n                                        \"!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-sm mb-4\",\n                                    children: \"Create your DJ profile to unlock all PlayBeg features including session management, song request handling, and real-time audience interaction.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleCreateDJProfile,\n                                    disabled: isCreating,\n                                    className: \"w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700\",\n                                    children: isCreating ? \"Creating...\" : \"Create DJ Profile\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"bg-gray-800/50 border-purple-500/20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                        className: \"text-white flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"DJ Profile\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                variant: onboardingComplete ? \"default\" : \"secondary\",\n                                children: onboardingComplete ? \"Complete\" : \"Setup Needed\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                        className: \"text-gray-400\",\n                        children: \"Your DJ profile and account status\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 bg-gray-600 rounded-full flex items-center justify-center overflow-hidden\",\n                                            children: (currentProfilePicture === null || currentProfilePicture === void 0 ? void 0 : currentProfilePicture.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: currentProfilePicture.url,\n                                                alt: \"Profile\",\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-8 h-8 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setShowProfilePictureUpload(!showProfilePictureUpload),\n                                            className: \"absolute -bottom-2 -right-2 w-8 h-8 rounded-full p-0 border-gray-600 bg-gray-800 hover:bg-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-white font-medium\",\n                                            children: (djProfile === null || djProfile === void 0 ? void 0 : djProfile.displayName) || \"Unnamed DJ\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: (user === null || user === void 0 ? void 0 : user.email) || \"No email\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this),\n                                        currentProfilePicture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: [\n                                                \"Picture uploaded \",\n                                                new Date(currentProfilePicture.uploadedAt).toLocaleDateString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this),\n                        showProfilePictureUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border border-gray-600 rounded-lg p-4 bg-gray-700/30\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                    className: \"text-white font-medium mb-3 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Update Profile Picture\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_file_upload__WEBPACK_IMPORTED_MODULE_6__.FileUpload, {\n                                    purpose: \"profile_picture\",\n                                    onUploadComplete: handleProfilePictureUpload,\n                                    onUploadError: (error)=>console.error(\"Profile picture upload error:\", error),\n                                    maxSize: 5 * 1024 * 1024,\n                                    className: \"mb-3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setShowProfilePictureUpload(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Display Name\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: (djProfile === null || djProfile === void 0 ? void 0 : djProfile.displayName) || \"Not set\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: (user === null || user === void 0 ? void 0 : user.email) || \"Not available\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full mx-auto mb-2 \".concat((userStatus === null || userStatus === void 0 ? void 0 : userStatus.isAuthenticated) ? 'bg-green-400' : 'bg-red-400')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Auth Status\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm font-medium\",\n                                            children: (userStatus === null || userStatus === void 0 ? void 0 : userStatus.isAuthenticated) ? 'Active' : 'Inactive'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full mx-auto mb-2 \".concat((userStatus === null || userStatus === void 0 ? void 0 : userStatus.hasDjProfile) ? 'bg-green-400' : 'bg-gray-400')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"DJ Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm font-medium\",\n                                            children: (userStatus === null || userStatus === void 0 ? void 0 : userStatus.hasDjProfile) ? 'Created' : 'Missing'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full mx-auto mb-2 \".concat(onboardingComplete ? 'bg-green-400' : 'bg-orange-400')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Onboarding\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm font-medium\",\n                                            children: onboardingComplete ? 'Done' : 'Pending'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full mx-auto mb-2 bg-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Member Since\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm font-medium\",\n                                            children: djProfile ? new Date(djProfile.createdAt).toLocaleDateString() : 'N/A'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    onClick: handleEditProfile,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Edit Profile\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this),\n                                !onboardingComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"sm\",\n                                    className: \"bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700\",\n                                    onClick: handleCompleteSetup,\n                                    children: \"Complete Setup\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n_s(DJProfileCard, \"KTClivDFSK8zmCWkjHW+lB5dTSc=\", false, function() {\n    return [\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useMutation\n    ];\n});\n_c = DJProfileCard;\nvar _c;\n$RefreshReg$(_c, \"DJProfileCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/dashboard/DJProfileCard.tsx\n"));

/***/ })

});