"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @convex-dev/auth/react */ \"(app-pages-browser)/./node_modules/@convex-dev/auth/dist/react/index.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _components_dashboard_DJProfileCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/DJProfileCard */ \"(app-pages-browser)/./components/dashboard/DJProfileCard.tsx\");\n/* harmony import */ var _components_dashboard_SessionManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/SessionManager */ \"(app-pages-browser)/./components/dashboard/SessionManager.tsx\");\n/* harmony import */ var _components_SongRequestManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/SongRequestManager */ \"(app-pages-browser)/./components/SongRequestManager.tsx\");\n/* harmony import */ var _components_dashboard_NotificationCenter__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/NotificationCenter */ \"(app-pages-browser)/./components/dashboard/NotificationCenter.tsx\");\n/* harmony import */ var _components_PassPurchase__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/PassPurchase */ \"(app-pages-browser)/./components/PassPurchase.tsx\");\n/* harmony import */ var _components_SongSearchInput__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/SongSearchInput */ \"(app-pages-browser)/./components/SongSearchInput.tsx\");\n/* harmony import */ var _components_testing_IntegrationTestSuite__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/testing/IntegrationTestSuite */ \"(app-pages-browser)/./components/testing/IntegrationTestSuite.tsx\");\n/* harmony import */ var _components_testing_RealtimeTestSuite__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/testing/RealtimeTestSuite */ \"(app-pages-browser)/./components/testing/RealtimeTestSuite.tsx\");\n/* harmony import */ var _components_session_SessionManagementHub__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/session/SessionManagementHub */ \"(app-pages-browser)/./components/session/SessionManagementHub.tsx\");\n/* harmony import */ var _components_admin_BlogManagement__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/admin/BlogManagement */ \"(app-pages-browser)/./components/admin/BlogManagement.tsx\");\n/* harmony import */ var _components_search_AdvancedSearchInterface__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/search/AdvancedSearchInterface */ \"(app-pages-browser)/./components/search/AdvancedSearchInterface.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const { isAuthenticated, isLoading } = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useConvexAuth)();\n    const { signOut } = (0,_convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_17__.useAuthActions)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('overview');\n    // Get current user and their DJ profile\n    const currentUser = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.getCurrentUser);\n    const userWithProfile = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.getCurrentUserWithProfile);\n    const djProfile = userWithProfile === null || userWithProfile === void 0 ? void 0 : userWithProfile.djProfile;\n    // Get user status\n    const userStatus = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.checkUserStatus);\n    // Get DJ profiles list (for testing)\n    const allDjProfiles = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.djProfiles.listDjProfiles, {\n        limit: 10,\n        onboardingStatus: true\n    });\n    // Get active sessions for the current user\n    const activeSessions = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.sessions.getCurrentUserSessions, currentUser ? {\n        activeOnly: true,\n        limit: 10\n    } : \"skip\");\n    // Get the first active session for song request management\n    const activeSession = activeSessions && activeSessions.length > 0 ? activeSessions[0] : null;\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated) {\n                router.push(\"/signin\");\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    const handleSignOut = async ()=>{\n        await signOut();\n        router.push(\"/signin\");\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-lg\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null; // Will redirect to signin\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gray-900/50 backdrop-blur-md border-b border-purple-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-white\",\n                                        children: \"PlayBeg\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300\",\n                                        children: [\n                                            \"Welcome, \",\n                                            (currentUser === null || currentUser === void 0 ? void 0 : currentUser.name) || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.email) || \"DJ\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_NotificationCenter__WEBPACK_IMPORTED_MODULE_9__.NotificationCenter, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: handleSignOut,\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"text-gray-300 hover:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Sign Out\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-white mb-2\",\n                                children: \"DJ Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Manage your sessions, song requests, and audience engagement\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 border-b border-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('overview'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'overview' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('requests'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'requests' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Song Requests\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('billing'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'billing' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Billing & Passes\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('analytics'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'analytics' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('spotify-test'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'spotify-test' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83C\\uDFB5 Spotify Test\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('integration-test'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'integration-test' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83E\\uDDEA Integration Test\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('realtime-test'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'realtime-test' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"⚡ Real-time Test\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('session-management'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'session-management' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83C\\uDF9B️ Sessions\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('blog-admin'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'blog-admin' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83D\\uDCDD Blog Admin\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('advanced-search'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'advanced-search' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83D\\uDD0D Advanced Search\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DJProfileCard__WEBPACK_IMPORTED_MODULE_6__.DJProfileCard, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SessionManager__WEBPACK_IMPORTED_MODULE_7__.SessionManager, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'requests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: activeSession ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SongRequestManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            sessionId: activeSession._id\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: \"\\uD83C\\uDFB5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white mb-2\",\n                                    children: \"No Active Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-6\",\n                                    children: \"You need to have an active session to manage song requests.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: ()=>setActiveTab('overview'),\n                                    className: \"bg-purple-600 hover:bg-purple-700 text-white\",\n                                    children: \"Create a Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'billing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PassPurchase__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'analytics' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Analytics Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl mb-4\",\n                                        children: \"\\uD83D\\uDCCA\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Detailed analytics coming soon...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'spotify-test' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-4\",\n                                children: \"\\uD83C\\uDFB5 Spotify Integration Test\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-6\",\n                                children: \"Test the Spotify search functionality to verify the integration is working properly.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SongSearchInput__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    onSongSelect: (song)=>{\n                                        console.log('Selected song:', song);\n                                        alert(\"Selected: \".concat(song.title, \" by \").concat(song.artist));\n                                    },\n                                    placeholder: \"Search for a song on Spotify...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'integration-test' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_testing_IntegrationTestSuite__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'realtime-test' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_testing_RealtimeTestSuite__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'session-management' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_session_SessionManagementHub__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        onSessionSelect: (sessionId)=>{\n                            console.log('Selected session:', sessionId);\n                        // Could navigate to session detail page or open session in new tab\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'blog-admin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_BlogManagement__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'advanced-search' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_search_AdvancedSearchInterface__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Quick Stats\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                            children: \"View Full Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: (allDjProfiles === null || allDjProfiles === void 0 ? void 0 : allDjProfiles.length) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Total DJs\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Active Sessions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Song Requests\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: (userStatus === null || userStatus === void 0 ? void 0 : userStatus.isAuthenticated) ? '100%' : '0%'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"System Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"N2GOEDHqEQMaB1gROcBEnLiv8FE=\", false, function() {\n    return [\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useConvexAuth,\n        _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_17__.useAuthActions,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/search/AdvancedSearchInterface.tsx":
/*!*******************************************************!*\
  !*** ./components/search/AdvancedSearchInterface.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdvancedSearchInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronUp,Eye,FileText,Filter,Music,Play,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronUp,Eye,FileText,Filter,Music,Play,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronUp,Eye,FileText,Filter,Music,Play,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronUp,Eye,FileText,Filter,Music,Play,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronUp,Eye,FileText,Filter,Music,Play,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronUp,Eye,FileText,Filter,Music,Play,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronUp,Eye,FileText,Filter,Music,Play,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronUp,Eye,FileText,Filter,Music,Play,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronUp,Eye,FileText,Filter,Music,Play,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronUp,Eye,FileText,Filter,Music,Play,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction AdvancedSearchInterface() {\n    var _filters_active, _filters_weddingMode, _filterOptions_statuses, _filterOptions_categories, _filterOptions_authors, _filters_featured, _searchResults_sessions, _searchResults_songRequests, _searchResults_blogPosts;\n    _s();\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        contentType: 'all',\n        searchQuery: ''\n    });\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Search queries\n    const globalResults = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.search.globalSearch, filters.contentType === 'all' && filters.searchQuery.length > 0 ? {\n        searchQuery: filters.searchQuery,\n        limit: 20\n    } : \"skip\");\n    const sessionResults = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.search.searchSessions, filters.contentType === 'sessions' && filters.searchQuery.length > 0 ? {\n        searchQuery: filters.searchQuery,\n        active: filters.active,\n        weddingMode: filters.weddingMode,\n        limit: 20\n    } : \"skip\");\n    const songRequestResults = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.search.searchSongRequests, filters.contentType === 'songRequests' && filters.searchQuery.length > 0 ? {\n        searchQuery: filters.searchQuery,\n        status: filters.status,\n        artistName: filters.artistName,\n        limit: 20\n    } : \"skip\");\n    const blogPostResults = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.search.searchBlogPosts, filters.contentType === 'blogPosts' && filters.searchQuery.length > 0 ? {\n        searchQuery: filters.searchQuery,\n        category: filters.category,\n        author: filters.author,\n        featured: filters.featured,\n        limit: 20\n    } : \"skip\");\n    const filterOptions = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.search.getFilterOptions, filters.contentType !== 'all' ? {\n        type: filters.contentType\n    } : \"skip\");\n    // Update search results when queries change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdvancedSearchInterface.useEffect\": ()=>{\n            if (filters.contentType === 'all') {\n                setSearchResults(globalResults);\n            } else if (filters.contentType === 'sessions') {\n                setSearchResults(sessionResults);\n            } else if (filters.contentType === 'songRequests') {\n                setSearchResults(songRequestResults);\n            } else if (filters.contentType === 'blogPosts') {\n                setSearchResults(blogPostResults);\n            }\n        }\n    }[\"AdvancedSearchInterface.useEffect\"], [\n        globalResults,\n        sessionResults,\n        songRequestResults,\n        blogPostResults,\n        filters.contentType\n    ]);\n    const handleSearch = (query)=>{\n        setFilters((prev)=>({\n                ...prev,\n                searchQuery: query\n            }));\n    };\n    const clearFilters = ()=>{\n        setFilters({\n            contentType: 'all',\n            searchQuery: ''\n        });\n        setSearchResults(null);\n    };\n    const getContentTypeIcon = (type)=>{\n        switch(type){\n            case 'sessions':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 31\n                }, this);\n            case 'songRequests':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 35\n                }, this);\n            case 'blogPosts':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 32\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const formatDate = (timestamp)=>{\n        return new Date(timestamp).toLocaleDateString('en-US', {\n            month: 'short',\n            day: 'numeric',\n            year: 'numeric'\n        });\n    };\n    const renderSearchResult = (result, type)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n            className: \"bg-white/10 backdrop-blur-md border-white/20 p-4 hover:bg-white/20 transition-colors\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 mb-2\",\n                            children: [\n                                getContentTypeIcon(type),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-white\",\n                                    children: result.title || result.name || result.songTitle\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"text-xs\",\n                                    children: type === 'sessions' ? 'Session' : type === 'songRequests' ? 'Song Request' : type === 'blogPosts' ? 'Blog Post' : type\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/70 text-sm mb-2\",\n                            children: result.description || result.excerpt || (type === 'songRequests' ? \"by \".concat(result.artistName) : '')\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 text-xs text-white/60\",\n                            children: [\n                                result.createdAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-3 h-3 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 19\n                                        }, this),\n                                        formatDate(result.createdAt)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 17\n                                }, this),\n                                result.status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                    className: \"text-xs\",\n                                    variant: result.status === 'active' || result.status === 'published' ? 'default' : 'secondary',\n                                    children: result.status\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 17\n                                }, this),\n                                result.viewCount !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-3 h-3 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 19\n                                        }, this),\n                                        result.viewCount,\n                                        \" views\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this)\n        }, result._id, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                    value: filters.searchQuery,\n                                    onChange: (e)=>handleSearch(e.target.value),\n                                    placeholder: \"Search sessions, song requests, blog posts...\",\n                                    className: \"pl-10 bg-gray-800 border-gray-600 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                    className: \"text-white text-sm\",\n                                    children: \"Search in:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        {\n                                            value: 'all',\n                                            label: 'All Content',\n                                            icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                        },\n                                        {\n                                            value: 'sessions',\n                                            label: 'Sessions',\n                                            icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                                        },\n                                        {\n                                            value: 'songRequests',\n                                            label: 'Song Requests',\n                                            icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                                        },\n                                        {\n                                            value: 'blogPosts',\n                                            label: 'Blog Posts',\n                                            icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                        }\n                                    ].map((type)=>{\n                                        const Icon = type.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setFilters((prev)=>({\n                                                        ...prev,\n                                                        contentType: type.value\n                                                    })),\n                                            className: \"flex items-center space-x-1 px-3 py-1 rounded-md text-sm transition-colors \".concat(filters.contentType === type.value ? 'bg-purple-600 text-white' : 'bg-gray-700 text-white/70 hover:bg-gray-600'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: type.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, type.value, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this),\n                        filters.contentType !== 'all' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setShowFilters(!showFilters),\n                                    className: \"text-white border-white/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Advanced Filters\",\n                                        showFilters ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-4 h-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 32\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"w-4 h-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 73\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this),\n                                Object.keys(filters).length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: clearFilters,\n                                    className: \"text-white border-white/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Clear Filters\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 13\n                        }, this),\n                        showFilters && filters.contentType !== 'all' && filterOptions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/5 rounded-lg p-4 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold text-white\",\n                                    children: \"Advanced Filters\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 15\n                                }, this),\n                                filters.contentType === 'sessions' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                    className: \"text-white text-sm\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: ((_filters_active = filters.active) === null || _filters_active === void 0 ? void 0 : _filters_active.toString()) || '',\n                                                    onChange: (e)=>setFilters((prev)=>({\n                                                                ...prev,\n                                                                active: e.target.value ? e.target.value === 'true' : undefined\n                                                            })),\n                                                    className: \"w-full bg-gray-800 border-gray-600 text-white rounded px-3 py-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"All Statuses\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"true\",\n                                                            children: \"Active\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"false\",\n                                                            children: \"Inactive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                    className: \"text-white text-sm\",\n                                                    children: \"Mode\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: ((_filters_weddingMode = filters.weddingMode) === null || _filters_weddingMode === void 0 ? void 0 : _filters_weddingMode.toString()) || '',\n                                                    onChange: (e)=>setFilters((prev)=>({\n                                                                ...prev,\n                                                                weddingMode: e.target.value ? e.target.value === 'true' : undefined\n                                                            })),\n                                                    className: \"w-full bg-gray-800 border-gray-600 text-white rounded px-3 py-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"All Modes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"true\",\n                                                            children: \"Wedding Mode\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"false\",\n                                                            children: \"Regular Mode\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 17\n                                }, this),\n                                filters.contentType === 'songRequests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                    className: \"text-white text-sm\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: filters.status || '',\n                                                    onChange: (e)=>setFilters((prev)=>({\n                                                                ...prev,\n                                                                status: e.target.value || undefined\n                                                            })),\n                                                    className: \"w-full bg-gray-800 border-gray-600 text-white rounded px-3 py-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"All Statuses\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        (_filterOptions_statuses = filterOptions.statuses) === null || _filterOptions_statuses === void 0 ? void 0 : _filterOptions_statuses.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: status.value,\n                                                                children: status.label\n                                                            }, status.value, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                    className: \"text-white text-sm\",\n                                                    children: \"Artist\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                    value: filters.artistName || '',\n                                                    onChange: (e)=>setFilters((prev)=>({\n                                                                ...prev,\n                                                                artistName: e.target.value || undefined\n                                                            })),\n                                                    placeholder: \"Filter by artist...\",\n                                                    className: \"bg-gray-800 border-gray-600 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 17\n                                }, this),\n                                filters.contentType === 'blogPosts' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                    className: \"text-white text-sm\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: filters.category || '',\n                                                    onChange: (e)=>setFilters((prev)=>({\n                                                                ...prev,\n                                                                category: e.target.value || undefined\n                                                            })),\n                                                    className: \"w-full bg-gray-800 border-gray-600 text-white rounded px-3 py-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"All Categories\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        (_filterOptions_categories = filterOptions.categories) === null || _filterOptions_categories === void 0 ? void 0 : _filterOptions_categories.map((cat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: cat.value,\n                                                                children: cat.label\n                                                            }, cat.value, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                    className: \"text-white text-sm\",\n                                                    children: \"Author\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: filters.author || '',\n                                                    onChange: (e)=>setFilters((prev)=>({\n                                                                ...prev,\n                                                                author: e.target.value || undefined\n                                                            })),\n                                                    className: \"w-full bg-gray-800 border-gray-600 text-white rounded px-3 py-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"All Authors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        (_filterOptions_authors = filterOptions.authors) === null || _filterOptions_authors === void 0 ? void 0 : _filterOptions_authors.map((author)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: author.value,\n                                                                children: author.label\n                                                            }, author.value, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                    className: \"text-white text-sm\",\n                                                    children: \"Featured\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: ((_filters_featured = filters.featured) === null || _filters_featured === void 0 ? void 0 : _filters_featured.toString()) || '',\n                                                    onChange: (e)=>setFilters((prev)=>({\n                                                                ...prev,\n                                                                featured: e.target.value ? e.target.value === 'true' : undefined\n                                                            })),\n                                                    className: \"w-full bg-gray-800 border-gray-600 text-white rounded px-3 py-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"All Posts\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"true\",\n                                                            children: \"Featured Only\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"false\",\n                                                            children: \"Regular Only\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            searchResults && filters.searchQuery.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-white\",\n                            children: [\n                                \"Search Results\",\n                                searchResults.totalResults !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white/60 ml-2\",\n                                    children: [\n                                        \"(\",\n                                        searchResults.totalResults,\n                                        \" found)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 11\n                    }, this),\n                    filters.contentType === 'all' && searchResults.totalResults > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            ((_searchResults_sessions = searchResults.sessions) === null || _searchResults_sessions === void 0 ? void 0 : _searchResults_sessions.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-white mb-3 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Sessions (\",\n                                            searchResults.sessions.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: searchResults.sessions.map((result)=>renderSearchResult(result, 'sessions'))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 17\n                            }, this),\n                            ((_searchResults_songRequests = searchResults.songRequests) === null || _searchResults_songRequests === void 0 ? void 0 : _searchResults_songRequests.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-white mb-3 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Song Requests (\",\n                                            searchResults.songRequests.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: searchResults.songRequests.map((result)=>renderSearchResult(result, 'songRequests'))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 17\n                            }, this),\n                            ((_searchResults_blogPosts = searchResults.blogPosts) === null || _searchResults_blogPosts === void 0 ? void 0 : _searchResults_blogPosts.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-white mb-3 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Blog Posts (\",\n                                            searchResults.blogPosts.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: searchResults.blogPosts.map((result)=>renderSearchResult(result, 'blogPosts'))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 13\n                    }, this),\n                    filters.contentType !== 'all' && Array.isArray(searchResults) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: searchResults.map((result)=>renderSearchResult(result, filters.contentType))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 13\n                    }, this),\n                    (filters.contentType === 'all' && searchResults.totalResults === 0 || filters.contentType !== 'all' && Array.isArray(searchResults) && searchResults.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"bg-white/10 backdrop-blur-md border-white/20 p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-12 h-12 text-white/40 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-2\",\n                                children: \"No results found\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/60\",\n                                children: \"Try adjusting your search terms or filters to find what you're looking for.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                lineNumber: 387,\n                columnNumber: 9\n            }, this),\n            !searchResults && filters.searchQuery.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"w-16 h-16 text-white/40 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-white mb-2\",\n                        children: \"Advanced Search\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/60 mb-4\",\n                        children: \"Search across sessions, song requests, and blog posts with powerful filtering options.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-4 text-sm text-white/60\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Sessions\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Song Requests\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronUp_Eye_FileText_Filter_Music_Play_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Blog Posts\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n                lineNumber: 461,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/search/AdvancedSearchInterface.tsx\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, this);\n}\n_s(AdvancedSearchInterface, \"LuZMtActpW9VYCmUOfFA3X99ePY=\", false, function() {\n    return [\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery\n    ];\n});\n_c = AdvancedSearchInterface;\nvar _c;\n$RefreshReg$(_c, \"AdvancedSearchInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/search/AdvancedSearchInterface.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-down.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChevronDown)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.522.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m6 9 6 6 6-6\",\n            key: \"qrunsl\"\n        }\n    ]\n];\nconst ChevronDown = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chevron-down\", __iconNode);\n //# sourceMappingURL=chevron-down.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi1kb3duLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUdhLG1CQUF1QjtJQUFDO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxjQUFnQjtZQUFBLElBQUssU0FBUztRQUFBLENBQUM7S0FBQztDQUFBO0FBYTdFLGtCQUFjLGtFQUFpQixpQkFBZ0IsQ0FBVSIsInNvdXJjZXMiOlsiL1VzZXJzL3JvYmVydC5oYW5zZW4vc3JjL2ljb25zL2NoZXZyb24tZG93bi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbWydwYXRoJywgeyBkOiAnbTYgOSA2IDYgNi02Jywga2V5OiAncXJ1bnNsJyB9XV07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDaGV2cm9uRG93blxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0p0TmlBNUlEWWdOaUEyTFRZaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2NoZXZyb24tZG93blxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENoZXZyb25Eb3duID0gY3JlYXRlTHVjaWRlSWNvbignY2hldnJvbi1kb3duJywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IENoZXZyb25Eb3duO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-up.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChevronUp)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.522.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m18 15-6-6-6 6\",\n            key: \"153udz\"\n        }\n    ]\n];\nconst ChevronUp = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chevron-up\", __iconNode);\n //# sourceMappingURL=chevron-up.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi11cC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHYSxtQkFBdUI7SUFBQztRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsZ0JBQWtCO1lBQUEsSUFBSyxTQUFTO1FBQUEsQ0FBQztLQUFDO0NBQUE7QUFhL0UsZ0JBQVksa0VBQWlCLGVBQWMsQ0FBVSIsInNvdXJjZXMiOlsiL1VzZXJzL3JvYmVydC5oYW5zZW4vc3JjL2ljb25zL2NoZXZyb24tdXAudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5pbXBvcnQgeyBJY29uTm9kZSB9IGZyb20gJy4uL3R5cGVzJztcblxuZXhwb3J0IGNvbnN0IF9faWNvbk5vZGU6IEljb25Ob2RlID0gW1sncGF0aCcsIHsgZDogJ20xOCAxNS02LTYtNiA2Jywga2V5OiAnMTUzdWR6JyB9XV07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDaGV2cm9uVXBcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKdE1UZ2dNVFV0TmkwMkxUWWdOaUlnTHo0S1BDOXpkbWMrQ2c9PSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvY2hldnJvbi11cFxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENoZXZyb25VcCA9IGNyZWF0ZUx1Y2lkZUljb24oJ2NoZXZyb24tdXAnLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2hldnJvblVwO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/funnel.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Funnel)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.522.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z\",\n            key: \"sc7q7i\"\n        }\n    ]\n];\nconst Funnel = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"funnel\", __iconNode);\n //# sourceMappingURL=funnel.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/search.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Search)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.522.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m21 21-4.34-4.34\",\n            key: \"14j7rj\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"11\",\n            cy: \"11\",\n            r: \"8\",\n            key: \"4ej97u\"\n        }\n    ]\n];\nconst Search = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"search\", __iconNode);\n //# sourceMappingURL=search.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\n"));

/***/ })

});