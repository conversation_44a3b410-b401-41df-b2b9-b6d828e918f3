"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/dashboard/SessionManager.tsx":
/*!*************************************************!*\
  !*** ./components/dashboard/SessionManager.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ SessionManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SessionManager() {\n    _s();\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    // Get current user with profile\n    const userWithProfile = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.users.getCurrentUserWithProfile);\n    const currentUser = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.users.getCurrentUser);\n    const djProfile = userWithProfile === null || userWithProfile === void 0 ? void 0 : userWithProfile.djProfile;\n    // Get real user sessions\n    const userSessions = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.sessions.getUserSessions, currentUser ? {\n        userId: currentUser._id\n    } : \"skip\");\n    // Get session analytics\n    const sessionStats = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.analytics.getUserSessionStats, currentUser ? {\n        userId: currentUser._id\n    } : \"skip\");\n    // Mutations\n    const createSession = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.sessions.createSession);\n    const handleCreateSession = async ()=>{\n        setIsCreating(true);\n        try {\n            const sessionId = await createSession({\n                name: \"Session \".concat(new Date().toLocaleDateString()),\n                acceptRequests: true,\n                autoApproval: false,\n                maxRequestsPerUser: 5,\n                timeframeMinutes: 60,\n                weddingModeEnabled: false\n            });\n            console.log(\"Session created:\", sessionId);\n        } catch (error) {\n            console.error(\"Failed to create session:\", error);\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    const handleManageActive = ()=>{\n        // Navigate to sessions tab\n        const event = new CustomEvent('switchTab', {\n            detail: 'session-management'\n        });\n        window.dispatchEvent(event);\n    };\n    const handleViewAnalytics = ()=>{\n        // Navigate to analytics tab\n        const event = new CustomEvent('switchTab', {\n            detail: 'analytics'\n        });\n        window.dispatchEvent(event);\n    };\n    if (!djProfile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"bg-gray-800/50 border-purple-500/20\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"text-white flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                \"Session Manager\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                            className: \"text-gray-400\",\n                            children: \"Create a DJ profile first to manage sessions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-4\",\n                                children: \"DJ profile required\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-sm\",\n                                children: \"Create your DJ profile to start managing sessions and receiving song requests.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this);\n    }\n    // Process real session data\n    const activeSessions = (userSessions === null || userSessions === void 0 ? void 0 : userSessions.filter((s)=>s.active)) || [];\n    const recentSessions = (userSessions === null || userSessions === void 0 ? void 0 : userSessions.slice(0, 3)) || [];\n    const totalRequests = (sessionStats === null || sessionStats === void 0 ? void 0 : sessionStats.totalRequests) || 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"bg-gray-800/50 border-purple-500/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: \"Create Session\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-6 h-6 text-purple-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4 text-sm\",\n                                    children: \"Start a new DJ session to receive song requests\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleCreateSession,\n                                    disabled: isCreating,\n                                    className: \"w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700\",\n                                    children: isCreating ? \"Creating...\" : \"New Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"bg-gray-800/50 border-purple-500/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: \"Active Sessions\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full \".concat(activeSessions.length > 0 ? 'bg-green-400 animate-pulse' : 'bg-gray-400')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4 text-sm\",\n                                    children: [\n                                        activeSessions.length,\n                                        \" active session\",\n                                        activeSessions.length !== 1 ? 's' : ''\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    className: \"w-full border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    disabled: activeSessions.length === 0,\n                                    children: activeSessions.length > 0 ? \"Manage Active\" : \"No Active Sessions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"bg-gray-800/50 border-purple-500/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: \"Total Requests\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-6 h-6 text-purple-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4 text-sm\",\n                                    children: [\n                                        mockSessions.reduce((sum, s)=>sum + s.requestCount, 0),\n                                        \" total requests received\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    className: \"w-full border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"View Analytics\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"bg-gray-800/50 border-purple-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"text-white flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Recent Sessions\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                className: \"text-gray-400\",\n                                children: \"Your latest DJ sessions and their status\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: recentSessions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: recentSessions.map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 bg-gray-700/50 rounded-lg border border-gray-600/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 rounded-full flex items-center justify-center \".concat(session.status === 'active' ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'),\n                                                    children: session.status === 'active' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 54\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 85\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-white\",\n                                                            children: session.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                                            lineNumber: 187,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        new Date(session.createdAt).toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                                    lineNumber: 186,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                                            lineNumber: 191,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        session.requestCount,\n                                                                        \"/\",\n                                                                        session.maxRequests,\n                                                                        \" requests\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: session.status === 'active' ? 'default' : 'secondary',\n                                                    children: session.status === 'active' ? 'Live' : 'Ended'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    variant: \"outline\",\n                                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                    children: session.status === 'active' ? 'Manage' : 'View'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, session.id, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4\",\n                                    children: \"No sessions yet\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-sm mb-6\",\n                                    children: \"Create your first session to start receiving song requests from your audience.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleCreateSession,\n                                    disabled: isCreating,\n                                    className: \"bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700\",\n                                    children: isCreating ? \"Creating...\" : \"Create Your First Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionManager, \"pdjhA4QfLYDxJXRghdBisnZ3Gy8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useMutation\n    ];\n});\n_c = SessionManager;\nvar _c;\n$RefreshReg$(_c, \"SessionManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/dashboard/SessionManager.tsx\n"));

/***/ })

});