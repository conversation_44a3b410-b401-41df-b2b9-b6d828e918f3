"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/ui/switch.tsx":
/*!**********************************!*\
  !*** ./components/ui/switch.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Switch: () => (/* binding */ Switch)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Switch auto */ \n\n\nconst Switch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, checked = false, onCheckedChange, disabled = false, id, ...props } = param;\n    const handleClick = ()=>{\n        if (!disabled && onCheckedChange) {\n            onCheckedChange(!checked);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        type: \"button\",\n        role: \"switch\",\n        \"aria-checked\": checked,\n        id: id,\n        ref: ref,\n        disabled: disabled,\n        onClick: handleClick,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-purple-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", checked ? \"bg-purple-600\" : \"bg-gray-600\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"pointer-events-none block h-5 w-5 rounded-full bg-white shadow-lg ring-0 transition-transform\", checked ? \"translate-x-5\" : \"translate-x-0\")\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/switch.tsx\",\n            lineNumber: 40,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/ui/switch.tsx\",\n        lineNumber: 23,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Switch;\nSwitch.displayName = \"Switch\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Switch$React.forwardRef\");\n$RefreshReg$(_c1, \"Switch\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/switch.tsx\n"));

/***/ })

});