"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/dashboard/DJProfileCard.tsx":
/*!************************************************!*\
  !*** ./components/dashboard/DJProfileCard.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DJProfileCard: () => (/* binding */ DJProfileCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_file_upload__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/file-upload */ \"(app-pages-browser)/./components/ui/file-upload.tsx\");\n/* harmony import */ var _barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Music,Settings,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Music,Settings,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Music,Settings,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Music,Settings,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,Music,Settings,Upload,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ DJProfileCard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction DJProfileCard() {\n    _s();\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [isCompletingSetup, setIsCompletingSetup] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [showProfilePictureUpload, setShowProfilePictureUpload] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    // Get current user with profile\n    const userWithProfile = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.users.getCurrentUserWithProfile);\n    const userStatus = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.users.checkUserStatus);\n    // Get user's profile pictures\n    const profilePictures = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.fileStorage.getUserFiles, {\n        purpose: \"profile_picture\",\n        limit: 1\n    });\n    // Mutations\n    const initializeSetup = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.users.initializeUserSetup);\n    const updateProfilePicture = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.fileStorage.updateProfilePicture);\n    const completeOnboarding = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.users.completeOnboarding);\n    const handleCreateDJProfile = async ()=>{\n        setIsCreating(true);\n        try {\n            var _userWithProfile_user;\n            await initializeSetup({\n                createDjProfile: true,\n                displayName: (userWithProfile === null || userWithProfile === void 0 ? void 0 : (_userWithProfile_user = userWithProfile.user) === null || _userWithProfile_user === void 0 ? void 0 : _userWithProfile_user.name) || \"New DJ\"\n            });\n        } catch (error) {\n            console.error(\"Failed to create DJ profile:\", error);\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    const handleProfilePictureUpload = async (fileId, url)=>{\n        try {\n            await updateProfilePicture({\n                storageId: fileId\n            });\n            setShowProfilePictureUpload(false);\n        } catch (error) {\n            console.error(\"Failed to update profile picture:\", error);\n        }\n    };\n    const handleEditProfile = ()=>{\n        // Navigate to profile settings or open edit modal\n        console.log(\"Edit profile clicked\");\n        // For now, we'll just show an alert - in a real app this would open a modal or navigate\n        alert(\"Profile editing functionality would be implemented here. This could open a modal with editable fields for display name, bio, etc.\");\n    };\n    const handleCompleteSetup = async ()=>{\n        setIsCompletingSetup(true);\n        try {\n            const result = await completeOnboarding();\n            console.log(\"Onboarding completed:\", result);\n            if (result.alreadyCompleted) {\n                alert(\"Setup is already complete!\");\n            } else {\n                alert(\"Setup completed successfully! Welcome to PlayBeg!\");\n            }\n        } catch (error) {\n            console.error(\"Failed to complete onboarding:\", error);\n            alert(\"Failed to complete setup. Please try again.\");\n        } finally{\n            setIsCompletingSetup(false);\n        }\n    };\n    const currentProfilePicture = profilePictures === null || profilePictures === void 0 ? void 0 : profilePictures[0];\n    if (!userWithProfile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"bg-gray-800/50 border-purple-500/20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-700 rounded w-3/4 mb-2\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-700 rounded w-1/2\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this);\n    }\n    const { user, djProfile, hasDjProfile, onboardingComplete } = userWithProfile;\n    if (!hasDjProfile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"bg-gray-800/50 border-purple-500/20\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"text-white flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                \"Create DJ Profile\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                            className: \"text-gray-400\",\n                            children: \"Set up your DJ profile to start creating sessions and receiving requests\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 bg-purple-500/20 border border-purple-500/30 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-white font-medium mb-2\",\n                                    children: [\n                                        \"Welcome, \",\n                                        (user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.email),\n                                        \"!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-sm mb-4\",\n                                    children: \"Create your DJ profile to unlock all PlayBeg features including session management, song request handling, and real-time audience interaction.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleCreateDJProfile,\n                                    disabled: isCreating,\n                                    className: \"w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700\",\n                                    children: isCreating ? \"Creating...\" : \"Create DJ Profile\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"bg-gray-800/50 border-purple-500/20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                        className: \"text-white flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"DJ Profile\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                variant: onboardingComplete ? \"default\" : \"secondary\",\n                                children: onboardingComplete ? \"Complete\" : \"Setup Needed\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                        className: \"text-gray-400\",\n                        children: \"Your DJ profile and account status\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 bg-gray-600 rounded-full flex items-center justify-center overflow-hidden\",\n                                            children: (currentProfilePicture === null || currentProfilePicture === void 0 ? void 0 : currentProfilePicture.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: currentProfilePicture.url,\n                                                alt: \"Profile\",\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-8 h-8 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setShowProfilePictureUpload(!showProfilePictureUpload),\n                                            className: \"absolute -bottom-2 -right-2 w-8 h-8 rounded-full p-0 border-gray-600 bg-gray-800 hover:bg-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-white font-medium\",\n                                            children: (djProfile === null || djProfile === void 0 ? void 0 : djProfile.displayName) || \"Unnamed DJ\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: (user === null || user === void 0 ? void 0 : user.email) || \"No email\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this),\n                                        currentProfilePicture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: [\n                                                \"Picture uploaded \",\n                                                new Date(currentProfilePicture.uploadedAt).toLocaleDateString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this),\n                        showProfilePictureUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border border-gray-600 rounded-lg p-4 bg-gray-700/30\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                    className: \"text-white font-medium mb-3 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Update Profile Picture\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_file_upload__WEBPACK_IMPORTED_MODULE_6__.FileUpload, {\n                                    purpose: \"profile_picture\",\n                                    onUploadComplete: handleProfilePictureUpload,\n                                    onUploadError: (error)=>console.error(\"Profile picture upload error:\", error),\n                                    maxSize: 5 * 1024 * 1024,\n                                    className: \"mb-3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setShowProfilePictureUpload(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Display Name\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: (djProfile === null || djProfile === void 0 ? void 0 : djProfile.displayName) || \"Not set\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white font-medium\",\n                                            children: (user === null || user === void 0 ? void 0 : user.email) || \"Not available\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full mx-auto mb-2 \".concat((userStatus === null || userStatus === void 0 ? void 0 : userStatus.isAuthenticated) ? 'bg-green-400' : 'bg-red-400')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Auth Status\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm font-medium\",\n                                            children: (userStatus === null || userStatus === void 0 ? void 0 : userStatus.isAuthenticated) ? 'Active' : 'Inactive'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full mx-auto mb-2 \".concat((userStatus === null || userStatus === void 0 ? void 0 : userStatus.hasDjProfile) ? 'bg-green-400' : 'bg-gray-400')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"DJ Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm font-medium\",\n                                            children: (userStatus === null || userStatus === void 0 ? void 0 : userStatus.hasDjProfile) ? 'Created' : 'Missing'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full mx-auto mb-2 \".concat(onboardingComplete ? 'bg-green-400' : 'bg-orange-400')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Onboarding\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm font-medium\",\n                                            children: onboardingComplete ? 'Done' : 'Pending'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full mx-auto mb-2 bg-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Member Since\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-sm font-medium\",\n                                            children: djProfile ? new Date(djProfile.createdAt).toLocaleDateString() : 'N/A'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    onClick: handleEditProfile,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_Music_Settings_Upload_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Edit Profile\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this),\n                                !onboardingComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"sm\",\n                                    className: \"bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700\",\n                                    onClick: handleCompleteSetup,\n                                    disabled: isCompletingSetup,\n                                    children: isCompletingSetup ? \"Completing...\" : \"Complete Setup\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/DJProfileCard.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n_s(DJProfileCard, \"KTClivDFSK8zmCWkjHW+lB5dTSc=\", false, function() {\n    return [\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useMutation\n    ];\n});\n_c = DJProfileCard;\nvar _c;\n$RefreshReg$(_c, \"DJProfileCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvZGFzaGJvYXJkL0RKUHJvZmlsZUNhcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRXFEO0FBQ1A7QUFDRTtBQUNpRDtBQUNuRDtBQUNXO0FBQ3NCO0FBQzlDO0FBRTFCLFNBQVNpQjs7SUFDZCxNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR0gsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDSSxtQkFBbUJDLHFCQUFxQixHQUFHTCwrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUNNLDBCQUEwQkMsNEJBQTRCLEdBQUdQLCtDQUFRQSxDQUFDO0lBRXpFLGdDQUFnQztJQUNoQyxNQUFNUSxrQkFBa0J4QixzREFBUUEsQ0FBQ0Usc0RBQUdBLENBQUN1QixLQUFLLENBQUNDLHlCQUF5QjtJQUNwRSxNQUFNQyxhQUFhM0Isc0RBQVFBLENBQUNFLHNEQUFHQSxDQUFDdUIsS0FBSyxDQUFDRyxlQUFlO0lBRXJELDhCQUE4QjtJQUM5QixNQUFNQyxrQkFBa0I3QixzREFBUUEsQ0FBQ0Usc0RBQUdBLENBQUM0QixXQUFXLENBQUNDLFlBQVksRUFBRTtRQUM3REMsU0FBUztRQUNUQyxPQUFPO0lBQ1Q7SUFFQSxZQUFZO0lBQ1osTUFBTUMsa0JBQWtCakMseURBQVdBLENBQUNDLHNEQUFHQSxDQUFDdUIsS0FBSyxDQUFDVSxtQkFBbUI7SUFDakUsTUFBTUMsdUJBQXVCbkMseURBQVdBLENBQUNDLHNEQUFHQSxDQUFDNEIsV0FBVyxDQUFDTSxvQkFBb0I7SUFDN0UsTUFBTUMscUJBQXFCcEMseURBQVdBLENBQUNDLHNEQUFHQSxDQUFDdUIsS0FBSyxDQUFDWSxrQkFBa0I7SUFFbkUsTUFBTUMsd0JBQXdCO1FBQzVCbkIsY0FBYztRQUNkLElBQUk7Z0JBR2FLO1lBRmYsTUFBTVUsZ0JBQWdCO2dCQUNwQkssaUJBQWlCO2dCQUNqQkMsYUFBYWhCLENBQUFBLDRCQUFBQSx1Q0FBQUEsd0JBQUFBLGdCQUFpQmlCLElBQUksY0FBckJqQiw0Q0FBQUEsc0JBQXVCa0IsSUFBSSxLQUFJO1lBQzlDO1FBQ0YsRUFBRSxPQUFPQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxnQ0FBZ0NBO1FBQ2hELFNBQVU7WUFDUnhCLGNBQWM7UUFDaEI7SUFDRjtJQUVBLE1BQU0wQiw2QkFBNkIsT0FBT0MsUUFBZ0JDO1FBQ3hELElBQUk7WUFDRixNQUFNWCxxQkFBcUI7Z0JBQUVZLFdBQVdGO1lBQWM7WUFDdER2Qiw0QkFBNEI7UUFDOUIsRUFBRSxPQUFPb0IsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMscUNBQXFDQTtRQUNyRDtJQUNGO0lBRUEsTUFBTU0sb0JBQW9CO1FBQ3hCLGtEQUFrRDtRQUNsREwsUUFBUU0sR0FBRyxDQUFDO1FBQ1osd0ZBQXdGO1FBQ3hGQyxNQUFNO0lBQ1I7SUFFQSxNQUFNQyxzQkFBc0I7UUFDMUIvQixxQkFBcUI7UUFDckIsSUFBSTtZQUNGLE1BQU1nQyxTQUFTLE1BQU1oQjtZQUNyQk8sUUFBUU0sR0FBRyxDQUFDLHlCQUF5Qkc7WUFDckMsSUFBSUEsT0FBT0MsZ0JBQWdCLEVBQUU7Z0JBQzNCSCxNQUFNO1lBQ1IsT0FBTztnQkFDTEEsTUFBTTtZQUNSO1FBQ0YsRUFBRSxPQUFPUixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxrQ0FBa0NBO1lBQ2hEUSxNQUFNO1FBQ1IsU0FBVTtZQUNSOUIscUJBQXFCO1FBQ3ZCO0lBQ0Y7SUFFQSxNQUFNa0Msd0JBQXdCMUIsNEJBQUFBLHNDQUFBQSxlQUFpQixDQUFDLEVBQUU7SUFFbEQsSUFBSSxDQUFDTCxpQkFBaUI7UUFDcEIscUJBQ0UsOERBQUNwQixxREFBSUE7WUFBQ29ELFdBQVU7c0JBQ2QsNEVBQUNuRCw0REFBV0E7Z0JBQUNtRCxXQUFVOzBCQUNyQiw0RUFBQ0M7b0JBQUlELFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBSUQsV0FBVTs7Ozs7O3NDQUNmLDhEQUFDQzs0QkFBSUQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUt6QjtJQUVBLE1BQU0sRUFBRWYsSUFBSSxFQUFFaUIsU0FBUyxFQUFFQyxZQUFZLEVBQUVDLGtCQUFrQixFQUFFLEdBQUdwQztJQUU5RCxJQUFJLENBQUNtQyxjQUFjO1FBQ2pCLHFCQUNFLDhEQUFDdkQscURBQUlBO1lBQUNvRCxXQUFVOzs4QkFDZCw4REFBQ2pELDJEQUFVQTs7c0NBQ1QsOERBQUNDLDBEQUFTQTs0QkFBQ2dELFdBQVU7OzhDQUNuQiw4REFBQzdDLDZHQUFJQTtvQ0FBQzZDLFdBQVU7Ozs7OztnQ0FBaUI7Ozs7Ozs7c0NBR25DLDhEQUFDbEQsZ0VBQWVBOzRCQUFDa0QsV0FBVTtzQ0FBZ0I7Ozs7Ozs7Ozs7Ozs4QkFJN0MsOERBQUNuRCw0REFBV0E7OEJBQ1YsNEVBQUNvRDt3QkFBSUQsV0FBVTtrQ0FDYiw0RUFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDSztvQ0FBR0wsV0FBVTs7d0NBQThCO3dDQUFVZixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1DLElBQUksTUFBSUQsaUJBQUFBLDJCQUFBQSxLQUFNcUIsS0FBSzt3Q0FBQzs7Ozs7Ozs4Q0FDaEYsOERBQUNDO29DQUFFUCxXQUFVOzhDQUE2Qjs7Ozs7OzhDQUkxQyw4REFBQ3JELHlEQUFNQTtvQ0FDTDZELFNBQVMxQjtvQ0FDVDJCLFVBQVUvQztvQ0FDVnNDLFdBQVU7OENBRVR0QyxhQUFhLGdCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU81QztJQUVBLHFCQUNFLDhEQUFDZCxxREFBSUE7UUFBQ29ELFdBQVU7OzBCQUNkLDhEQUFDakQsMkRBQVVBOztrQ0FDVCw4REFBQ0MsMERBQVNBO3dCQUFDZ0QsV0FBVTs7MENBQ25CLDhEQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUM1Qyw2R0FBS0E7d0NBQUM0QyxXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7OzBDQUdwQyw4REFBQy9DLHVEQUFLQTtnQ0FBQ3lELFNBQVNOLHFCQUFxQixZQUFZOzBDQUM5Q0EscUJBQXFCLGFBQWE7Ozs7Ozs7Ozs7OztrQ0FHdkMsOERBQUN0RCxnRUFBZUE7d0JBQUNrRCxXQUFVO2tDQUFnQjs7Ozs7Ozs7Ozs7OzBCQUk3Qyw4REFBQ25ELDREQUFXQTswQkFDViw0RUFBQ29EO29CQUFJRCxXQUFVOztzQ0FFYiw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNDOzRDQUFJRCxXQUFVO3NEQUNaRCxDQUFBQSxrQ0FBQUEsNENBQUFBLHNCQUF1QlIsR0FBRyxrQkFDekIsOERBQUNvQjtnREFDQ0MsS0FBS2Isc0JBQXNCUixHQUFHO2dEQUM5QnNCLEtBQUk7Z0RBQ0piLFdBQVU7Ozs7O3FFQUdaLDhEQUFDN0MsNkdBQUlBO2dEQUFDNkMsV0FBVTs7Ozs7Ozs7Ozs7c0RBR3BCLDhEQUFDckQseURBQU1BOzRDQUNMK0QsU0FBUTs0Q0FDUkksTUFBSzs0Q0FDTE4sU0FBUyxJQUFNekMsNEJBQTRCLENBQUNEOzRDQUM1Q2tDLFdBQVU7c0RBRVYsNEVBQUMxQyw4R0FBTUE7Z0RBQUMwQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FHdEIsOERBQUNDO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ0s7NENBQUdMLFdBQVU7c0RBQTBCRSxDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVdsQixXQUFXLEtBQUk7Ozs7OztzREFDbEUsOERBQUN1Qjs0Q0FBRVAsV0FBVTtzREFBeUJmLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTXFCLEtBQUssS0FBSTs7Ozs7O3dDQUNwRFAsdUNBQ0MsOERBQUNROzRDQUFFUCxXQUFVOztnREFBNkI7Z0RBQ3RCLElBQUllLEtBQUtoQixzQkFBc0JpQixVQUFVLEVBQUVDLGtCQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3QkFPdEZuRCwwQ0FDQyw4REFBQ21DOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ2tCO29DQUFHbEIsV0FBVTs7c0RBQ1osOERBQUN6Qyw4R0FBTUE7NENBQUN5QyxXQUFVOzs7Ozs7d0NBQWlCOzs7Ozs7OzhDQUdyQyw4REFBQzlDLGtFQUFVQTtvQ0FDVHNCLFNBQVE7b0NBQ1IyQyxrQkFBa0I5QjtvQ0FDbEIrQixlQUFlLENBQUNqQyxRQUFVQyxRQUFRRCxLQUFLLENBQUMsaUNBQWlDQTtvQ0FDekVrQyxTQUFTLElBQUksT0FBTztvQ0FDcEJyQixXQUFVOzs7Ozs7OENBRVosOERBQUNyRCx5REFBTUE7b0NBQ0wrRCxTQUFRO29DQUNSSSxNQUFLO29DQUNMTixTQUFTLElBQU16Qyw0QkFBNEI7b0NBQzNDaUMsV0FBVTs4Q0FDWDs7Ozs7Ozs7Ozs7O3NDQU9MLDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNDOztzREFDQyw4REFBQ3FCOzRDQUFNdEIsV0FBVTtzREFBd0I7Ozs7OztzREFDekMsOERBQUNPOzRDQUFFUCxXQUFVO3NEQUEwQkUsQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXbEIsV0FBVyxLQUFJOzs7Ozs7Ozs7Ozs7OENBRW5FLDhEQUFDaUI7O3NEQUNDLDhEQUFDcUI7NENBQU10QixXQUFVO3NEQUF3Qjs7Ozs7O3NEQUN6Qyw4REFBQ087NENBQUVQLFdBQVU7c0RBQTBCZixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1xQixLQUFLLEtBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLMUQsOERBQUNMOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVyxxQ0FBaUcsT0FBNUQ3QixDQUFBQSx1QkFBQUEsaUNBQUFBLFdBQVlvRCxlQUFlLElBQUcsaUJBQWlCOzs7Ozs7c0RBQ3BHLDhEQUFDaEI7NENBQUVQLFdBQVU7c0RBQXdCOzs7Ozs7c0RBQ3JDLDhEQUFDTzs0Q0FBRVAsV0FBVTtzREFBa0M3QixDQUFBQSx1QkFBQUEsaUNBQUFBLFdBQVlvRCxlQUFlLElBQUcsV0FBVzs7Ozs7Ozs7Ozs7OzhDQUUxRiw4REFBQ3RCO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ0M7NENBQUlELFdBQVcscUNBQStGLE9BQTFEN0IsQ0FBQUEsdUJBQUFBLGlDQUFBQSxXQUFZZ0MsWUFBWSxJQUFHLGlCQUFpQjs7Ozs7O3NEQUNqRyw4REFBQ0k7NENBQUVQLFdBQVU7c0RBQXdCOzs7Ozs7c0RBQ3JDLDhEQUFDTzs0Q0FBRVAsV0FBVTtzREFBa0M3QixDQUFBQSx1QkFBQUEsaUNBQUFBLFdBQVlnQyxZQUFZLElBQUcsWUFBWTs7Ozs7Ozs7Ozs7OzhDQUV4Riw4REFBQ0Y7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVyxxQ0FBMkYsT0FBdERJLHFCQUFxQixpQkFBaUI7Ozs7OztzREFDM0YsOERBQUNHOzRDQUFFUCxXQUFVO3NEQUF3Qjs7Ozs7O3NEQUNyQyw4REFBQ087NENBQUVQLFdBQVU7c0RBQWtDSSxxQkFBcUIsU0FBUzs7Ozs7Ozs7Ozs7OzhDQUUvRSw4REFBQ0g7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTs7Ozs7O3NEQUNmLDhEQUFDTzs0Q0FBRVAsV0FBVTtzREFBd0I7Ozs7OztzREFDckMsOERBQUNPOzRDQUFFUCxXQUFVO3NEQUNWRSxZQUFZLElBQUlhLEtBQUtiLFVBQVVzQixTQUFTLEVBQUVQLGtCQUFrQixLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTXhFLDhEQUFDaEI7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDckQseURBQU1BO29DQUNMK0QsU0FBUTtvQ0FDUkksTUFBSztvQ0FDTGQsV0FBVTtvQ0FDVlEsU0FBU2Y7O3NEQUVULDhEQUFDcEMsOEdBQVFBOzRDQUFDMkMsV0FBVTs7Ozs7O3dDQUFpQjs7Ozs7OztnQ0FHdEMsQ0FBQ0ksb0NBQ0EsOERBQUN6RCx5REFBTUE7b0NBQ0xtRSxNQUFLO29DQUNMZCxXQUFVO29DQUNWUSxTQUFTWjtvQ0FDVGEsVUFBVTdDOzhDQUVUQSxvQkFBb0Isa0JBQWtCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVF2RDtHQXBRZ0JIOztRQU1VakIsa0RBQVFBO1FBQ2JBLGtEQUFRQTtRQUdIQSxrREFBUUE7UUFNUkMscURBQVdBO1FBQ05BLHFEQUFXQTtRQUNiQSxxREFBV0E7OztLQWxCeEJnQiIsInNvdXJjZXMiOlsiL1VzZXJzL3JvYmVydC5oYW5zZW4vUGxheUJlZy9QbGF5QmVnL3BsYXliZWctY29udmV4L2NvbXBvbmVudHMvZGFzaGJvYXJkL0RKUHJvZmlsZUNhcmQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyB1c2VRdWVyeSwgdXNlTXV0YXRpb24gfSBmcm9tIFwiY29udmV4L3JlYWN0XCI7XG5pbXBvcnQgeyBhcGkgfSBmcm9tIFwiQC9jb252ZXgvX2dlbmVyYXRlZC9hcGlcIjtcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCI7XG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2NhcmRcIjtcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9iYWRnZVwiO1xuaW1wb3J0IHsgRmlsZVVwbG9hZCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvZmlsZS11cGxvYWRcIjtcbmltcG9ydCB7IFVzZXIsIE11c2ljLCBDYWxlbmRhciwgU2V0dGluZ3MsIENhbWVyYSwgVXBsb2FkIH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcblxuZXhwb3J0IGZ1bmN0aW9uIERKUHJvZmlsZUNhcmQoKSB7XG4gIGNvbnN0IFtpc0NyZWF0aW5nLCBzZXRJc0NyZWF0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzQ29tcGxldGluZ1NldHVwLCBzZXRJc0NvbXBsZXRpbmdTZXR1cF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93UHJvZmlsZVBpY3R1cmVVcGxvYWQsIHNldFNob3dQcm9maWxlUGljdHVyZVVwbG9hZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gR2V0IGN1cnJlbnQgdXNlciB3aXRoIHByb2ZpbGVcbiAgY29uc3QgdXNlcldpdGhQcm9maWxlID0gdXNlUXVlcnkoYXBpLnVzZXJzLmdldEN1cnJlbnRVc2VyV2l0aFByb2ZpbGUpO1xuICBjb25zdCB1c2VyU3RhdHVzID0gdXNlUXVlcnkoYXBpLnVzZXJzLmNoZWNrVXNlclN0YXR1cyk7XG5cbiAgLy8gR2V0IHVzZXIncyBwcm9maWxlIHBpY3R1cmVzXG4gIGNvbnN0IHByb2ZpbGVQaWN0dXJlcyA9IHVzZVF1ZXJ5KGFwaS5maWxlU3RvcmFnZS5nZXRVc2VyRmlsZXMsIHtcbiAgICBwdXJwb3NlOiBcInByb2ZpbGVfcGljdHVyZVwiLFxuICAgIGxpbWl0OiAxLFxuICB9KTtcblxuICAvLyBNdXRhdGlvbnNcbiAgY29uc3QgaW5pdGlhbGl6ZVNldHVwID0gdXNlTXV0YXRpb24oYXBpLnVzZXJzLmluaXRpYWxpemVVc2VyU2V0dXApO1xuICBjb25zdCB1cGRhdGVQcm9maWxlUGljdHVyZSA9IHVzZU11dGF0aW9uKGFwaS5maWxlU3RvcmFnZS51cGRhdGVQcm9maWxlUGljdHVyZSk7XG4gIGNvbnN0IGNvbXBsZXRlT25ib2FyZGluZyA9IHVzZU11dGF0aW9uKGFwaS51c2Vycy5jb21wbGV0ZU9uYm9hcmRpbmcpO1xuICBcbiAgY29uc3QgaGFuZGxlQ3JlYXRlREpQcm9maWxlID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldElzQ3JlYXRpbmcodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IGluaXRpYWxpemVTZXR1cCh7XG4gICAgICAgIGNyZWF0ZURqUHJvZmlsZTogdHJ1ZSxcbiAgICAgICAgZGlzcGxheU5hbWU6IHVzZXJXaXRoUHJvZmlsZT8udXNlcj8ubmFtZSB8fCBcIk5ldyBESlwiLFxuICAgICAgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJGYWlsZWQgdG8gY3JlYXRlIERKIHByb2ZpbGU6XCIsIGVycm9yKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNDcmVhdGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVByb2ZpbGVQaWN0dXJlVXBsb2FkID0gYXN5bmMgKGZpbGVJZDogc3RyaW5nLCB1cmw6IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBhd2FpdCB1cGRhdGVQcm9maWxlUGljdHVyZSh7IHN0b3JhZ2VJZDogZmlsZUlkIGFzIGFueSB9KTtcbiAgICAgIHNldFNob3dQcm9maWxlUGljdHVyZVVwbG9hZChmYWxzZSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJGYWlsZWQgdG8gdXBkYXRlIHByb2ZpbGUgcGljdHVyZTpcIiwgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVFZGl0UHJvZmlsZSA9ICgpID0+IHtcbiAgICAvLyBOYXZpZ2F0ZSB0byBwcm9maWxlIHNldHRpbmdzIG9yIG9wZW4gZWRpdCBtb2RhbFxuICAgIGNvbnNvbGUubG9nKFwiRWRpdCBwcm9maWxlIGNsaWNrZWRcIik7XG4gICAgLy8gRm9yIG5vdywgd2UnbGwganVzdCBzaG93IGFuIGFsZXJ0IC0gaW4gYSByZWFsIGFwcCB0aGlzIHdvdWxkIG9wZW4gYSBtb2RhbCBvciBuYXZpZ2F0ZVxuICAgIGFsZXJ0KFwiUHJvZmlsZSBlZGl0aW5nIGZ1bmN0aW9uYWxpdHkgd291bGQgYmUgaW1wbGVtZW50ZWQgaGVyZS4gVGhpcyBjb3VsZCBvcGVuIGEgbW9kYWwgd2l0aCBlZGl0YWJsZSBmaWVsZHMgZm9yIGRpc3BsYXkgbmFtZSwgYmlvLCBldGMuXCIpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUNvbXBsZXRlU2V0dXAgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0SXNDb21wbGV0aW5nU2V0dXAodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGNvbXBsZXRlT25ib2FyZGluZygpO1xuICAgICAgY29uc29sZS5sb2coXCJPbmJvYXJkaW5nIGNvbXBsZXRlZDpcIiwgcmVzdWx0KTtcbiAgICAgIGlmIChyZXN1bHQuYWxyZWFkeUNvbXBsZXRlZCkge1xuICAgICAgICBhbGVydChcIlNldHVwIGlzIGFscmVhZHkgY29tcGxldGUhXCIpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgYWxlcnQoXCJTZXR1cCBjb21wbGV0ZWQgc3VjY2Vzc2Z1bGx5ISBXZWxjb21lIHRvIFBsYXlCZWchXCIpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiRmFpbGVkIHRvIGNvbXBsZXRlIG9uYm9hcmRpbmc6XCIsIGVycm9yKTtcbiAgICAgIGFsZXJ0KFwiRmFpbGVkIHRvIGNvbXBsZXRlIHNldHVwLiBQbGVhc2UgdHJ5IGFnYWluLlwiKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNDb21wbGV0aW5nU2V0dXAoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBjdXJyZW50UHJvZmlsZVBpY3R1cmUgPSBwcm9maWxlUGljdHVyZXM/LlswXTtcblxuICBpZiAoIXVzZXJXaXRoUHJvZmlsZSkge1xuICAgIHJldHVybiAoXG4gICAgICA8Q2FyZCBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMC81MCBib3JkZXItcHVycGxlLTUwMC8yMFwiPlxuICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXB1bHNlXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNCBiZy1ncmF5LTcwMCByb3VuZGVkIHctMy80IG1iLTJcIj48L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLWdyYXktNzAwIHJvdW5kZWQgdy0xLzJcIj48L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgIDwvQ2FyZD5cbiAgICApO1xuICB9XG5cbiAgY29uc3QgeyB1c2VyLCBkalByb2ZpbGUsIGhhc0RqUHJvZmlsZSwgb25ib2FyZGluZ0NvbXBsZXRlIH0gPSB1c2VyV2l0aFByb2ZpbGU7XG5cbiAgaWYgKCFoYXNEalByb2ZpbGUpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPENhcmQgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAvNTAgYm9yZGVyLXB1cnBsZS01MDAvMjBcIj5cbiAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICA8VXNlciBjbGFzc05hbWU9XCJ3LTUgaC01IG1yLTJcIiAvPlxuICAgICAgICAgICAgQ3JlYXRlIERKIFByb2ZpbGVcbiAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgIFNldCB1cCB5b3VyIERKIHByb2ZpbGUgdG8gc3RhcnQgY3JlYXRpbmcgc2Vzc2lvbnMgYW5kIHJlY2VpdmluZyByZXF1ZXN0c1xuICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYmctcHVycGxlLTUwMC8yMCBib3JkZXIgYm9yZGVyLXB1cnBsZS01MDAvMzAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LW1lZGl1bSBtYi0yXCI+V2VsY29tZSwge3VzZXI/Lm5hbWUgfHwgdXNlcj8uZW1haWx9ITwvaDQ+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgdGV4dC1zbSBtYi00XCI+XG4gICAgICAgICAgICAgICAgQ3JlYXRlIHlvdXIgREogcHJvZmlsZSB0byB1bmxvY2sgYWxsIFBsYXlCZWcgZmVhdHVyZXMgaW5jbHVkaW5nIHNlc3Npb24gbWFuYWdlbWVudCwgXG4gICAgICAgICAgICAgICAgc29uZyByZXF1ZXN0IGhhbmRsaW5nLCBhbmQgcmVhbC10aW1lIGF1ZGllbmNlIGludGVyYWN0aW9uLlxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDxCdXR0b24gXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ3JlYXRlREpQcm9maWxlfVxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0NyZWF0aW5nfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmFkaWVudC10by1yIGZyb20tcHVycGxlLTYwMCB0by1jeWFuLTYwMCBob3Zlcjpmcm9tLXB1cnBsZS03MDAgaG92ZXI6dG8tY3lhbi03MDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2lzQ3JlYXRpbmcgPyBcIkNyZWF0aW5nLi4uXCIgOiBcIkNyZWF0ZSBESiBQcm9maWxlXCJ9XG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICA8L0NhcmQ+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPENhcmQgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAvNTAgYm9yZGVyLXB1cnBsZS01MDAvMjBcIj5cbiAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPE11c2ljIGNsYXNzTmFtZT1cInctNSBoLTUgbXItMlwiIC8+XG4gICAgICAgICAgICBESiBQcm9maWxlXG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9e29uYm9hcmRpbmdDb21wbGV0ZSA/IFwiZGVmYXVsdFwiIDogXCJzZWNvbmRhcnlcIn0+XG4gICAgICAgICAgICB7b25ib2FyZGluZ0NvbXBsZXRlID8gXCJDb21wbGV0ZVwiIDogXCJTZXR1cCBOZWVkZWRcIn1cbiAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgPENhcmREZXNjcmlwdGlvbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgWW91ciBESiBwcm9maWxlIGFuZCBhY2NvdW50IHN0YXR1c1xuICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICB7LyogUHJvZmlsZSBQaWN0dXJlIFNlY3Rpb24gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIwIGgtMjAgYmctZ3JheS02MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgICAgIHtjdXJyZW50UHJvZmlsZVBpY3R1cmU/LnVybCA/IChcbiAgICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgICAgc3JjPXtjdXJyZW50UHJvZmlsZVBpY3R1cmUudXJsfVxuICAgICAgICAgICAgICAgICAgICBhbHQ9XCJQcm9maWxlXCJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXJcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgPFVzZXIgY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dQcm9maWxlUGljdHVyZVVwbG9hZCghc2hvd1Byb2ZpbGVQaWN0dXJlVXBsb2FkKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtYm90dG9tLTIgLXJpZ2h0LTIgdy04IGgtOCByb3VuZGVkLWZ1bGwgcC0wIGJvcmRlci1ncmF5LTYwMCBiZy1ncmF5LTgwMCBob3ZlcjpiZy1ncmF5LTcwMFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8Q2FtZXJhIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tZWRpdW1cIj57ZGpQcm9maWxlPy5kaXNwbGF5TmFtZSB8fCBcIlVubmFtZWQgREpcIn08L2g0PlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc21cIj57dXNlcj8uZW1haWwgfHwgXCJObyBlbWFpbFwifTwvcD5cbiAgICAgICAgICAgICAge2N1cnJlbnRQcm9maWxlUGljdHVyZSAmJiAoXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgIFBpY3R1cmUgdXBsb2FkZWQge25ldyBEYXRlKGN1cnJlbnRQcm9maWxlUGljdHVyZS51cGxvYWRlZEF0KS50b0xvY2FsZURhdGVTdHJpbmcoKX1cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBQcm9maWxlIFBpY3R1cmUgVXBsb2FkICovfVxuICAgICAgICAgIHtzaG93UHJvZmlsZVBpY3R1cmVVcGxvYWQgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXIgYm9yZGVyLWdyYXktNjAwIHJvdW5kZWQtbGcgcC00IGJnLWdyYXktNzAwLzMwXCI+XG4gICAgICAgICAgICAgIDxoNSBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtIG1iLTMgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8VXBsb2FkIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgVXBkYXRlIFByb2ZpbGUgUGljdHVyZVxuICAgICAgICAgICAgICA8L2g1PlxuICAgICAgICAgICAgICA8RmlsZVVwbG9hZFxuICAgICAgICAgICAgICAgIHB1cnBvc2U9XCJwcm9maWxlX3BpY3R1cmVcIlxuICAgICAgICAgICAgICAgIG9uVXBsb2FkQ29tcGxldGU9e2hhbmRsZVByb2ZpbGVQaWN0dXJlVXBsb2FkfVxuICAgICAgICAgICAgICAgIG9uVXBsb2FkRXJyb3I9eyhlcnJvcikgPT4gY29uc29sZS5lcnJvcihcIlByb2ZpbGUgcGljdHVyZSB1cGxvYWQgZXJyb3I6XCIsIGVycm9yKX1cbiAgICAgICAgICAgICAgICBtYXhTaXplPXs1ICogMTAyNCAqIDEwMjR9IC8vIDVNQiBsaW1pdCBmb3IgcHJvZmlsZSBwaWN0dXJlc1xuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1iLTNcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1Byb2ZpbGVQaWN0dXJlVXBsb2FkKGZhbHNlKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXItZ3JheS02MDAgdGV4dC1ncmF5LTMwMCBob3ZlcjpiZy1ncmF5LTcwMFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBDYW5jZWxcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuXG4gICAgICAgICAgey8qIFByb2ZpbGUgSW5mbyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5EaXNwbGF5IE5hbWU8L2xhYmVsPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtXCI+e2RqUHJvZmlsZT8uZGlzcGxheU5hbWUgfHwgXCJOb3Qgc2V0XCJ9PC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+RW1haWw8L2xhYmVsPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtXCI+e3VzZXI/LmVtYWlsIHx8IFwiTm90IGF2YWlsYWJsZVwifTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFN0YXR1cyBJbmRpY2F0b3JzICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtNCBnYXAtNCBwdC00IGJvcmRlci10IGJvcmRlci1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctMyBoLTMgcm91bmRlZC1mdWxsIG14LWF1dG8gbWItMiAke3VzZXJTdGF0dXM/LmlzQXV0aGVudGljYXRlZCA/ICdiZy1ncmVlbi00MDAnIDogJ2JnLXJlZC00MDAnfWB9PjwvZGl2PlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5BdXRoIFN0YXR1czwvcD5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+e3VzZXJTdGF0dXM/LmlzQXV0aGVudGljYXRlZCA/ICdBY3RpdmUnIDogJ0luYWN0aXZlJ308L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTMgaC0zIHJvdW5kZWQtZnVsbCBteC1hdXRvIG1iLTIgJHt1c2VyU3RhdHVzPy5oYXNEalByb2ZpbGUgPyAnYmctZ3JlZW4tNDAwJyA6ICdiZy1ncmF5LTQwMCd9YH0+PC9kaXY+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPkRKIFByb2ZpbGU8L3A+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC1zbSBmb250LW1lZGl1bVwiPnt1c2VyU3RhdHVzPy5oYXNEalByb2ZpbGUgPyAnQ3JlYXRlZCcgOiAnTWlzc2luZyd9PC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy0zIGgtMyByb3VuZGVkLWZ1bGwgbXgtYXV0byBtYi0yICR7b25ib2FyZGluZ0NvbXBsZXRlID8gJ2JnLWdyZWVuLTQwMCcgOiAnYmctb3JhbmdlLTQwMCd9YH0+PC9kaXY+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPk9uYm9hcmRpbmc8L3A+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC1zbSBmb250LW1lZGl1bVwiPntvbmJvYXJkaW5nQ29tcGxldGUgPyAnRG9uZScgOiAnUGVuZGluZyd9PC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0zIGgtMyByb3VuZGVkLWZ1bGwgbXgtYXV0byBtYi0yIGJnLWJsdWUtNDAwXCI+PC9kaXY+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPk1lbWJlciBTaW5jZTwvcD5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAge2RqUHJvZmlsZSA/IG5ldyBEYXRlKGRqUHJvZmlsZS5jcmVhdGVkQXQpLnRvTG9jYWxlRGF0ZVN0cmluZygpIDogJ04vQSd9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEFjdGlvbnMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMiBwdC00XCI+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyLWdyYXktNjAwIHRleHQtZ3JheS0zMDAgaG92ZXI6YmctZ3JheS03MDBcIlxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVFZGl0UHJvZmlsZX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFNldHRpbmdzIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgIEVkaXQgUHJvZmlsZVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICB7IW9uYm9hcmRpbmdDb21wbGV0ZSAmJiAoXG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1wdXJwbGUtNjAwIHRvLWN5YW4tNjAwIGhvdmVyOmZyb20tcHVycGxlLTcwMCBob3Zlcjp0by1jeWFuLTcwMFwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ29tcGxldGVTZXR1cH1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNDb21wbGV0aW5nU2V0dXB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7aXNDb21wbGV0aW5nU2V0dXAgPyBcIkNvbXBsZXRpbmcuLi5cIiA6IFwiQ29tcGxldGUgU2V0dXBcIn1cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgPC9DYXJkPlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVF1ZXJ5IiwidXNlTXV0YXRpb24iLCJhcGkiLCJCdXR0b24iLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkRGVzY3JpcHRpb24iLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQmFkZ2UiLCJGaWxlVXBsb2FkIiwiVXNlciIsIk11c2ljIiwiU2V0dGluZ3MiLCJDYW1lcmEiLCJVcGxvYWQiLCJ1c2VTdGF0ZSIsIkRKUHJvZmlsZUNhcmQiLCJpc0NyZWF0aW5nIiwic2V0SXNDcmVhdGluZyIsImlzQ29tcGxldGluZ1NldHVwIiwic2V0SXNDb21wbGV0aW5nU2V0dXAiLCJzaG93UHJvZmlsZVBpY3R1cmVVcGxvYWQiLCJzZXRTaG93UHJvZmlsZVBpY3R1cmVVcGxvYWQiLCJ1c2VyV2l0aFByb2ZpbGUiLCJ1c2VycyIsImdldEN1cnJlbnRVc2VyV2l0aFByb2ZpbGUiLCJ1c2VyU3RhdHVzIiwiY2hlY2tVc2VyU3RhdHVzIiwicHJvZmlsZVBpY3R1cmVzIiwiZmlsZVN0b3JhZ2UiLCJnZXRVc2VyRmlsZXMiLCJwdXJwb3NlIiwibGltaXQiLCJpbml0aWFsaXplU2V0dXAiLCJpbml0aWFsaXplVXNlclNldHVwIiwidXBkYXRlUHJvZmlsZVBpY3R1cmUiLCJjb21wbGV0ZU9uYm9hcmRpbmciLCJoYW5kbGVDcmVhdGVESlByb2ZpbGUiLCJjcmVhdGVEalByb2ZpbGUiLCJkaXNwbGF5TmFtZSIsInVzZXIiLCJuYW1lIiwiZXJyb3IiLCJjb25zb2xlIiwiaGFuZGxlUHJvZmlsZVBpY3R1cmVVcGxvYWQiLCJmaWxlSWQiLCJ1cmwiLCJzdG9yYWdlSWQiLCJoYW5kbGVFZGl0UHJvZmlsZSIsImxvZyIsImFsZXJ0IiwiaGFuZGxlQ29tcGxldGVTZXR1cCIsInJlc3VsdCIsImFscmVhZHlDb21wbGV0ZWQiLCJjdXJyZW50UHJvZmlsZVBpY3R1cmUiLCJjbGFzc05hbWUiLCJkaXYiLCJkalByb2ZpbGUiLCJoYXNEalByb2ZpbGUiLCJvbmJvYXJkaW5nQ29tcGxldGUiLCJoNCIsImVtYWlsIiwicCIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsInZhcmlhbnQiLCJpbWciLCJzcmMiLCJhbHQiLCJzaXplIiwiRGF0ZSIsInVwbG9hZGVkQXQiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJoNSIsIm9uVXBsb2FkQ29tcGxldGUiLCJvblVwbG9hZEVycm9yIiwibWF4U2l6ZSIsImxhYmVsIiwiaXNBdXRoZW50aWNhdGVkIiwiY3JlYXRlZEF0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/dashboard/DJProfileCard.tsx\n"));

/***/ })

});