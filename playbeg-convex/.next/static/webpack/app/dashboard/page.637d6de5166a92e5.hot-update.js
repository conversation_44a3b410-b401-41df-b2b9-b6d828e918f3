"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/dashboard/SessionManager.tsx":
/*!*************************************************!*\
  !*** ./components/dashboard/SessionManager.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ SessionManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SessionManager() {\n    var _userAnalytics_metrics;\n    _s();\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    // Get current user with profile\n    const userWithProfile = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.users.getCurrentUserWithProfile);\n    const currentUser = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.users.getCurrentUser);\n    const djProfile = userWithProfile === null || userWithProfile === void 0 ? void 0 : userWithProfile.djProfile;\n    // Get real user sessions\n    const userSessions = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.sessions.getUserSessions, currentUser ? {\n        userId: currentUser._id\n    } : \"skip\");\n    // Get user analytics\n    const userAnalytics = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.analytics.getUserEngagementAnalytics, currentUser ? {\n        timeRange: 30 * 24 * 60 * 60 * 1000\n    } : \"skip\" // Last 30 days\n    );\n    // Mutations\n    const createSession = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.sessions.createSession);\n    const handleCreateSession = async ()=>{\n        setIsCreating(true);\n        try {\n            const sessionId = await createSession({\n                name: \"Session \".concat(new Date().toLocaleDateString()),\n                acceptRequests: true,\n                autoApproval: false,\n                maxRequestsPerUser: 5,\n                timeframeMinutes: 60,\n                weddingModeEnabled: false\n            });\n            console.log(\"Session created:\", sessionId);\n        } catch (error) {\n            console.error(\"Failed to create session:\", error);\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    const handleManageActive = ()=>{\n        // Navigate to sessions tab\n        const event = new CustomEvent('switchTab', {\n            detail: 'session-management'\n        });\n        window.dispatchEvent(event);\n    };\n    const handleViewAnalytics = ()=>{\n        // Navigate to analytics tab\n        const event = new CustomEvent('switchTab', {\n            detail: 'analytics'\n        });\n        window.dispatchEvent(event);\n    };\n    if (!djProfile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"bg-gray-800/50 border-purple-500/20\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"text-white flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                \"Session Manager\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                            className: \"text-gray-400\",\n                            children: \"Create a DJ profile first to manage sessions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-4\",\n                                children: \"DJ profile required\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-sm\",\n                                children: \"Create your DJ profile to start managing sessions and receiving song requests.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this);\n    }\n    // Process real session data\n    const activeSessions = (userSessions === null || userSessions === void 0 ? void 0 : userSessions.filter((s)=>s.active)) || [];\n    const recentSessions = (userSessions === null || userSessions === void 0 ? void 0 : userSessions.slice(0, 3)) || [];\n    const totalConnections = (userAnalytics === null || userAnalytics === void 0 ? void 0 : (_userAnalytics_metrics = userAnalytics.metrics) === null || _userAnalytics_metrics === void 0 ? void 0 : _userAnalytics_metrics.totalConnections) || 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"bg-gray-800/50 border-purple-500/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: \"Create Session\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-6 h-6 text-purple-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4 text-sm\",\n                                    children: \"Start a new DJ session to receive song requests\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleCreateSession,\n                                    disabled: isCreating,\n                                    className: \"w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700\",\n                                    children: isCreating ? \"Creating...\" : \"New Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"bg-gray-800/50 border-purple-500/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: \"Active Sessions\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full \".concat(activeSessions.length > 0 ? 'bg-green-400 animate-pulse' : 'bg-gray-400')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4 text-sm\",\n                                    children: [\n                                        activeSessions.length,\n                                        \" active session\",\n                                        activeSessions.length !== 1 ? 's' : ''\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    className: \"w-full border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    disabled: activeSessions.length === 0,\n                                    onClick: handleManageActive,\n                                    children: activeSessions.length > 0 ? \"Manage Active\" : \"No Active Sessions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"bg-gray-800/50 border-purple-500/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: \"Total Requests\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-6 h-6 text-purple-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4 text-sm\",\n                                    children: [\n                                        totalConnections,\n                                        \" total connections made\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    className: \"w-full border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    onClick: handleViewAnalytics,\n                                    children: \"View Analytics\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"bg-gray-800/50 border-purple-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"text-white flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Recent Sessions\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                className: \"text-gray-400\",\n                                children: \"Your latest DJ sessions and their status\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: recentSessions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: recentSessions.map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 bg-gray-700/50 rounded-lg border border-gray-600/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 rounded-full flex items-center justify-center \".concat(session.active ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'),\n                                                    children: session.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 41\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 72\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-white\",\n                                                            children: session.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                                            lineNumber: 189,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        new Date(session.createdAt).toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                                            lineNumber: 193,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        session.maxRequestsPerUser || 'Unlimited',\n                                                                        \" max per user\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: session.active ? 'default' : 'secondary',\n                                                    children: session.active ? 'Live' : 'Ended'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    variant: \"outline\",\n                                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                    onClick: handleManageActive,\n                                                    children: session.active ? 'Manage' : 'View'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, session._id, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4\",\n                                    children: \"No sessions yet\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-sm mb-6\",\n                                    children: \"Create your first session to start receiving song requests from your audience.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleCreateSession,\n                                    disabled: isCreating,\n                                    className: \"bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700\",\n                                    children: isCreating ? \"Creating...\" : \"Create Your First Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionManager, \"fAmpgFiQasUPtCjI3WI9Dnk215w=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useMutation\n    ];\n});\n_c = SessionManager;\nvar _c;\n$RefreshReg$(_c, \"SessionManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/dashboard/SessionManager.tsx\n"));

/***/ })

});