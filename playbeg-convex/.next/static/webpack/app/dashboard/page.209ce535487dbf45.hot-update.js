"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @convex-dev/auth/react */ \"(app-pages-browser)/./node_modules/@convex-dev/auth/dist/react/index.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _components_dashboard_DJProfileCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/DJProfileCard */ \"(app-pages-browser)/./components/dashboard/DJProfileCard.tsx\");\n/* harmony import */ var _components_dashboard_SessionManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/SessionManager */ \"(app-pages-browser)/./components/dashboard/SessionManager.tsx\");\n/* harmony import */ var _components_SongRequestManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/SongRequestManager */ \"(app-pages-browser)/./components/SongRequestManager.tsx\");\n/* harmony import */ var _components_dashboard_NotificationCenter__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/NotificationCenter */ \"(app-pages-browser)/./components/dashboard/NotificationCenter.tsx\");\n/* harmony import */ var _components_PassPurchase__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/PassPurchase */ \"(app-pages-browser)/./components/PassPurchase.tsx\");\n/* harmony import */ var _components_SongSearchInput__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/SongSearchInput */ \"(app-pages-browser)/./components/SongSearchInput.tsx\");\n/* harmony import */ var _components_testing_IntegrationTestSuite__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/testing/IntegrationTestSuite */ \"(app-pages-browser)/./components/testing/IntegrationTestSuite.tsx\");\n/* harmony import */ var _components_testing_RealtimeTestSuite__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/testing/RealtimeTestSuite */ \"(app-pages-browser)/./components/testing/RealtimeTestSuite.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const { isAuthenticated, isLoading } = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useConvexAuth)();\n    const { signOut } = (0,_convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_14__.useAuthActions)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('overview');\n    // Get current user and their DJ profile\n    const currentUser = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.getCurrentUser);\n    const userWithProfile = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.getCurrentUserWithProfile);\n    const djProfile = userWithProfile === null || userWithProfile === void 0 ? void 0 : userWithProfile.djProfile;\n    // Get user status\n    const userStatus = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.checkUserStatus);\n    // Get DJ profiles list (for testing)\n    const allDjProfiles = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.djProfiles.listDjProfiles, {\n        limit: 10,\n        onboardingStatus: true\n    });\n    // Get active sessions for the current user\n    const activeSessions = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.sessions.getCurrentUserSessions, currentUser ? {\n        activeOnly: true,\n        limit: 10\n    } : \"skip\");\n    // Get the first active session for song request management\n    const activeSession = activeSessions && activeSessions.length > 0 ? activeSessions[0] : null;\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated) {\n                router.push(\"/signin\");\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    const handleSignOut = async ()=>{\n        await signOut();\n        router.push(\"/signin\");\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-lg\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null; // Will redirect to signin\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gray-900/50 backdrop-blur-md border-b border-purple-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-white\",\n                                        children: \"PlayBeg\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300\",\n                                        children: [\n                                            \"Welcome, \",\n                                            (currentUser === null || currentUser === void 0 ? void 0 : currentUser.name) || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.email) || \"DJ\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_NotificationCenter__WEBPACK_IMPORTED_MODULE_9__.NotificationCenter, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: handleSignOut,\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"text-gray-300 hover:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Sign Out\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-white mb-2\",\n                                children: \"DJ Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Manage your sessions, song requests, and audience engagement\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 border-b border-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('overview'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'overview' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('requests'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'requests' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Song Requests\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('billing'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'billing' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Billing & Passes\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('analytics'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'analytics' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('spotify-test'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'spotify-test' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83C\\uDFB5 Spotify Test\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('integration-test'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'integration-test' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83E\\uDDEA Integration Test\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('realtime-test'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'realtime-test' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"⚡ Real-time Test\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DJProfileCard__WEBPACK_IMPORTED_MODULE_6__.DJProfileCard, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SessionManager__WEBPACK_IMPORTED_MODULE_7__.SessionManager, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'requests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: activeSession ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SongRequestManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            sessionId: activeSession._id\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: \"\\uD83C\\uDFB5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white mb-2\",\n                                    children: \"No Active Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-6\",\n                                    children: \"You need to have an active session to manage song requests.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: ()=>setActiveTab('overview'),\n                                    className: \"bg-purple-600 hover:bg-purple-700 text-white\",\n                                    children: \"Create a Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'billing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PassPurchase__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'analytics' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Analytics Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl mb-4\",\n                                        children: \"\\uD83D\\uDCCA\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Detailed analytics coming soon...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'spotify-test' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-4\",\n                                children: \"\\uD83C\\uDFB5 Spotify Integration Test\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-6\",\n                                children: \"Test the Spotify search functionality to verify the integration is working properly.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SongSearchInput__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    onSongSelect: (song)=>{\n                                        console.log('Selected song:', song);\n                                        alert(\"Selected: \".concat(song.title, \" by \").concat(song.artist));\n                                    },\n                                    placeholder: \"Search for a song on Spotify...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'integration-test' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_testing_IntegrationTestSuite__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'realtime-test' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_testing_RealtimeTestSuite__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Quick Stats\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                            children: \"View Full Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: (allDjProfiles === null || allDjProfiles === void 0 ? void 0 : allDjProfiles.length) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Total DJs\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Active Sessions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Song Requests\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: (userStatus === null || userStatus === void 0 ? void 0 : userStatus.isAuthenticated) ? '100%' : '0%'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"System Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"N2GOEDHqEQMaB1gROcBEnLiv8FE=\", false, function() {\n    return [\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useConvexAuth,\n        _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_14__.useAuthActions,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/testing/RealtimeTestSuite.tsx":
/*!**************************************************!*\
  !*** ./components/testing/RealtimeTestSuite.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RealtimeTestSuite)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction RealtimeTestSuite() {\n    _s();\n    const [testResults, setTestResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testSessionId, setTestSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [requestCount, setRequestCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Real-time queries to test subscriptions\n    const sessions = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.getUserSessions);\n    const sessionDetails = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.getSessionById, testSessionId ? {\n        sessionId: testSessionId\n    } : \"skip\");\n    const sessionRequests = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.songRequests.getSessionRequests, testSessionId ? {\n        sessionId: testSessionId\n    } : \"skip\");\n    const sessionAnalytics = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.analytics.getSessionAnalytics, testSessionId ? {\n        sessionId: testSessionId\n    } : \"skip\");\n    // Mutations for testing\n    const createSession = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.createSession);\n    const activateSession = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.activateSession);\n    const deactivateSession = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.deactivateSession);\n    const updateSession = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.updateSession);\n    const createSongRequest = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.songRequests.createSongRequest);\n    const updateRequestStatus = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.songRequests.updateRequestStatus);\n    const addTestResult = (test, status, message)=>{\n        setTestResults((prev)=>[\n                ...prev,\n                {\n                    test,\n                    status,\n                    message,\n                    timestamp: Date.now()\n                }\n            ]);\n    };\n    const runRealtimeTests = async ()=>{\n        setIsRunning(true);\n        setTestResults([]);\n        setRequestCount(0);\n        try {\n            // Test 1: Session Creation and Real-time Updates\n            addTestResult('Session Creation', 'running', 'Creating test session...');\n            const sessionId = await createSession({\n                name: \"Realtime Test Session \".concat(Date.now()),\n                acceptRequests: true,\n                autoApproval: false,\n                maxRequestsPerUser: 10\n            });\n            setTestSessionId(sessionId);\n            addTestResult('Session Creation', 'success', 'Session created, testing real-time updates...');\n            // Wait for real-time update\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Test 2: Session Activation\n            addTestResult('Session Activation', 'running', 'Activating session...');\n            await activateSession({\n                sessionId\n            });\n            addTestResult('Session Activation', 'success', 'Session activated, checking real-time status...');\n            // Wait for real-time update\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Test 3: Multiple Song Requests (Real-time stress test)\n            addTestResult('Song Request Batch', 'running', 'Creating multiple song requests...');\n            const requestPromises = [];\n            for(let i = 1; i <= 5; i++){\n                requestPromises.push(createSongRequest({\n                    sessionId,\n                    songTitle: \"Test Song \".concat(i),\n                    artistName: \"Test Artist \".concat(i),\n                    requesterName: \"Test User \".concat(i),\n                    requesterIp: '127.0.0.1'\n                }));\n            }\n            await Promise.all(requestPromises);\n            setRequestCount(5);\n            addTestResult('Song Request Batch', 'success', 'Created 5 song requests, monitoring real-time updates...');\n            // Wait for real-time updates\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // Test 4: Request Status Updates\n            addTestResult('Request Status Updates', 'running', 'Testing request status changes...');\n            if (sessionRequests && sessionRequests.length > 0) {\n                const firstRequest = sessionRequests[0];\n                await updateRequestStatus({\n                    requestId: firstRequest._id,\n                    status: 'approved'\n                });\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                await updateRequestStatus({\n                    requestId: firstRequest._id,\n                    status: 'played'\n                });\n                addTestResult('Request Status Updates', 'success', 'Request status updated, checking real-time sync...');\n            } else {\n                addTestResult('Request Status Updates', 'error', 'No requests found to update');\n            }\n            // Test 5: Session Settings Update\n            addTestResult('Session Settings Update', 'running', 'Updating session settings...');\n            await updateSession({\n                sessionId,\n                name: \"Updated Realtime Test Session \".concat(Date.now()),\n                maxRequestsPerUser: 15,\n                autoApproval: true\n            });\n            addTestResult('Session Settings Update', 'success', 'Session settings updated, checking real-time sync...');\n            // Wait for final updates\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Test 6: Session Deactivation\n            addTestResult('Session Deactivation', 'running', 'Deactivating session...');\n            await deactivateSession({\n                sessionId\n            });\n            addTestResult('Session Deactivation', 'success', 'Session deactivated, checking real-time status...');\n            // Final validation\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            addTestResult('Real-time Validation Complete', 'success', 'All real-time features validated successfully! 🎉');\n        } catch (error) {\n            addTestResult('Test Error', 'error', \"Unexpected error: \".concat(error.message));\n        } finally{\n            setIsRunning(false);\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'pending':\n                return '⏳';\n            case 'running':\n                return '🔄';\n            case 'success':\n                return '✅';\n            case 'error':\n                return '❌';\n            default:\n                return '⏳';\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'pending':\n                return 'text-gray-400';\n            case 'running':\n                return 'text-blue-400';\n            case 'success':\n                return 'text-green-400';\n            case 'error':\n                return 'text-red-400';\n            default:\n                return 'text-gray-400';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-4\",\n                        children: \"⚡ Real-time Feature Validation\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/80 mb-6\",\n                        children: \"Test real-time subscriptions, live updates, and data synchronization across the application.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: runRealtimeTests,\n                        disabled: isRunning,\n                        className: \"w-full bg-blue-600 hover:bg-blue-700 text-white mb-6\",\n                        children: isRunning ? 'Running Real-time Tests...' : 'Run Real-time Validation'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this),\n                    testSessionId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                className: \"bg-white/5 border-white/10 p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-white mb-2\",\n                                        children: \"Session Status (Live)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/80\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Active: \",\n                                                    (sessionDetails === null || sessionDetails === void 0 ? void 0 : sessionDetails.active) ? '✅ Yes' : '❌ No'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Name: \",\n                                                    (sessionDetails === null || sessionDetails === void 0 ? void 0 : sessionDetails.name) || 'Loading...'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Requests: \",\n                                                    (sessionDetails === null || sessionDetails === void 0 ? void 0 : sessionDetails.acceptRequests) ? '✅ Accepting' : '❌ Closed'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Auto-approval: \",\n                                                    (sessionDetails === null || sessionDetails === void 0 ? void 0 : sessionDetails.autoApproval) ? '✅ On' : '❌ Off'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                className: \"bg-white/5 border-white/10 p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-white mb-2\",\n                                        children: \"Song Requests (Live)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/80\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Total Requests: \",\n                                                    (sessionRequests === null || sessionRequests === void 0 ? void 0 : sessionRequests.length) || 0\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Expected: \",\n                                                    requestCount\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Pending: \",\n                                                    (sessionRequests === null || sessionRequests === void 0 ? void 0 : sessionRequests.filter((r)=>r.status === 'pending').length) || 0\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Approved: \",\n                                                    (sessionRequests === null || sessionRequests === void 0 ? void 0 : sessionRequests.filter((r)=>r.status === 'approved').length) || 0\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Played: \",\n                                                    (sessionRequests === null || sessionRequests === void 0 ? void 0 : sessionRequests.filter((r)=>r.status === 'played').length) || 0\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this),\n            testResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold text-white mb-4\",\n                        children: \"Real-time Test Results\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3 max-h-96 overflow-y-auto\",\n                        children: testResults.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: getStatusIcon(result.status)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-white\",\n                                                    children: result.test\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm \".concat(getStatusColor(result.status)),\n                                                    children: result.message\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-white/40 mt-1\",\n                                                    children: new Date(result.timestamp).toLocaleTimeString()\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 17\n                                }, this)\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                lineNumber: 229,\n                columnNumber: 9\n            }, this),\n            sessionAnalytics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold text-white mb-4\",\n                        children: \"\\uD83D\\uDCCA Live Session Analytics\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: sessionAnalytics.totalRequests\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/60\",\n                                        children: \"Total Requests\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: sessionAnalytics.approvedRequests\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/60\",\n                                        children: \"Approved\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: sessionAnalytics.playedRequests\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/60\",\n                                        children: \"Played\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: sessionAnalytics.uniqueRequesters\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/60\",\n                                        children: \"Unique Users\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                lineNumber: 254,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n        lineNumber: 185,\n        columnNumber: 5\n    }, this);\n}\n_s(RealtimeTestSuite, \"f0Yhp0PQbJ11fvMppXMQGs93qno=\", false, function() {\n    return [\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation\n    ];\n});\n_c = RealtimeTestSuite;\nvar _c;\n$RefreshReg$(_c, \"RealtimeTestSuite\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/testing/RealtimeTestSuite.tsx\n"));

/***/ })

});