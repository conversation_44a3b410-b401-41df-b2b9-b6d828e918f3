"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @convex-dev/auth/react */ \"(app-pages-browser)/./node_modules/@convex-dev/auth/dist/react/index.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _components_dashboard_DJProfileCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/DJProfileCard */ \"(app-pages-browser)/./components/dashboard/DJProfileCard.tsx\");\n/* harmony import */ var _components_dashboard_SessionManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/SessionManager */ \"(app-pages-browser)/./components/dashboard/SessionManager.tsx\");\n/* harmony import */ var _components_SongRequestManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/SongRequestManager */ \"(app-pages-browser)/./components/SongRequestManager.tsx\");\n/* harmony import */ var _components_dashboard_NotificationCenter__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/NotificationCenter */ \"(app-pages-browser)/./components/dashboard/NotificationCenter.tsx\");\n/* harmony import */ var _components_PassPurchase__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/PassPurchase */ \"(app-pages-browser)/./components/PassPurchase.tsx\");\n/* harmony import */ var _components_SongSearchInput__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/SongSearchInput */ \"(app-pages-browser)/./components/SongSearchInput.tsx\");\n/* harmony import */ var _components_testing_IntegrationTestSuite__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/testing/IntegrationTestSuite */ \"(app-pages-browser)/./components/testing/IntegrationTestSuite.tsx\");\n/* harmony import */ var _components_testing_RealtimeTestSuite__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/testing/RealtimeTestSuite */ \"(app-pages-browser)/./components/testing/RealtimeTestSuite.tsx\");\n/* harmony import */ var _components_session_SessionManagementHub__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/session/SessionManagementHub */ \"(app-pages-browser)/./components/session/SessionManagementHub.tsx\");\n/* harmony import */ var _components_admin_BlogManagement__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/admin/BlogManagement */ \"(app-pages-browser)/./components/admin/BlogManagement.tsx\");\n/* harmony import */ var _components_search_AdvancedSearchInterface__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/search/AdvancedSearchInterface */ \"(app-pages-browser)/./components/search/AdvancedSearchInterface.tsx\");\n/* harmony import */ var _components_performance_PerformanceMonitor__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/performance/PerformanceMonitor */ \"(app-pages-browser)/./components/performance/PerformanceMonitor.tsx\");\n/* harmony import */ var _components_analytics_AdvancedAnalyticsDashboard__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/analytics/AdvancedAnalyticsDashboard */ \"(app-pages-browser)/./components/analytics/AdvancedAnalyticsDashboard.tsx\");\n/* harmony import */ var _components_security_SecurityDashboard__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/security/SecurityDashboard */ \"(app-pages-browser)/./components/security/SecurityDashboard.tsx\");\n/* harmony import */ var _components_mobile_PWAManager__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/mobile/PWAManager */ \"(app-pages-browser)/./components/mobile/PWAManager.tsx\");\n/* harmony import */ var _components_testing_LoadTestDashboard__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/testing/LoadTestDashboard */ \"(app-pages-browser)/./components/testing/LoadTestDashboard.tsx\");\n/* harmony import */ var _components_testing_SecurityTestDashboard__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/testing/SecurityTestDashboard */ \"(app-pages-browser)/./components/testing/SecurityTestDashboard.tsx\");\n/* harmony import */ var _components_deployment_ProductionDeploymentDashboard__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/deployment/ProductionDeploymentDashboard */ \"(app-pages-browser)/./components/deployment/ProductionDeploymentDashboard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const { isAuthenticated, isLoading } = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useConvexAuth)();\n    const { signOut } = (0,_convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_24__.useAuthActions)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('overview');\n    // Get current user and their DJ profile\n    const currentUser = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.getCurrentUser);\n    const userWithProfile = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.getCurrentUserWithProfile);\n    const djProfile = userWithProfile === null || userWithProfile === void 0 ? void 0 : userWithProfile.djProfile;\n    // Get user status\n    const userStatus = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.checkUserStatus);\n    // Get active sessions for the current user\n    const activeSessions = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.sessions.getCurrentUserSessions, currentUser ? {\n        activeOnly: true,\n        limit: 10\n    } : \"skip\");\n    // Get user session statistics\n    const sessionStats = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.analytics.getUserSessionStats, currentUser ? {\n        userId: currentUser._id,\n        timeRange: 30 * 24 * 60 * 60 * 1000\n    } : \"skip\" // Last 30 days\n    );\n    // Get the first active session for song request management\n    const activeSession = activeSessions && activeSessions.length > 0 ? activeSessions[0] : null;\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated) {\n                router.push(\"/signin\");\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    // Listen for tab switching events from components\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            const handleTabSwitch = {\n                \"Dashboard.useEffect.handleTabSwitch\": (event)=>{\n                    setActiveTab(event.detail);\n                }\n            }[\"Dashboard.useEffect.handleTabSwitch\"];\n            window.addEventListener('switchTab', handleTabSwitch);\n            return ({\n                \"Dashboard.useEffect\": ()=>{\n                    window.removeEventListener('switchTab', handleTabSwitch);\n                }\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const handleSignOut = async ()=>{\n        await signOut();\n        router.push(\"/signin\");\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-lg\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null; // Will redirect to signin\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gray-900/50 backdrop-blur-md border-b border-purple-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-white\",\n                                        children: \"PlayBeg\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300\",\n                                        children: [\n                                            \"Welcome, \",\n                                            (currentUser === null || currentUser === void 0 ? void 0 : currentUser.name) || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.email) || \"DJ\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_NotificationCenter__WEBPACK_IMPORTED_MODULE_9__.NotificationCenter, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: handleSignOut,\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"text-gray-300 hover:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Sign Out\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-white mb-2\",\n                                children: \"DJ Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Manage your sessions, song requests, and audience engagement\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 border-b border-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('overview'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'overview' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('requests'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'requests' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Song Requests\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('billing'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'billing' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Billing & Passes\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('analytics'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'analytics' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('spotify-test'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'spotify-test' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83C\\uDFB5 Spotify Test\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('integration-test'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'integration-test' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83E\\uDDEA Integration Test\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('realtime-test'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'realtime-test' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"⚡ Real-time Test\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('session-management'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'session-management' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83C\\uDF9B️ Sessions\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('blog-admin'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'blog-admin' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83D\\uDCDD Blog Admin\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('advanced-search'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'advanced-search' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83D\\uDD0D Advanced Search\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('performance'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'performance' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"⚡ Performance\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('security'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'security' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83D\\uDEE1️ Security\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('mobile-pwa'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'mobile-pwa' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83D\\uDCF1 Mobile & PWA\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('load-testing'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'load-testing' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83E\\uDDEA Load Testing\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('security-testing'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'security-testing' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83D\\uDD12 Security Testing\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('production-deployment'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'production-deployment' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83D\\uDE80 Production Deployment\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DJProfileCard__WEBPACK_IMPORTED_MODULE_6__.DJProfileCard, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SessionManager__WEBPACK_IMPORTED_MODULE_7__.SessionManager, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'requests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: activeSession ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SongRequestManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            sessionId: activeSession._id\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: \"\\uD83C\\uDFB5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white mb-2\",\n                                    children: \"No Active Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-6\",\n                                    children: \"You need to have an active session to manage song requests.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: ()=>setActiveTab('overview'),\n                                    className: \"bg-purple-600 hover:bg-purple-700 text-white\",\n                                    children: \"Create a Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'billing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PassPurchase__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'analytics' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_analytics_AdvancedAnalyticsDashboard__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'spotify-test' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-4\",\n                                children: \"\\uD83C\\uDFB5 Spotify Integration Test\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-6\",\n                                children: \"Test the Spotify search functionality to verify the integration is working properly.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SongSearchInput__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    onSongSelect: (song)=>{\n                                        console.log('Selected song:', song);\n                                        alert(\"Selected: \".concat(song.title, \" by \").concat(song.artist));\n                                    },\n                                    placeholder: \"Search for a song on Spotify...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'integration-test' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_testing_IntegrationTestSuite__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'realtime-test' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_testing_RealtimeTestSuite__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'session-management' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_session_SessionManagementHub__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        onSessionSelect: (sessionId)=>{\n                            console.log('Selected session:', sessionId);\n                        // Could navigate to session detail page or open session in new tab\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'blog-admin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_BlogManagement__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'advanced-search' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_search_AdvancedSearchInterface__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'performance' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_performance_PerformanceMonitor__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'security' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_security_SecurityDashboard__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'mobile-pwa' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"Mobile & PWA Features\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_PWAManager__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'load-testing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_testing_LoadTestDashboard__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'security-testing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_testing_SecurityTestDashboard__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'production-deployment' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_deployment_ProductionDeploymentDashboard__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Quick Stats\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                            children: \"View Full Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: (sessionStats === null || sessionStats === void 0 ? void 0 : sessionStats.totalSessions) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Total Sessions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: (sessionStats === null || sessionStats === void 0 ? void 0 : sessionStats.activeSessions) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Active Sessions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: (sessionStats === null || sessionStats === void 0 ? void 0 : sessionStats.totalRequests) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Song Requests\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: (userStatus === null || userStatus === void 0 ? void 0 : userStatus.isAuthenticated) ? '100%' : '0%'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"System Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                            lineNumber: 417,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"Kl+yo8Jh94V97eeD9K/KryrZeis=\", false, function() {\n    return [\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useConvexAuth,\n        _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_24__.useAuthActions,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});