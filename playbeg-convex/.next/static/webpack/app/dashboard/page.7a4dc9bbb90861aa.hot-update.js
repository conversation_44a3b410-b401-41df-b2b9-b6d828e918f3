"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/session/SessionManagementHub.tsx":
/*!*****************************************************!*\
  !*** ./components/session/SessionManagementHub.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionManagementHub)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _ui_switch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Pause_Play_Plus_QrCode_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Pause,Play,Plus,QrCode,Settings,Share!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Pause_Play_Plus_QrCode_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Pause,Play,Plus,QrCode,Settings,Share!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Pause_Play_Plus_QrCode_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Pause,Play,Plus,QrCode,Settings,Share!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Pause_Play_Plus_QrCode_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Pause,Play,Plus,QrCode,Settings,Share!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Pause_Play_Plus_QrCode_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Pause,Play,Plus,QrCode,Settings,Share!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Pause_Play_Plus_QrCode_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Pause,Play,Plus,QrCode,Settings,Share!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Pause_Play_Plus_QrCode_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Pause,Play,Plus,QrCode,Settings,Share!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction SessionManagementHub(param) {\n    let { onSessionSelect } = param;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const [selectedSessionId, setSelectedSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCreateForm, setShowCreateForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Session creation form state\n    const [newSessionName, setNewSessionName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newSessionDescription, setNewSessionDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [acceptRequests, setAcceptRequests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [autoApproval, setAutoApproval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [maxRequestsPerUser, setMaxRequestsPerUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5);\n    const [enableWeddingMode, setEnableWeddingMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get current user\n    const currentUser = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.users.getCurrentUser);\n    // Queries\n    const userSessions = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.getUserSessions, currentUser ? {\n        userId: currentUser._id\n    } : \"skip\");\n    const selectedSession = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.getSessionById, selectedSessionId ? {\n        sessionId: selectedSessionId\n    } : \"skip\");\n    const sessionAnalytics = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.analytics.getSessionAnalytics, selectedSessionId ? {\n        sessionId: selectedSessionId\n    } : \"skip\");\n    // Mutations\n    const createSession = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.createSession);\n    const updateSession = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.updateSession);\n    const activateSession = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.activateSession);\n    const deactivateSession = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.deactivateSession);\n    const deleteSession = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.deleteSession);\n    const handleCreateSession = async ()=>{\n        if (!newSessionName.trim()) return;\n        try {\n            const sessionId = await createSession({\n                name: newSessionName.trim(),\n                acceptRequests,\n                autoApproval,\n                maxRequestsPerUser,\n                timeframeMinutes: 60,\n                weddingModeEnabled: enableWeddingMode\n            });\n            setNewSessionName('');\n            setNewSessionDescription('');\n            setShowCreateForm(false);\n            setSelectedSessionId(sessionId);\n            onSessionSelect === null || onSessionSelect === void 0 ? void 0 : onSessionSelect(sessionId);\n        } catch (error) {\n            console.error('Failed to create session:', error);\n        }\n    };\n    const handleSessionAction = async (sessionId, action)=>{\n        try {\n            switch(action){\n                case 'activate':\n                    await activateSession({\n                        sessionId\n                    });\n                    break;\n                case 'deactivate':\n                    await deactivateSession({\n                        sessionId\n                    });\n                    break;\n                case 'delete':\n                    await deleteSession({\n                        sessionId\n                    });\n                    if (selectedSessionId === sessionId) {\n                        setSelectedSessionId(null);\n                    }\n                    break;\n            }\n        } catch (error) {\n            console.error(\"Failed to \".concat(action, \" session:\"), error);\n        }\n    };\n    const getSessionStatusBadge = (session)=>{\n        if (session.active) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                className: \"bg-green-600 text-white\",\n                children: \"Active\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                lineNumber: 117,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n            variant: \"secondary\",\n            children: \"Inactive\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n            lineNumber: 119,\n            columnNumber: 12\n        }, this);\n    };\n    const formatDate = (timestamp)=>{\n        return new Date(timestamp).toLocaleDateString('en-US', {\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: \"Session Management\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: ()=>setShowCreateForm(true),\n                        className: \"bg-purple-600 hover:bg-purple-700 text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Pause_Play_Plus_QrCode_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            \"New Session\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-1 bg-white/10 rounded-lg p-1\",\n                children: [\n                    {\n                        id: 'overview',\n                        label: 'Overview',\n                        icon: _barrel_optimize_names_BarChart3_Pause_Play_Plus_QrCode_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                    },\n                    {\n                        id: 'create',\n                        label: 'Create',\n                        icon: _barrel_optimize_names_BarChart3_Pause_Play_Plus_QrCode_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                    },\n                    {\n                        id: 'manage',\n                        label: 'Manage',\n                        icon: _barrel_optimize_names_BarChart3_Pause_Play_Plus_QrCode_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                    },\n                    {\n                        id: 'analytics',\n                        label: 'Analytics',\n                        icon: _barrel_optimize_names_BarChart3_Pause_Play_Plus_QrCode_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                    }\n                ].map((tab)=>{\n                    const Icon = tab.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveTab(tab.id),\n                        className: \"flex items-center space-x-2 px-4 py-2 rounded-md transition-colors \".concat(activeTab === tab.id ? 'bg-purple-600 text-white' : 'text-white/70 hover:text-white hover:bg-white/10'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: tab.label\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, tab.id, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this),\n            activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Your Sessions\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 max-h-96 overflow-y-auto\",\n                                children: [\n                                    userSessions === null || userSessions === void 0 ? void 0 : userSessions.map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/10 rounded-lg p-4 cursor-pointer transition-colors \".concat(selectedSessionId === session._id ? 'ring-2 ring-purple-500' : 'hover:bg-white/20'),\n                                            onClick: ()=>setSelectedSessionId(session._id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-white\",\n                                                            children: session.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        getSessionStatusBadge(session)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-white/70\",\n                                                    children: [\n                                                        \"Created: \",\n                                                        formatDate(session.createdAt)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            size: \"sm\",\n                                                            variant: session.active ? \"destructive\" : \"default\",\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                handleSessionAction(session._id, session.active ? 'deactivate' : 'activate');\n                                                            },\n                                                            className: \"text-xs\",\n                                                            children: session.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Pause_Play_Plus_QrCode_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 41\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Pause_Play_Plus_QrCode_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 73\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                onSessionSelect === null || onSessionSelect === void 0 ? void 0 : onSessionSelect(session._id);\n                                                            },\n                                                            className: \"text-xs\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Pause_Play_Plus_QrCode_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, session._id, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this)),\n                                    (!userSessions || userSessions.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-white/60\",\n                                        children: \"No sessions yet. Create your first session to get started!\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-4\",\n                                children: \"Session Details\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this),\n                            selectedSession ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold text-white\",\n                                                children: selectedSession.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mt-1\",\n                                                children: [\n                                                    getSessionStatusBadge(selectedSession),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"text-white border-white/30\",\n                                                        children: selectedSession.acceptRequests ? 'Accepting Requests' : 'Requests Closed'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white/60\",\n                                                        children: \"Max Requests/User\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: selectedSession.maxRequestsPerUser || 'Unlimited'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white/60\",\n                                                        children: \"Auto Approval\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: selectedSession.autoApproval ? 'On' : 'Off'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white/60\",\n                                                        children: \"Wedding Mode\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: selectedSession.weddingModeEnabled ? 'On' : 'Off'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white/60\",\n                                                        children: \"Created\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: formatDate(selectedSession.createdAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this),\n                                    sessionAnalytics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-white/20 pt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"font-semibold text-white mb-2\",\n                                                children: \"Quick Stats\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white/60\",\n                                                                children: \"Total Requests\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white font-medium\",\n                                                                children: sessionAnalytics.totalRequests\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white/60\",\n                                                                children: \"Approved\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white font-medium\",\n                                                                children: sessionAnalytics.approvedRequests\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2 pt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                size: \"sm\",\n                                                variant: selectedSession.active ? \"destructive\" : \"default\",\n                                                onClick: ()=>handleSessionAction(selectedSession._id, selectedSession.active ? 'deactivate' : 'activate'),\n                                                className: \"flex-1\",\n                                                children: selectedSession.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Pause_Play_Plus_QrCode_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Deactivate\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Pause_Play_Plus_QrCode_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Activate\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                size: \"sm\",\n                                                variant: \"outline\",\n                                                onClick: ()=>onSessionSelect === null || onSessionSelect === void 0 ? void 0 : onSessionSelect(selectedSession._id),\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Pause_Play_Plus_QrCode_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Share\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-white/60\",\n                                children: \"Select a session to view details\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                lineNumber: 173,\n                columnNumber: 9\n            }, this),\n            showCreateForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"bg-gray-900 border-gray-700 p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-white mb-4\",\n                            children: \"Create New Session\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            htmlFor: \"session-name\",\n                                            className: \"text-white\",\n                                            children: \"Session Name\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                            id: \"session-name\",\n                                            value: newSessionName,\n                                            onChange: (e)=>setNewSessionName(e.target.value),\n                                            placeholder: \"Saturday Night Mix, Birthday Party, etc.\",\n                                            className: \"bg-gray-800 border-gray-600 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            htmlFor: \"accept-requests\",\n                                            className: \"text-white\",\n                                            children: \"Accept Requests\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                            id: \"accept-requests\",\n                                            checked: acceptRequests,\n                                            onCheckedChange: setAcceptRequests\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            htmlFor: \"auto-approval\",\n                                            className: \"text-white\",\n                                            children: \"Auto Approval\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                            id: \"auto-approval\",\n                                            checked: autoApproval,\n                                            onCheckedChange: setAutoApproval\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            htmlFor: \"max-requests\",\n                                            className: \"text-white\",\n                                            children: \"Max Requests per User\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                            id: \"max-requests\",\n                                            type: \"number\",\n                                            value: maxRequestsPerUser,\n                                            onChange: (e)=>setMaxRequestsPerUser(parseInt(e.target.value) || 5),\n                                            className: \"bg-gray-800 border-gray-600 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            htmlFor: \"wedding-mode\",\n                                            className: \"text-white\",\n                                            children: \"Wedding Mode\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                            id: \"wedding-mode\",\n                                            checked: enableWeddingMode,\n                                            onCheckedChange: setEnableWeddingMode\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowCreateForm(false),\n                                            className: \"flex-1\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleCreateSession,\n                                            disabled: !newSessionName.trim(),\n                                            className: \"flex-1 bg-purple-600 hover:bg-purple-700\",\n                                            children: \"Create Session\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                    lineNumber: 319,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n                lineNumber: 318,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/session/SessionManagementHub.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionManagementHub, \"Se0MvgMwiapD9njpIgvph9z2gCs=\", false, function() {\n    return [\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation\n    ];\n});\n_c = SessionManagementHub;\nvar _c;\n$RefreshReg$(_c, \"SessionManagementHub\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/session/SessionManagementHub.tsx\n"));

/***/ })

});