"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/dashboard/SessionManager.tsx":
/*!*************************************************!*\
  !*** ./components/dashboard/SessionManager.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ SessionManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SessionManager() {\n    _s();\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    // Get current user with profile\n    const userWithProfile = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.users.getCurrentUserWithProfile);\n    const currentUser = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.users.getCurrentUser);\n    const djProfile = userWithProfile === null || userWithProfile === void 0 ? void 0 : userWithProfile.djProfile;\n    // Get real user sessions\n    const userSessions = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.sessions.getUserSessions, currentUser ? {\n        userId: currentUser._id\n    } : \"skip\");\n    // Get user session statistics\n    const sessionStats = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.analytics.getUserSessionStats, currentUser ? {\n        userId: currentUser._id,\n        timeRange: 30 * 24 * 60 * 60 * 1000\n    } : \"skip\" // Last 30 days\n    );\n    // Mutations\n    const createSession = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.sessions.createSession);\n    const handleCreateSession = async ()=>{\n        setIsCreating(true);\n        try {\n            const sessionId = await createSession({\n                name: \"Session \".concat(new Date().toLocaleDateString()),\n                acceptRequests: true,\n                autoApproval: false,\n                maxRequestsPerUser: 5,\n                timeframeMinutes: 60,\n                weddingModeEnabled: false\n            });\n            console.log(\"Session created:\", sessionId);\n        } catch (error) {\n            console.error(\"Failed to create session:\", error);\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    const handleManageActive = ()=>{\n        // Navigate to sessions tab\n        const event = new CustomEvent('switchTab', {\n            detail: 'session-management'\n        });\n        window.dispatchEvent(event);\n    };\n    const handleViewAnalytics = ()=>{\n        // Navigate to analytics tab\n        const event = new CustomEvent('switchTab', {\n            detail: 'analytics'\n        });\n        window.dispatchEvent(event);\n    };\n    if (!djProfile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"bg-gray-800/50 border-purple-500/20\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"text-white flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                \"Session Manager\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                            className: \"text-gray-400\",\n                            children: \"Create a DJ profile first to manage sessions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-4\",\n                                children: \"DJ profile required\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-sm\",\n                                children: \"Create your DJ profile to start managing sessions and receiving song requests.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this);\n    }\n    // Process real session data\n    const activeSessions = (userSessions === null || userSessions === void 0 ? void 0 : userSessions.filter((s)=>s.active)) || [];\n    const recentSessions = (userSessions === null || userSessions === void 0 ? void 0 : userSessions.slice(0, 3)) || [];\n    const totalRequests = (sessionStats === null || sessionStats === void 0 ? void 0 : sessionStats.totalRequests) || 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"bg-gray-800/50 border-purple-500/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: \"Create Session\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-6 h-6 text-purple-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4 text-sm\",\n                                    children: \"Start a new DJ session to receive song requests\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleCreateSession,\n                                    disabled: isCreating,\n                                    className: \"w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700\",\n                                    children: isCreating ? \"Creating...\" : \"New Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"bg-gray-800/50 border-purple-500/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: \"Active Sessions\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full \".concat(activeSessions.length > 0 ? 'bg-green-400 animate-pulse' : 'bg-gray-400')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4 text-sm\",\n                                    children: [\n                                        activeSessions.length,\n                                        \" active session\",\n                                        activeSessions.length !== 1 ? 's' : ''\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    className: \"w-full border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    disabled: activeSessions.length === 0,\n                                    onClick: handleManageActive,\n                                    children: activeSessions.length > 0 ? \"Manage Active\" : \"No Active Sessions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"bg-gray-800/50 border-purple-500/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: \"Total Requests\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-6 h-6 text-purple-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4 text-sm\",\n                                    children: [\n                                        totalRequests,\n                                        \" total requests received\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    className: \"w-full border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    onClick: handleViewAnalytics,\n                                    children: \"View Analytics\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"bg-gray-800/50 border-purple-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"text-white flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Recent Sessions\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                className: \"text-gray-400\",\n                                children: \"Your latest DJ sessions and their status\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: recentSessions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: recentSessions.map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 bg-gray-700/50 rounded-lg border border-gray-600/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 rounded-full flex items-center justify-center \".concat(session.active ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'),\n                                                    children: session.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 41\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 72\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-white\",\n                                                            children: session.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                                            lineNumber: 189,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        new Date(session.createdAt).toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                                            lineNumber: 193,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        session.maxRequestsPerUser || 'Unlimited',\n                                                                        \" max per user\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: session.active ? 'default' : 'secondary',\n                                                    children: session.active ? 'Live' : 'Ended'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    variant: \"outline\",\n                                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                    onClick: handleManageActive,\n                                                    children: session.active ? 'Manage' : 'View'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, session._id, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4\",\n                                    children: \"No sessions yet\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-sm mb-6\",\n                                    children: \"Create your first session to start receiving song requests from your audience.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleCreateSession,\n                                    disabled: isCreating,\n                                    className: \"bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700\",\n                                    children: isCreating ? \"Creating...\" : \"Create Your First Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionManager, \"pdjhA4QfLYDxJXRghdBisnZ3Gy8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useMutation\n    ];\n});\n_c = SessionManager;\nvar _c;\n$RefreshReg$(_c, \"SessionManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/dashboard/SessionManager.tsx\n"));

/***/ })

});