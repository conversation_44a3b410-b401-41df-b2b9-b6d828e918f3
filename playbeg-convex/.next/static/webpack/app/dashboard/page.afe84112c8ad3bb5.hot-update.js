"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/testing/RealtimeTestSuite.tsx":
/*!**************************************************!*\
  !*** ./components/testing/RealtimeTestSuite.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RealtimeTestSuite)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction RealtimeTestSuite() {\n    _s();\n    const [testResults, setTestResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testSessionId, setTestSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [requestCount, setRequestCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Get current user\n    const currentUser = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.users.getCurrentUser);\n    // Real-time queries to test subscriptions\n    const sessions = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.getUserSessions, currentUser ? {\n        userId: currentUser._id\n    } : \"skip\");\n    const sessionDetails = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.getSessionById, testSessionId ? {\n        sessionId: testSessionId\n    } : \"skip\");\n    const sessionRequests = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.songRequests.getSessionRequests, testSessionId ? {\n        sessionId: testSessionId\n    } : \"skip\");\n    const sessionAnalytics = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.analytics.getSessionAnalytics, testSessionId ? {\n        sessionId: testSessionId\n    } : \"skip\");\n    // Mutations for testing\n    const createSession = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.createSession);\n    const activateSession = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.activateSession);\n    const deactivateSession = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.deactivateSession);\n    const updateSession = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.updateSession);\n    const createSongRequest = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.songRequests.createSongRequest);\n    const updateRequestStatus = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.songRequests.updateRequestStatus);\n    const addTestResult = (test, status, message)=>{\n        setTestResults((prev)=>[\n                ...prev,\n                {\n                    test,\n                    status,\n                    message,\n                    timestamp: Date.now()\n                }\n            ]);\n    };\n    const runRealtimeTests = async ()=>{\n        setIsRunning(true);\n        setTestResults([]);\n        setRequestCount(0);\n        try {\n            // Test 1: Session Creation and Real-time Updates\n            addTestResult('Session Creation', 'running', 'Creating test session...');\n            const sessionId = await createSession({\n                name: \"Realtime Test Session \".concat(Date.now()),\n                acceptRequests: true,\n                autoApproval: false,\n                maxRequestsPerUser: 10\n            });\n            setTestSessionId(sessionId);\n            addTestResult('Session Creation', 'success', 'Session created, testing real-time updates...');\n            // Wait for real-time update\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Test 2: Session Activation\n            addTestResult('Session Activation', 'running', 'Activating session...');\n            await activateSession({\n                sessionId\n            });\n            addTestResult('Session Activation', 'success', 'Session activated, checking real-time status...');\n            // Wait for real-time update\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Test 3: Multiple Song Requests (Real-time stress test)\n            addTestResult('Song Request Batch', 'running', 'Creating multiple song requests...');\n            const requestPromises = [];\n            for(let i = 1; i <= 5; i++){\n                requestPromises.push(createSongRequest({\n                    sessionId,\n                    songTitle: \"Test Song \".concat(i),\n                    artistName: \"Test Artist \".concat(i),\n                    requesterName: \"Test User \".concat(i),\n                    requesterIp: '127.0.0.1'\n                }));\n            }\n            await Promise.all(requestPromises);\n            setRequestCount(5);\n            addTestResult('Song Request Batch', 'success', 'Created 5 song requests, monitoring real-time updates...');\n            // Wait for real-time updates\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // Test 4: Request Status Updates\n            addTestResult('Request Status Updates', 'running', 'Testing request status changes...');\n            if (sessionRequests && sessionRequests.length > 0) {\n                const firstRequest = sessionRequests[0];\n                await updateRequestStatus({\n                    requestId: firstRequest._id,\n                    status: 'approved'\n                });\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                await updateRequestStatus({\n                    requestId: firstRequest._id,\n                    status: 'played'\n                });\n                addTestResult('Request Status Updates', 'success', 'Request status updated, checking real-time sync...');\n            } else {\n                addTestResult('Request Status Updates', 'error', 'No requests found to update');\n            }\n            // Test 5: Session Settings Update\n            addTestResult('Session Settings Update', 'running', 'Updating session settings...');\n            await updateSession({\n                sessionId,\n                name: \"Updated Realtime Test Session \".concat(Date.now()),\n                maxRequestsPerUser: 15,\n                autoApproval: true\n            });\n            addTestResult('Session Settings Update', 'success', 'Session settings updated, checking real-time sync...');\n            // Wait for final updates\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Test 6: Session Deactivation\n            addTestResult('Session Deactivation', 'running', 'Deactivating session...');\n            await deactivateSession({\n                sessionId\n            });\n            addTestResult('Session Deactivation', 'success', 'Session deactivated, checking real-time status...');\n            // Final validation\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            addTestResult('Real-time Validation Complete', 'success', 'All real-time features validated successfully! 🎉');\n        } catch (error) {\n            addTestResult('Test Error', 'error', \"Unexpected error: \".concat(error.message));\n        } finally{\n            setIsRunning(false);\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'pending':\n                return '⏳';\n            case 'running':\n                return '🔄';\n            case 'success':\n                return '✅';\n            case 'error':\n                return '❌';\n            default:\n                return '⏳';\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'pending':\n                return 'text-gray-400';\n            case 'running':\n                return 'text-blue-400';\n            case 'success':\n                return 'text-green-400';\n            case 'error':\n                return 'text-red-400';\n            default:\n                return 'text-gray-400';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-4\",\n                        children: \"⚡ Real-time Feature Validation\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/80 mb-6\",\n                        children: \"Test real-time subscriptions, live updates, and data synchronization across the application.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: runRealtimeTests,\n                        disabled: isRunning,\n                        className: \"w-full bg-blue-600 hover:bg-blue-700 text-white mb-6\",\n                        children: isRunning ? 'Running Real-time Tests...' : 'Run Real-time Validation'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this),\n                    testSessionId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                className: \"bg-white/5 border-white/10 p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-white mb-2\",\n                                        children: \"Session Status (Live)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/80\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Active: \",\n                                                    (sessionDetails === null || sessionDetails === void 0 ? void 0 : sessionDetails.active) ? '✅ Yes' : '❌ No'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Name: \",\n                                                    (sessionDetails === null || sessionDetails === void 0 ? void 0 : sessionDetails.name) || 'Loading...'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Requests: \",\n                                                    (sessionDetails === null || sessionDetails === void 0 ? void 0 : sessionDetails.acceptRequests) ? '✅ Accepting' : '❌ Closed'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Auto-approval: \",\n                                                    (sessionDetails === null || sessionDetails === void 0 ? void 0 : sessionDetails.autoApproval) ? '✅ On' : '❌ Off'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                className: \"bg-white/5 border-white/10 p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-white mb-2\",\n                                        children: \"Song Requests (Live)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/80\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Total Requests: \",\n                                                    (sessionRequests === null || sessionRequests === void 0 ? void 0 : sessionRequests.length) || 0\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Expected: \",\n                                                    requestCount\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Pending: \",\n                                                    (sessionRequests === null || sessionRequests === void 0 ? void 0 : sessionRequests.filter((r)=>r.status === 'pending').length) || 0\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Approved: \",\n                                                    (sessionRequests === null || sessionRequests === void 0 ? void 0 : sessionRequests.filter((r)=>r.status === 'approved').length) || 0\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Played: \",\n                                                    (sessionRequests === null || sessionRequests === void 0 ? void 0 : sessionRequests.filter((r)=>r.status === 'played').length) || 0\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            testResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold text-white mb-4\",\n                        children: \"Real-time Test Results\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3 max-h-96 overflow-y-auto\",\n                        children: testResults.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: getStatusIcon(result.status)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-white\",\n                                                    children: result.test\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm \".concat(getStatusColor(result.status)),\n                                                    children: result.message\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-white/40 mt-1\",\n                                                    children: new Date(result.timestamp).toLocaleTimeString()\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 17\n                                }, this)\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                lineNumber: 236,\n                columnNumber: 9\n            }, this),\n            sessionAnalytics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold text-white mb-4\",\n                        children: \"\\uD83D\\uDCCA Live Session Analytics\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: sessionAnalytics.totalRequests\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/60\",\n                                        children: \"Total Requests\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: sessionAnalytics.approvedRequests\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/60\",\n                                        children: \"Approved\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: sessionAnalytics.playedRequests\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/60\",\n                                        children: \"Played\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: sessionAnalytics.uniqueRequesters\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/60\",\n                                        children: \"Unique Users\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n                lineNumber: 261,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/RealtimeTestSuite.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_s(RealtimeTestSuite, \"qILqJiQpzDU7KvJSiWp/HCV68JY=\", false, function() {\n    return [\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation\n    ];\n});\n_c = RealtimeTestSuite;\nvar _c;\n$RefreshReg$(_c, \"RealtimeTestSuite\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/testing/RealtimeTestSuite.tsx\n"));

/***/ })

});