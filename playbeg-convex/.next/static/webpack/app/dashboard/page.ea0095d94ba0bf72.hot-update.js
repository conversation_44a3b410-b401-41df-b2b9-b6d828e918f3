"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @convex-dev/auth/react */ \"(app-pages-browser)/./node_modules/@convex-dev/auth/dist/react/index.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _components_dashboard_DJProfileCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/DJProfileCard */ \"(app-pages-browser)/./components/dashboard/DJProfileCard.tsx\");\n/* harmony import */ var _components_dashboard_SessionManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/SessionManager */ \"(app-pages-browser)/./components/dashboard/SessionManager.tsx\");\n/* harmony import */ var _components_SongRequestManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/SongRequestManager */ \"(app-pages-browser)/./components/SongRequestManager.tsx\");\n/* harmony import */ var _components_dashboard_NotificationCenter__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/NotificationCenter */ \"(app-pages-browser)/./components/dashboard/NotificationCenter.tsx\");\n/* harmony import */ var _components_PassPurchase__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/PassPurchase */ \"(app-pages-browser)/./components/PassPurchase.tsx\");\n/* harmony import */ var _components_SongSearchInput__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/SongSearchInput */ \"(app-pages-browser)/./components/SongSearchInput.tsx\");\n/* harmony import */ var _components_testing_IntegrationTestSuite__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/testing/IntegrationTestSuite */ \"(app-pages-browser)/./components/testing/IntegrationTestSuite.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const { isAuthenticated, isLoading } = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useConvexAuth)();\n    const { signOut } = (0,_convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_13__.useAuthActions)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('overview');\n    // Get current user and their DJ profile\n    const currentUser = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.getCurrentUser);\n    const userWithProfile = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.getCurrentUserWithProfile);\n    const djProfile = userWithProfile === null || userWithProfile === void 0 ? void 0 : userWithProfile.djProfile;\n    // Get user status\n    const userStatus = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.checkUserStatus);\n    // Get DJ profiles list (for testing)\n    const allDjProfiles = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.djProfiles.listDjProfiles, {\n        limit: 10,\n        onboardingStatus: true\n    });\n    // Get active sessions for the current user\n    const activeSessions = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.sessions.getCurrentUserSessions, currentUser ? {\n        activeOnly: true,\n        limit: 10\n    } : \"skip\");\n    // Get the first active session for song request management\n    const activeSession = activeSessions && activeSessions.length > 0 ? activeSessions[0] : null;\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated) {\n                router.push(\"/signin\");\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    const handleSignOut = async ()=>{\n        await signOut();\n        router.push(\"/signin\");\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-lg\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null; // Will redirect to signin\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gray-900/50 backdrop-blur-md border-b border-purple-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-white\",\n                                        children: \"PlayBeg\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300\",\n                                        children: [\n                                            \"Welcome, \",\n                                            (currentUser === null || currentUser === void 0 ? void 0 : currentUser.name) || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.email) || \"DJ\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_NotificationCenter__WEBPACK_IMPORTED_MODULE_9__.NotificationCenter, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: handleSignOut,\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"text-gray-300 hover:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Sign Out\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-white mb-2\",\n                                children: \"DJ Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Manage your sessions, song requests, and audience engagement\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 border-b border-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('overview'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'overview' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('requests'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'requests' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Song Requests\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('billing'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'billing' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Billing & Passes\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('analytics'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'analytics' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('spotify-test'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'spotify-test' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83C\\uDFB5 Spotify Test\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('integration-test'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'integration-test' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83E\\uDDEA Integration Test\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DJProfileCard__WEBPACK_IMPORTED_MODULE_6__.DJProfileCard, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SessionManager__WEBPACK_IMPORTED_MODULE_7__.SessionManager, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'requests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: activeSession ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SongRequestManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            sessionId: activeSession._id\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: \"\\uD83C\\uDFB5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white mb-2\",\n                                    children: \"No Active Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-6\",\n                                    children: \"You need to have an active session to manage song requests.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: ()=>setActiveTab('overview'),\n                                    className: \"bg-purple-600 hover:bg-purple-700 text-white\",\n                                    children: \"Create a Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'billing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PassPurchase__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'analytics' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Analytics Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl mb-4\",\n                                        children: \"\\uD83D\\uDCCA\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Detailed analytics coming soon...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'spotify-test' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-4\",\n                                children: \"\\uD83C\\uDFB5 Spotify Integration Test\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-6\",\n                                children: \"Test the Spotify search functionality to verify the integration is working properly.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SongSearchInput__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    onSongSelect: (song)=>{\n                                        console.log('Selected song:', song);\n                                        alert(\"Selected: \".concat(song.title, \" by \").concat(song.artist));\n                                    },\n                                    placeholder: \"Search for a song on Spotify...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'integration-test' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_testing_IntegrationTestSuite__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Quick Stats\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                            children: \"View Full Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: (allDjProfiles === null || allDjProfiles === void 0 ? void 0 : allDjProfiles.length) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Total DJs\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Active Sessions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Song Requests\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: (userStatus === null || userStatus === void 0 ? void 0 : userStatus.isAuthenticated) ? '100%' : '0%'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"System Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"N2GOEDHqEQMaB1gROcBEnLiv8FE=\", false, function() {\n    return [\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useConvexAuth,\n        _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_13__.useAuthActions,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/testing/IntegrationTestSuite.tsx":
/*!*****************************************************!*\
  !*** ./components/testing/IntegrationTestSuite.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IntegrationTestSuite)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @convex-dev/auth/react */ \"(app-pages-browser)/./node_modules/@convex-dev/auth/dist/react/index.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction IntegrationTestSuite() {\n    _s();\n    const { isAuthenticated, isLoading } = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useConvexAuth)();\n    const { signIn, signOut } = (0,_convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_7__.useAuthActions)();\n    const [testResults, setTestResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testEmail, setTestEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('<EMAIL>');\n    const [testPassword, setTestPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('testpass123');\n    // Queries and mutations\n    const currentUser = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.users.getCurrentUser);\n    const userWithProfile = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.users.getCurrentUserWithProfile);\n    const createDjProfile = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.djProfiles.createDjProfile);\n    const completeOnboarding = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.djProfiles.completeOnboarding);\n    const createSession = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.createSession);\n    const createSongRequest = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.songRequests.createSongRequest);\n    const activateSession = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.sessions.activateSession);\n    const updateTestResult = (step, status, message, data)=>{\n        setTestResults((prev)=>{\n            const existing = prev.find((r)=>r.step === step);\n            if (existing) {\n                return prev.map((r)=>r.step === step ? {\n                        ...r,\n                        status,\n                        message,\n                        data\n                    } : r);\n            } else {\n                return [\n                    ...prev,\n                    {\n                        step,\n                        status,\n                        message,\n                        data\n                    }\n                ];\n            }\n        });\n    };\n    const runIntegrationTest = async ()=>{\n        setIsRunning(true);\n        setTestResults([]);\n        try {\n            var _userWithProfile_djProfile;\n            // Step 1: Test Authentication\n            updateTestResult('1. Authentication', 'running', 'Testing user authentication...');\n            if (!isAuthenticated) {\n                updateTestResult('1. Authentication', 'error', 'User not authenticated. Please sign in first.');\n                return;\n            }\n            updateTestResult('1. Authentication', 'success', 'User authenticated successfully', {\n                user: currentUser\n            });\n            // Step 2: Test User Profile Retrieval\n            updateTestResult('2. User Profile', 'running', 'Retrieving user profile...');\n            if (!currentUser) {\n                updateTestResult('2. User Profile', 'error', 'Failed to retrieve current user');\n                return;\n            }\n            updateTestResult('2. User Profile', 'success', 'User profile retrieved', {\n                userId: currentUser._id,\n                email: currentUser.email\n            });\n            // Step 3: Test DJ Profile Creation/Retrieval\n            updateTestResult('3. DJ Profile', 'running', 'Checking DJ profile...');\n            let djProfile = userWithProfile === null || userWithProfile === void 0 ? void 0 : userWithProfile.djProfile;\n            if (!djProfile) {\n                try {\n                    const profileId = await createDjProfile({\n                        displayName: \"Test DJ \".concat(Date.now())\n                    });\n                    updateTestResult('3. DJ Profile', 'success', 'DJ profile created', {\n                        profileId\n                    });\n                } catch (error) {\n                    if (error.message.includes('already exists')) {\n                        updateTestResult('3. DJ Profile', 'success', 'DJ profile already exists');\n                    } else {\n                        updateTestResult('3. DJ Profile', 'error', \"Failed to create DJ profile: \".concat(error.message));\n                        return;\n                    }\n                }\n            } else {\n                updateTestResult('3. DJ Profile', 'success', 'DJ profile exists', {\n                    profileId: djProfile._id,\n                    displayName: djProfile.displayName,\n                    onboarding: djProfile.completedOnboarding\n                });\n            }\n            // Step 4: Test Onboarding Completion\n            updateTestResult('4. Onboarding', 'running', 'Checking onboarding status...');\n            if (!(userWithProfile === null || userWithProfile === void 0 ? void 0 : (_userWithProfile_djProfile = userWithProfile.djProfile) === null || _userWithProfile_djProfile === void 0 ? void 0 : _userWithProfile_djProfile.completedOnboarding)) {\n                try {\n                    await completeOnboarding({\n                        displayName: \"Test DJ \".concat(Date.now())\n                    });\n                    updateTestResult('4. Onboarding', 'success', 'Onboarding completed');\n                } catch (error) {\n                    if (error.message.includes('already completed')) {\n                        updateTestResult('4. Onboarding', 'success', 'Onboarding already completed');\n                    } else {\n                        updateTestResult('4. Onboarding', 'error', \"Failed to complete onboarding: \".concat(error.message));\n                        return;\n                    }\n                }\n            } else {\n                updateTestResult('4. Onboarding', 'success', 'Onboarding already completed');\n            }\n            // Step 5: Test Session Creation\n            updateTestResult('5. Session Creation', 'running', 'Creating test session...');\n            try {\n                const sessionId = await createSession({\n                    name: \"Integration Test Session \".concat(Date.now()),\n                    acceptRequests: true,\n                    autoApproval: true,\n                    maxRequestsPerUser: 5,\n                    timeframeMinutes: 60\n                });\n                updateTestResult('5. Session Creation', 'success', 'Session created successfully', {\n                    sessionId\n                });\n                // Step 6: Test Session Activation\n                updateTestResult('6. Session Activation', 'running', 'Activating session...');\n                try {\n                    await activateSession({\n                        sessionId\n                    });\n                    updateTestResult('6. Session Activation', 'success', 'Session activated successfully');\n                    // Step 7: Test Song Request Creation\n                    updateTestResult('7. Song Request', 'running', 'Creating test song request...');\n                    try {\n                        const requestResult = await createSongRequest({\n                            sessionId,\n                            songTitle: 'Test Song',\n                            artistName: 'Test Artist',\n                            requesterName: 'Integration Test User',\n                            requesterIp: '127.0.0.1'\n                        });\n                        updateTestResult('7. Song Request', 'success', 'Song request created successfully', requestResult);\n                        // All tests passed\n                        updateTestResult('8. Integration Complete', 'success', 'All integration tests passed! 🎉');\n                    } catch (error) {\n                        updateTestResult('7. Song Request', 'error', \"Failed to create song request: \".concat(error.message));\n                    }\n                } catch (error) {\n                    updateTestResult('6. Session Activation', 'error', \"Failed to activate session: \".concat(error.message));\n                }\n            } catch (error) {\n                updateTestResult('5. Session Creation', 'error', \"Failed to create session: \".concat(error.message));\n            }\n        } catch (error) {\n            updateTestResult('Test Error', 'error', \"Unexpected error: \".concat(error.message));\n        } finally{\n            setIsRunning(false);\n        }\n    };\n    const handleSignIn = async ()=>{\n        try {\n            const formData = new FormData();\n            formData.set(\"email\", testEmail);\n            formData.set(\"password\", testPassword);\n            formData.set(\"flow\", \"signIn\");\n            await signIn(\"password\", formData);\n        } catch (error) {\n            updateTestResult('Sign In', 'error', \"Sign in failed: \".concat(error.message));\n        }\n    };\n    const handleSignOut = async ()=>{\n        await signOut();\n        setTestResults([]);\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'pending':\n                return '⏳';\n            case 'running':\n                return '🔄';\n            case 'success':\n                return '✅';\n            case 'error':\n                return '❌';\n            default:\n                return '⏳';\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'pending':\n                return 'text-gray-400';\n            case 'running':\n                return 'text-blue-400';\n            case 'success':\n                return 'text-green-400';\n            case 'error':\n                return 'text-red-400';\n            default:\n                return 'text-gray-400';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-4\",\n                        children: \"\\uD83E\\uDDEA Integration Test Suite\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/80 mb-6\",\n                        children: \"Test the complete user workflow from authentication to song requests.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this),\n                    !isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-white mb-2\",\n                                                children: \"Test Email\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                type: \"email\",\n                                                value: testEmail,\n                                                onChange: (e)=>setTestEmail(e.target.value),\n                                                className: \"bg-white/20 border-white/30 text-white\",\n                                                placeholder: \"<EMAIL>\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-white mb-2\",\n                                                children: \"Test Password\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                type: \"password\",\n                                                value: testPassword,\n                                                onChange: (e)=>setTestPassword(e.target.value),\n                                                className: \"bg-white/20 border-white/30 text-white\",\n                                                placeholder: \"testpass123\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleSignIn,\n                                disabled: isLoading,\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white\",\n                                children: isLoading ? 'Signing In...' : 'Sign In for Testing'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white\",\n                                        children: [\n                                            \"Authenticated as: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold\",\n                                                children: currentUser === null || currentUser === void 0 ? void 0 : currentUser.email\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 35\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: handleSignOut,\n                                        variant: \"outline\",\n                                        className: \"text-white border-white/30 hover:bg-white/10\",\n                                        children: \"Sign Out\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: runIntegrationTest,\n                                disabled: isRunning,\n                                className: \"w-full bg-green-600 hover:bg-green-700 text-white\",\n                                children: isRunning ? 'Running Tests...' : 'Run Integration Test'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this),\n            testResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold text-white mb-4\",\n                        children: \"Test Results\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: testResults.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: getStatusIcon(result.status)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-white\",\n                                                    children: result.step\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm \".concat(getStatusColor(result.status)),\n                                                    children: result.message\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 21\n                                                }, this),\n                                                result.data && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                                    className: \"mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                            className: \"text-xs text-white/60 cursor-pointer\",\n                                                            children: \"View Data\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"text-xs text-white/80 mt-1 bg-black/20 p-2 rounded overflow-auto\",\n                                                            children: JSON.stringify(result.data, null, 2)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 17\n                                }, this)\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n                lineNumber: 282,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/testing/IntegrationTestSuite.tsx\",\n        lineNumber: 216,\n        columnNumber: 5\n    }, this);\n}\n_s(IntegrationTestSuite, \"yOzTAO9R8hQaZvsn58ud6oOBtio=\", false, function() {\n    return [\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useConvexAuth,\n        _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_7__.useAuthActions,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation\n    ];\n});\n_c = IntegrationTestSuite;\nvar _c;\n$RefreshReg$(_c, \"IntegrationTestSuite\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/testing/IntegrationTestSuite.tsx\n"));

/***/ })

});