"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @convex-dev/auth/react */ \"(app-pages-browser)/./node_modules/@convex-dev/auth/dist/react/index.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _components_dashboard_DJProfileCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/DJProfileCard */ \"(app-pages-browser)/./components/dashboard/DJProfileCard.tsx\");\n/* harmony import */ var _components_dashboard_SessionManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/SessionManager */ \"(app-pages-browser)/./components/dashboard/SessionManager.tsx\");\n/* harmony import */ var _components_SongRequestManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/SongRequestManager */ \"(app-pages-browser)/./components/SongRequestManager.tsx\");\n/* harmony import */ var _components_dashboard_NotificationCenter__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/NotificationCenter */ \"(app-pages-browser)/./components/dashboard/NotificationCenter.tsx\");\n/* harmony import */ var _components_PassPurchase__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/PassPurchase */ \"(app-pages-browser)/./components/PassPurchase.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const { isAuthenticated, isLoading } = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useConvexAuth)();\n    const { signOut } = (0,_convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_11__.useAuthActions)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('overview');\n    // Get current user and their DJ profile\n    const currentUser = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.getCurrentUser);\n    const userWithProfile = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.getCurrentUserWithProfile);\n    const djProfile = userWithProfile === null || userWithProfile === void 0 ? void 0 : userWithProfile.djProfile;\n    // Get user status\n    const userStatus = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.checkUserStatus);\n    // Get DJ profiles list (for testing)\n    const allDjProfiles = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.djProfiles.listDjProfiles, {\n        limit: 10,\n        onboardingStatus: true\n    });\n    // Get active sessions for the current user\n    const activeSessions = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.sessions.getCurrentUserSessions, currentUser ? {\n        activeOnly: true,\n        limit: 10\n    } : \"skip\");\n    // Get the first active session for song request management\n    const activeSession = activeSessions && activeSessions.length > 0 ? activeSessions[0] : null;\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated) {\n                router.push(\"/signin\");\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    const handleSignOut = async ()=>{\n        await signOut();\n        router.push(\"/signin\");\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-lg\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null; // Will redirect to signin\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gray-900/50 backdrop-blur-md border-b border-purple-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-white\",\n                                        children: \"PlayBeg\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300\",\n                                        children: [\n                                            \"Welcome, \",\n                                            (currentUser === null || currentUser === void 0 ? void 0 : currentUser.name) || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.email) || \"DJ\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_NotificationCenter__WEBPACK_IMPORTED_MODULE_9__.NotificationCenter, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: handleSignOut,\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"text-gray-300 hover:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Sign Out\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-white mb-2\",\n                                children: \"DJ Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Manage your sessions, song requests, and audience engagement\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 border-b border-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('overview'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'overview' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('requests'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'requests' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Song Requests\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('billing'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'billing' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Billing & Passes\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('analytics'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'analytics' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DJProfileCard__WEBPACK_IMPORTED_MODULE_6__.DJProfileCard, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SessionManager__WEBPACK_IMPORTED_MODULE_7__.SessionManager, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'requests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: activeSession ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SongRequestManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            sessionId: activeSession._id\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: \"\\uD83C\\uDFB5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white mb-2\",\n                                    children: \"No Active Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-6\",\n                                    children: \"You need to have an active session to manage song requests.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: ()=>setActiveTab('overview'),\n                                    className: \"bg-purple-600 hover:bg-purple-700 text-white\",\n                                    children: \"Create a Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'billing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PassPurchase__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'analytics' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Analytics Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl mb-4\",\n                                        children: \"\\uD83D\\uDCCA\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Detailed analytics coming soon...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Quick Stats\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                            children: \"View Full Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: (allDjProfiles === null || allDjProfiles === void 0 ? void 0 : allDjProfiles.length) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Total DJs\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Active Sessions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Song Requests\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: (userStatus === null || userStatus === void 0 ? void 0 : userStatus.isAuthenticated) ? '100%' : '0%'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"System Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"N2GOEDHqEQMaB1gROcBEnLiv8FE=\", false, function() {\n    return [\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useConvexAuth,\n        _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_11__.useAuthActions,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});