"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @convex-dev/auth/react */ \"(app-pages-browser)/./node_modules/@convex-dev/auth/dist/react/index.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _components_dashboard_DJProfileCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/DJProfileCard */ \"(app-pages-browser)/./components/dashboard/DJProfileCard.tsx\");\n/* harmony import */ var _components_dashboard_SessionManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/SessionManager */ \"(app-pages-browser)/./components/dashboard/SessionManager.tsx\");\n/* harmony import */ var _components_NotificationCenter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/NotificationCenter */ \"(app-pages-browser)/./components/NotificationCenter.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const { isAuthenticated, isLoading } = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useConvexAuth)();\n    const { signOut } = (0,_convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_9__.useAuthActions)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Get current user and their DJ profile\n    const currentUser = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.getCurrentUser);\n    const userWithProfile = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.getCurrentUserWithProfile);\n    const djProfile = userWithProfile === null || userWithProfile === void 0 ? void 0 : userWithProfile.djProfile;\n    // Get user status\n    const userStatus = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.checkUserStatus);\n    // Get DJ profiles list (for testing)\n    const allDjProfiles = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.djProfiles.listDjProfiles, {\n        limit: 10,\n        onboardingStatus: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated) {\n                router.push(\"/signin\");\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    const handleSignOut = async ()=>{\n        await signOut();\n        router.push(\"/signin\");\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-lg\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 48,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null; // Will redirect to signin\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gray-900/50 backdrop-blur-md border-b border-purple-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-white\",\n                                        children: \"PlayBeg\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300\",\n                                        children: [\n                                            \"Welcome, \",\n                                            (currentUser === null || currentUser === void 0 ? void 0 : currentUser.name) || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.email) || \"DJ\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationCenter__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        userId: currentUser._id\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: handleSignOut,\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"text-gray-300 hover:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Sign Out\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-white mb-2\",\n                                children: \"DJ Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Manage your sessions, song requests, and audience engagement\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DJProfileCard__WEBPACK_IMPORTED_MODULE_6__.DJProfileCard, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SessionManager__WEBPACK_IMPORTED_MODULE_7__.SessionManager, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Quick Stats\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                            children: \"View Full Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: (allDjProfiles === null || allDjProfiles === void 0 ? void 0 : allDjProfiles.length) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Total DJs\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Active Sessions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Song Requests\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: (userStatus === null || userStatus === void 0 ? void 0 : userStatus.isAuthenticated) ? '100%' : '0%'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"System Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"DwQP0PUhZRyQcMyKyyPfDlLiGPQ=\", false, function() {\n    return [\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useConvexAuth,\n        _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_9__.useAuthActions,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/NotificationCenter.tsx":
/*!*******************************************!*\
  !*** ./components/NotificationCenter.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationCenter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction NotificationCenter(param) {\n    let { userId } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastNotificationCount, setLastNotificationCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Queries\n    const notifications = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.notifications.getUserNotifications, {\n        userId,\n        limit: 20\n    });\n    const unreadCount = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.notifications.getUnreadCount, {\n        userId\n    });\n    // Mutations\n    const markAsRead = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.notifications.markAsRead);\n    const markAllAsRead = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.notifications.markAllAsRead);\n    // Show browser notification for new notifications\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationCenter.useEffect\": ()=>{\n            if (unreadCount && unreadCount > lastNotificationCount && lastNotificationCount > 0) {\n                // Request permission for browser notifications\n                if (Notification.permission === 'granted') {\n                    new Notification('PlayBeg', {\n                        body: \"You have \".concat(unreadCount - lastNotificationCount, \" new notification(s)\"),\n                        icon: '/convex.svg'\n                    });\n                } else if (Notification.permission !== 'denied') {\n                    Notification.requestPermission();\n                }\n            }\n            if (unreadCount !== undefined) {\n                setLastNotificationCount(unreadCount);\n            }\n        }\n    }[\"NotificationCenter.useEffect\"], [\n        unreadCount,\n        lastNotificationCount\n    ]);\n    const handleNotificationClick = async (notificationId)=>{\n        try {\n            await markAsRead({\n                notificationId\n            });\n        } catch (error) {\n            console.error('Failed to mark notification as read:', error);\n        }\n    };\n    const handleMarkAllAsRead = async ()=>{\n        try {\n            await markAllAsRead({\n                userId\n            });\n        } catch (error) {\n            console.error('Failed to mark all notifications as read:', error);\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'urgent':\n                return 'border-l-red-500 bg-red-600/10';\n            case 'high':\n                return 'border-l-orange-500 bg-orange-600/10';\n            case 'medium':\n                return 'border-l-yellow-500 bg-yellow-600/10';\n            case 'low':\n                return 'border-l-blue-500 bg-blue-600/10';\n            default:\n                return 'border-l-gray-500 bg-gray-600/10';\n        }\n    };\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case 'song_request':\n                return '🎵';\n            case 'session_status':\n                return '🎛️';\n            case 'audience_interaction':\n                return '👥';\n            case 'system_alert':\n                return '⚠️';\n            case 'engagement_milestone':\n                return '🎉';\n            default:\n                return '📢';\n        }\n    };\n    const formatTimeAgo = (timestamp)=>{\n        const now = Date.now();\n        const diff = now - timestamp;\n        const minutes = Math.floor(diff / (1000 * 60));\n        const hours = Math.floor(minutes / 60);\n        const days = Math.floor(hours / 24);\n        if (days > 0) return \"\".concat(days, \"d ago\");\n        if (hours > 0) return \"\".concat(hours, \"h ago\");\n        if (minutes > 0) return \"\".concat(minutes, \"m ago\");\n        return 'Just now';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                onClick: ()=>setIsOpen(!isOpen),\n                variant: \"outline\",\n                className: \"relative text-white border-white/30 hover:bg-white/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg\",\n                        children: \"\\uD83D\\uDD14\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    unreadCount && unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                        children: unreadCount > 99 ? '99+' : unreadCount\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 top-full mt-2 w-96 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"bg-white/10 backdrop-blur-md border-white/20 max-h-96 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 17\n                                    }, this),\n                                    unreadCount && unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: handleMarkAllAsRead,\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        className: \"text-white border-white/30 hover:bg-white/10\",\n                                        children: \"Mark All Read\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-h-80 overflow-y-auto\",\n                            children: notifications && notifications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"divide-y divide-white/10\",\n                                children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onClick: ()=>handleNotificationClick(notification._id),\n                                        className: \"p-4 cursor-pointer hover:bg-white/5 transition-colors border-l-4 \".concat(getPriorityColor(notification.priority), \" \").concat(!notification.isRead ? 'bg-white/5' : ''),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    children: getNotificationIcon(notification.type)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-sm font-medium \".concat(!notification.isRead ? 'text-white' : 'text-white/80'),\n                                                                    children: notification.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                                                                    lineNumber: 152,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                !notification.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm mt-1 \".concat(!notification.isRead ? 'text-white/90' : 'text-white/70'),\n                                                            children: notification.message\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-white/60\",\n                                                                    children: formatTimeAgo(notification.createdAt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                                                                    lineNumber: 167,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs px-2 py-1 rounded \".concat(notification.priority === 'urgent' ? 'bg-red-600/20 text-red-300' : notification.priority === 'high' ? 'bg-orange-600/20 text-orange-300' : notification.priority === 'medium' ? 'bg-yellow-600/20 text-yellow-300' : 'bg-blue-600/20 text-blue-300'),\n                                                                    children: notification.priority\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                                                                    lineNumber: 170,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, notification._id, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl mb-4\",\n                                        children: \"\\uD83D\\uDD14\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/80\",\n                                        children: \"No notifications yet\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this),\n                        notifications && notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-t border-white/20 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setIsOpen(false),\n                                size: \"sm\",\n                                variant: \"outline\",\n                                className: \"text-white border-white/30 hover:bg-white/10\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40\",\n                onClick: ()=>setIsOpen(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n                lineNumber: 211,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/NotificationCenter.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationCenter, \"lLFbjjzdaeSbmlYoGqV6AwTqN9I=\", false, function() {\n    return [\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation\n    ];\n});\n_c = NotificationCenter;\nvar _c;\n$RefreshReg$(_c, \"NotificationCenter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/NotificationCenter.tsx\n"));

/***/ })

});