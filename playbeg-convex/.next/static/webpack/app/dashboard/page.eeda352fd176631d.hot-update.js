"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/dashboard/SessionManager.tsx":
/*!*************************************************!*\
  !*** ./components/dashboard/SessionManager.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Music,Pause,Play,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ SessionManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SessionManager() {\n    _s();\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    // Get current user with profile\n    const userWithProfile = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.users.getCurrentUserWithProfile);\n    const currentUser = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.users.getCurrentUser);\n    const djProfile = userWithProfile === null || userWithProfile === void 0 ? void 0 : userWithProfile.djProfile;\n    // Get real user sessions\n    const userSessions = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.sessions.getUserSessions, currentUser ? {\n        userId: currentUser._id\n    } : \"skip\");\n    // Get session analytics\n    const sessionStats = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.analytics.getUserSessionStats, currentUser ? {\n        userId: currentUser._id\n    } : \"skip\");\n    // Mutations\n    const createSession = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_2__.api.sessions.createSession);\n    const handleCreateSession = async ()=>{\n        setIsCreating(true);\n        try {\n            const sessionId = await createSession({\n                name: \"Session \".concat(new Date().toLocaleDateString()),\n                acceptRequests: true,\n                autoApproval: false,\n                maxRequestsPerUser: 5,\n                timeframeMinutes: 60,\n                weddingModeEnabled: false\n            });\n            console.log(\"Session created:\", sessionId);\n        } catch (error) {\n            console.error(\"Failed to create session:\", error);\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    const handleManageActive = ()=>{\n        // Navigate to sessions tab\n        const event = new CustomEvent('switchTab', {\n            detail: 'session-management'\n        });\n        window.dispatchEvent(event);\n    };\n    const handleViewAnalytics = ()=>{\n        // Navigate to analytics tab\n        const event = new CustomEvent('switchTab', {\n            detail: 'analytics'\n        });\n        window.dispatchEvent(event);\n    };\n    if (!djProfile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"bg-gray-800/50 border-purple-500/20\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"text-white flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                \"Session Manager\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                            className: \"text-gray-400\",\n                            children: \"Create a DJ profile first to manage sessions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-4\",\n                                children: \"DJ profile required\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-sm\",\n                                children: \"Create your DJ profile to start managing sessions and receiving song requests.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this);\n    }\n    // Process real session data\n    const activeSessions = (userSessions === null || userSessions === void 0 ? void 0 : userSessions.filter((s)=>s.active)) || [];\n    const recentSessions = (userSessions === null || userSessions === void 0 ? void 0 : userSessions.slice(0, 3)) || [];\n    const totalRequests = (sessionStats === null || sessionStats === void 0 ? void 0 : sessionStats.totalRequests) || 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"bg-gray-800/50 border-purple-500/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: \"Create Session\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-6 h-6 text-purple-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4 text-sm\",\n                                    children: \"Start a new DJ session to receive song requests\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleCreateSession,\n                                    disabled: isCreating,\n                                    className: \"w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700\",\n                                    children: isCreating ? \"Creating...\" : \"New Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"bg-gray-800/50 border-purple-500/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: \"Active Sessions\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full \".concat(activeSessions.length > 0 ? 'bg-green-400 animate-pulse' : 'bg-gray-400')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4 text-sm\",\n                                    children: [\n                                        activeSessions.length,\n                                        \" active session\",\n                                        activeSessions.length !== 1 ? 's' : ''\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    className: \"w-full border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    disabled: activeSessions.length === 0,\n                                    onClick: handleManageActive,\n                                    children: activeSessions.length > 0 ? \"Manage Active\" : \"No Active Sessions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"bg-gray-800/50 border-purple-500/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: \"Total Requests\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-6 h-6 text-purple-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4 text-sm\",\n                                    children: [\n                                        mockSessions.reduce((sum, s)=>sum + s.requestCount, 0),\n                                        \" total requests received\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    className: \"w-full border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"View Analytics\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"bg-gray-800/50 border-purple-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"text-white flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Recent Sessions\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                className: \"text-gray-400\",\n                                children: \"Your latest DJ sessions and their status\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: recentSessions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: recentSessions.map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 bg-gray-700/50 rounded-lg border border-gray-600/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 rounded-full flex items-center justify-center \".concat(session.status === 'active' ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'),\n                                                    children: session.status === 'active' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 54\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 85\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-white\",\n                                                            children: session.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                                            lineNumber: 188,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        new Date(session.createdAt).toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                                            lineNumber: 192,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        session.requestCount,\n                                                                        \"/\",\n                                                                        session.maxRequests,\n                                                                        \" requests\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: session.status === 'active' ? 'default' : 'secondary',\n                                                    children: session.status === 'active' ? 'Live' : 'Ended'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    variant: \"outline\",\n                                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                    children: session.status === 'active' ? 'Manage' : 'View'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, session.id, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Music_Pause_Play_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4\",\n                                    children: \"No sessions yet\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-sm mb-6\",\n                                    children: \"Create your first session to start receiving song requests from your audience.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleCreateSession,\n                                    disabled: isCreating,\n                                    className: \"bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700\",\n                                    children: isCreating ? \"Creating...\" : \"Create Your First Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/dashboard/SessionManager.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionManager, \"pdjhA4QfLYDxJXRghdBisnZ3Gy8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useMutation\n    ];\n});\n_c = SessionManager;\nvar _c;\n$RefreshReg$(_c, \"SessionManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvZGFzaGJvYXJkL1Nlc3Npb25NYW5hZ2VyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRXFEO0FBQ1A7QUFDRTtBQUNpRDtBQUNuRDtBQUN1RDtBQUNwRTtBQUNXO0FBRXJDLFNBQVNtQjs7SUFDZCxNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR0osK0NBQVFBLENBQUM7SUFDN0MsTUFBTUssU0FBU0osMERBQVNBO0lBRXhCLGdDQUFnQztJQUNoQyxNQUFNSyxrQkFBa0J2QixzREFBUUEsQ0FBQ0Usc0RBQUdBLENBQUNzQixLQUFLLENBQUNDLHlCQUF5QjtJQUNwRSxNQUFNQyxjQUFjMUIsc0RBQVFBLENBQUNFLHNEQUFHQSxDQUFDc0IsS0FBSyxDQUFDRyxjQUFjO0lBQ3JELE1BQU1DLFlBQVlMLDRCQUFBQSxzQ0FBQUEsZ0JBQWlCSyxTQUFTO0lBRTVDLHlCQUF5QjtJQUN6QixNQUFNQyxlQUFlN0Isc0RBQVFBLENBQzNCRSxzREFBR0EsQ0FBQzRCLFFBQVEsQ0FBQ0MsZUFBZSxFQUM1QkwsY0FBYztRQUFFTSxRQUFRTixZQUFZTyxHQUFHO0lBQUMsSUFBSTtJQUc5Qyx3QkFBd0I7SUFDeEIsTUFBTUMsZUFBZWxDLHNEQUFRQSxDQUMzQkUsc0RBQUdBLENBQUNpQyxTQUFTLENBQUNDLG1CQUFtQixFQUNqQ1YsY0FBYztRQUFFTSxRQUFRTixZQUFZTyxHQUFHO0lBQUMsSUFBSTtJQUc5QyxZQUFZO0lBQ1osTUFBTUksZ0JBQWdCcEMseURBQVdBLENBQUNDLHNEQUFHQSxDQUFDNEIsUUFBUSxDQUFDTyxhQUFhO0lBRTVELE1BQU1DLHNCQUFzQjtRQUMxQmpCLGNBQWM7UUFDZCxJQUFJO1lBQ0YsTUFBTWtCLFlBQVksTUFBTUYsY0FBYztnQkFDcENHLE1BQU0sV0FBMkMsT0FBaEMsSUFBSUMsT0FBT0Msa0JBQWtCO2dCQUM5Q0MsZ0JBQWdCO2dCQUNoQkMsY0FBYztnQkFDZEMsb0JBQW9CO2dCQUNwQkMsa0JBQWtCO2dCQUNsQkMsb0JBQW9CO1lBQ3RCO1lBQ0FDLFFBQVFDLEdBQUcsQ0FBQyxvQkFBb0JWO1FBQ2xDLEVBQUUsT0FBT1csT0FBTztZQUNkRixRQUFRRSxLQUFLLENBQUMsNkJBQTZCQTtRQUM3QyxTQUFVO1lBQ1I3QixjQUFjO1FBQ2hCO0lBQ0Y7SUFFQSxNQUFNOEIscUJBQXFCO1FBQ3pCLDJCQUEyQjtRQUMzQixNQUFNQyxRQUFRLElBQUlDLFlBQVksYUFBYTtZQUFFQyxRQUFRO1FBQXFCO1FBQzFFQyxPQUFPQyxhQUFhLENBQUNKO0lBQ3ZCO0lBRUEsTUFBTUssc0JBQXNCO1FBQzFCLDRCQUE0QjtRQUM1QixNQUFNTCxRQUFRLElBQUlDLFlBQVksYUFBYTtZQUFFQyxRQUFRO1FBQVk7UUFDakVDLE9BQU9DLGFBQWEsQ0FBQ0o7SUFDdkI7SUFFQSxJQUFJLENBQUN4QixXQUFXO1FBQ2QscUJBQ0UsOERBQUN4QixxREFBSUE7WUFBQ3NELFdBQVU7OzhCQUNkLDhEQUFDbkQsMkRBQVVBOztzQ0FDVCw4REFBQ0MsMERBQVNBOzRCQUFDa0QsV0FBVTs7OENBQ25CLDhEQUFDNUMsc0hBQUtBO29DQUFDNEMsV0FBVTs7Ozs7O2dDQUFpQjs7Ozs7OztzQ0FHcEMsOERBQUNwRCxnRUFBZUE7NEJBQUNvRCxXQUFVO3NDQUFnQjs7Ozs7Ozs7Ozs7OzhCQUk3Qyw4REFBQ3JELDREQUFXQTs4QkFDViw0RUFBQ3NEO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQzVDLHNIQUFLQTtnQ0FBQzRDLFdBQVU7Ozs7OzswQ0FDakIsOERBQUNFO2dDQUFFRixXQUFVOzBDQUFxQjs7Ozs7OzBDQUNsQyw4REFBQ0U7Z0NBQUVGLFdBQVU7MENBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU8vQztJQUVBLDRCQUE0QjtJQUM1QixNQUFNRyxpQkFBaUJoQyxDQUFBQSx5QkFBQUEsbUNBQUFBLGFBQWNpQyxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLE1BQU0sTUFBSyxFQUFFO0lBQ2hFLE1BQU1DLGlCQUFpQnBDLENBQUFBLHlCQUFBQSxtQ0FBQUEsYUFBY3FDLEtBQUssQ0FBQyxHQUFHLE9BQU0sRUFBRTtJQUN0RCxNQUFNQyxnQkFBZ0JqQyxDQUFBQSx5QkFBQUEsbUNBQUFBLGFBQWNpQyxhQUFhLEtBQUk7SUFFckQscUJBQ0UsOERBQUNSO1FBQUlELFdBQVU7OzBCQUViLDhEQUFDQztnQkFBSUQsV0FBVTs7a0NBQ2IsOERBQUN0RCxxREFBSUE7d0JBQUNzRCxXQUFVO2tDQUNkLDRFQUFDckQsNERBQVdBOzRCQUFDcUQsV0FBVTs7OENBQ3JCLDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNVOzRDQUFHVixXQUFVO3NEQUFtQzs7Ozs7O3NEQUNqRCw4REFBQzlDLHNIQUFJQTs0Q0FBQzhDLFdBQVU7Ozs7Ozs7Ozs7Ozs4Q0FFbEIsOERBQUNFO29DQUFFRixXQUFVOzhDQUE2Qjs7Ozs7OzhDQUMxQyw4REFBQ3ZELHlEQUFNQTtvQ0FDTGtFLFNBQVMvQjtvQ0FDVGdDLFVBQVVsRDtvQ0FDVnNDLFdBQVU7OENBRVR0QyxhQUFhLGdCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBS3BDLDhEQUFDaEIscURBQUlBO3dCQUFDc0QsV0FBVTtrQ0FDZCw0RUFBQ3JELDREQUFXQTs0QkFBQ3FELFdBQVU7OzhDQUNyQiw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDVTs0Q0FBR1YsV0FBVTtzREFBbUM7Ozs7OztzREFDakQsOERBQUNDOzRDQUFJRCxXQUFXLHdCQUFpRyxPQUF6RUcsZUFBZVUsTUFBTSxHQUFHLElBQUksK0JBQStCOzs7Ozs7Ozs7Ozs7OENBRXJHLDhEQUFDWDtvQ0FBRUYsV0FBVTs7d0NBQ1ZHLGVBQWVVLE1BQU07d0NBQUM7d0NBQWdCVixlQUFlVSxNQUFNLEtBQUssSUFBSSxNQUFNOzs7Ozs7OzhDQUU3RSw4REFBQ3BFLHlEQUFNQTtvQ0FDTHFFLFNBQVE7b0NBQ1JkLFdBQVU7b0NBQ1ZZLFVBQVVULGVBQWVVLE1BQU0sS0FBSztvQ0FDcENGLFNBQVNsQjs4Q0FFUlUsZUFBZVUsTUFBTSxHQUFHLElBQUksa0JBQWtCOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLckQsOERBQUNuRSxxREFBSUE7d0JBQUNzRCxXQUFVO2tDQUNkLDRFQUFDckQsNERBQVdBOzRCQUFDcUQsV0FBVTs7OENBQ3JCLDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNVOzRDQUFHVixXQUFVO3NEQUFtQzs7Ozs7O3NEQUNqRCw4REFBQzdDLHVIQUFLQTs0Q0FBQzZDLFdBQVU7Ozs7Ozs7Ozs7Ozs4Q0FFbkIsOERBQUNFO29DQUFFRixXQUFVOzt3Q0FDVmUsYUFBYUMsTUFBTSxDQUFDLENBQUNDLEtBQUtaLElBQU1ZLE1BQU1aLEVBQUVhLFlBQVksRUFBRTt3Q0FBRzs7Ozs7Ozs4Q0FFNUQsOERBQUN6RSx5REFBTUE7b0NBQ0xxRSxTQUFRO29DQUNSZCxXQUFVOzhDQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRUCw4REFBQ3RELHFEQUFJQTtnQkFBQ3NELFdBQVU7O2tDQUNkLDhEQUFDbkQsMkRBQVVBOzswQ0FDVCw4REFBQ0MsMERBQVNBO2dDQUFDa0QsV0FBVTs7a0RBQ25CLDhEQUFDM0MsdUhBQVFBO3dDQUFDMkMsV0FBVTs7Ozs7O29DQUFpQjs7Ozs7OzswQ0FHdkMsOERBQUNwRCxnRUFBZUE7Z0NBQUNvRCxXQUFVOzBDQUFnQjs7Ozs7Ozs7Ozs7O2tDQUk3Qyw4REFBQ3JELDREQUFXQTtrQ0FDVDRELGVBQWVNLE1BQU0sR0FBRyxrQkFDdkIsOERBQUNaOzRCQUFJRCxXQUFVO3NDQUNaTyxlQUFlWSxHQUFHLENBQUMsQ0FBQ0Msd0JBQ25CLDhEQUFDbkI7b0NBRUNELFdBQVU7O3NEQUVWLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNDO29EQUFJRCxXQUFXLDJEQUlmLE9BSENvQixRQUFRQyxNQUFNLEtBQUssV0FDZixtQ0FDQTs4REFFSEQsUUFBUUMsTUFBTSxLQUFLLHlCQUFXLDhEQUFDckUsdUhBQUlBO3dEQUFDZ0QsV0FBVTs7Ozs7NkVBQWUsOERBQUMvQyx1SEFBS0E7d0RBQUMrQyxXQUFVOzs7Ozs7Ozs7Ozs4REFFakYsOERBQUNDOztzRUFDQyw4REFBQ3FCOzREQUFHdEIsV0FBVTtzRUFBMEJvQixRQUFRdEMsSUFBSTs7Ozs7O3NFQUNwRCw4REFBQ21COzREQUFJRCxXQUFVOzs4RUFDYiw4REFBQ3VCO29FQUFLdkIsV0FBVTs7c0ZBQ2QsOERBQUMxQyx1SEFBS0E7NEVBQUMwQyxXQUFVOzs7Ozs7d0VBQ2hCLElBQUlqQixLQUFLcUMsUUFBUUksU0FBUyxFQUFFeEMsa0JBQWtCOzs7Ozs7OzhFQUVqRCw4REFBQ3VDO29FQUFLdkIsV0FBVTs7c0ZBQ2QsOERBQUM3Qyx1SEFBS0E7NEVBQUM2QyxXQUFVOzs7Ozs7d0VBQ2hCb0IsUUFBUUYsWUFBWTt3RUFBQzt3RUFBRUUsUUFBUUssV0FBVzt3RUFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFLcEQsOERBQUN4Qjs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNqRCx1REFBS0E7b0RBQUMrRCxTQUFTTSxRQUFRQyxNQUFNLEtBQUssV0FBVyxZQUFZOzhEQUN2REQsUUFBUUMsTUFBTSxLQUFLLFdBQVcsU0FBUzs7Ozs7OzhEQUUxQyw4REFBQzVFLHlEQUFNQTtvREFDTGlGLE1BQUs7b0RBQ0xaLFNBQVE7b0RBQ1JkLFdBQVU7OERBRVRvQixRQUFRQyxNQUFNLEtBQUssV0FBVyxXQUFXOzs7Ozs7Ozs7Ozs7O21DQWxDekNELFFBQVFPLEVBQUU7Ozs7Ozs7OztpREF5Q3JCLDhEQUFDMUI7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDNUMsc0hBQUtBO29DQUFDNEMsV0FBVTs7Ozs7OzhDQUNqQiw4REFBQ0U7b0NBQUVGLFdBQVU7OENBQXFCOzs7Ozs7OENBQ2xDLDhEQUFDRTtvQ0FBRUYsV0FBVTs4Q0FBNkI7Ozs7Ozs4Q0FHMUMsOERBQUN2RCx5REFBTUE7b0NBQ0xrRSxTQUFTL0I7b0NBQ1RnQyxVQUFVbEQ7b0NBQ1ZzQyxXQUFVOzhDQUVUdEMsYUFBYSxnQkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUTlDO0dBN05nQkQ7O1FBRUNELHNEQUFTQTtRQUdBbEIsa0RBQVFBO1FBQ1pBLGtEQUFRQTtRQUlQQSxrREFBUUE7UUFNUkEsa0RBQVFBO1FBTVBDLHFEQUFXQTs7O0tBdEJuQmtCIiwic291cmNlcyI6WyIvVXNlcnMvcm9iZXJ0LmhhbnNlbi9QbGF5QmVnL1BsYXlCZWcvcGxheWJlZy1jb252ZXgvY29tcG9uZW50cy9kYXNoYm9hcmQvU2Vzc2lvbk1hbmFnZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyB1c2VRdWVyeSwgdXNlTXV0YXRpb24gfSBmcm9tIFwiY29udmV4L3JlYWN0XCI7XG5pbXBvcnQgeyBhcGkgfSBmcm9tIFwiQC9jb252ZXgvX2dlbmVyYXRlZC9hcGlcIjtcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCI7XG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2NhcmRcIjtcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9iYWRnZVwiO1xuaW1wb3J0IHsgUGxheSwgUGF1c2UsIFBsdXMsIFVzZXJzLCBNdXNpYywgQ2FsZW5kYXIsIENsb2NrLCBCYXJDaGFydDMsIFNldHRpbmdzIH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIjtcblxuZXhwb3J0IGZ1bmN0aW9uIFNlc3Npb25NYW5hZ2VyKCkge1xuICBjb25zdCBbaXNDcmVhdGluZywgc2V0SXNDcmVhdGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuXG4gIC8vIEdldCBjdXJyZW50IHVzZXIgd2l0aCBwcm9maWxlXG4gIGNvbnN0IHVzZXJXaXRoUHJvZmlsZSA9IHVzZVF1ZXJ5KGFwaS51c2Vycy5nZXRDdXJyZW50VXNlcldpdGhQcm9maWxlKTtcbiAgY29uc3QgY3VycmVudFVzZXIgPSB1c2VRdWVyeShhcGkudXNlcnMuZ2V0Q3VycmVudFVzZXIpO1xuICBjb25zdCBkalByb2ZpbGUgPSB1c2VyV2l0aFByb2ZpbGU/LmRqUHJvZmlsZTtcblxuICAvLyBHZXQgcmVhbCB1c2VyIHNlc3Npb25zXG4gIGNvbnN0IHVzZXJTZXNzaW9ucyA9IHVzZVF1ZXJ5KFxuICAgIGFwaS5zZXNzaW9ucy5nZXRVc2VyU2Vzc2lvbnMsXG4gICAgY3VycmVudFVzZXIgPyB7IHVzZXJJZDogY3VycmVudFVzZXIuX2lkIH0gOiBcInNraXBcIlxuICApO1xuXG4gIC8vIEdldCBzZXNzaW9uIGFuYWx5dGljc1xuICBjb25zdCBzZXNzaW9uU3RhdHMgPSB1c2VRdWVyeShcbiAgICBhcGkuYW5hbHl0aWNzLmdldFVzZXJTZXNzaW9uU3RhdHMsXG4gICAgY3VycmVudFVzZXIgPyB7IHVzZXJJZDogY3VycmVudFVzZXIuX2lkIH0gOiBcInNraXBcIlxuICApO1xuXG4gIC8vIE11dGF0aW9uc1xuICBjb25zdCBjcmVhdGVTZXNzaW9uID0gdXNlTXV0YXRpb24oYXBpLnNlc3Npb25zLmNyZWF0ZVNlc3Npb24pO1xuXG4gIGNvbnN0IGhhbmRsZUNyZWF0ZVNlc3Npb24gPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0SXNDcmVhdGluZyh0cnVlKTtcbiAgICB0cnkge1xuICAgICAgY29uc3Qgc2Vzc2lvbklkID0gYXdhaXQgY3JlYXRlU2Vzc2lvbih7XG4gICAgICAgIG5hbWU6IGBTZXNzaW9uICR7bmV3IERhdGUoKS50b0xvY2FsZURhdGVTdHJpbmcoKX1gLFxuICAgICAgICBhY2NlcHRSZXF1ZXN0czogdHJ1ZSxcbiAgICAgICAgYXV0b0FwcHJvdmFsOiBmYWxzZSxcbiAgICAgICAgbWF4UmVxdWVzdHNQZXJVc2VyOiA1LFxuICAgICAgICB0aW1lZnJhbWVNaW51dGVzOiA2MCxcbiAgICAgICAgd2VkZGluZ01vZGVFbmFibGVkOiBmYWxzZSxcbiAgICAgIH0pO1xuICAgICAgY29uc29sZS5sb2coXCJTZXNzaW9uIGNyZWF0ZWQ6XCIsIHNlc3Npb25JZCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJGYWlsZWQgdG8gY3JlYXRlIHNlc3Npb246XCIsIGVycm9yKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNDcmVhdGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZU1hbmFnZUFjdGl2ZSA9ICgpID0+IHtcbiAgICAvLyBOYXZpZ2F0ZSB0byBzZXNzaW9ucyB0YWJcbiAgICBjb25zdCBldmVudCA9IG5ldyBDdXN0b21FdmVudCgnc3dpdGNoVGFiJywgeyBkZXRhaWw6ICdzZXNzaW9uLW1hbmFnZW1lbnQnIH0pO1xuICAgIHdpbmRvdy5kaXNwYXRjaEV2ZW50KGV2ZW50KTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVWaWV3QW5hbHl0aWNzID0gKCkgPT4ge1xuICAgIC8vIE5hdmlnYXRlIHRvIGFuYWx5dGljcyB0YWJcbiAgICBjb25zdCBldmVudCA9IG5ldyBDdXN0b21FdmVudCgnc3dpdGNoVGFiJywgeyBkZXRhaWw6ICdhbmFseXRpY3MnIH0pO1xuICAgIHdpbmRvdy5kaXNwYXRjaEV2ZW50KGV2ZW50KTtcbiAgfTtcblxuICBpZiAoIWRqUHJvZmlsZSkge1xuICAgIHJldHVybiAoXG4gICAgICA8Q2FyZCBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMC81MCBib3JkZXItcHVycGxlLTUwMC8yMFwiPlxuICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxNdXNpYyBjbGFzc05hbWU9XCJ3LTUgaC01IG1yLTJcIiAvPlxuICAgICAgICAgICAgU2Vzc2lvbiBNYW5hZ2VyXG4gICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgPENhcmREZXNjcmlwdGlvbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICBDcmVhdGUgYSBESiBwcm9maWxlIGZpcnN0IHRvIG1hbmFnZSBzZXNzaW9uc1xuICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LThcIj5cbiAgICAgICAgICAgIDxNdXNpYyBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgdGV4dC1ncmF5LTUwMCBteC1hdXRvIG1iLTRcIiAvPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBtYi00XCI+REogcHJvZmlsZSByZXF1aXJlZDwvcD5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICBDcmVhdGUgeW91ciBESiBwcm9maWxlIHRvIHN0YXJ0IG1hbmFnaW5nIHNlc3Npb25zIGFuZCByZWNlaXZpbmcgc29uZyByZXF1ZXN0cy5cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgIDwvQ2FyZD5cbiAgICApO1xuICB9XG5cbiAgLy8gUHJvY2VzcyByZWFsIHNlc3Npb24gZGF0YVxuICBjb25zdCBhY3RpdmVTZXNzaW9ucyA9IHVzZXJTZXNzaW9ucz8uZmlsdGVyKHMgPT4gcy5hY3RpdmUpIHx8IFtdO1xuICBjb25zdCByZWNlbnRTZXNzaW9ucyA9IHVzZXJTZXNzaW9ucz8uc2xpY2UoMCwgMykgfHwgW107XG4gIGNvbnN0IHRvdGFsUmVxdWVzdHMgPSBzZXNzaW9uU3RhdHM/LnRvdGFsUmVxdWVzdHMgfHwgMDtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICB7LyogUXVpY2sgQWN0aW9ucyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMyBnYXAtNFwiPlxuICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMC81MCBib3JkZXItcHVycGxlLTUwMC8yMFwiPlxuICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlXCI+Q3JlYXRlIFNlc3Npb248L2gzPlxuICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtcHVycGxlLTQwMFwiIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgbWItNCB0ZXh0LXNtXCI+U3RhcnQgYSBuZXcgREogc2Vzc2lvbiB0byByZWNlaXZlIHNvbmcgcmVxdWVzdHM8L3A+XG4gICAgICAgICAgICA8QnV0dG9uIFxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDcmVhdGVTZXNzaW9ufVxuICAgICAgICAgICAgICBkaXNhYmxlZD17aXNDcmVhdGluZ31cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1wdXJwbGUtNjAwIHRvLWN5YW4tNjAwIGhvdmVyOmZyb20tcHVycGxlLTcwMCBob3Zlcjp0by1jeWFuLTcwMFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtpc0NyZWF0aW5nID8gXCJDcmVhdGluZy4uLlwiIDogXCJOZXcgU2Vzc2lvblwifVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImJnLWdyYXktODAwLzUwIGJvcmRlci1wdXJwbGUtNTAwLzIwXCI+XG4gICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj5BY3RpdmUgU2Vzc2lvbnM8L2gzPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctMyBoLTMgcm91bmRlZC1mdWxsICR7YWN0aXZlU2Vzc2lvbnMubGVuZ3RoID4gMCA/ICdiZy1ncmVlbi00MDAgYW5pbWF0ZS1wdWxzZScgOiAnYmctZ3JheS00MDAnfWB9PjwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIG1iLTQgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICB7YWN0aXZlU2Vzc2lvbnMubGVuZ3RofSBhY3RpdmUgc2Vzc2lvbnthY3RpdmVTZXNzaW9ucy5sZW5ndGggIT09IDEgPyAncycgOiAnJ31cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYm9yZGVyLWdyYXktNjAwIHRleHQtZ3JheS0zMDAgaG92ZXI6YmctZ3JheS03MDBcIlxuICAgICAgICAgICAgICBkaXNhYmxlZD17YWN0aXZlU2Vzc2lvbnMubGVuZ3RoID09PSAwfVxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVNYW5hZ2VBY3RpdmV9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHthY3RpdmVTZXNzaW9ucy5sZW5ndGggPiAwID8gXCJNYW5hZ2UgQWN0aXZlXCIgOiBcIk5vIEFjdGl2ZSBTZXNzaW9uc1wifVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImJnLWdyYXktODAwLzUwIGJvcmRlci1wdXJwbGUtNTAwLzIwXCI+XG4gICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj5Ub3RhbCBSZXF1ZXN0czwvaDM+XG4gICAgICAgICAgICAgIDxVc2VycyBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtcHVycGxlLTQwMFwiIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgbWItNCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgIHttb2NrU2Vzc2lvbnMucmVkdWNlKChzdW0sIHMpID0+IHN1bSArIHMucmVxdWVzdENvdW50LCAwKX0gdG90YWwgcmVxdWVzdHMgcmVjZWl2ZWRcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDxCdXR0b24gXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCIgXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBib3JkZXItZ3JheS02MDAgdGV4dC1ncmF5LTMwMCBob3ZlcjpiZy1ncmF5LTcwMFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIFZpZXcgQW5hbHl0aWNzXG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFJlY2VudCBTZXNzaW9ucyAqL31cbiAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImJnLWdyYXktODAwLzUwIGJvcmRlci1wdXJwbGUtNTAwLzIwXCI+XG4gICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cInctNSBoLTUgbXItMlwiIC8+XG4gICAgICAgICAgICBSZWNlbnQgU2Vzc2lvbnNcbiAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgIFlvdXIgbGF0ZXN0IERKIHNlc3Npb25zIGFuZCB0aGVpciBzdGF0dXNcbiAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAge3JlY2VudFNlc3Npb25zLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICB7cmVjZW50U2Vzc2lvbnMubWFwKChzZXNzaW9uKSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAga2V5PXtzZXNzaW9uLmlkfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNCBiZy1ncmF5LTcwMC81MCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS02MDAvMzBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy0xMCBoLTEwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciAke1xuICAgICAgICAgICAgICAgICAgICAgIHNlc3Npb24uc3RhdHVzID09PSAnYWN0aXZlJyBcbiAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTUwMC8yMCB0ZXh0LWdyZWVuLTQwMCcgXG4gICAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1ncmF5LTUwMC8yMCB0ZXh0LWdyYXktNDAwJ1xuICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAge3Nlc3Npb24uc3RhdHVzID09PSAnYWN0aXZlJyA/IDxQbGF5IGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPiA6IDxQYXVzZSBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz59XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXdoaXRlXCI+e3Nlc3Npb24ubmFtZX08L2g0PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00IHRleHQtc20gdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENsb2NrIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtuZXcgRGF0ZShzZXNzaW9uLmNyZWF0ZWRBdCkudG9Mb2NhbGVEYXRlU3RyaW5nKCl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VXNlcnMgY2xhc3NOYW1lPVwidy00IGgtNCBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3Nlc3Npb24ucmVxdWVzdENvdW50fS97c2Vzc2lvbi5tYXhSZXF1ZXN0c30gcmVxdWVzdHNcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PXtzZXNzaW9uLnN0YXR1cyA9PT0gJ2FjdGl2ZScgPyAnZGVmYXVsdCcgOiAnc2Vjb25kYXJ5J30+XG4gICAgICAgICAgICAgICAgICAgICAge3Nlc3Npb24uc3RhdHVzID09PSAnYWN0aXZlJyA/ICdMaXZlJyA6ICdFbmRlZCd9XG4gICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgIDxCdXR0b24gXG4gICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCIgXG4gICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIiBcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXItZ3JheS02MDAgdGV4dC1ncmF5LTMwMCBob3ZlcjpiZy1ncmF5LTcwMFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7c2Vzc2lvbi5zdGF0dXMgPT09ICdhY3RpdmUnID8gJ01hbmFnZScgOiAnVmlldyd9XG4gICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOFwiPlxuICAgICAgICAgICAgICA8TXVzaWMgY2xhc3NOYW1lPVwidy0xMiBoLTEyIHRleHQtZ3JheS01MDAgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBtYi00XCI+Tm8gc2Vzc2lvbnMgeWV0PC9wPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIHRleHQtc20gbWItNlwiPlxuICAgICAgICAgICAgICAgIENyZWF0ZSB5b3VyIGZpcnN0IHNlc3Npb24gdG8gc3RhcnQgcmVjZWl2aW5nIHNvbmcgcmVxdWVzdHMgZnJvbSB5b3VyIGF1ZGllbmNlLlxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDxCdXR0b24gXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ3JlYXRlU2Vzc2lvbn1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNDcmVhdGluZ31cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tcHVycGxlLTYwMCB0by1jeWFuLTYwMCBob3Zlcjpmcm9tLXB1cnBsZS03MDAgaG92ZXI6dG8tY3lhbi03MDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2lzQ3JlYXRpbmcgPyBcIkNyZWF0aW5nLi4uXCIgOiBcIkNyZWF0ZSBZb3VyIEZpcnN0IFNlc3Npb25cIn1cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgPC9DYXJkPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVF1ZXJ5IiwidXNlTXV0YXRpb24iLCJhcGkiLCJCdXR0b24iLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkRGVzY3JpcHRpb24iLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQmFkZ2UiLCJQbGF5IiwiUGF1c2UiLCJQbHVzIiwiVXNlcnMiLCJNdXNpYyIsIkNhbGVuZGFyIiwiQ2xvY2siLCJ1c2VTdGF0ZSIsInVzZVJvdXRlciIsIlNlc3Npb25NYW5hZ2VyIiwiaXNDcmVhdGluZyIsInNldElzQ3JlYXRpbmciLCJyb3V0ZXIiLCJ1c2VyV2l0aFByb2ZpbGUiLCJ1c2VycyIsImdldEN1cnJlbnRVc2VyV2l0aFByb2ZpbGUiLCJjdXJyZW50VXNlciIsImdldEN1cnJlbnRVc2VyIiwiZGpQcm9maWxlIiwidXNlclNlc3Npb25zIiwic2Vzc2lvbnMiLCJnZXRVc2VyU2Vzc2lvbnMiLCJ1c2VySWQiLCJfaWQiLCJzZXNzaW9uU3RhdHMiLCJhbmFseXRpY3MiLCJnZXRVc2VyU2Vzc2lvblN0YXRzIiwiY3JlYXRlU2Vzc2lvbiIsImhhbmRsZUNyZWF0ZVNlc3Npb24iLCJzZXNzaW9uSWQiLCJuYW1lIiwiRGF0ZSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsImFjY2VwdFJlcXVlc3RzIiwiYXV0b0FwcHJvdmFsIiwibWF4UmVxdWVzdHNQZXJVc2VyIiwidGltZWZyYW1lTWludXRlcyIsIndlZGRpbmdNb2RlRW5hYmxlZCIsImNvbnNvbGUiLCJsb2ciLCJlcnJvciIsImhhbmRsZU1hbmFnZUFjdGl2ZSIsImV2ZW50IiwiQ3VzdG9tRXZlbnQiLCJkZXRhaWwiLCJ3aW5kb3ciLCJkaXNwYXRjaEV2ZW50IiwiaGFuZGxlVmlld0FuYWx5dGljcyIsImNsYXNzTmFtZSIsImRpdiIsInAiLCJhY3RpdmVTZXNzaW9ucyIsImZpbHRlciIsInMiLCJhY3RpdmUiLCJyZWNlbnRTZXNzaW9ucyIsInNsaWNlIiwidG90YWxSZXF1ZXN0cyIsImgzIiwib25DbGljayIsImRpc2FibGVkIiwibGVuZ3RoIiwidmFyaWFudCIsIm1vY2tTZXNzaW9ucyIsInJlZHVjZSIsInN1bSIsInJlcXVlc3RDb3VudCIsIm1hcCIsInNlc3Npb24iLCJzdGF0dXMiLCJoNCIsInNwYW4iLCJjcmVhdGVkQXQiLCJtYXhSZXF1ZXN0cyIsInNpemUiLCJpZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/dashboard/SessionManager.tsx\n"));

/***/ })

});