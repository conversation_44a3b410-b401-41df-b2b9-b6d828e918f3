"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @convex-dev/auth/react */ \"(app-pages-browser)/./node_modules/@convex-dev/auth/dist/react/index.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,LogOut,Music!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _components_dashboard_DJProfileCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/DJProfileCard */ \"(app-pages-browser)/./components/dashboard/DJProfileCard.tsx\");\n/* harmony import */ var _components_dashboard_SessionManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/SessionManager */ \"(app-pages-browser)/./components/dashboard/SessionManager.tsx\");\n/* harmony import */ var _components_SongRequestManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/SongRequestManager */ \"(app-pages-browser)/./components/SongRequestManager.tsx\");\n/* harmony import */ var _components_dashboard_NotificationCenter__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/NotificationCenter */ \"(app-pages-browser)/./components/dashboard/NotificationCenter.tsx\");\n/* harmony import */ var _components_PassPurchase__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/PassPurchase */ \"(app-pages-browser)/./components/PassPurchase.tsx\");\n/* harmony import */ var _components_SongSearchInput__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/SongSearchInput */ \"(app-pages-browser)/./components/SongSearchInput.tsx\");\n/* harmony import */ var _components_testing_IntegrationTestSuite__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/testing/IntegrationTestSuite */ \"(app-pages-browser)/./components/testing/IntegrationTestSuite.tsx\");\n/* harmony import */ var _components_testing_RealtimeTestSuite__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/testing/RealtimeTestSuite */ \"(app-pages-browser)/./components/testing/RealtimeTestSuite.tsx\");\n/* harmony import */ var _components_session_SessionManagementHub__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/session/SessionManagementHub */ \"(app-pages-browser)/./components/session/SessionManagementHub.tsx\");\n/* harmony import */ var _components_admin_BlogManagement__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/admin/BlogManagement */ \"(app-pages-browser)/./components/admin/BlogManagement.tsx\");\n/* harmony import */ var _components_search_AdvancedSearchInterface__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/search/AdvancedSearchInterface */ \"(app-pages-browser)/./components/search/AdvancedSearchInterface.tsx\");\n/* harmony import */ var _components_performance_PerformanceMonitor__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/performance/PerformanceMonitor */ \"(app-pages-browser)/./components/performance/PerformanceMonitor.tsx\");\n/* harmony import */ var _components_analytics_AdvancedAnalyticsDashboard__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/analytics/AdvancedAnalyticsDashboard */ \"(app-pages-browser)/./components/analytics/AdvancedAnalyticsDashboard.tsx\");\n/* harmony import */ var _components_security_SecurityDashboard__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/security/SecurityDashboard */ \"(app-pages-browser)/./components/security/SecurityDashboard.tsx\");\n/* harmony import */ var _components_mobile_PWAManager__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/mobile/PWAManager */ \"(app-pages-browser)/./components/mobile/PWAManager.tsx\");\n/* harmony import */ var _components_testing_LoadTestDashboard__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/testing/LoadTestDashboard */ \"(app-pages-browser)/./components/testing/LoadTestDashboard.tsx\");\n/* harmony import */ var _components_testing_SecurityTestDashboard__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/testing/SecurityTestDashboard */ \"(app-pages-browser)/./components/testing/SecurityTestDashboard.tsx\");\n/* harmony import */ var _components_deployment_ProductionDeploymentDashboard__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/deployment/ProductionDeploymentDashboard */ \"(app-pages-browser)/./components/deployment/ProductionDeploymentDashboard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const { isAuthenticated, isLoading } = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useConvexAuth)();\n    const { signOut } = (0,_convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_24__.useAuthActions)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('overview');\n    // Get current user and their DJ profile\n    const currentUser = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.getCurrentUser);\n    const userWithProfile = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.getCurrentUserWithProfile);\n    const djProfile = userWithProfile === null || userWithProfile === void 0 ? void 0 : userWithProfile.djProfile;\n    // Get user status\n    const userStatus = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.users.checkUserStatus);\n    // Get DJ profiles list (for testing)\n    const allDjProfiles = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.djProfiles.listDjProfiles, {\n        limit: 10,\n        onboardingStatus: true\n    });\n    // Get active sessions for the current user\n    const activeSessions = (0,convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_4__.api.sessions.getCurrentUserSessions, currentUser ? {\n        activeOnly: true,\n        limit: 10\n    } : \"skip\");\n    // Get the first active session for song request management\n    const activeSession = activeSessions && activeSessions.length > 0 ? activeSessions[0] : null;\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated) {\n                router.push(\"/signin\");\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    const handleSignOut = async ()=>{\n        await signOut();\n        router.push(\"/signin\");\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-lg\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null; // Will redirect to signin\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gray-900/50 backdrop-blur-md border-b border-purple-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center mr-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-white\",\n                                        children: \"PlayBeg\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300\",\n                                        children: [\n                                            \"Welcome, \",\n                                            (currentUser === null || currentUser === void 0 ? void 0 : currentUser.name) || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.email) || \"DJ\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_NotificationCenter__WEBPACK_IMPORTED_MODULE_9__.NotificationCenter, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: handleSignOut,\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"text-gray-300 hover:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Sign Out\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-white mb-2\",\n                                children: \"DJ Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Manage your sessions, song requests, and audience engagement\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 border-b border-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('overview'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'overview' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('requests'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'requests' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Song Requests\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('billing'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'billing' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Billing & Passes\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('analytics'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'analytics' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('spotify-test'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'spotify-test' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83C\\uDFB5 Spotify Test\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('integration-test'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'integration-test' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83E\\uDDEA Integration Test\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('realtime-test'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'realtime-test' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"⚡ Real-time Test\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('session-management'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'session-management' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83C\\uDF9B️ Sessions\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('blog-admin'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'blog-admin' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83D\\uDCDD Blog Admin\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('advanced-search'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'advanced-search' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83D\\uDD0D Advanced Search\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('performance'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'performance' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"⚡ Performance\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('security'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'security' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83D\\uDEE1️ Security\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('mobile-pwa'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'mobile-pwa' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83D\\uDCF1 Mobile & PWA\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('load-testing'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'load-testing' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83E\\uDDEA Load Testing\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('security-testing'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'security-testing' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83D\\uDD12 Security Testing\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('production-deployment'),\n                                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'production-deployment' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'),\n                                            children: \"\\uD83D\\uDE80 Production Deployment\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DJProfileCard__WEBPACK_IMPORTED_MODULE_6__.DJProfileCard, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SessionManager__WEBPACK_IMPORTED_MODULE_7__.SessionManager, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'requests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: activeSession ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SongRequestManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            sessionId: activeSession._id\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: \"\\uD83C\\uDFB5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white mb-2\",\n                                    children: \"No Active Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-6\",\n                                    children: \"You need to have an active session to manage song requests.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: ()=>setActiveTab('overview'),\n                                    className: \"bg-purple-600 hover:bg-purple-700 text-white\",\n                                    children: \"Create a Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'billing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PassPurchase__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'analytics' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_analytics_AdvancedAnalyticsDashboard__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'spotify-test' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-4\",\n                                children: \"\\uD83C\\uDFB5 Spotify Integration Test\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-6\",\n                                children: \"Test the Spotify search functionality to verify the integration is working properly.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SongSearchInput__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    onSongSelect: (song)=>{\n                                        console.log('Selected song:', song);\n                                        alert(\"Selected: \".concat(song.title, \" by \").concat(song.artist));\n                                    },\n                                    placeholder: \"Search for a song on Spotify...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'integration-test' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_testing_IntegrationTestSuite__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'realtime-test' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_testing_RealtimeTestSuite__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'session-management' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_session_SessionManagementHub__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        onSessionSelect: (sessionId)=>{\n                            console.log('Selected session:', sessionId);\n                        // Could navigate to session detail page or open session in new tab\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'blog-admin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_BlogManagement__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'advanced-search' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_search_AdvancedSearchInterface__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'performance' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_performance_PerformanceMonitor__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'security' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_security_SecurityDashboard__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'mobile-pwa' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"Mobile & PWA Features\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_PWAManager__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'load-testing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_testing_LoadTestDashboard__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'security-testing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_testing_SecurityTestDashboard__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'production-deployment' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_deployment_ProductionDeploymentDashboard__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_LogOut_Music_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Quick Stats\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                            children: \"View Full Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: (allDjProfiles === null || allDjProfiles === void 0 ? void 0 : allDjProfiles.length) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Total DJs\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Active Sessions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Song Requests\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white mb-1\",\n                                                    children: (userStatus === null || userStatus === void 0 ? void 0 : userStatus.isAuthenticated) ? '100%' : '0%'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"System Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/app/dashboard/page.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"N2GOEDHqEQMaB1gROcBEnLiv8FE=\", false, function() {\n    return [\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useConvexAuth,\n        _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_24__.useAuthActions,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_1__.useQuery\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9kYXNoYm9hcmQvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFdUQ7QUFDWDtBQUNBO0FBQ0U7QUFDVTtBQUNSO0FBQ1E7QUFDYTtBQUNFO0FBQ047QUFDYztBQUMxQjtBQUNNO0FBQ2tCO0FBQ047QUFDTTtBQUNkO0FBQ21CO0FBQ0w7QUFDYztBQUNuQjtBQUNoQjtBQUNlO0FBQ1E7QUFDbUI7QUFFbkYsU0FBUzZCOztJQUN0QixNQUFNLEVBQUVDLGVBQWUsRUFBRUMsU0FBUyxFQUFFLEdBQUcvQiwyREFBYUE7SUFDcEQsTUFBTSxFQUFFZ0MsT0FBTyxFQUFFLEdBQUcxQix1RUFBY0E7SUFDbEMsTUFBTTJCLFNBQVMvQiwwREFBU0E7SUFDeEIsTUFBTSxDQUFDZ0MsV0FBV0MsYUFBYSxHQUFHL0IsK0NBQVFBLENBQWtSO0lBRTVULHdDQUF3QztJQUN4QyxNQUFNZ0MsY0FBY25DLHNEQUFRQSxDQUFDSSxzREFBR0EsQ0FBQ2dDLEtBQUssQ0FBQ0MsY0FBYztJQUNyRCxNQUFNQyxrQkFBa0J0QyxzREFBUUEsQ0FBQ0ksc0RBQUdBLENBQUNnQyxLQUFLLENBQUNHLHlCQUF5QjtJQUNwRSxNQUFNQyxZQUFZRiw0QkFBQUEsc0NBQUFBLGdCQUFpQkUsU0FBUztJQUU1QyxrQkFBa0I7SUFDbEIsTUFBTUMsYUFBYXpDLHNEQUFRQSxDQUFDSSxzREFBR0EsQ0FBQ2dDLEtBQUssQ0FBQ00sZUFBZTtJQUVyRCxxQ0FBcUM7SUFDckMsTUFBTUMsZ0JBQWdCM0Msc0RBQVFBLENBQUNJLHNEQUFHQSxDQUFDd0MsVUFBVSxDQUFDQyxjQUFjLEVBQUU7UUFDNURDLE9BQU87UUFDUEMsa0JBQWtCO0lBQ3BCO0lBRUEsMkNBQTJDO0lBQzNDLE1BQU1DLGlCQUFpQmhELHNEQUFRQSxDQUFDSSxzREFBR0EsQ0FBQzZDLFFBQVEsQ0FBQ0Msc0JBQXNCLEVBQ2pFZixjQUFjO1FBQUVnQixZQUFZO1FBQU1MLE9BQU87SUFBRyxJQUFJO0lBR2xELDJEQUEyRDtJQUMzRCxNQUFNTSxnQkFBZ0JKLGtCQUFrQkEsZUFBZUssTUFBTSxHQUFHLElBQUlMLGNBQWMsQ0FBQyxFQUFFLEdBQUc7SUFFeEY5QyxnREFBU0E7K0JBQUM7WUFDUixJQUFJLENBQUM0QixhQUFhLENBQUNELGlCQUFpQjtnQkFDbENHLE9BQU9zQixJQUFJLENBQUM7WUFDZDtRQUNGOzhCQUFHO1FBQUN6QjtRQUFpQkM7UUFBV0U7S0FBTztJQUV2QyxNQUFNdUIsZ0JBQWdCO1FBQ3BCLE1BQU14QjtRQUNOQyxPQUFPc0IsSUFBSSxDQUFDO0lBQ2Q7SUFFQSxJQUFJeEIsV0FBVztRQUNiLHFCQUNFLDhEQUFDMEI7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQXFCOzs7Ozs7Ozs7OztJQUcxQztJQUVBLElBQUksQ0FBQzVCLGlCQUFpQjtRQUNwQixPQUFPLE1BQU0sMEJBQTBCO0lBQ3pDO0lBRUEscUJBQ0UsOERBQUMyQjtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0M7Z0JBQU9ELFdBQVU7MEJBQ2hCLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDbEQsbUdBQUtBOzRDQUFDa0QsV0FBVTs7Ozs7Ozs7Ozs7a0RBRW5CLDhEQUFDRTt3Q0FBR0YsV0FBVTtrREFBK0I7Ozs7Ozs7Ozs7OzswQ0FFL0MsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0c7d0NBQUtILFdBQVU7OzRDQUFnQjs0Q0FDcEJ0QixDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWEwQixJQUFJLE1BQUkxQix3QkFBQUEsa0NBQUFBLFlBQWEyQixLQUFLLEtBQUk7Ozs7Ozs7a0RBRXZELDhEQUFDakQsd0ZBQWtCQTs7Ozs7a0RBQ25CLDhEQUFDUCx5REFBTUE7d0NBQ0x5RCxTQUFTUjt3Q0FDVFMsU0FBUTt3Q0FDUkMsTUFBSzt3Q0FDTFIsV0FBVTs7MERBRVYsOERBQUNqRCxtR0FBTUE7Z0RBQUNpRCxXQUFVOzs7Ozs7NENBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTN0MsOERBQUNTO2dCQUFLVCxXQUFVOztrQ0FDZCw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDVTtnQ0FBR1YsV0FBVTswQ0FBcUM7Ozs7OzswQ0FDbkQsOERBQUNXO2dDQUFFWCxXQUFVOzBDQUFnQjs7Ozs7OzBDQUc3Qiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNZO29DQUFJWixXQUFVOztzREFDYiw4REFBQ2E7NENBQ0NQLFNBQVMsSUFBTTdCLGFBQWE7NENBQzVCdUIsV0FBVyw0Q0FJVixPQUhDeEIsY0FBYyxhQUNWLHNDQUNBO3NEQUVQOzs7Ozs7c0RBR0QsOERBQUNxQzs0Q0FDQ1AsU0FBUyxJQUFNN0IsYUFBYTs0Q0FDNUJ1QixXQUFXLDRDQUlWLE9BSEN4QixjQUFjLGFBQ1Ysc0NBQ0E7c0RBRVA7Ozs7OztzREFHRCw4REFBQ3FDOzRDQUNDUCxTQUFTLElBQU03QixhQUFhOzRDQUM1QnVCLFdBQVcsNENBSVYsT0FIQ3hCLGNBQWMsWUFDVixzQ0FDQTtzREFFUDs7Ozs7O3NEQUdELDhEQUFDcUM7NENBQ0NQLFNBQVMsSUFBTTdCLGFBQWE7NENBQzVCdUIsV0FBVyw0Q0FJVixPQUhDeEIsY0FBYyxjQUNWLHNDQUNBO3NEQUVQOzs7Ozs7c0RBR0QsOERBQUNxQzs0Q0FDQ1AsU0FBUyxJQUFNN0IsYUFBYTs0Q0FDNUJ1QixXQUFXLDRDQUlWLE9BSEN4QixjQUFjLGlCQUNWLHNDQUNBO3NEQUVQOzs7Ozs7c0RBR0QsOERBQUNxQzs0Q0FDQ1AsU0FBUyxJQUFNN0IsYUFBYTs0Q0FDNUJ1QixXQUFXLDRDQUlWLE9BSEN4QixjQUFjLHFCQUNWLHNDQUNBO3NEQUVQOzs7Ozs7c0RBR0QsOERBQUNxQzs0Q0FDQ1AsU0FBUyxJQUFNN0IsYUFBYTs0Q0FDNUJ1QixXQUFXLDRDQUlWLE9BSEN4QixjQUFjLGtCQUNWLHNDQUNBO3NEQUVQOzs7Ozs7c0RBR0QsOERBQUNxQzs0Q0FDQ1AsU0FBUyxJQUFNN0IsYUFBYTs0Q0FDNUJ1QixXQUFXLDRDQUlWLE9BSEN4QixjQUFjLHVCQUNWLHNDQUNBO3NEQUVQOzs7Ozs7c0RBR0QsOERBQUNxQzs0Q0FDQ1AsU0FBUyxJQUFNN0IsYUFBYTs0Q0FDNUJ1QixXQUFXLDRDQUlWLE9BSEN4QixjQUFjLGVBQ1Ysc0NBQ0E7c0RBRVA7Ozs7OztzREFHRCw4REFBQ3FDOzRDQUNDUCxTQUFTLElBQU03QixhQUFhOzRDQUM1QnVCLFdBQVcsNENBSVYsT0FIQ3hCLGNBQWMsb0JBQ1Ysc0NBQ0E7c0RBRVA7Ozs7OztzREFHRCw4REFBQ3FDOzRDQUNDUCxTQUFTLElBQU03QixhQUFhOzRDQUM1QnVCLFdBQVcsNENBSVYsT0FIQ3hCLGNBQWMsZ0JBQ1Ysc0NBQ0E7c0RBRVA7Ozs7OztzREFHRCw4REFBQ3FDOzRDQUNDUCxTQUFTLElBQU03QixhQUFhOzRDQUM1QnVCLFdBQVcsNENBSVYsT0FIQ3hCLGNBQWMsYUFDVixzQ0FDQTtzREFFUDs7Ozs7O3NEQUdELDhEQUFDcUM7NENBQ0NQLFNBQVMsSUFBTTdCLGFBQWE7NENBQzVCdUIsV0FBVyw0Q0FJVixPQUhDeEIsY0FBYyxlQUNWLHNDQUNBO3NEQUVQOzs7Ozs7c0RBR0QsOERBQUNxQzs0Q0FDQ1AsU0FBUyxJQUFNN0IsYUFBYTs0Q0FDNUJ1QixXQUFXLDRDQUlWLE9BSEN4QixjQUFjLGlCQUNWLHNDQUNBO3NEQUVQOzs7Ozs7c0RBR0QsOERBQUNxQzs0Q0FDQ1AsU0FBUyxJQUFNN0IsYUFBYTs0Q0FDNUJ1QixXQUFXLDRDQUlWLE9BSEN4QixjQUFjLHFCQUNWLHNDQUNBO3NEQUVQOzs7Ozs7c0RBR0QsOERBQUNxQzs0Q0FDQ1AsU0FBUyxJQUFNN0IsYUFBYTs0Q0FDNUJ1QixXQUFXLDRDQUlWLE9BSEN4QixjQUFjLDBCQUNWLHNDQUNBO3NEQUVQOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkFRTkEsY0FBYyw0QkFDYiw4REFBQ3VCO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUMvQyw4RUFBYUE7Ozs7Ozs7Ozs7MENBSWhCLDhEQUFDOEM7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUM5QyxnRkFBY0E7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBS3BCc0IsY0FBYyw0QkFDYiw4REFBQ3VCO2tDQUNFSiw4QkFDQyw4REFBQ3hDLHNFQUFrQkE7NEJBQUMyRCxXQUFXbkIsY0FBY29CLEdBQUc7Ozs7O2lEQUVoRCw4REFBQ2hCOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQWdCOzs7Ozs7OENBQy9CLDhEQUFDZ0I7b0NBQUdoQixXQUFVOzhDQUF3Qzs7Ozs7OzhDQUN0RCw4REFBQ1c7b0NBQUVYLFdBQVU7OENBQXFCOzs7Ozs7OENBR2xDLDhEQUFDbkQseURBQU1BO29DQUNMeUQsU0FBUyxJQUFNN0IsYUFBYTtvQ0FDNUJ1QixXQUFVOzhDQUNYOzs7Ozs7Ozs7Ozs7Ozs7OztvQkFRUnhCLGNBQWMsMkJBQ2IsOERBQUNuQixpRUFBWUE7Ozs7O29CQUdkbUIsY0FBYyw2QkFDYiw4REFBQ1gseUZBQTBCQTs7Ozs7b0JBRzVCVyxjQUFjLGdDQUNiLDhEQUFDdUI7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDVTtnQ0FBR1YsV0FBVTswQ0FBcUM7Ozs7OzswQ0FDbkQsOERBQUNXO2dDQUFFWCxXQUFVOzBDQUFxQjs7Ozs7OzBDQUdsQyw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUMxQyxvRUFBZUE7b0NBQ2QyRCxjQUFjLENBQUNDO3dDQUNiQyxRQUFRQyxHQUFHLENBQUMsa0JBQWtCRjt3Q0FDOUJHLE1BQU0sYUFBOEJILE9BQWpCQSxLQUFLSSxLQUFLLEVBQUMsUUFBa0IsT0FBWkosS0FBS0ssTUFBTTtvQ0FDakQ7b0NBQ0FDLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQU1uQmhELGNBQWMsb0NBQ2IsOERBQUNqQixpRkFBb0JBOzs7OztvQkFHdEJpQixjQUFjLGlDQUNiLDhEQUFDaEIsOEVBQWlCQTs7Ozs7b0JBR25CZ0IsY0FBYyxzQ0FDYiw4REFBQ2YsaUZBQW9CQTt3QkFDbkJnRSxpQkFBaUIsQ0FBQ1g7NEJBQ2hCSyxRQUFRQyxHQUFHLENBQUMscUJBQXFCTjt3QkFDakMsbUVBQW1FO3dCQUNyRTs7Ozs7O29CQUlIdEMsY0FBYyw4QkFDYiw4REFBQ2QseUVBQWNBOzs7OztvQkFHaEJjLGNBQWMsbUNBQ2IsOERBQUNiLG1GQUF1QkE7Ozs7O29CQUd6QmEsY0FBYywrQkFDYiw4REFBQ1osbUZBQWtCQTs7Ozs7b0JBR3BCWSxjQUFjLDRCQUNiLDhEQUFDViwrRUFBaUJBOzs7OztvQkFHbkJVLGNBQWMsOEJBQ2IsOERBQUN1Qjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDVTtvQ0FBR1YsV0FBVTs4Q0FBZ0M7Ozs7Ozs7Ozs7OzBDQUVoRCw4REFBQ2pDLHNFQUFVQTs7Ozs7Ozs7Ozs7b0JBSWRTLGNBQWMsZ0NBQ2IsOERBQUNSLDhFQUFpQkE7Ozs7O29CQUduQlEsY0FBYyxvQ0FDYiw4REFBQ1Asa0ZBQXFCQTs7Ozs7b0JBR3ZCTyxjQUFjLHlDQUNiLDhEQUFDTiw2RkFBNkJBOzs7OztrQ0FJaEMsOERBQUM2Qjt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNnQjs0Q0FBR2hCLFdBQVU7OzhEQUNaLDhEQUFDaEQsbUdBQVNBO29EQUFDZ0QsV0FBVTs7Ozs7O2dEQUFpQjs7Ozs7OztzREFHeEMsOERBQUNuRCx5REFBTUE7NENBQUMwRCxTQUFROzRDQUFVQyxNQUFLOzRDQUFLUixXQUFVO3NEQUFrRDs7Ozs7Ozs7Ozs7OzhDQUtsRyw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOzhEQUFzQ2QsQ0FBQUEsMEJBQUFBLG9DQUFBQSxjQUFlVSxNQUFNLEtBQUk7Ozs7Ozs4REFDOUUsOERBQUNlO29EQUFFWCxXQUFVOzhEQUF3Qjs7Ozs7Ozs7Ozs7O3NEQUV2Qyw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs4REFBcUM7Ozs7Ozs4REFDcEQsOERBQUNXO29EQUFFWCxXQUFVOzhEQUF3Qjs7Ozs7Ozs7Ozs7O3NEQUV2Qyw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs4REFBcUM7Ozs7Ozs4REFDcEQsOERBQUNXO29EQUFFWCxXQUFVOzhEQUF3Qjs7Ozs7Ozs7Ozs7O3NEQUV2Qyw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDWmhCLENBQUFBLHVCQUFBQSxpQ0FBQUEsV0FBWVosZUFBZSxJQUFHLFNBQVM7Ozs7Ozs4REFFMUMsOERBQUN1QztvREFBRVgsV0FBVTs4REFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUXJEO0dBMVp3QjdCOztRQUNpQjdCLHVEQUFhQTtRQUNoQ00sbUVBQWNBO1FBQ25CSixzREFBU0E7UUFJSkQsa0RBQVFBO1FBQ0pBLGtEQUFRQTtRQUliQSxrREFBUUE7UUFHTEEsa0RBQVFBO1FBTVBBLGtEQUFRQTs7O0tBckJUNEIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yb2JlcnQuaGFuc2VuL1BsYXlCZWcvUGxheUJlZy9wbGF5YmVnLWNvbnZleC9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyB1c2VDb252ZXhBdXRoLCB1c2VRdWVyeSB9IGZyb20gXCJjb252ZXgvcmVhY3RcIjtcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIjtcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IGFwaSB9IGZyb20gXCJAL2NvbnZleC9fZ2VuZXJhdGVkL2FwaVwiO1xuaW1wb3J0IHsgdXNlQXV0aEFjdGlvbnMgfSBmcm9tIFwiQGNvbnZleC1kZXYvYXV0aC9yZWFjdFwiO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIjtcbmltcG9ydCB7IE11c2ljLCBMb2dPdXQsIEJhckNoYXJ0MyB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIjtcbmltcG9ydCB7IERKUHJvZmlsZUNhcmQgfSBmcm9tIFwiQC9jb21wb25lbnRzL2Rhc2hib2FyZC9ESlByb2ZpbGVDYXJkXCI7XG5pbXBvcnQgeyBTZXNzaW9uTWFuYWdlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvZGFzaGJvYXJkL1Nlc3Npb25NYW5hZ2VyXCI7XG5pbXBvcnQgU29uZ1JlcXVlc3RNYW5hZ2VyIGZyb20gXCJAL2NvbXBvbmVudHMvU29uZ1JlcXVlc3RNYW5hZ2VyXCI7XG5pbXBvcnQgeyBOb3RpZmljYXRpb25DZW50ZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL2Rhc2hib2FyZC9Ob3RpZmljYXRpb25DZW50ZXJcIjtcbmltcG9ydCBQYXNzUHVyY2hhc2UgZnJvbSBcIkAvY29tcG9uZW50cy9QYXNzUHVyY2hhc2VcIjtcbmltcG9ydCBTb25nU2VhcmNoSW5wdXQgZnJvbSBcIkAvY29tcG9uZW50cy9Tb25nU2VhcmNoSW5wdXRcIjtcbmltcG9ydCBJbnRlZ3JhdGlvblRlc3RTdWl0ZSBmcm9tIFwiQC9jb21wb25lbnRzL3Rlc3RpbmcvSW50ZWdyYXRpb25UZXN0U3VpdGVcIjtcbmltcG9ydCBSZWFsdGltZVRlc3RTdWl0ZSBmcm9tIFwiQC9jb21wb25lbnRzL3Rlc3RpbmcvUmVhbHRpbWVUZXN0U3VpdGVcIjtcbmltcG9ydCBTZXNzaW9uTWFuYWdlbWVudEh1YiBmcm9tIFwiQC9jb21wb25lbnRzL3Nlc3Npb24vU2Vzc2lvbk1hbmFnZW1lbnRIdWJcIjtcbmltcG9ydCBCbG9nTWFuYWdlbWVudCBmcm9tIFwiQC9jb21wb25lbnRzL2FkbWluL0Jsb2dNYW5hZ2VtZW50XCI7XG5pbXBvcnQgQWR2YW5jZWRTZWFyY2hJbnRlcmZhY2UgZnJvbSBcIkAvY29tcG9uZW50cy9zZWFyY2gvQWR2YW5jZWRTZWFyY2hJbnRlcmZhY2VcIjtcbmltcG9ydCBQZXJmb3JtYW5jZU1vbml0b3IgZnJvbSBcIkAvY29tcG9uZW50cy9wZXJmb3JtYW5jZS9QZXJmb3JtYW5jZU1vbml0b3JcIjtcbmltcG9ydCBBZHZhbmNlZEFuYWx5dGljc0Rhc2hib2FyZCBmcm9tIFwiQC9jb21wb25lbnRzL2FuYWx5dGljcy9BZHZhbmNlZEFuYWx5dGljc0Rhc2hib2FyZFwiO1xuaW1wb3J0IFNlY3VyaXR5RGFzaGJvYXJkIGZyb20gXCJAL2NvbXBvbmVudHMvc2VjdXJpdHkvU2VjdXJpdHlEYXNoYm9hcmRcIjtcbmltcG9ydCBQV0FNYW5hZ2VyIGZyb20gXCJAL2NvbXBvbmVudHMvbW9iaWxlL1BXQU1hbmFnZXJcIjtcbmltcG9ydCBMb2FkVGVzdERhc2hib2FyZCBmcm9tIFwiQC9jb21wb25lbnRzL3Rlc3RpbmcvTG9hZFRlc3REYXNoYm9hcmRcIjtcbmltcG9ydCBTZWN1cml0eVRlc3REYXNoYm9hcmQgZnJvbSBcIkAvY29tcG9uZW50cy90ZXN0aW5nL1NlY3VyaXR5VGVzdERhc2hib2FyZFwiO1xuaW1wb3J0IFByb2R1Y3Rpb25EZXBsb3ltZW50RGFzaGJvYXJkIGZyb20gXCJAL2NvbXBvbmVudHMvZGVwbG95bWVudC9Qcm9kdWN0aW9uRGVwbG95bWVudERhc2hib2FyZFwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEYXNoYm9hcmQoKSB7XG4gIGNvbnN0IHsgaXNBdXRoZW50aWNhdGVkLCBpc0xvYWRpbmcgfSA9IHVzZUNvbnZleEF1dGgoKTtcbiAgY29uc3QgeyBzaWduT3V0IH0gPSB1c2VBdXRoQWN0aW9ucygpO1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3QgW2FjdGl2ZVRhYiwgc2V0QWN0aXZlVGFiXSA9IHVzZVN0YXRlPCdvdmVydmlldycgfCAncmVxdWVzdHMnIHwgJ2JpbGxpbmcnIHwgJ2FuYWx5dGljcycgfCAnc3BvdGlmeS10ZXN0JyB8ICdpbnRlZ3JhdGlvbi10ZXN0JyB8ICdyZWFsdGltZS10ZXN0JyB8ICdzZXNzaW9uLW1hbmFnZW1lbnQnIHwgJ2Jsb2ctYWRtaW4nIHwgJ2FkdmFuY2VkLXNlYXJjaCcgfCAncGVyZm9ybWFuY2UnIHwgJ3NlY3VyaXR5JyB8ICdtb2JpbGUtcHdhJyB8ICdsb2FkLXRlc3RpbmcnIHwgJ3NlY3VyaXR5LXRlc3RpbmcnIHwgJ3Byb2R1Y3Rpb24tZGVwbG95bWVudCc+KCdvdmVydmlldycpO1xuXG4gIC8vIEdldCBjdXJyZW50IHVzZXIgYW5kIHRoZWlyIERKIHByb2ZpbGVcbiAgY29uc3QgY3VycmVudFVzZXIgPSB1c2VRdWVyeShhcGkudXNlcnMuZ2V0Q3VycmVudFVzZXIpO1xuICBjb25zdCB1c2VyV2l0aFByb2ZpbGUgPSB1c2VRdWVyeShhcGkudXNlcnMuZ2V0Q3VycmVudFVzZXJXaXRoUHJvZmlsZSk7XG4gIGNvbnN0IGRqUHJvZmlsZSA9IHVzZXJXaXRoUHJvZmlsZT8uZGpQcm9maWxlO1xuXG4gIC8vIEdldCB1c2VyIHN0YXR1c1xuICBjb25zdCB1c2VyU3RhdHVzID0gdXNlUXVlcnkoYXBpLnVzZXJzLmNoZWNrVXNlclN0YXR1cyk7XG5cbiAgLy8gR2V0IERKIHByb2ZpbGVzIGxpc3QgKGZvciB0ZXN0aW5nKVxuICBjb25zdCBhbGxEalByb2ZpbGVzID0gdXNlUXVlcnkoYXBpLmRqUHJvZmlsZXMubGlzdERqUHJvZmlsZXMsIHtcbiAgICBsaW1pdDogMTAsXG4gICAgb25ib2FyZGluZ1N0YXR1czogdHJ1ZSxcbiAgfSk7XG5cbiAgLy8gR2V0IGFjdGl2ZSBzZXNzaW9ucyBmb3IgdGhlIGN1cnJlbnQgdXNlclxuICBjb25zdCBhY3RpdmVTZXNzaW9ucyA9IHVzZVF1ZXJ5KGFwaS5zZXNzaW9ucy5nZXRDdXJyZW50VXNlclNlc3Npb25zLFxuICAgIGN1cnJlbnRVc2VyID8geyBhY3RpdmVPbmx5OiB0cnVlLCBsaW1pdDogMTAgfSA6IFwic2tpcFwiXG4gICk7XG5cbiAgLy8gR2V0IHRoZSBmaXJzdCBhY3RpdmUgc2Vzc2lvbiBmb3Igc29uZyByZXF1ZXN0IG1hbmFnZW1lbnRcbiAgY29uc3QgYWN0aXZlU2Vzc2lvbiA9IGFjdGl2ZVNlc3Npb25zICYmIGFjdGl2ZVNlc3Npb25zLmxlbmd0aCA+IDAgPyBhY3RpdmVTZXNzaW9uc1swXSA6IG51bGw7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWlzTG9hZGluZyAmJiAhaXNBdXRoZW50aWNhdGVkKSB7XG4gICAgICByb3V0ZXIucHVzaChcIi9zaWduaW5cIik7XG4gICAgfVxuICB9LCBbaXNBdXRoZW50aWNhdGVkLCBpc0xvYWRpbmcsIHJvdXRlcl0pO1xuXG4gIGNvbnN0IGhhbmRsZVNpZ25PdXQgPSBhc3luYyAoKSA9PiB7XG4gICAgYXdhaXQgc2lnbk91dCgpO1xuICAgIHJvdXRlci5wdXNoKFwiL3NpZ25pblwiKTtcbiAgfTtcblxuICBpZiAoaXNMb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tZ3JheS05MDAgdmlhLXB1cnBsZS05MDAgdG8tZ3JheS05MDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtbGdcIj5Mb2FkaW5nLi4uPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgaWYgKCFpc0F1dGhlbnRpY2F0ZWQpIHtcbiAgICByZXR1cm4gbnVsbDsgLy8gV2lsbCByZWRpcmVjdCB0byBzaWduaW5cbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmF5LTkwMCB2aWEtcHVycGxlLTkwMCB0by1ncmF5LTkwMFwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiYmctZ3JheS05MDAvNTAgYmFja2Ryb3AtYmx1ci1tZCBib3JkZXItYiBib3JkZXItcHVycGxlLTUwMC8yMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgaC0xNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctZ3JhZGllbnQtdG8tciBmcm9tLXB1cnBsZS02MDAgdG8tY3lhbi02MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1yLTNcIj5cbiAgICAgICAgICAgICAgICA8TXVzaWMgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+UGxheUJlZzwvaDE+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgICAgICBXZWxjb21lLCB7Y3VycmVudFVzZXI/Lm5hbWUgfHwgY3VycmVudFVzZXI/LmVtYWlsIHx8IFwiREpcIn1cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8Tm90aWZpY2F0aW9uQ2VudGVyIC8+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTaWduT3V0fVxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtd2hpdGVcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPExvZ091dCBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIFNpZ24gT3V0XG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9oZWFkZXI+XG5cbiAgICAgIHsvKiBNYWluIENvbnRlbnQgKi99XG4gICAgICA8bWFpbiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOCBweS04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+REogRGFzaGJvYXJkPC9oMj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+TWFuYWdlIHlvdXIgc2Vzc2lvbnMsIHNvbmcgcmVxdWVzdHMsIGFuZCBhdWRpZW5jZSBlbmdhZ2VtZW50PC9wPlxuXG4gICAgICAgICAgey8qIFRhYiBOYXZpZ2F0aW9uICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNiBib3JkZXItYiBib3JkZXItZ3JheS03MDBcIj5cbiAgICAgICAgICAgIDxuYXYgY2xhc3NOYW1lPVwiLW1iLXB4IGZsZXggc3BhY2UteC04XCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoJ292ZXJ2aWV3Jyl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHktMiBweC0xIGJvcmRlci1iLTIgZm9udC1tZWRpdW0gdGV4dC1zbSAke1xuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSAnb3ZlcnZpZXcnXG4gICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1wdXJwbGUtNTAwIHRleHQtcHVycGxlLTQwMCdcbiAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTMwMCBob3Zlcjpib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBPdmVydmlld1xuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYigncmVxdWVzdHMnKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweS0yIHB4LTEgYm9yZGVyLWItMiBmb250LW1lZGl1bSB0ZXh0LXNtICR7XG4gICAgICAgICAgICAgICAgICBhY3RpdmVUYWIgPT09ICdyZXF1ZXN0cydcbiAgICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLXB1cnBsZS01MDAgdGV4dC1wdXJwbGUtNDAwJ1xuICAgICAgICAgICAgICAgICAgICA6ICdib3JkZXItdHJhbnNwYXJlbnQgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktMzAwIGhvdmVyOmJvcmRlci1ncmF5LTMwMCdcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIFNvbmcgUmVxdWVzdHNcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoJ2JpbGxpbmcnKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweS0yIHB4LTEgYm9yZGVyLWItMiBmb250LW1lZGl1bSB0ZXh0LXNtICR7XG4gICAgICAgICAgICAgICAgICBhY3RpdmVUYWIgPT09ICdiaWxsaW5nJ1xuICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItcHVycGxlLTUwMCB0ZXh0LXB1cnBsZS00MDAnXG4gICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci10cmFuc3BhcmVudCB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS0zMDAgaG92ZXI6Ym9yZGVyLWdyYXktMzAwJ1xuICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgQmlsbGluZyAmIFBhc3Nlc1xuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYignYW5hbHl0aWNzJyl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHktMiBweC0xIGJvcmRlci1iLTIgZm9udC1tZWRpdW0gdGV4dC1zbSAke1xuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSAnYW5hbHl0aWNzJ1xuICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItcHVycGxlLTUwMCB0ZXh0LXB1cnBsZS00MDAnXG4gICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci10cmFuc3BhcmVudCB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS0zMDAgaG92ZXI6Ym9yZGVyLWdyYXktMzAwJ1xuICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgQW5hbHl0aWNzXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKCdzcG90aWZ5LXRlc3QnKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweS0yIHB4LTEgYm9yZGVyLWItMiBmb250LW1lZGl1bSB0ZXh0LXNtICR7XG4gICAgICAgICAgICAgICAgICBhY3RpdmVUYWIgPT09ICdzcG90aWZ5LXRlc3QnXG4gICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1wdXJwbGUtNTAwIHRleHQtcHVycGxlLTQwMCdcbiAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTMwMCBob3Zlcjpib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDwn461IFNwb3RpZnkgVGVzdFxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYignaW50ZWdyYXRpb24tdGVzdCcpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB5LTIgcHgtMSBib3JkZXItYi0yIGZvbnQtbWVkaXVtIHRleHQtc20gJHtcbiAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ2ludGVncmF0aW9uLXRlc3QnXG4gICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1wdXJwbGUtNTAwIHRleHQtcHVycGxlLTQwMCdcbiAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTMwMCBob3Zlcjpib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDwn6eqIEludGVncmF0aW9uIFRlc3RcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoJ3JlYWx0aW1lLXRlc3QnKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweS0yIHB4LTEgYm9yZGVyLWItMiBmb250LW1lZGl1bSB0ZXh0LXNtICR7XG4gICAgICAgICAgICAgICAgICBhY3RpdmVUYWIgPT09ICdyZWFsdGltZS10ZXN0J1xuICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItcHVycGxlLTUwMCB0ZXh0LXB1cnBsZS00MDAnXG4gICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci10cmFuc3BhcmVudCB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS0zMDAgaG92ZXI6Ym9yZGVyLWdyYXktMzAwJ1xuICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg4pqhIFJlYWwtdGltZSBUZXN0XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKCdzZXNzaW9uLW1hbmFnZW1lbnQnKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweS0yIHB4LTEgYm9yZGVyLWItMiBmb250LW1lZGl1bSB0ZXh0LXNtICR7XG4gICAgICAgICAgICAgICAgICBhY3RpdmVUYWIgPT09ICdzZXNzaW9uLW1hbmFnZW1lbnQnXG4gICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1wdXJwbGUtNTAwIHRleHQtcHVycGxlLTQwMCdcbiAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTMwMCBob3Zlcjpib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDwn46b77iPIFNlc3Npb25zXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKCdibG9nLWFkbWluJyl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHktMiBweC0xIGJvcmRlci1iLTIgZm9udC1tZWRpdW0gdGV4dC1zbSAke1xuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSAnYmxvZy1hZG1pbidcbiAgICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLXB1cnBsZS01MDAgdGV4dC1wdXJwbGUtNDAwJ1xuICAgICAgICAgICAgICAgICAgICA6ICdib3JkZXItdHJhbnNwYXJlbnQgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktMzAwIGhvdmVyOmJvcmRlci1ncmF5LTMwMCdcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIPCfk50gQmxvZyBBZG1pblxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYignYWR2YW5jZWQtc2VhcmNoJyl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHktMiBweC0xIGJvcmRlci1iLTIgZm9udC1tZWRpdW0gdGV4dC1zbSAke1xuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSAnYWR2YW5jZWQtc2VhcmNoJ1xuICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItcHVycGxlLTUwMCB0ZXh0LXB1cnBsZS00MDAnXG4gICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci10cmFuc3BhcmVudCB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS0zMDAgaG92ZXI6Ym9yZGVyLWdyYXktMzAwJ1xuICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg8J+UjSBBZHZhbmNlZCBTZWFyY2hcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoJ3BlcmZvcm1hbmNlJyl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHktMiBweC0xIGJvcmRlci1iLTIgZm9udC1tZWRpdW0gdGV4dC1zbSAke1xuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSAncGVyZm9ybWFuY2UnXG4gICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1wdXJwbGUtNTAwIHRleHQtcHVycGxlLTQwMCdcbiAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTMwMCBob3Zlcjpib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDimqEgUGVyZm9ybWFuY2VcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoJ3NlY3VyaXR5Jyl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHktMiBweC0xIGJvcmRlci1iLTIgZm9udC1tZWRpdW0gdGV4dC1zbSAke1xuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSAnc2VjdXJpdHknXG4gICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1wdXJwbGUtNTAwIHRleHQtcHVycGxlLTQwMCdcbiAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTMwMCBob3Zlcjpib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDwn5uh77iPIFNlY3VyaXR5XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKCdtb2JpbGUtcHdhJyl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHktMiBweC0xIGJvcmRlci1iLTIgZm9udC1tZWRpdW0gdGV4dC1zbSAke1xuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSAnbW9iaWxlLXB3YSdcbiAgICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLXB1cnBsZS01MDAgdGV4dC1wdXJwbGUtNDAwJ1xuICAgICAgICAgICAgICAgICAgICA6ICdib3JkZXItdHJhbnNwYXJlbnQgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktMzAwIGhvdmVyOmJvcmRlci1ncmF5LTMwMCdcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIPCfk7EgTW9iaWxlICYgUFdBXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKCdsb2FkLXRlc3RpbmcnKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweS0yIHB4LTEgYm9yZGVyLWItMiBmb250LW1lZGl1bSB0ZXh0LXNtICR7XG4gICAgICAgICAgICAgICAgICBhY3RpdmVUYWIgPT09ICdsb2FkLXRlc3RpbmcnXG4gICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1wdXJwbGUtNTAwIHRleHQtcHVycGxlLTQwMCdcbiAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTMwMCBob3Zlcjpib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDwn6eqIExvYWQgVGVzdGluZ1xuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYignc2VjdXJpdHktdGVzdGluZycpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB5LTIgcHgtMSBib3JkZXItYi0yIGZvbnQtbWVkaXVtIHRleHQtc20gJHtcbiAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ3NlY3VyaXR5LXRlc3RpbmcnXG4gICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1wdXJwbGUtNTAwIHRleHQtcHVycGxlLTQwMCdcbiAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTMwMCBob3Zlcjpib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDwn5SSIFNlY3VyaXR5IFRlc3RpbmdcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoJ3Byb2R1Y3Rpb24tZGVwbG95bWVudCcpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB5LTIgcHgtMSBib3JkZXItYi0yIGZvbnQtbWVkaXVtIHRleHQtc20gJHtcbiAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ3Byb2R1Y3Rpb24tZGVwbG95bWVudCdcbiAgICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLXB1cnBsZS01MDAgdGV4dC1wdXJwbGUtNDAwJ1xuICAgICAgICAgICAgICAgICAgICA6ICdib3JkZXItdHJhbnNwYXJlbnQgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktMzAwIGhvdmVyOmJvcmRlci1ncmF5LTMwMCdcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIPCfmoAgUHJvZHVjdGlvbiBEZXBsb3ltZW50XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9uYXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBUYWIgQ29udGVudCAqL31cbiAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ292ZXJ2aWV3JyAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0zIGdhcC04XCI+XG4gICAgICAgICAgICB7LyogTGVmdCBDb2x1bW4gLSBESiBQcm9maWxlICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0xXCI+XG4gICAgICAgICAgICAgIDxESlByb2ZpbGVDYXJkIC8+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFJpZ2h0IENvbHVtbiAtIFNlc3Npb24gTWFuYWdlbWVudCAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tMlwiPlxuICAgICAgICAgICAgICA8U2Vzc2lvbk1hbmFnZXIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHthY3RpdmVUYWIgPT09ICdyZXF1ZXN0cycgJiYgKFxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICB7YWN0aXZlU2Vzc2lvbiA/IChcbiAgICAgICAgICAgICAgPFNvbmdSZXF1ZXN0TWFuYWdlciBzZXNzaW9uSWQ9e2FjdGl2ZVNlc3Npb24uX2lkfSAvPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMC81MCBiYWNrZHJvcC1ibHVyLW1kIGJvcmRlciBib3JkZXItcHVycGxlLTUwMC8yMCByb3VuZGVkLWxnIHAtOCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC00eGwgbWItNFwiPvCfjrU8L2Rpdj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItMlwiPk5vIEFjdGl2ZSBTZXNzaW9uPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgIFlvdSBuZWVkIHRvIGhhdmUgYW4gYWN0aXZlIHNlc3Npb24gdG8gbWFuYWdlIHNvbmcgcmVxdWVzdHMuXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYignb3ZlcnZpZXcnKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXB1cnBsZS02MDAgaG92ZXI6YmctcHVycGxlLTcwMCB0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBDcmVhdGUgYSBTZXNzaW9uXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7YWN0aXZlVGFiID09PSAnYmlsbGluZycgJiYgKFxuICAgICAgICAgIDxQYXNzUHVyY2hhc2UgLz5cbiAgICAgICAgKX1cblxuICAgICAgICB7YWN0aXZlVGFiID09PSAnYW5hbHl0aWNzJyAmJiAoXG4gICAgICAgICAgPEFkdmFuY2VkQW5hbHl0aWNzRGFzaGJvYXJkIC8+XG4gICAgICAgICl9XG5cbiAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ3Nwb3RpZnktdGVzdCcgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAvNTAgYmFja2Ryb3AtYmx1ci1tZCBib3JkZXIgYm9yZGVyLXB1cnBsZS01MDAvMjAgcm91bmRlZC1sZyBwLTZcIj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi00XCI+8J+OtSBTcG90aWZ5IEludGVncmF0aW9uIFRlc3Q8L2gyPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBtYi02XCI+XG4gICAgICAgICAgICAgIFRlc3QgdGhlIFNwb3RpZnkgc2VhcmNoIGZ1bmN0aW9uYWxpdHkgdG8gdmVyaWZ5IHRoZSBpbnRlZ3JhdGlvbiBpcyB3b3JraW5nIHByb3Blcmx5LlxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy1tZFwiPlxuICAgICAgICAgICAgICA8U29uZ1NlYXJjaElucHV0XG4gICAgICAgICAgICAgICAgb25Tb25nU2VsZWN0PXsoc29uZykgPT4ge1xuICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1NlbGVjdGVkIHNvbmc6Jywgc29uZyk7XG4gICAgICAgICAgICAgICAgICBhbGVydChgU2VsZWN0ZWQ6ICR7c29uZy50aXRsZX0gYnkgJHtzb25nLmFydGlzdH1gKTtcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIGZvciBhIHNvbmcgb24gU3BvdGlmeS4uLlwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7YWN0aXZlVGFiID09PSAnaW50ZWdyYXRpb24tdGVzdCcgJiYgKFxuICAgICAgICAgIDxJbnRlZ3JhdGlvblRlc3RTdWl0ZSAvPlxuICAgICAgICApfVxuXG4gICAgICAgIHthY3RpdmVUYWIgPT09ICdyZWFsdGltZS10ZXN0JyAmJiAoXG4gICAgICAgICAgPFJlYWx0aW1lVGVzdFN1aXRlIC8+XG4gICAgICAgICl9XG5cbiAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ3Nlc3Npb24tbWFuYWdlbWVudCcgJiYgKFxuICAgICAgICAgIDxTZXNzaW9uTWFuYWdlbWVudEh1YlxuICAgICAgICAgICAgb25TZXNzaW9uU2VsZWN0PXsoc2Vzc2lvbklkKSA9PiB7XG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdTZWxlY3RlZCBzZXNzaW9uOicsIHNlc3Npb25JZCk7XG4gICAgICAgICAgICAgIC8vIENvdWxkIG5hdmlnYXRlIHRvIHNlc3Npb24gZGV0YWlsIHBhZ2Ugb3Igb3BlbiBzZXNzaW9uIGluIG5ldyB0YWJcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgLz5cbiAgICAgICAgKX1cblxuICAgICAgICB7YWN0aXZlVGFiID09PSAnYmxvZy1hZG1pbicgJiYgKFxuICAgICAgICAgIDxCbG9nTWFuYWdlbWVudCAvPlxuICAgICAgICApfVxuXG4gICAgICAgIHthY3RpdmVUYWIgPT09ICdhZHZhbmNlZC1zZWFyY2gnICYmIChcbiAgICAgICAgICA8QWR2YW5jZWRTZWFyY2hJbnRlcmZhY2UgLz5cbiAgICAgICAgKX1cblxuICAgICAgICB7YWN0aXZlVGFiID09PSAncGVyZm9ybWFuY2UnICYmIChcbiAgICAgICAgICA8UGVyZm9ybWFuY2VNb25pdG9yIC8+XG4gICAgICAgICl9XG5cbiAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ3NlY3VyaXR5JyAmJiAoXG4gICAgICAgICAgPFNlY3VyaXR5RGFzaGJvYXJkIC8+XG4gICAgICAgICl9XG5cbiAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ21vYmlsZS1wd2EnICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+TW9iaWxlICYgUFdBIEZlYXR1cmVzPC9oMj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPFBXQU1hbmFnZXIgLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7YWN0aXZlVGFiID09PSAnbG9hZC10ZXN0aW5nJyAmJiAoXG4gICAgICAgICAgPExvYWRUZXN0RGFzaGJvYXJkIC8+XG4gICAgICAgICl9XG5cbiAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ3NlY3VyaXR5LXRlc3RpbmcnICYmIChcbiAgICAgICAgICA8U2VjdXJpdHlUZXN0RGFzaGJvYXJkIC8+XG4gICAgICAgICl9XG5cbiAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ3Byb2R1Y3Rpb24tZGVwbG95bWVudCcgJiYgKFxuICAgICAgICAgIDxQcm9kdWN0aW9uRGVwbG95bWVudERhc2hib2FyZCAvPlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBBbmFseXRpY3MgU2VjdGlvbiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMC81MCBiYWNrZHJvcC1ibHVyLW1kIGJvcmRlciBib3JkZXItcHVycGxlLTUwMC8yMCByb3VuZGVkLWxnIHAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNlwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8QmFyQ2hhcnQzIGNsYXNzTmFtZT1cInctNSBoLTUgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgUXVpY2sgU3RhdHNcbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIHNpemU9XCJzbVwiIGNsYXNzTmFtZT1cImJvcmRlci1ncmF5LTYwMCB0ZXh0LWdyYXktMzAwIGhvdmVyOmJnLWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAgVmlldyBGdWxsIEFuYWx5dGljc1xuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgbWQ6Z3JpZC1jb2xzLTQgZ2FwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItMVwiPnthbGxEalByb2ZpbGVzPy5sZW5ndGggfHwgMH08L2Rpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5Ub3RhbCBESnM8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi0xXCI+MDwvZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPkFjdGl2ZSBTZXNzaW9uczwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTFcIj4wPC9kaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+U29uZyBSZXF1ZXN0czwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgIHt1c2VyU3RhdHVzPy5pc0F1dGhlbnRpY2F0ZWQgPyAnMTAwJScgOiAnMCUnfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPlN5c3RlbSBTdGF0dXM8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9tYWluPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZUNvbnZleEF1dGgiLCJ1c2VRdWVyeSIsInVzZVJvdXRlciIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiYXBpIiwidXNlQXV0aEFjdGlvbnMiLCJCdXR0b24iLCJNdXNpYyIsIkxvZ091dCIsIkJhckNoYXJ0MyIsIkRKUHJvZmlsZUNhcmQiLCJTZXNzaW9uTWFuYWdlciIsIlNvbmdSZXF1ZXN0TWFuYWdlciIsIk5vdGlmaWNhdGlvbkNlbnRlciIsIlBhc3NQdXJjaGFzZSIsIlNvbmdTZWFyY2hJbnB1dCIsIkludGVncmF0aW9uVGVzdFN1aXRlIiwiUmVhbHRpbWVUZXN0U3VpdGUiLCJTZXNzaW9uTWFuYWdlbWVudEh1YiIsIkJsb2dNYW5hZ2VtZW50IiwiQWR2YW5jZWRTZWFyY2hJbnRlcmZhY2UiLCJQZXJmb3JtYW5jZU1vbml0b3IiLCJBZHZhbmNlZEFuYWx5dGljc0Rhc2hib2FyZCIsIlNlY3VyaXR5RGFzaGJvYXJkIiwiUFdBTWFuYWdlciIsIkxvYWRUZXN0RGFzaGJvYXJkIiwiU2VjdXJpdHlUZXN0RGFzaGJvYXJkIiwiUHJvZHVjdGlvbkRlcGxveW1lbnREYXNoYm9hcmQiLCJEYXNoYm9hcmQiLCJpc0F1dGhlbnRpY2F0ZWQiLCJpc0xvYWRpbmciLCJzaWduT3V0Iiwicm91dGVyIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwiY3VycmVudFVzZXIiLCJ1c2VycyIsImdldEN1cnJlbnRVc2VyIiwidXNlcldpdGhQcm9maWxlIiwiZ2V0Q3VycmVudFVzZXJXaXRoUHJvZmlsZSIsImRqUHJvZmlsZSIsInVzZXJTdGF0dXMiLCJjaGVja1VzZXJTdGF0dXMiLCJhbGxEalByb2ZpbGVzIiwiZGpQcm9maWxlcyIsImxpc3REalByb2ZpbGVzIiwibGltaXQiLCJvbmJvYXJkaW5nU3RhdHVzIiwiYWN0aXZlU2Vzc2lvbnMiLCJzZXNzaW9ucyIsImdldEN1cnJlbnRVc2VyU2Vzc2lvbnMiLCJhY3RpdmVPbmx5IiwiYWN0aXZlU2Vzc2lvbiIsImxlbmd0aCIsInB1c2giLCJoYW5kbGVTaWduT3V0IiwiZGl2IiwiY2xhc3NOYW1lIiwiaGVhZGVyIiwiaDEiLCJzcGFuIiwibmFtZSIsImVtYWlsIiwib25DbGljayIsInZhcmlhbnQiLCJzaXplIiwibWFpbiIsImgyIiwicCIsIm5hdiIsImJ1dHRvbiIsInNlc3Npb25JZCIsIl9pZCIsImgzIiwib25Tb25nU2VsZWN0Iiwic29uZyIsImNvbnNvbGUiLCJsb2ciLCJhbGVydCIsInRpdGxlIiwiYXJ0aXN0IiwicGxhY2Vob2xkZXIiLCJvblNlc3Npb25TZWxlY3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/deployment/ProductionDeploymentDashboard.tsx":
/*!*****************************************************************!*\
  !*** ./components/deployment/ProductionDeploymentDashboard.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductionDeploymentDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,FileText,Play,RefreshCw,Rocket,Settings,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,FileText,Play,RefreshCw,Rocket,Settings,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,FileText,Play,RefreshCw,Rocket,Settings,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,FileText,Play,RefreshCw,Rocket,Settings,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,FileText,Play,RefreshCw,Rocket,Settings,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,FileText,Play,RefreshCw,Rocket,Settings,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,FileText,Play,RefreshCw,Rocket,Settings,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,FileText,Play,RefreshCw,Rocket,Settings,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,FileText,Play,RefreshCw,Rocket,Settings,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,FileText,Play,RefreshCw,Rocket,Settings,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,FileText,Play,RefreshCw,Rocket,Settings,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,CheckCircle,Clock,Database,FileText,Play,RefreshCw,Rocket,Settings,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ProductionDeploymentDashboard() {\n    _s();\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [migrationConfig, setMigrationConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        tables: [\n            'users',\n            'djProfiles',\n            'sessions',\n            'songRequests',\n            'blogPosts'\n        ],\n        batchSize: 100,\n        validateData: true,\n        dryRun: true\n    });\n    const migrationStatus = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.migration.getMigrationStatus, {\n        limit: 20\n    });\n    const executeMigration = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useAction)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.migration.executeMigration);\n    const validateProductionReadiness = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useAction)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.migration.validateProductionReadiness);\n    const createDeploymentChecklist = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useAction)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.migration.createDeploymentChecklist);\n    const [readinessCheck, setReadinessCheck] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deploymentChecklist, setDeploymentChecklist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleRunMigration = async ()=>{\n        setIsRunning(true);\n        try {\n            const result = await executeMigration({\n                migrationPlan: migrationConfig,\n                supabaseConfig: {\n                    url: 'https://your-project.supabase.co',\n                    key: 'your-anon-key'\n                }\n            });\n            console.log('Migration completed:', result);\n        } catch (error) {\n            console.error('Migration failed:', error);\n        } finally{\n            setIsRunning(false);\n        }\n    };\n    const handleValidateReadiness = async ()=>{\n        try {\n            const result = await validateProductionReadiness();\n            setReadinessCheck(result);\n        } catch (error) {\n            console.error('Readiness check failed:', error);\n        }\n    };\n    const handleCreateChecklist = async ()=>{\n        try {\n            const result = await createDeploymentChecklist();\n            setDeploymentChecklist(result);\n        } catch (error) {\n            console.error('Failed to create checklist:', error);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'completed':\n            case 'passed':\n                return 'bg-green-600';\n            case 'in_progress':\n                return 'bg-blue-600';\n            case 'failed':\n                return 'bg-red-600';\n            case 'warning':\n                return 'bg-yellow-600';\n            default:\n                return 'bg-gray-600';\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'completed':\n            case 'passed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 47\n                }, this);\n            case 'in_progress':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 34\n                }, this);\n            case 'failed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 29\n                }, this);\n            case 'warning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 30\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const formatDuration = (ms)=>{\n        if (ms < 1000) return \"\".concat(ms, \"ms\");\n        if (ms < 60000) return \"\".concat((ms / 1000).toFixed(1), \"s\");\n        return \"\".concat((ms / 60000).toFixed(1), \"m\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"w-6 h-6 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this),\n                            \"Production Deployment Dashboard\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"outline\",\n                        size: \"sm\",\n                        onClick: ()=>window.location.reload(),\n                        className: \"text-white border-white/30\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            \"Refresh\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            migrationStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"bg-white/10 backdrop-blur-md border-white/20 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"Total Migrations\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: migrationStatus.summary.totalMigrations\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"w-8 h-8 text-purple-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"bg-white/10 backdrop-blur-md border-white/20 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"Completed\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: migrationStatus.summary.completedMigrations\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-8 h-8 text-green-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"bg-white/10 backdrop-blur-md border-white/20 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"In Progress\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: migrationStatus.summary.inProgressMigrations\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-8 h-8 text-blue-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"bg-white/10 backdrop-blur-md border-white/20 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-sm\",\n                                            children: \"Failed\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: migrationStatus.summary.failedMigrations\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-8 h-8 text-red-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold text-white mb-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this),\n                            \"Data Migration Configuration\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                htmlFor: \"batchSize\",\n                                                className: \"text-white\",\n                                                children: \"Batch Size\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                id: \"batchSize\",\n                                                type: \"number\",\n                                                value: migrationConfig.batchSize,\n                                                onChange: (e)=>setMigrationConfig((prev)=>({\n                                                            ...prev,\n                                                            batchSize: parseInt(e.target.value)\n                                                        })),\n                                                className: \"bg-gray-800 border-gray-600 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"validateData\",\n                                                checked: migrationConfig.validateData,\n                                                onChange: (e)=>setMigrationConfig((prev)=>({\n                                                            ...prev,\n                                                            validateData: e.target.checked\n                                                        })),\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                htmlFor: \"validateData\",\n                                                className: \"text-white\",\n                                                children: \"Validate Data\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"dryRun\",\n                                                checked: migrationConfig.dryRun,\n                                                onChange: (e)=>setMigrationConfig((prev)=>({\n                                                            ...prev,\n                                                            dryRun: e.target.checked\n                                                        })),\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                htmlFor: \"dryRun\",\n                                                className: \"text-white\",\n                                                children: \"Dry Run (Test Only)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-semibold mb-3\",\n                                        children: \"Tables to Migrate\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: migrationConfig.tables.map((table, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between bg-white/5 rounded p-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: table\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"text-xs\",\n                                                        children: \"Ready\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            onClick: handleRunMigration,\n                            disabled: isRunning,\n                            className: \"bg-purple-600 hover:bg-purple-700 text-white\",\n                            children: isRunning ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Running Migration...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this),\n                                    migrationConfig.dryRun ? 'Run Test Migration' : 'Start Production Migration'\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Production Readiness Check\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                onClick: handleValidateReadiness,\n                                variant: \"outline\",\n                                className: \"text-white border-white/30\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Run Readiness Check\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, this),\n                    readinessCheck && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded \".concat(getStatusColor(readinessCheck.overallStatus)),\n                                                children: getStatusIcon(readinessCheck.overallStatus)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-white\",\n                                                        children: \"Overall Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/60 text-sm\",\n                                                        children: [\n                                                            readinessCheck.summary.passed,\n                                                            \"/\",\n                                                            readinessCheck.summary.total,\n                                                            \" checks passed\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                        className: \"\".concat(getStatusColor(readinessCheck.overallStatus), \" text-white\"),\n                                        children: readinessCheck.overallStatus.toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: readinessCheck.checks.map((check, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between bg-white/5 rounded p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-1 rounded \".concat(getStatusColor(check.status)),\n                                                        children: getStatusIcon(check.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-white font-medium\",\n                                                                children: check.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/60 text-sm\",\n                                                                children: check.message\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                className: \"\".concat(getStatusColor(check.status), \" text-white text-xs\"),\n                                                children: check.status\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Deployment Checklist\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                onClick: handleCreateChecklist,\n                                variant: \"outline\",\n                                className: \"text-white border-white/30\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Generate Checklist\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, this),\n                    deploymentChecklist && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: deploymentChecklist.totalTasks\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/60 text-sm\",\n                                                children: \"Total Tasks\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: deploymentChecklist.criticalTasks\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/60 text-sm\",\n                                                children: \"Critical Tasks\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: \"0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/60 text-sm\",\n                                                children: \"Completed\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-semibold mb-3\",\n                                        children: \"Pre-Deployment Tasks\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: deploymentChecklist.checklist.preDeployment.map((task, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between bg-white/5 rounded p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-1 rounded \".concat(getStatusColor(task.status)),\n                                                                children: getStatusIcon(task.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"text-white font-medium\",\n                                                                        children: task.task\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                                        lineNumber: 353,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white/60 text-sm\",\n                                                                        children: task.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                                        lineNumber: 354,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            task.critical && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                className: \"bg-red-600 text-white text-xs\",\n                                                                children: \"Critical\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                className: \"\".concat(getStatusColor(task.status), \" text-white text-xs\"),\n                                                                children: task.status\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-semibold mb-3\",\n                                        children: \"Deployment Tasks\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: deploymentChecklist.checklist.deployment.map((task, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between bg-white/5 rounded p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-1 rounded \".concat(getStatusColor(task.status)),\n                                                                children: getStatusIcon(task.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"text-white font-medium\",\n                                                                        children: task.task\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white/60 text-sm\",\n                                                                        children: task.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                                        lineNumber: 382,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            task.critical && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                className: \"bg-red-600 text-white text-xs\",\n                                                                children: \"Critical\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                className: \"\".concat(getStatusColor(task.status), \" text-white text-xs\"),\n                                                                children: task.status\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-semibold mb-3\",\n                                        children: \"Post-Deployment Tasks\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: deploymentChecklist.checklist.postDeployment.map((task, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between bg-white/5 rounded p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-1 rounded \".concat(getStatusColor(task.status)),\n                                                                children: getStatusIcon(task.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"text-white font-medium\",\n                                                                        children: task.task\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white/60 text-sm\",\n                                                                        children: task.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                                        lineNumber: 410,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            task.critical && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                className: \"bg-red-600 text-white text-xs\",\n                                                                children: \"Critical\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                className: \"\".concat(getStatusColor(task.status), \" text-white text-xs\"),\n                                                                children: task.status\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, this),\n            migrationStatus && migrationStatus.logs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold text-white mb-4\",\n                        children: \"Recent Migration Activity\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3 max-h-96 overflow-y-auto\",\n                        children: migrationStatus.logs.map((log, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between bg-white/5 rounded p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-400 mt-0.5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-white\",\n                                                        children: log.eventType\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/70 text-sm\",\n                                                        children: log.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/60 text-xs\",\n                                                        children: [\n                                                            \"Migration: \",\n                                                            log.migrationId.slice(-8)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-xs\",\n                                            children: new Date(log.timestamp).toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                        lineNumber: 434,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                lineNumber: 431,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"bg-white/10 backdrop-blur-md border-white/20 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold text-white mb-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"w-5 h-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 11\n                            }, this),\n                            \"Deployment Guidelines\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-400 mt-0.5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-white font-semibold\",\n                                                        children: \"Pre-Deployment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/60 text-sm\",\n                                                        children: \"Complete all testing, backup data, and validate production environment before deployment.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-400 mt-0.5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-white font-semibold\",\n                                                        children: \"Migration Process\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/60 text-sm\",\n                                                        children: \"Run dry-run first, then execute production migration during low-traffic hours.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-400 mt-0.5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-white font-semibold\",\n                                                        children: \"Post-Deployment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/60 text-sm\",\n                                                        children: \"Monitor system performance, validate functionality, and be ready to rollback if needed.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_CheckCircle_Clock_Database_FileText_Play_RefreshCw_Rocket_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-400 mt-0.5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-white font-semibold\",\n                                                        children: \"Monitoring\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/60 text-sm\",\n                                                        children: \"Continuously monitor error rates, performance metrics, and user feedback.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n                lineNumber: 459,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PlayBeg/PlayBeg/playbeg-convex/components/deployment/ProductionDeploymentDashboard.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductionDeploymentDashboard, \"xhDHPrmJG+OT+MYNsbpiQRe3b1w=\", false, function() {\n    return [\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useAction,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useAction,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useAction\n    ];\n});\n_c = ProductionDeploymentDashboard;\nvar _c;\n$RefreshReg$(_c, \"ProductionDeploymentDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/deployment/ProductionDeploymentDashboard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/rocket.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Rocket)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.522.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z\",\n            key: \"m3kijz\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z\",\n            key: \"1fmvmk\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0\",\n            key: \"1f8sc4\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5\",\n            key: \"qeys4\"\n        }\n    ]\n];\nconst Rocket = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"rocket\", __iconNode);\n //# sourceMappingURL=rocket.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\n"));

/***/ })

});