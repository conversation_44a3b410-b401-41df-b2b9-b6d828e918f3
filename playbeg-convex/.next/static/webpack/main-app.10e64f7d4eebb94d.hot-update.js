"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main-app",{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-index.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/client/app-index.js ***!
  \****************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// imports polyfill from `@next/polyfill-module` after build.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hydrate\", ({\n    enumerable: true,\n    get: function() {\n        return hydrate;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n__webpack_require__(/*! ../build/polyfills/polyfill-module */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/polyfill-module.js\");\n__webpack_require__(/*! ./components/globals/patch-console */ \"(app-pages-browser)/./node_modules/next/dist/client/components/globals/patch-console.js\");\n__webpack_require__(/*! ./components/globals/handle-global-errors */ \"(app-pages-browser)/./node_modules/next/dist/client/components/globals/handle-global-errors.js\");\nconst _client = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/client.js\"));\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _client1 = __webpack_require__(/*! react-server-dom-webpack/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/client.js\");\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ../shared/lib/head-manager-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _onrecoverableerror = __webpack_require__(/*! ./react-client-callbacks/on-recoverable-error */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js\");\nconst _errorboundarycallbacks = __webpack_require__(/*! ./react-client-callbacks/error-boundary-callbacks */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js\");\nconst _appcallserver = __webpack_require__(/*! ./app-call-server */ \"(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js\");\nconst _appfindsourcemapurl = __webpack_require__(/*! ./app-find-source-map-url */ \"(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\");\nconst _actionqueue = __webpack_require__(/*! ../shared/lib/router/action-queue */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/action-queue.js\");\nconst _approuter = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./components/app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\"));\nconst _createinitialrouterstate = __webpack_require__(/*! ./components/router-reducer/create-initial-router-state */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _appbuildid = __webpack_require__(/*! ./app-build-id */ \"(app-pages-browser)/./node_modules/next/dist/client/app-build-id.js\");\nconst _iserrorthrownwhilerenderingrsc = __webpack_require__(/*! ./lib/is-error-thrown-while-rendering-rsc */ \"(app-pages-browser)/./node_modules/next/dist/client/lib/is-error-thrown-while-rendering-rsc.js\");\n/// <reference types=\"react-dom/experimental\" />\nconst appElement = document;\nconst encoder = new TextEncoder();\nlet initialServerDataBuffer = undefined;\nlet initialServerDataWriter = undefined;\nlet initialServerDataLoaded = false;\nlet initialServerDataFlushed = false;\nlet initialFormStateData = null;\nfunction nextServerDataCallback(seg) {\n    if (seg[0] === 0) {\n        initialServerDataBuffer = [];\n    } else if (seg[0] === 1) {\n        if (!initialServerDataBuffer) throw Object.defineProperty(new Error('Unexpected server data: missing bootstrap script.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E18\",\n            enumerable: false,\n            configurable: true\n        });\n        if (initialServerDataWriter) {\n            initialServerDataWriter.enqueue(encoder.encode(seg[1]));\n        } else {\n            initialServerDataBuffer.push(seg[1]);\n        }\n    } else if (seg[0] === 2) {\n        initialFormStateData = seg[1];\n    } else if (seg[0] === 3) {\n        if (!initialServerDataBuffer) throw Object.defineProperty(new Error('Unexpected server data: missing bootstrap script.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E18\",\n            enumerable: false,\n            configurable: true\n        });\n        // Decode the base64 string back to binary data.\n        const binaryString = atob(seg[1]);\n        const decodedChunk = new Uint8Array(binaryString.length);\n        for(var i = 0; i < binaryString.length; i++){\n            decodedChunk[i] = binaryString.charCodeAt(i);\n        }\n        if (initialServerDataWriter) {\n            initialServerDataWriter.enqueue(decodedChunk);\n        } else {\n            initialServerDataBuffer.push(decodedChunk);\n        }\n    }\n}\nfunction isStreamErrorOrUnfinished(ctr) {\n    // If `desiredSize` is null, it means the stream is closed or errored. If it is lower than 0, the stream is still unfinished.\n    return ctr.desiredSize === null || ctr.desiredSize < 0;\n}\n// There might be race conditions between `nextServerDataRegisterWriter` and\n// `DOMContentLoaded`. The former will be called when React starts to hydrate\n// the root, the latter will be called when the DOM is fully loaded.\n// For streaming, the former is called first due to partial hydration.\n// For non-streaming, the latter can be called first.\n// Hence, we use two variables `initialServerDataLoaded` and\n// `initialServerDataFlushed` to make sure the writer will be closed and\n// `initialServerDataBuffer` will be cleared in the right time.\nfunction nextServerDataRegisterWriter(ctr) {\n    if (initialServerDataBuffer) {\n        initialServerDataBuffer.forEach((val)=>{\n            ctr.enqueue(typeof val === 'string' ? encoder.encode(val) : val);\n        });\n        if (initialServerDataLoaded && !initialServerDataFlushed) {\n            if (isStreamErrorOrUnfinished(ctr)) {\n                ctr.error(Object.defineProperty(new Error('The connection to the page was unexpectedly closed, possibly due to the stop button being clicked, loss of Wi-Fi, or an unstable internet connection.'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E117\",\n                    enumerable: false,\n                    configurable: true\n                }));\n            } else {\n                ctr.close();\n            }\n            initialServerDataFlushed = true;\n            initialServerDataBuffer = undefined;\n        }\n    }\n    initialServerDataWriter = ctr;\n}\n// When `DOMContentLoaded`, we can close all pending writers to finish hydration.\nconst DOMContentLoaded = function() {\n    if (initialServerDataWriter && !initialServerDataFlushed) {\n        initialServerDataWriter.close();\n        initialServerDataFlushed = true;\n        initialServerDataBuffer = undefined;\n    }\n    initialServerDataLoaded = true;\n};\n_c = DOMContentLoaded;\n// It's possible that the DOM is already loaded.\nif (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', DOMContentLoaded, false);\n} else {\n    // Delayed in marco task to ensure it's executed later than hydration\n    setTimeout(DOMContentLoaded);\n}\nconst nextServerDataLoadingGlobal = self.__next_f = self.__next_f || [];\nnextServerDataLoadingGlobal.forEach(nextServerDataCallback);\nnextServerDataLoadingGlobal.push = nextServerDataCallback;\nconst readable = new ReadableStream({\n    start (controller) {\n        nextServerDataRegisterWriter(controller);\n    }\n});\nconst initialServerResponse = (0, _client1.createFromReadableStream)(readable, {\n    callServer: _appcallserver.callServer,\n    findSourceMapURL: _appfindsourcemapurl.findSourceMapURL\n});\n// React overrides `.then` and doesn't return a new promise chain,\n// so we wrap the action queue in a promise to ensure that its value\n// is defined when the promise resolves.\n// https://github.com/facebook/react/blob/163365a07872337e04826c4f501565d43dbd2fd4/packages/react-client/src/ReactFlightClient.js#L189-L190\nconst pendingActionQueue = new Promise((resolve, reject)=>{\n    initialServerResponse.then((initialRSCPayload)=>{\n        // setAppBuildId should be called only once, during JS initialization\n        // and before any components have hydrated.\n        (0, _appbuildid.setAppBuildId)(initialRSCPayload.b);\n        resolve((0, _actionqueue.createMutableActionQueue)((0, _createinitialrouterstate.createInitialRouterState)({\n            initialFlightData: initialRSCPayload.f,\n            initialCanonicalUrlParts: initialRSCPayload.c,\n            initialParallelRoutes: new Map(),\n            location: window.location,\n            couldBeIntercepted: initialRSCPayload.i,\n            postponed: initialRSCPayload.s,\n            prerendered: initialRSCPayload.S\n        })));\n    }, (err)=>reject(err));\n});\nfunction ServerRoot() {\n    const initialRSCPayload = (0, _react.use)(initialServerResponse);\n    const actionQueue = (0, _react.use)(pendingActionQueue);\n    const router = /*#__PURE__*/ (0, _jsxruntime.jsx)(_approuter.default, {\n        actionQueue: actionQueue,\n        globalErrorComponentAndStyles: initialRSCPayload.G,\n        assetPrefix: initialRSCPayload.p\n    });\n    if ( true && initialRSCPayload.m) {\n        // We provide missing slot information in a context provider only during development\n        // as we log some additional information about the missing slots in the console.\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.MissingSlotContext, {\n            value: initialRSCPayload.m,\n            children: router\n        });\n    }\n    return router;\n}\n_c1 = ServerRoot;\nconst StrictModeIfEnabled =  true ? _react.default.StrictMode : 0;\nfunction Root(param) {\n    let { children } = param;\n    if (false) {}\n    return children;\n}\n_c2 = Root;\nconst reactRootOptions = {\n    onRecoverableError: _onrecoverableerror.onRecoverableError,\n    onCaughtError: _errorboundarycallbacks.onCaughtError,\n    onUncaughtError: _errorboundarycallbacks.onUncaughtError\n};\nfunction hydrate() {\n    var _window___next_root_layout_missing_tags;\n    const reactEl = /*#__PURE__*/ (0, _jsxruntime.jsx)(StrictModeIfEnabled, {\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_headmanagercontextsharedruntime.HeadManagerContext.Provider, {\n            value: {\n                appDir: true\n            },\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Root, {\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(ServerRoot, {})\n            })\n        })\n    });\n    if (document.documentElement.id === '__next_error__' || !!((_window___next_root_layout_missing_tags = window.__next_root_layout_missing_tags) == null ? void 0 : _window___next_root_layout_missing_tags.length)) {\n        let element = reactEl;\n        // Server rendering failed, fall back to client-side rendering\n        if ( true && (0, _iserrorthrownwhilerenderingrsc.shouldRenderRootLevelErrorOverlay)()) {\n            const { createRootLevelDevOverlayElement } = __webpack_require__(/*! ./components/react-dev-overlay/app/client-entry */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/client-entry.js\");\n            // Note this won't cause hydration mismatch because we are doing CSR w/o hydration\n            element = createRootLevelDevOverlayElement(element);\n        }\n        _client.default.createRoot(appElement, reactRootOptions).render(element);\n    } else {\n        _react.default.startTransition(()=>{\n            _client.default.hydrateRoot(appElement, reactEl, {\n                ...reactRootOptions,\n                formState: initialFormStateData\n            });\n        });\n    }\n    // TODO-APP: Remove this logic when Float has GC built-in in development.\n    if (true) {\n        const { linkGc } = __webpack_require__(/*! ./app-link-gc */ \"(app-pages-browser)/./node_modules/next/dist/client/app-link-gc.js\");\n        linkGc();\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-index.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"DOMContentLoaded\");\n$RefreshReg$(_c1, \"ServerRoot\");\n$RefreshReg$(_c2, \"Root\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-link-gc.js":
/*!******************************************************!*\
  !*** ./node_modules/next/dist/client/app-link-gc.js ***!
  \******************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"linkGc\", ({\n    enumerable: true,\n    get: function() {\n        return linkGc;\n    }\n}));\nfunction linkGc() {\n    // TODO-APP: Remove this logic when Float has GC built-in in development.\n    if (true) {\n        const callback = (mutationList)=>{\n            for (const mutation of mutationList){\n                if (mutation.type === 'childList') {\n                    for (const node of mutation.addedNodes){\n                        if ('tagName' in node && node.tagName === 'LINK') {\n                            var _link_dataset_precedence;\n                            const link = node;\n                            if ((_link_dataset_precedence = link.dataset.precedence) == null ? void 0 : _link_dataset_precedence.startsWith('next')) {\n                                const href = link.getAttribute('href');\n                                if (href) {\n                                    const [resource, version] = href.split('?v=', 2);\n                                    if (version) {\n                                        const currentOrigin = window.location.origin;\n                                        const allLinks = [\n                                            ...document.querySelectorAll('link[href^=\"' + resource + '\"]'),\n                                            // It's possible that the resource is a full URL or only pathname,\n                                            // so we need to remove the alternative href as well.\n                                            ...document.querySelectorAll('link[href^=\"' + (resource.startsWith(currentOrigin) ? resource.slice(currentOrigin.length) : currentOrigin + resource) + '\"]')\n                                        ];\n                                        for (const otherLink of allLinks){\n                                            var _otherLink_dataset_precedence;\n                                            if ((_otherLink_dataset_precedence = otherLink.dataset.precedence) == null ? void 0 : _otherLink_dataset_precedence.startsWith('next')) {\n                                                const otherHref = otherLink.getAttribute('href');\n                                                if (otherHref) {\n                                                    const [, otherVersion] = otherHref.split('?v=', 2);\n                                                    if (!otherVersion || +otherVersion < +version) {\n                                                        // Delay the removal of the stylesheet to avoid FOUC\n                                                        // caused by `@font-face` rules, as they seem to be\n                                                        // a couple of ticks delayed between the old and new\n                                                        // styles being swapped even if the font is cached.\n                                                        setTimeout(()=>{\n                                                            otherLink.remove();\n                                                        }, 5);\n                                                        const preloadLink = document.querySelector('link[rel=\"preload\"][as=\"style\"][href=\"' + otherHref + '\"]');\n                                                        if (preloadLink) {\n                                                            preloadLink.remove();\n                                                        }\n                                                    }\n                                                }\n                                            }\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        };\n        // Create an observer instance linked to the callback function\n        const observer = new MutationObserver(callback);\n        observer.observe(document.head, {\n            childList: true\n        });\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-link-gc.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-link-gc.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/globals/intercept-console-error.js ***!
  \*************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    originConsoleError: function() {\n        return originConsoleError;\n    },\n    patchConsoleError: function() {\n        return patchConsoleError;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst _isnextroutererror = __webpack_require__(/*! ../is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _console = __webpack_require__(/*! ../../lib/console */ \"(app-pages-browser)/./node_modules/next/dist/client/lib/console.js\");\nconst originConsoleError = globalThis.console.error;\nfunction patchConsoleError() {\n    // Ensure it's only patched once\n    if (false) {}\n    window.console.error = function error() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        let maybeError;\n        if (true) {\n            const { error: replayedError } = (0, _console.parseConsoleArgs)(args);\n            if (replayedError) {\n                maybeError = replayedError;\n            } else if ((0, _iserror.default)(args[0])) {\n                maybeError = args[0];\n            } else {\n                // See https://github.com/facebook/react/blob/d50323eb845c5fde0d720cae888bf35dedd05506/packages/react-reconciler/src/ReactFiberErrorLogger.js#L78\n                maybeError = args[1];\n            }\n        } else {}\n        if (!(0, _isnextroutererror.isNextRouterError)(maybeError)) {\n            if (true) {\n                (0, _useerrorhandler.handleClientError)(// but if we pass the error directly, `handleClientError` will ignore it\n                maybeError, args, true);\n            }\n            originConsoleError.apply(window.console, args);\n        }\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=intercept-console-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"HTTPAccessFallbackBoundary\", ({\n    enumerable: true,\n    get: function() {\n        return HTTPAccessFallbackBoundary;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _navigationuntracked = __webpack_require__(/*! ../navigation-untracked */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation-untracked.js\");\nconst _httpaccessfallback = __webpack_require__(/*! ./http-access-fallback */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/http-access-fallback.js\");\nconst _warnonce = __webpack_require__(/*! ../../../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nclass HTTPAccessFallbackErrorBoundary extends _react.default.Component {\n    componentDidCatch() {\n        if ( true && this.props.missingSlots && this.props.missingSlots.size > 0 && // A missing children slot is the typical not-found case, so no need to warn\n        !this.props.missingSlots.has('children')) {\n            let warningMessage = 'No default component was found for a parallel route rendered on this page. Falling back to nearest NotFound boundary.\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/routing/parallel-routes#defaultjs\\n\\n';\n            const formattedSlots = Array.from(this.props.missingSlots).sort((a, b)=>a.localeCompare(b)).map((slot)=>\"@\" + slot).join(', ');\n            warningMessage += 'Missing slots: ' + formattedSlots;\n            (0, _warnonce.warnOnce)(warningMessage);\n        }\n    }\n    static getDerivedStateFromError(error) {\n        if ((0, _httpaccessfallback.isHTTPAccessFallbackError)(error)) {\n            const httpStatus = (0, _httpaccessfallback.getAccessFallbackHTTPStatus)(error);\n            return {\n                triggeredStatus: httpStatus\n            };\n        }\n        // Re-throw if error is not for 404\n        throw error;\n    }\n    static getDerivedStateFromProps(props, state) {\n        /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */ if (props.pathname !== state.previousPathname && state.triggeredStatus) {\n            return {\n                triggeredStatus: undefined,\n                previousPathname: props.pathname\n            };\n        }\n        return {\n            triggeredStatus: state.triggeredStatus,\n            previousPathname: props.pathname\n        };\n    }\n    render() {\n        const { notFound, forbidden, unauthorized, children } = this.props;\n        const { triggeredStatus } = this.state;\n        const errorComponents = {\n            [_httpaccessfallback.HTTPAccessErrorStatus.NOT_FOUND]: notFound,\n            [_httpaccessfallback.HTTPAccessErrorStatus.FORBIDDEN]: forbidden,\n            [_httpaccessfallback.HTTPAccessErrorStatus.UNAUTHORIZED]: unauthorized\n        };\n        if (triggeredStatus) {\n            const isNotFound = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.NOT_FOUND && notFound;\n            const isForbidden = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.FORBIDDEN && forbidden;\n            const isUnauthorized = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.UNAUTHORIZED && unauthorized;\n            // If there's no matched boundary in this layer, keep throwing the error by rendering the children\n            if (!(isNotFound || isForbidden || isUnauthorized)) {\n                return children;\n            }\n            return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                        name: \"robots\",\n                        content: \"noindex\"\n                    }),\n                     true && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                        name: \"boundary-next-error\",\n                        content: (0, _httpaccessfallback.getAccessFallbackErrorTypeByStatus)(triggeredStatus)\n                    }),\n                    errorComponents[triggeredStatus]\n                ]\n            });\n        }\n        return children;\n    }\n    constructor(props){\n        super(props);\n        this.state = {\n            triggeredStatus: undefined,\n            previousPathname: props.pathname\n        };\n    }\n}\nfunction HTTPAccessFallbackBoundary(param) {\n    let { notFound, forbidden, unauthorized, children } = param;\n    // When we're rendering the missing params shell, this will return null. This\n    // is because we won't be rendering any not found boundaries or error\n    // boundaries for the missing params shell. When this runs on the client\n    // (where these error can occur), we will get the correct pathname.\n    const pathname = (0, _navigationuntracked.useUntrackedPathname)();\n    const missingSlots = (0, _react.useContext)(_approutercontextsharedruntime.MissingSlotContext);\n    const hasErrorFallback = !!(notFound || forbidden || unauthorized);\n    if (hasErrorFallback) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(HTTPAccessFallbackErrorBoundary, {\n            pathname: pathname,\n            notFound: notFound,\n            forbidden: forbidden,\n            unauthorized: unauthorized,\n            missingSlots: missingSlots,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: children\n    });\n}\n_c = HTTPAccessFallbackBoundary;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-boundary.js.map\nvar _c;\n$RefreshReg$(_c, \"HTTPAccessFallbackBoundary\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/links.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/components/links.js ***!
  \***********************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    mountLinkInstance: function() {\n        return mountLinkInstance;\n    },\n    onLinkVisibilityChanged: function() {\n        return onLinkVisibilityChanged;\n    },\n    onNavigationIntent: function() {\n        return onNavigationIntent;\n    },\n    pingVisibleLinks: function() {\n        return pingVisibleLinks;\n    },\n    unmountLinkInstance: function() {\n        return unmountLinkInstance;\n    }\n});\nconst _actionqueue = __webpack_require__(/*! ../../shared/lib/router/action-queue */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/action-queue.js\");\nconst _approuter = __webpack_require__(/*! ./app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\");\n// Use a WeakMap to associate a Link instance with its DOM element. This is\n// used by the IntersectionObserver to track the link's visibility.\nconst links = typeof WeakMap === 'function' ? new WeakMap() : new Map();\n// A Set of the currently visible links. We re-prefetch visible links after a\n// cache invalidation, or when the current URL changes. It's a separate data\n// structure from the WeakMap above because only the visible links need to\n// be enumerated.\nconst visibleLinks = new Set();\n// A single IntersectionObserver instance shared by all <Link> components.\nconst observer = typeof IntersectionObserver === 'function' ? new IntersectionObserver(handleIntersect, {\n    rootMargin: '200px'\n}) : null;\nfunction mountLinkInstance(element, href, router, kind) {\n    let prefetchUrl = null;\n    try {\n        prefetchUrl = (0, _approuter.createPrefetchURL)(href);\n        if (prefetchUrl === null) {\n            // We only track the link if it's prefetchable. For example, this excludes\n            // links to external URLs.\n            return;\n        }\n    } catch (e) {\n        // createPrefetchURL sometimes throws an error if an invalid URL is\n        // provided, though I'm not sure if it's actually necessary.\n        // TODO: Consider removing the throw from the inner function, or change it\n        // to reportError. Or maybe the error isn't even necessary for automatic\n        // prefetches, just navigations.\n        const reportErrorFn = typeof reportError === 'function' ? reportError : console.error;\n        reportErrorFn(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\");\n        return;\n    }\n    const instance = {\n        prefetchHref: prefetchUrl.href,\n        router,\n        kind,\n        isVisible: false,\n        wasHoveredOrTouched: false,\n        prefetchTask: null,\n        cacheVersion: -1\n    };\n    const existingInstance = links.get(element);\n    if (existingInstance !== undefined) {\n        // This shouldn't happen because each <Link> component should have its own\n        // anchor tag instance, but it's defensive coding to avoid a memory leak in\n        // case there's a logical error somewhere else.\n        unmountLinkInstance(element);\n    }\n    links.set(element, instance);\n    if (observer !== null) {\n        observer.observe(element);\n    }\n}\nfunction unmountLinkInstance(element) {\n    const instance = links.get(element);\n    if (instance !== undefined) {\n        links.delete(element);\n        visibleLinks.delete(instance);\n        const prefetchTask = instance.prefetchTask;\n        if (prefetchTask !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(prefetchTask);\n        }\n    }\n    if (observer !== null) {\n        observer.unobserve(element);\n    }\n}\nfunction handleIntersect(entries) {\n    for (const entry of entries){\n        // Some extremely old browsers or polyfills don't reliably support\n        // isIntersecting so we check intersectionRatio instead. (Do we care? Not\n        // really. But whatever this is fine.)\n        const isVisible = entry.intersectionRatio > 0;\n        onLinkVisibilityChanged(entry.target, isVisible);\n    }\n}\nfunction onLinkVisibilityChanged(element, isVisible) {\n    if (true) {\n        // Prefetching on viewport is disabled in development for performance\n        // reasons, because it requires compiling the target page.\n        // TODO: Investigate re-enabling this.\n        return;\n    }\n    const instance = links.get(element);\n    if (instance === undefined) {\n        return;\n    }\n    instance.isVisible = isVisible;\n    if (isVisible) {\n        visibleLinks.add(instance);\n    } else {\n        visibleLinks.delete(instance);\n    }\n    rescheduleLinkPrefetch(instance);\n}\nfunction onNavigationIntent(element) {\n    const instance = links.get(element);\n    if (instance === undefined) {\n        return;\n    }\n    // Prefetch the link on hover/touchstart.\n    if (instance !== undefined) {\n        instance.wasHoveredOrTouched = true;\n        rescheduleLinkPrefetch(instance);\n    }\n}\nfunction rescheduleLinkPrefetch(instance) {\n    const existingPrefetchTask = instance.prefetchTask;\n    if (!instance.isVisible) {\n        // Cancel any in-progress prefetch task. (If it already finished then this\n        // is a no-op.)\n        if (existingPrefetchTask !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(existingPrefetchTask);\n        }\n        // We don't need to reset the prefetchTask to null upon cancellation; an\n        // old task object can be rescheduled with bumpPrefetchTask. This is a\n        // micro-optimization but also makes the code simpler (don't need to\n        // worry about whether an old task object is stale).\n        return;\n    }\n    if (true) {\n        // The old prefetch implementation does not have different priority levels.\n        // Just schedule a new prefetch task.\n        prefetchWithOldCacheImplementation(instance);\n        return;\n    }\n    // In the Segment Cache implementation, we assign a higher priority level to\n    // links that were at one point hovered or touched. Since the queue is last-\n    // in-first-out, the highest priority Link is whichever one was hovered last.\n    //\n    // We also increase the relative priority of links whenever they re-enter the\n    // viewport, as if they were being scheduled for the first time.\n    const priority = instance.wasHoveredOrTouched ? _segmentcache.PrefetchPriority.Intent : _segmentcache.PrefetchPriority.Default;\n    if (existingPrefetchTask === null) {\n        // Initiate a prefetch task.\n        const appRouterState = (0, _actionqueue.getCurrentAppRouterState)();\n        if (appRouterState !== null) {\n            const nextUrl = appRouterState.nextUrl;\n            const treeAtTimeOfPrefetch = appRouterState.tree;\n            const cacheKey = (0, _segmentcache.createCacheKey)(instance.prefetchHref, nextUrl);\n            instance.prefetchTask = (0, _segmentcache.schedulePrefetchTask)(cacheKey, treeAtTimeOfPrefetch, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n            instance.cacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n        }\n    } else {\n        // We already have an old task object that we can reschedule. This is\n        // effectively the same as canceling the old task and creating a new one.\n        (0, _segmentcache.bumpPrefetchTask)(existingPrefetchTask, priority);\n    }\n}\nfunction pingVisibleLinks(nextUrl, tree) {\n    // For each currently visible link, cancel the existing prefetch task (if it\n    // exists) and schedule a new one. This is effectively the same as if all the\n    // visible links left and then re-entered the viewport.\n    //\n    // This is called when the Next-Url or the base tree changes, since those\n    // may affect the result of a prefetch task. It's also called after a\n    // cache invalidation.\n    const currentCacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    for (const instance of visibleLinks){\n        const task = instance.prefetchTask;\n        if (task !== null && instance.cacheVersion === currentCacheVersion && task.key.nextUrl === nextUrl && task.treeAtTimeOfPrefetch === tree) {\n            continue;\n        }\n        // Something changed. Cancel the existing prefetch task and schedule a\n        // new one.\n        if (task !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(task);\n        }\n        const cacheKey = (0, _segmentcache.createCacheKey)(instance.prefetchHref, nextUrl);\n        const priority = instance.wasHoveredOrTouched ? _segmentcache.PrefetchPriority.Intent : _segmentcache.PrefetchPriority.Default;\n        instance.prefetchTask = (0, _segmentcache.schedulePrefetchTask)(cacheKey, tree, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n        instance.cacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    }\n}\nfunction prefetchWithOldCacheImplementation(instance) {\n    // This is the path used when the Segment Cache is not enabled.\n    if (false) {}\n    const doPrefetch = async ()=>{\n        // note that `appRouter.prefetch()` is currently sync,\n        // so we have to wrap this call in an async function to be able to catch() errors below.\n        return instance.router.prefetch(instance.prefetchHref, {\n            kind: instance.kind\n        });\n    };\n    // Prefetch the page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    doPrefetch().catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=links.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/links.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js ***!
  \************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createInitialRouterState\", ({\n    enumerable: true,\n    get: function() {\n        return createInitialRouterState;\n    }\n}));\nconst _createhreffromurl = __webpack_require__(/*! ./create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _filllazyitemstillleafwithhead = __webpack_require__(/*! ./fill-lazy-items-till-leaf-with-head */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.js\");\nconst _computechangedpath = __webpack_require__(/*! ./compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _prefetchcacheutils = __webpack_require__(/*! ./prefetch-cache-utils */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _refetchinactiveparallelsegments = __webpack_require__(/*! ./refetch-inactive-parallel-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js\");\nconst _flightdatahelpers = __webpack_require__(/*! ../../flight-data-helpers */ \"(app-pages-browser)/./node_modules/next/dist/client/flight-data-helpers.js\");\nfunction createInitialRouterState(param) {\n    let { initialFlightData, initialCanonicalUrlParts, initialParallelRoutes, location, couldBeIntercepted, postponed, prerendered } = param;\n    // When initialized on the server, the canonical URL is provided as an array of parts.\n    // This is to ensure that when the RSC payload streamed to the client, crawlers don't interpret it\n    // as a URL that should be crawled.\n    const initialCanonicalUrl = initialCanonicalUrlParts.join('/');\n    const normalizedFlightData = (0, _flightdatahelpers.getFlightDataPartsFromPath)(initialFlightData[0]);\n    const { tree: initialTree, seedData: initialSeedData, head: initialHead } = normalizedFlightData;\n    // For the SSR render, seed data should always be available (we only send back a `null` response\n    // in the case of a `loading` segment, pre-PPR.)\n    const rsc = initialSeedData == null ? void 0 : initialSeedData[1];\n    var _initialSeedData_;\n    const loading = (_initialSeedData_ = initialSeedData == null ? void 0 : initialSeedData[3]) != null ? _initialSeedData_ : null;\n    const cache = {\n        lazyData: null,\n        rsc,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        // The cache gets seeded during the first render. `initialParallelRoutes` ensures the cache from the first render is there during the second render.\n        parallelRoutes: initialParallelRoutes,\n        loading\n    };\n    const canonicalUrl = // This is safe to do as canonicalUrl can't be rendered, it's only used to control the history updates in the useEffect further down in this file.\n    location ? (0, _createhreffromurl.createHrefFromUrl)(location) : initialCanonicalUrl;\n    (0, _refetchinactiveparallelsegments.addRefreshMarkerToActiveParallelSegments)(initialTree, canonicalUrl);\n    const prefetchCache = new Map();\n    // When the cache hasn't been seeded yet we fill the cache with the head.\n    if (initialParallelRoutes === null || initialParallelRoutes.size === 0) {\n        (0, _filllazyitemstillleafwithhead.fillLazyItemsTillLeafWithHead)(cache, undefined, initialTree, initialSeedData, initialHead, undefined);\n    }\n    var _ref;\n    const initialState = {\n        tree: initialTree,\n        cache,\n        prefetchCache,\n        pushRef: {\n            pendingPush: false,\n            mpaNavigation: false,\n            // First render needs to preserve the previous window.history.state\n            // to avoid it being overwritten on navigation back/forward with MPA Navigation.\n            preserveCustomHistoryState: true\n        },\n        focusAndScrollRef: {\n            apply: false,\n            onlyHashChange: false,\n            hashFragment: null,\n            segmentPaths: []\n        },\n        canonicalUrl,\n        nextUrl: (_ref = (0, _computechangedpath.extractPathFromFlightRouterState)(initialTree) || (location == null ? void 0 : location.pathname)) != null ? _ref : null\n    };\n    if (false) {}\n    return initialState;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=create-initial-router-state.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js ***!
  \********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"handleSegmentMismatch\", ({\n    enumerable: true,\n    get: function() {\n        return handleSegmentMismatch;\n    }\n}));\nconst _navigatereducer = __webpack_require__(/*! ./reducers/navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nfunction handleSegmentMismatch(state, action, treePatch) {\n    if (true) {\n        console.warn('Performing hard navigation because your application experienced an unrecoverable error. If this keeps occurring, please file a Next.js issue.\\n\\n' + 'Reason: Segment mismatch\\n' + (\"Last Action: \" + action.type + \"\\n\\n\") + (\"Current Tree: \" + JSON.stringify(state.tree) + \"\\n\\n\") + (\"Tree Patch Payload: \" + JSON.stringify(treePatch)));\n    }\n    return (0, _navigatereducer.handleExternalUrl)(state, {}, state.canonicalUrl, true);\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=handle-segment-mismatch.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    STATIC_STALETIME_MS: function() {\n        return STATIC_STALETIME_MS;\n    },\n    createSeededPrefetchCacheEntry: function() {\n        return createSeededPrefetchCacheEntry;\n    },\n    getOrCreatePrefetchCacheEntry: function() {\n        return getOrCreatePrefetchCacheEntry;\n    },\n    prunePrefetchCache: function() {\n        return prunePrefetchCache;\n    }\n});\nconst _fetchserverresponse = __webpack_require__(/*! ./fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst INTERCEPTION_CACHE_KEY_MARKER = '%';\n/**\n * Creates a cache key for the router prefetch cache\n *\n * @param url - The URL being navigated to\n * @param nextUrl - an internal URL, primarily used for handling rewrites. Defaults to '/'.\n * @return The generated prefetch cache key.\n */ function createPrefetchCacheKeyImpl(url, includeSearchParams, prefix) {\n    // Initially we only use the pathname as the cache key. We don't want to include\n    // search params so that multiple URLs with the same search parameter can re-use\n    // loading states.\n    let pathnameFromUrl = url.pathname;\n    // RSC responses can differ based on search params, specifically in the case where we aren't\n    // returning a partial response (ie with `PrefetchKind.AUTO`).\n    // In the auto case, since loading.js & layout.js won't have access to search params,\n    // we can safely re-use that cache entry. But for full prefetches, we should not\n    // re-use the cache entry as the response may differ.\n    if (includeSearchParams) {\n        // if we have a full prefetch, we can include the search param in the key,\n        // as we'll be getting back a full response. The server might have read the search\n        // params when generating the full response.\n        pathnameFromUrl += url.search;\n    }\n    if (prefix) {\n        return \"\" + prefix + INTERCEPTION_CACHE_KEY_MARKER + pathnameFromUrl;\n    }\n    return pathnameFromUrl;\n}\nfunction createPrefetchCacheKey(url, kind, nextUrl) {\n    return createPrefetchCacheKeyImpl(url, kind === _routerreducertypes.PrefetchKind.FULL, nextUrl);\n}\nfunction getExistingCacheEntry(url, kind, nextUrl, prefetchCache, allowAliasing) {\n    if (kind === void 0) kind = _routerreducertypes.PrefetchKind.TEMPORARY;\n    // We first check if there's a more specific interception route prefetch entry\n    // This is because when we detect a prefetch that corresponds with an interception route, we prefix it with nextUrl (see `createPrefetchCacheKey`)\n    // to avoid conflicts with other pages that may have the same URL but render different things depending on the `Next-URL` header.\n    for (const maybeNextUrl of [\n        nextUrl,\n        null\n    ]){\n        const cacheKeyWithParams = createPrefetchCacheKeyImpl(url, true, maybeNextUrl);\n        const cacheKeyWithoutParams = createPrefetchCacheKeyImpl(url, false, maybeNextUrl);\n        // First, we check if we have a cache entry that exactly matches the URL\n        const cacheKeyToUse = url.search ? cacheKeyWithParams : cacheKeyWithoutParams;\n        const existingEntry = prefetchCache.get(cacheKeyToUse);\n        if (existingEntry && allowAliasing) {\n            // We know we're returning an aliased entry when the pathname matches but the search params don't,\n            const isAliased = existingEntry.url.pathname === url.pathname && existingEntry.url.search !== url.search;\n            if (isAliased) {\n                return {\n                    ...existingEntry,\n                    aliased: true\n                };\n            }\n            return existingEntry;\n        }\n        // If the request contains search params, and we're not doing a full prefetch, we can return the\n        // param-less entry if it exists.\n        // This is technically covered by the check at the bottom of this function, which iterates over cache entries,\n        // but lets us arrive there quicker in the param-full case.\n        const entryWithoutParams = prefetchCache.get(cacheKeyWithoutParams);\n        if (false) {}\n    }\n    // If we've gotten to this point, we didn't find a specific cache entry that matched\n    // the request URL.\n    // We attempt a partial match by checking if there's a cache entry with the same pathname.\n    // Regardless of what we find, since it doesn't correspond with the requested URL, we'll mark it \"aliased\".\n    // This will signal to the router that it should only apply the loading state on the prefetched data.\n    if (false) {}\n    return undefined;\n}\nfunction getOrCreatePrefetchCacheEntry(param) {\n    let { url, nextUrl, tree, prefetchCache, kind, allowAliasing = true } = param;\n    const existingCacheEntry = getExistingCacheEntry(url, kind, nextUrl, prefetchCache, allowAliasing);\n    if (existingCacheEntry) {\n        // Grab the latest status of the cache entry and update it\n        existingCacheEntry.status = getPrefetchEntryCacheStatus(existingCacheEntry);\n        // when `kind` is provided, an explicit prefetch was requested.\n        // if the requested prefetch is \"full\" and the current cache entry wasn't, we want to re-prefetch with the new intent\n        const switchedToFullPrefetch = existingCacheEntry.kind !== _routerreducertypes.PrefetchKind.FULL && kind === _routerreducertypes.PrefetchKind.FULL;\n        if (switchedToFullPrefetch) {\n            // If we switched to a full prefetch, validate that the existing cache entry contained partial data.\n            // It's possible that the cache entry was seeded with full data but has a cache type of \"auto\" (ie when cache entries\n            // are seeded but without a prefetch intent)\n            existingCacheEntry.data.then((prefetchResponse)=>{\n                const isFullPrefetch = Array.isArray(prefetchResponse.flightData) && prefetchResponse.flightData.some((flightData)=>{\n                    // If we started rendering from the root and we returned RSC data (seedData), we already had a full prefetch.\n                    return flightData.isRootRender && flightData.seedData !== null;\n                });\n                if (!isFullPrefetch) {\n                    return createLazyPrefetchEntry({\n                        tree,\n                        url,\n                        nextUrl,\n                        prefetchCache,\n                        // If we didn't get an explicit prefetch kind, we want to set a temporary kind\n                        // rather than assuming the same intent as the previous entry, to be consistent with how we\n                        // lazily create prefetch entries when intent is left unspecified.\n                        kind: kind != null ? kind : _routerreducertypes.PrefetchKind.TEMPORARY\n                    });\n                }\n            });\n        }\n        // If the existing cache entry was marked as temporary, it means it was lazily created when attempting to get an entry,\n        // where we didn't have the prefetch intent. Now that we have the intent (in `kind`), we want to update the entry to the more accurate kind.\n        if (kind && existingCacheEntry.kind === _routerreducertypes.PrefetchKind.TEMPORARY) {\n            existingCacheEntry.kind = kind;\n        }\n        // We've determined that the existing entry we found is still valid, so we return it.\n        return existingCacheEntry;\n    }\n    // If we didn't return an entry, create a new one.\n    return createLazyPrefetchEntry({\n        tree,\n        url,\n        nextUrl,\n        prefetchCache,\n        kind: kind || _routerreducertypes.PrefetchKind.TEMPORARY\n    });\n}\n/*\n * Used to take an existing cache entry and prefix it with the nextUrl, if it exists.\n * This ensures that we don't have conflicting cache entries for the same URL (as is the case with route interception).\n */ function prefixExistingPrefetchCacheEntry(param) {\n    let { url, nextUrl, prefetchCache, existingCacheKey } = param;\n    const existingCacheEntry = prefetchCache.get(existingCacheKey);\n    if (!existingCacheEntry) {\n        // no-op -- there wasn't an entry to move\n        return;\n    }\n    const newCacheKey = createPrefetchCacheKey(url, existingCacheEntry.kind, nextUrl);\n    prefetchCache.set(newCacheKey, {\n        ...existingCacheEntry,\n        key: newCacheKey\n    });\n    prefetchCache.delete(existingCacheKey);\n    return newCacheKey;\n}\nfunction createSeededPrefetchCacheEntry(param) {\n    let { nextUrl, tree, prefetchCache, url, data, kind } = param;\n    // The initial cache entry technically includes full data, but it isn't explicitly prefetched -- we just seed the\n    // prefetch cache so that we can skip an extra prefetch request later, since we already have the data.\n    // if the prefetch corresponds with an interception route, we use the nextUrl to prefix the cache key\n    const prefetchCacheKey = data.couldBeIntercepted ? createPrefetchCacheKey(url, kind, nextUrl) : createPrefetchCacheKey(url, kind);\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data: Promise.resolve(data),\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: Date.now(),\n        staleTime: -1,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh,\n        url\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\n/**\n * Creates a prefetch entry entry and enqueues a fetch request to retrieve the data.\n */ function createLazyPrefetchEntry(param) {\n    let { url, kind, tree, nextUrl, prefetchCache } = param;\n    const prefetchCacheKey = createPrefetchCacheKey(url, kind);\n    // initiates the fetch request for the prefetch and attaches a listener\n    // to the promise to update the prefetch cache entry when the promise resolves (if necessary)\n    const data = _prefetchreducer.prefetchQueue.enqueue(()=>(0, _fetchserverresponse.fetchServerResponse)(url, {\n            flightRouterState: tree,\n            nextUrl,\n            prefetchKind: kind\n        }).then((prefetchResponse)=>{\n            // TODO: `fetchServerResponse` should be more tighly coupled to these prefetch cache operations\n            // to avoid drift between this cache key prefixing logic\n            // (which is currently directly influenced by the server response)\n            let newCacheKey;\n            if (prefetchResponse.couldBeIntercepted) {\n                // Determine if we need to prefix the cache key with the nextUrl\n                newCacheKey = prefixExistingPrefetchCacheEntry({\n                    url,\n                    existingCacheKey: prefetchCacheKey,\n                    nextUrl,\n                    prefetchCache\n                });\n            }\n            // If the prefetch was a cache hit, we want to update the existing cache entry to reflect that it was a full prefetch.\n            // This is because we know that a static response will contain the full RSC payload, and can be updated to respect the `static`\n            // staleTime.\n            if (prefetchResponse.prerendered) {\n                const existingCacheEntry = prefetchCache.get(newCacheKey != null ? newCacheKey : prefetchCacheKey);\n                if (existingCacheEntry) {\n                    existingCacheEntry.kind = _routerreducertypes.PrefetchKind.FULL;\n                    if (prefetchResponse.staleTime !== -1) {\n                        // This is the stale time that was collected by the server during\n                        // static generation. Use this in place of the default stale time.\n                        existingCacheEntry.staleTime = prefetchResponse.staleTime;\n                    }\n                }\n            }\n            return prefetchResponse;\n        }));\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data,\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: null,\n        staleTime: -1,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh,\n        url\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\nfunction prunePrefetchCache(prefetchCache) {\n    for (const [href, prefetchCacheEntry] of prefetchCache){\n        if (getPrefetchEntryCacheStatus(prefetchCacheEntry) === _routerreducertypes.PrefetchCacheEntryStatus.expired) {\n            prefetchCache.delete(href);\n        }\n    }\n}\n// These values are set by `define-env-plugin` (based on `nextConfig.experimental.staleTimes`)\n// and default to 5 minutes (static) / 0 seconds (dynamic)\nconst DYNAMIC_STALETIME_MS = Number(\"0\") * 1000;\nconst STATIC_STALETIME_MS = Number(\"300\") * 1000;\nfunction getPrefetchEntryCacheStatus(param) {\n    let { kind, prefetchTime, lastUsedTime, staleTime } = param;\n    if (staleTime !== -1) {\n        // `staleTime` is the value sent by the server during static generation.\n        // When this is available, it takes precedence over any of the heuristics\n        // that follow.\n        //\n        // TODO: When PPR is enabled, the server will *always* return a stale time\n        // when prefetching. We should never use a prefetch entry that hasn't yet\n        // received data from the server. So the only two cases should be 1) we use\n        // the server-generated stale time 2) the unresolved entry is discarded.\n        return Date.now() < prefetchTime + staleTime ? _routerreducertypes.PrefetchCacheEntryStatus.fresh : _routerreducertypes.PrefetchCacheEntryStatus.stale;\n    }\n    // We will re-use the cache entry data for up to the `dynamic` staletime window.\n    if (Date.now() < (lastUsedTime != null ? lastUsedTime : prefetchTime) + DYNAMIC_STALETIME_MS) {\n        return lastUsedTime ? _routerreducertypes.PrefetchCacheEntryStatus.reusable : _routerreducertypes.PrefetchCacheEntryStatus.fresh;\n    }\n    // For \"auto\" prefetching, we'll re-use only the loading boundary for up to `static` staletime window.\n    // A stale entry will only re-use the `loading` boundary, not the full data.\n    // This will trigger a \"lazy fetch\" for the full data.\n    if (kind === _routerreducertypes.PrefetchKind.AUTO) {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.stale;\n        }\n    }\n    // for \"full\" prefetching, we'll re-use the cache entry data for up to `static` staletime window.\n    if (kind === _routerreducertypes.PrefetchKind.FULL) {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.reusable;\n        }\n    }\n    return _routerreducertypes.PrefetchCacheEntryStatus.expired;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=prefetch-cache-utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js ***!
  \*************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hmrRefreshReducer\", ({\n    enumerable: true,\n    get: function() {\n        return hmrRefreshReducer;\n    }\n}));\nconst _fetchserverresponse = __webpack_require__(/*! ../fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _applyrouterstatepatchtotree = __webpack_require__(/*! ../apply-router-state-patch-to-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-router-state-patch-to-tree.js\");\nconst _isnavigatingtonewrootlayout = __webpack_require__(/*! ../is-navigating-to-new-root-layout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js\");\nconst _navigatereducer = __webpack_require__(/*! ./navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nconst _handlemutable = __webpack_require__(/*! ../handle-mutable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-mutable.js\");\nconst _applyflightdata = __webpack_require__(/*! ../apply-flight-data */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-flight-data.js\");\nconst _approuter = __webpack_require__(/*! ../../app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _handlesegmentmismatch = __webpack_require__(/*! ../handle-segment-mismatch */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js\");\nconst _hasinterceptionrouteincurrenttree = __webpack_require__(/*! ./has-interception-route-in-current-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\");\n// A version of refresh reducer that keeps the cache around instead of wiping all of it.\nfunction hmrRefreshReducerImpl(state, action) {\n    const { origin } = action;\n    const mutable = {};\n    const href = state.canonicalUrl;\n    mutable.preserveCustomHistoryState = false;\n    const cache = (0, _approuter.createEmptyCacheNode)();\n    // If the current tree was intercepted, the nextUrl should be included in the request.\n    // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n    const includeNextUrl = (0, _hasinterceptionrouteincurrenttree.hasInterceptionRouteInCurrentTree)(state.tree);\n    // TODO-APP: verify that `href` is not an external url.\n    // Fetch data from the root of the tree.\n    cache.lazyData = (0, _fetchserverresponse.fetchServerResponse)(new URL(href, origin), {\n        flightRouterState: [\n            state.tree[0],\n            state.tree[1],\n            state.tree[2],\n            'refetch'\n        ],\n        nextUrl: includeNextUrl ? state.nextUrl : null,\n        isHmrRefresh: true\n    });\n    return cache.lazyData.then((param)=>{\n        let { flightData, canonicalUrl: canonicalUrlOverride } = param;\n        // Handle case when navigating to page in `pages` from `app`\n        if (typeof flightData === 'string') {\n            return (0, _navigatereducer.handleExternalUrl)(state, mutable, flightData, state.pushRef.pendingPush);\n        }\n        // Remove cache.lazyData as it has been resolved at this point.\n        cache.lazyData = null;\n        let currentTree = state.tree;\n        let currentCache = state.cache;\n        for (const normalizedFlightData of flightData){\n            const { tree: treePatch, isRootRender } = normalizedFlightData;\n            if (!isRootRender) {\n                // TODO-APP: handle this case better\n                console.log('REFRESH FAILED');\n                return state;\n            }\n            const newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)([\n                ''\n            ], currentTree, treePatch, state.canonicalUrl);\n            if (newTree === null) {\n                return (0, _handlesegmentmismatch.handleSegmentMismatch)(state, action, treePatch);\n            }\n            if ((0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(currentTree, newTree)) {\n                return (0, _navigatereducer.handleExternalUrl)(state, mutable, href, state.pushRef.pendingPush);\n            }\n            const canonicalUrlOverrideHref = canonicalUrlOverride ? (0, _createhreffromurl.createHrefFromUrl)(canonicalUrlOverride) : undefined;\n            if (canonicalUrlOverride) {\n                mutable.canonicalUrl = canonicalUrlOverrideHref;\n            }\n            const applied = (0, _applyflightdata.applyFlightData)(currentCache, cache, normalizedFlightData);\n            if (applied) {\n                mutable.cache = cache;\n                currentCache = cache;\n            }\n            mutable.patchedTree = newTree;\n            mutable.canonicalUrl = href;\n            currentTree = newTree;\n        }\n        return (0, _handlemutable.handleMutable)(state, mutable);\n    }, ()=>state);\n}\nfunction hmrRefreshReducerNoop(state, _action) {\n    return state;\n}\nconst hmrRefreshReducer =  false ? 0 : hmrRefreshReducerImpl;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hmr-refresh-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js ***!
  \******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// This file is only used in app router due to the specific error state handling.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    onCaughtError: function() {\n        return onCaughtError;\n    },\n    onUncaughtError: function() {\n        return onUncaughtError;\n    }\n});\nconst _stitchederror = __webpack_require__(/*! ../components/errors/stitched-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../components/errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _isnextroutererror = __webpack_require__(/*! ../components/is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _bailouttocsr = __webpack_require__(/*! ../../shared/lib/lazy-dynamic/bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nconst _reportglobalerror = __webpack_require__(/*! ./report-global-error */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/report-global-error.js\");\nconst _interceptconsoleerror = __webpack_require__(/*! ../components/globals/intercept-console-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js\");\nconst _errorboundary = __webpack_require__(/*! ../components/error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\");\nfunction onCaughtError(err, errorInfo) {\n    var _errorInfo_errorBoundary;\n    const errorBoundaryComponent = (_errorInfo_errorBoundary = errorInfo.errorBoundary) == null ? void 0 : _errorInfo_errorBoundary.constructor;\n    let isImplicitErrorBoundary;\n    if (true) {\n        const { AppDevOverlayErrorBoundary } = __webpack_require__(/*! ../components/react-dev-overlay/app/app-dev-overlay-error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js\");\n        isImplicitErrorBoundary = errorBoundaryComponent === AppDevOverlayErrorBoundary;\n    }\n    isImplicitErrorBoundary = isImplicitErrorBoundary || errorBoundaryComponent === _errorboundary.ErrorBoundaryHandler && errorInfo.errorBoundary.props.errorComponent === _errorboundary.GlobalError;\n    if (isImplicitErrorBoundary) {\n        // We don't consider errors caught unless they're caught by an explicit error\n        // boundary. The built-in ones are considered implicit.\n        // This mimics how the same app would behave without Next.js.\n        return onUncaughtError(err, errorInfo);\n    }\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(err) || (0, _isnextroutererror.isNextRouterError)(err)) return;\n    if (true) {\n        var _errorInfo_componentStack;\n        const errorBoundaryName = (errorBoundaryComponent == null ? void 0 : errorBoundaryComponent.displayName) || (errorBoundaryComponent == null ? void 0 : errorBoundaryComponent.name) || 'Unknown';\n        const componentThatErroredFrame = errorInfo == null ? void 0 : (_errorInfo_componentStack = errorInfo.componentStack) == null ? void 0 : _errorInfo_componentStack.split('\\n')[1];\n        var // example 1: at Page (http://localhost:3000/_next/static/chunks/pages/index.js?ts=1631600000000:2:1)\n        // example 2: Page@http://localhost:3000/_next/static/chunks/pages/index.js?ts=1631600000000:2:1\n        _componentThatErroredFrame_match;\n        // Match chrome or safari stack trace\n        const matches = (_componentThatErroredFrame_match = componentThatErroredFrame == null ? void 0 : componentThatErroredFrame.match(/\\s+at (\\w+)\\s+|(\\w+)@/)) != null ? _componentThatErroredFrame_match : [];\n        const componentThatErroredName = matches[1] || matches[2] || 'Unknown';\n        // Create error location with errored component and error boundary, to match the behavior of default React onCaughtError handler.\n        const errorBoundaryMessage = \"It was handled by the <\" + errorBoundaryName + \"> error boundary.\";\n        const componentErrorMessage = componentThatErroredName ? \"The above error occurred in the <\" + componentThatErroredName + \"> component.\" : \"The above error occurred in one of your components.\";\n        const errorLocation = componentErrorMessage + \" \" + errorBoundaryMessage;\n        const stitchedError = (0, _stitchederror.getReactStitchedError)(err);\n        // TODO: change to passing down errorInfo later\n        // In development mode, pass along the component stack to the error\n        if (errorInfo.componentStack) {\n            ;\n            stitchedError._componentStack = errorInfo.componentStack;\n        }\n        // Log and report the error with location but without modifying the error stack\n        (0, _interceptconsoleerror.originConsoleError)('%o\\n\\n%s', err, errorLocation);\n        (0, _useerrorhandler.handleClientError)(stitchedError, []);\n    } else {}\n}\nfunction onUncaughtError(err, errorInfo) {\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(err) || (0, _isnextroutererror.isNextRouterError)(err)) return;\n    if (true) {\n        const stitchedError = (0, _stitchederror.getReactStitchedError)(err);\n        // TODO: change to passing down errorInfo later\n        // In development mode, pass along the component stack to the error\n        if (errorInfo.componentStack) {\n            ;\n            stitchedError._componentStack = errorInfo.componentStack;\n        }\n        // TODO: Add an adendum to the overlay telling people about custom error boundaries.\n        (0, _reportglobalerror.reportGlobalError)(stitchedError);\n    } else {}\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-boundary-callbacks.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js ***!
  \**************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// This module can be shared between both pages router and app router\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"onRecoverableError\", ({\n    enumerable: true,\n    get: function() {\n        return onRecoverableError;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _bailouttocsr = __webpack_require__(/*! ../../shared/lib/lazy-dynamic/bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nconst _reportglobalerror = __webpack_require__(/*! ./report-global-error */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/report-global-error.js\");\nconst _stitchederror = __webpack_require__(/*! ../components/errors/stitched-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst onRecoverableError = (error, errorInfo)=>{\n    // x-ref: https://github.com/facebook/react/pull/28736\n    const cause = (0, _iserror.default)(error) && 'cause' in error ? error.cause : error;\n    const stitchedError = (0, _stitchederror.getReactStitchedError)(cause);\n    // In development mode, pass along the component stack to the error\n    if ( true && errorInfo.componentStack) {\n        ;\n        stitchedError._componentStack = errorInfo.componentStack;\n    }\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(cause)) return;\n    (0, _reportglobalerror.reportGlobalError)(stitchedError);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=on-recoverable-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js ***!
  \********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    AppRouterContext: function() {\n        return AppRouterContext;\n    },\n    GlobalLayoutRouterContext: function() {\n        return GlobalLayoutRouterContext;\n    },\n    LayoutRouterContext: function() {\n        return LayoutRouterContext;\n    },\n    MissingSlotContext: function() {\n        return MissingSlotContext;\n    },\n    TemplateContext: function() {\n        return TemplateContext;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst AppRouterContext = _react.default.createContext(null);\nconst LayoutRouterContext = _react.default.createContext(null);\nconst GlobalLayoutRouterContext = _react.default.createContext(null);\nconst TemplateContext = _react.default.createContext(null);\nif (true) {\n    AppRouterContext.displayName = 'AppRouterContext';\n    LayoutRouterContext.displayName = 'LayoutRouterContext';\n    GlobalLayoutRouterContext.displayName = 'GlobalLayoutRouterContext';\n    TemplateContext.displayName = 'TemplateContext';\n}\nconst MissingSlotContext = _react.default.createContext(new Set()); //# sourceMappingURL=app-router-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    PathParamsContext: function() {\n        return PathParamsContext;\n    },\n    PathnameContext: function() {\n        return PathnameContext;\n    },\n    SearchParamsContext: function() {\n        return SearchParamsContext;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst SearchParamsContext = (0, _react.createContext)(null);\nconst PathnameContext = (0, _react.createContext)(null);\nconst PathParamsContext = (0, _react.createContext)(null);\nif (true) {\n    SearchParamsContext.displayName = 'SearchParamsContext';\n    PathnameContext.displayName = 'PathnameContext';\n    PathParamsContext.displayName = 'PathParamsContext';\n} //# sourceMappingURL=hooks-client-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9ob29rcy1jbGllbnQtY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUFPYUEsaUJBQWlCO2VBQWpCQTs7SUFEQUMsZUFBZTtlQUFmQTs7SUFEQUMsbUJBQW1CO2VBQW5CQTs7O21DQUhpQjtBQUd2QixNQUFNQSxzQkFBc0JDLENBQUFBLEdBQUFBLE9BQUFBLGFBQUFBLEVBQXNDO0FBQ2xFLE1BQU1GLGtCQUFrQkUsQ0FBQUEsR0FBQUEsT0FBQUEsYUFBQUEsRUFBNkI7QUFDckQsTUFBTUgsb0JBQW9CRyxDQUFBQSxHQUFBQSxPQUFBQSxhQUFBQSxFQUE2QjtBQUU5RCxJQUZpQ0EsSUFFVCxFQUFtQjtJQUN6Q0Qsb0JBQW9CSyxXQUFXLEdBQUc7SUFDbENOLGdCQUFnQk0sV0FBVyxHQUFHO0lBQzlCUCxrQkFBa0JPLFdBQVcsR0FBRztBQUNsQyIsInNvdXJjZXMiOlsiL1VzZXJzL3JvYmVydC5oYW5zZW4vc3JjL3NoYXJlZC9saWIvaG9va3MtY2xpZW50LWNvbnRleHQuc2hhcmVkLXJ1bnRpbWUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IGNyZWF0ZUNvbnRleHQgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB0eXBlIHsgUGFyYW1zIH0gZnJvbSAnLi4vLi4vc2VydmVyL3JlcXVlc3QvcGFyYW1zJ1xuXG5leHBvcnQgY29uc3QgU2VhcmNoUGFyYW1zQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8VVJMU2VhcmNoUGFyYW1zIHwgbnVsbD4obnVsbClcbmV4cG9ydCBjb25zdCBQYXRobmFtZUNvbnRleHQgPSBjcmVhdGVDb250ZXh0PHN0cmluZyB8IG51bGw+KG51bGwpXG5leHBvcnQgY29uc3QgUGF0aFBhcmFtc0NvbnRleHQgPSBjcmVhdGVDb250ZXh0PFBhcmFtcyB8IG51bGw+KG51bGwpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIFNlYXJjaFBhcmFtc0NvbnRleHQuZGlzcGxheU5hbWUgPSAnU2VhcmNoUGFyYW1zQ29udGV4dCdcbiAgUGF0aG5hbWVDb250ZXh0LmRpc3BsYXlOYW1lID0gJ1BhdGhuYW1lQ29udGV4dCdcbiAgUGF0aFBhcmFtc0NvbnRleHQuZGlzcGxheU5hbWUgPSAnUGF0aFBhcmFtc0NvbnRleHQnXG59XG4iXSwibmFtZXMiOlsiUGF0aFBhcmFtc0NvbnRleHQiLCJQYXRobmFtZUNvbnRleHQiLCJTZWFyY2hQYXJhbXNDb250ZXh0IiwiY3JlYXRlQ29udGV4dCIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\n"));

/***/ })

});