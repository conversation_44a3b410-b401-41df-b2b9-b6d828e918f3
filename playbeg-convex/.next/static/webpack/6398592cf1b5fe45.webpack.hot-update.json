{"c": ["app/layout", "app/dashboard/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@radix-ui/primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-switch/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-size/dist/index.mjs"]}