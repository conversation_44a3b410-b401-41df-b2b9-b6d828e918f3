"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[312],{3312:(e,t,r)=>{r.d(t,{dp:()=>en.dp});var n=r(9089),s=r(2800),i=r(1388);function a(e,t){if("undefined"==typeof Convex||void 0===Convex.syscall)throw Error("The Convex database and auth objects are being used outside of a Convex backend. Did you mean to use `useQuery` or `useMutation` to call a Convex function?");return JSON.parse(Convex.syscall(e,JSON.stringify(t)))}async function o(e,t){let r;if("undefined"==typeof Convex||void 0===Convex.asyncSyscall)throw Error("The Convex database and auth objects are being used outside of a Convex backend. Did you mean to use `useQuery` or `useMutation` to call a Convex function?");try{r=await Convex.asyncSyscall(e,JSON.stringify(t))}catch(e){if(void 0!==e.data){let t=new s.i(e.message);throw t.data=(0,i.du)(e.data),t}throw Error(e.message)}return JSON.parse(r)}r(5947);var l=Object.defineProperty,u=(e,t,r)=>t in e?l(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,c=(e,t,r)=>u(e,"symbol"!=typeof t?t+"":t,r);class d{constructor(){c(this,"_isExpression"),c(this,"_value")}}var h=Object.defineProperty,y=(e,t,r)=>t in e?h(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,f=(e,t,r)=>y(e,"symbol"!=typeof t?t+"":t,r);class p extends d{constructor(e){super(),f(this,"inner"),this.inner=e}serialize(){return this.inner}}function m(e){return e instanceof p?e.serialize():{$literal:(0,i.cy)(e)}}let v={eq(e,t){if("string"!=typeof e)throw Error("The first argument to `q.eq` must be a field name.");return new p({$eq:[m(new p({$field:e})),m(t)]})},or:(...e)=>new p({$or:e.map(m)})};var b=Object.defineProperty,g=(e,t,r)=>t in e?b(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,w=(e,t,r)=>g(e,"symbol"!=typeof t?t+"":t,r);class x{constructor(){w(this,"_isExpression"),w(this,"_value")}}var q=Object.defineProperty,E=(e,t,r)=>t in e?q(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,C=(e,t,r)=>E(e,"symbol"!=typeof t?t+"":t,r);class $ extends x{constructor(e){super(),C(this,"inner"),this.inner=e}serialize(){return this.inner}}function j(e){return e instanceof $?e.serialize():{$literal:(0,i.cy)(e)}}let I={eq:(e,t)=>new $({$eq:[j(e),j(t)]}),neq:(e,t)=>new $({$neq:[j(e),j(t)]}),lt:(e,t)=>new $({$lt:[j(e),j(t)]}),lte:(e,t)=>new $({$lte:[j(e),j(t)]}),gt:(e,t)=>new $({$gt:[j(e),j(t)]}),gte:(e,t)=>new $({$gte:[j(e),j(t)]}),add:(e,t)=>new $({$add:[j(e),j(t)]}),sub:(e,t)=>new $({$sub:[j(e),j(t)]}),mul:(e,t)=>new $({$mul:[j(e),j(t)]}),div:(e,t)=>new $({$div:[j(e),j(t)]}),mod:(e,t)=>new $({$mod:[j(e),j(t)]}),neg:e=>new $({$neg:j(e)}),and:(...e)=>new $({$and:e.map(j)}),or:(...e)=>new $({$or:e.map(j)}),not:e=>new $({$not:j(e)}),field:e=>new $({$field:e})};var P=Object.defineProperty,S=(e,t,r)=>t in e?P(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,_=(e,t,r)=>S(e,"symbol"!=typeof t?t+"":t,r);class k{constructor(){_(this,"_isIndexRange")}}var O=Object.defineProperty,T=(e,t,r)=>t in e?O(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,A=(e,t,r)=>T(e,"symbol"!=typeof t?t+"":t,r);class J extends k{constructor(e){super(),A(this,"rangeExpressions"),A(this,"isConsumed"),this.rangeExpressions=e,this.isConsumed=!1}static new(){return new J([])}consume(){if(this.isConsumed)throw Error("IndexRangeBuilder has already been used! Chain your method calls like `q => q.eq(...).eq(...)`. See https://docs.convex.dev/using/indexes");this.isConsumed=!0}eq(e,t){return this.consume(),new J(this.rangeExpressions.concat({type:"Eq",fieldPath:e,value:(0,i.cy)(t)}))}gt(e,t){return this.consume(),new J(this.rangeExpressions.concat({type:"Gt",fieldPath:e,value:(0,n.rz)(t)}))}gte(e,t){return this.consume(),new J(this.rangeExpressions.concat({type:"Gte",fieldPath:e,value:(0,n.rz)(t)}))}lt(e,t){return this.consume(),new J(this.rangeExpressions.concat({type:"Lt",fieldPath:e,value:(0,n.rz)(t)}))}lte(e,t){return this.consume(),new J(this.rangeExpressions.concat({type:"Lte",fieldPath:e,value:(0,n.rz)(t)}))}export(){return this.consume(),this.rangeExpressions}}var N=Object.defineProperty,Q=(e,t,r)=>t in e?N(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,R=(e,t,r)=>Q(e,"symbol"!=typeof t?t+"":t,r);class F{constructor(){R(this,"_isSearchFilter")}}function z(e,t,r,n){if(void 0===e)throw TypeError(`Must provide arg ${t} \`${n}\` to \`${r}\``)}var V=Object.defineProperty,D=(e,t,r)=>t in e?V(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,B=(e,t,r)=>D(e,"symbol"!=typeof t?t+"":t,r);class M extends F{constructor(e){super(),B(this,"filters"),B(this,"isConsumed"),this.filters=e,this.isConsumed=!1}static new(){return new M([])}consume(){if(this.isConsumed)throw Error("SearchFilterBuilder has already been used! Chain your method calls like `q => q.search(...).eq(...)`.");this.isConsumed=!0}search(e,t){return z(e,1,"search","fieldName"),z(t,2,"search","query"),this.consume(),new M(this.filters.concat({type:"Search",fieldPath:e,value:t}))}eq(e,t){return z(e,1,"eq","fieldName"),2!=arguments.length&&z(t,2,"search","value"),this.consume(),new M(this.filters.concat({type:"Eq",fieldPath:e,value:(0,i.cy)(t)}))}export(){return this.consume(),this.filters}}var G=r(3216),L=Object.defineProperty,W=(e,t,r)=>t in e?L(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,H=(e,t,r)=>W(e,"symbol"!=typeof t?t+"":t,r);function K(e){throw Error("consumed"===e?"This query is closed and can't emit any more values.":"This query has been chained with another operator and can't be reused.")}Symbol.asyncIterator;class U{constructor(e){H(this,"state"),this.state={type:"preparing",query:e}}takeQuery(){if("preparing"!==this.state.type)throw Error("A query can only be chained once and can't be chained after iteration begins.");let e=this.state.query;return this.state={type:"closed"},e}startQuery(){if("executing"===this.state.type)throw Error("Iteration can only begin on a query once.");("closed"===this.state.type||"consumed"===this.state.type)&&K(this.state.type);let{queryId:e}=a("1.0/queryStream",{query:this.state.query,version:G.r});return this.state={type:"executing",queryId:e},e}closeQuery(){"executing"===this.state.type&&a("1.0/queryCleanup",{queryId:this.state.queryId}),this.state={type:"consumed"}}order(e){z(e,1,"order","order");let t=this.takeQuery();if("Search"===t.source.type)throw Error("Search queries must always be in relevance order. Can not set order manually.");if(null!==t.source.order)throw Error("Queries may only specify order at most once");return t.source.order=e,new U(t)}filter(e){z(e,1,"filter","predicate");let t=this.takeQuery();if(t.operators.length>=256)throw Error("Can't construct query with more than 256 operators");return t.operators.push({filter:j(e(I))}),new U(t)}limit(e){z(e,1,"limit","n");let t=this.takeQuery();return t.operators.push({limit:e}),new U(t)}[Symbol.asyncIterator](){return this.startQuery(),this}async next(){("closed"===this.state.type||"consumed"===this.state.type)&&K(this.state.type);let e="preparing"===this.state.type?this.startQuery():this.state.queryId,{value:t,done:r}=await o("1.0/queryStreamNext",{queryId:e});return r&&this.closeQuery(),{value:(0,n.du)(t),done:r}}return(){return this.closeQuery(),Promise.resolve({done:!0,value:void 0})}async paginate(e){if(z(e,1,"paginate","options"),"number"!=typeof e?.numItems||e.numItems<0)throw Error(`\`options.numItems\` must be a positive number. Received \`${e?.numItems}\`.`);let t=this.takeQuery(),r=e.numItems,s=e.cursor,i=e?.endCursor??null,a=e.maximumRowsRead??null,{page:l,isDone:u,continueCursor:c,splitCursor:d,pageStatus:h}=await o("1.0/queryPage",{query:t,cursor:s,endCursor:i,pageSize:r,maximumRowsRead:a,maximumBytesRead:e.maximumBytesRead,version:G.r});return{page:l.map(e=>(0,n.du)(e)),isDone:u,continueCursor:c,splitCursor:d,pageStatus:h}}async collect(){let e=[];for await(let t of this)e.push(t);return e}async take(e){return z(e,1,"take","n"),!function(e,t,r,n){if(!Number.isInteger(e)||e<0)throw TypeError(`Arg 1 \`n\` to \`${r}\` must be a non-negative integer`)}(e,0,"take",0),this.limit(e).collect()}async first(){let e=await this.take(1);return 0===e.length?null:e[0]}async unique(){let e=await this.take(2);if(0===e.length)return null;if(2===e.length)throw Error(`unique() query returned more than one result: 
 [${e[0]._id}, ${e[1]._id}, ...]`);return e[0]}}async function X(e,t){if(validateArg(e,1,"get","id"),"string"!=typeof e)throw Error(`Invalid argument \`id\` for \`db.get\`, expected string but got '${typeof e}': ${e}`);let r={id:convexToJson(e),isSystem:t,version};return jsonToConvex(await performAsyncSyscall("1.0/get",r))}async function Y(e,t){if(e.startsWith("_"))throw Error("System tables (prefixed with `_`) are read-only.");return validateArg(e,1,"insert","table"),validateArg(t,2,"insert","value"),jsonToConvex(await performAsyncSyscall("1.0/insert",{table:e,value:convexToJson(t)}))._id}async function Z(e,t){validateArg(e,1,"patch","id"),validateArg(t,2,"patch","value"),await performAsyncSyscall("1.0/shallowMerge",{id:convexToJson(e),value:patchValueToJson(t)})}async function ee(e,t){validateArg(e,1,"replace","id"),validateArg(t,2,"replace","value"),await performAsyncSyscall("1.0/replace",{id:convexToJson(e),value:convexToJson(t)})}async function et(e){validateArg(e,1,"delete","id"),await performAsyncSyscall("1.0/remove",{id:convexToJson(e)})}var er=r(8039);er.v.object({numItems:er.v.number(),cursor:er.v.union(er.v.string(),er.v.null()),endCursor:er.v.optional(er.v.union(er.v.string(),er.v.null())),id:er.v.optional(er.v.number()),maximumRowsRead:er.v.optional(er.v.number()),maximumBytesRead:er.v.optional(er.v.number())});var en=r(2828),es=Object.defineProperty,ei=Object.defineProperty,ea=Object.defineProperty,eo=(e,t,r)=>t in e?ea(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,el=(e,t,r)=>eo(e,"symbol"!=typeof t?t+"":t,r);class eu{constructor(e,t){el(this,"_definition"),el(this,"_name"),this._definition=e,this._name=t,setReferencePath(this,`_reference/childComponent/${t}`)}get exports(){return function e(t,r){return new Proxy({},{get(n,s){if("string"==typeof s)return e(t,[...r,s]);if(s===toReferencePath){let e=`_reference/childComponent/${t}`;for(let t of r)e+=`/${t}`;return e}}})}(this._name,[])}}function ec(e){let t=[];for(let[r,n]of Object.entries(e)){let e;e="string"==typeof n?{type:"leaf",leaf:n}:ec(n),t.push([r,e])}return{type:"branch",branch:t}}function ed(e){return e.map(([e,t,r])=>{let n=null;if(null!==r)for(let[e,t]of(n=[],Object.entries(r)))void 0!==t&&n.push([e,{type:"value",value:JSON.stringify(convexToJson(t))}]);let s=t.componentDefinitionPath;if(!s)throw Error("no .componentPath for component definition "+JSON.stringify(t,null,2));return{name:e,path:s,args:n}})}var eh=Object.defineProperty,ey=(e,t,r)=>t in e?eh(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,ef=(e,t,r)=>ey(e,"symbol"!=typeof t?t+"":t,r);class ep{constructor(e){ef(this,"indexes"),ef(this,"searchIndexes"),ef(this,"vectorIndexes"),ef(this,"validator"),this.indexes=[],this.searchIndexes=[],this.vectorIndexes=[],this.validator=e}index(e,t){return this.indexes.push({indexDescriptor:e,fields:t}),this}searchIndex(e,t){return this.searchIndexes.push({indexDescriptor:e,searchField:t.searchField,filterFields:t.filterFields||[]}),this}vectorIndex(e,t){return this.vectorIndexes.push({indexDescriptor:e,vectorField:t.vectorField,dimensions:t.dimensions,filterFields:t.filterFields||[]}),this}self(){return this}export(){let e=this.validator.json;if("object"!=typeof e)throw Error("Invalid validator: please make sure that the parameter of `defineTable` is valid (see https://docs.convex.dev/database/schemas)");return{indexes:this.indexes,searchIndexes:this.searchIndexes,vectorIndexes:this.vectorIndexes,documentType:e}}}function em(e){return new ep((0,er.d)(e)?e:er.v.object(e))}class ev{constructor(e,t){ef(this,"tables"),ef(this,"strictTableNameTypes"),ef(this,"schemaValidation"),this.tables=e,this.schemaValidation=t?.schemaValidation===void 0||t.schemaValidation}export(){return JSON.stringify({tables:Object.entries(this.tables).map(([e,t])=>{let{indexes:r,searchIndexes:n,vectorIndexes:s,documentType:i}=t.export();return{tableName:e,indexes:r,searchIndexes:n,vectorIndexes:s,documentType:i}}),schemaValidation:this.schemaValidation})}}new ev({_scheduled_functions:em({name:er.v.string(),args:er.v.array(er.v.any()),scheduledTime:er.v.float64(),completedTime:er.v.optional(er.v.float64()),state:er.v.union(er.v.object({kind:er.v.literal("pending")}),er.v.object({kind:er.v.literal("inProgress")}),er.v.object({kind:er.v.literal("success")}),er.v.object({kind:er.v.literal("failed"),error:er.v.string()}),er.v.object({kind:er.v.literal("canceled")}))}),_storage:em({sha256:er.v.string(),size:er.v.float64(),contentType:er.v.optional(er.v.string())})},void 0)}}]);