(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{1480:(e,t,n)=>{Promise.resolve().then(n.bind(n,2169)),Promise.resolve().then(n.bind(n,9181)),Promise.resolve().then(n.t.bind(n,5688,23)),Promise.resolve().then(n.t.bind(n,9432,23)),Promise.resolve().then(n.t.bind(n,9324,23))},2169:(e,t,n)=>{"use strict";n.d(t,{default:()=>u});var r=n(5155),l=n(4054),a=n(6593);function o(e){let{client:t,children:n}=e;return(0,r.jsx)(l.N2,{client:t,useAuth:a.As,children:n})}let i=new l.eH("https://lovely-cormorant-474.convex.cloud");function u(e){let{children:t}=e;return(0,r.jsx)(o,{client:i,children:t})}},4477:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{callServer:function(){return r.callServer},createServerReference:function(){return a},findSourceMapURL:function(){return l.findSourceMapURL}});let r=n(3806),l=n(1818),a=n(4979).createServerReference},5688:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},6593:(e,t,n)=>{"use strict";n.d(t,{OJ:()=>m,eB:()=>s,As:()=>d});var r=n(5155),l=n(2115);let a=Object.prototype.toString,o=e=>"[object Error]"===a.call(e),i=new Set(["network error","Failed to fetch","NetworkError when attempting to fetch resource.","The Internet connection appears to be offline.","Load failed","Network request failed","fetch failed","terminated"]),u=[500,2e3],s=(0,l.createContext)(void 0),c=(0,l.createContext)(void 0);function d(){return(0,l.useContext)(c)}let f=(0,l.createContext)(null),h="__convexAuthOAuthVerifier",v="__convexAuthJWT",w="__convexAuthRefreshToken",g="__convexAuthServerStateFetchTime";function m({client:e,serverState:t,onChange:n,storage:a,storageNamespace:d,replaceURL:m,children:b}){let k=(0,l.useRef)(t?._state.token??null),[_,S]=(0,l.useState)(null===k.current),[p,A]=(0,l.useState)(k.current),R=e.verbose??!1,M=(0,l.useCallback)(t=>{R&&(console.debug(`${new Date().toISOString()} ${t}`),e.logger?.logVerbose(t))},[R]),{storageSet:P,storageGet:E,storageRemove:N,storageKey:I}=function(e,t){let n=function(){let[e,t]=(0,l.useState)({});return()=>({getItem:t=>e[t],setItem:(e,n)=>{t(t=>({...t,[e]:n}))},removeItem:e=>{t(t=>{let{[e]:n,...r}=t;return r})}})}(),r=(0,l.useMemo)(()=>e??n(),[e]),a=t.replace(/[^a-zA-Z0-9]/g,""),o=(0,l.useCallback)(e=>`${e}_${a}`,[t]),i=(0,l.useCallback)((e,t)=>r.setItem(o(e),t),[r,o]);return{storageSet:i,storageGet:(0,l.useCallback)(e=>r.getItem(o(e)),[r,o]),storageRemove:(0,l.useCallback)(e=>r.removeItem(o(e)),[r,o]),storageKey:o}}(a,d),[T,L]=(0,l.useState)(!1),O=(0,l.useCallback)(async e=>{let t;let r=null!==k.current;if(null===e.tokens)k.current=null,e.shouldStore&&(await N(v),await N(w)),t=null;else{let{token:n}=e.tokens;if(k.current=n,e.shouldStore){let{refreshToken:t}=e.tokens;await P(v,n),await P(w,t)}t=n}r!==(null!==t)&&await n?.(),A(t),S(!1)},[P,N]);(0,l.useEffect)(()=>{let e=async e=>{if(T)return e.preventDefault(),e.returnValue=!0,"Are you sure you want to leave? Your changes may not be saved."};return x("beforeunload",e),()=>{C("beforeunload",e)}}),(0,l.useEffect)(()=>{let e=e=>{(async()=>{if(e.storageArea===a&&e.key===I(v)){let t=e.newValue;M(`synced access token, is null: ${null===t}`),await O({shouldStore:!1,tokens:null===t?null:{token:t}})}})()};return x("storage",e),()=>C("storage",e)},[O]);let $=(0,l.useCallback)(async t=>{let n;let r=0;for(;r<u.length;)try{return await e.unauthenticatedCall("auth:signIn","code"in t?{params:{code:t.code},verifier:t.verifier}:t)}catch(t){if(n=t,!(t&&o(t)&&"TypeError"===t.name&&"string"==typeof t.message&&("Load failed"===t.message?void 0===t.stack:i.has(t.message))))break;let e=u[r]+100*Math.random();r++,M(`verifyCode failed with network error, retry ${r} of ${u.length} in ${e}ms`),await new Promise(t=>setTimeout(t,e))}throw n},[e]),j=(0,l.useCallback)(async e=>{let{tokens:t}=await $(e);return M(`retrieved tokens, is null: ${null===t}`),await O({shouldStore:!0,tokens:t??null}),null!==t},[e,O]),F=(0,l.useCallback)(async(t,n)=>{let r=n instanceof FormData?Array.from(n.entries()).reduce((e,[t,n])=>(e[t]=n,e),{}):n??{},l=await E(h)??void 0;await N(h);let a=await e.authenticatedCall("auth:signIn",{provider:t,params:r,verifier:l});if(void 0!==a.redirect){let e=new URL(a.redirect);return await P(h,a.verifier),void 0!==window.location&&(window.location.href=e.toString()),{signingIn:!1,redirect:e}}if(void 0!==a.tokens){let{tokens:e}=a;return M(`signed in and got tokens, is null: ${null===e}`),await O({shouldStore:!0,tokens:e}),{signingIn:null!==a.tokens}}return{signingIn:!1}},[e,O,E]),U=(0,l.useCallback)(async()=>{try{await e.authenticatedCall("auth:signOut")}catch(e){}M("signed out, erasing tokens"),await O({shouldStore:!0,tokens:null})},[O,e]),G=(0,l.useCallback)(async({forceRefreshToken:e})=>{if(e){let e=k.current;return await y(w,async()=>{let t=k.current;if(t!==e)return M(`returning synced token, is null: ${null===t}`),t;let n=await E(w)??null;return null!==n?(L(!0),await j({refreshToken:n}).finally(()=>{L(!1)}),M(`returning retrieved token, is null: ${null===t}`),k.current):(L(!1),M("returning null, there is no refresh token"),null)})}return k.current},[j,U,E]),V=(0,l.useRef)(!1);(0,l.useEffect)(()=>{if(void 0===a)throw Error("`localStorage` is not available in this environment, set the `storage` prop on `ConvexAuthProvider`!");let e=async()=>{let e=await E(v)??null;M(`retrieved token from storage, is null: ${null===e}`),await O({shouldStore:!1,tokens:null===e?null:{token:e}})};if(void 0!==t){let n=E(g),r=n=>{if(!n||t._timeFetched>+n){let{token:e,refreshToken:n}=t._state;P(g,t._timeFetched.toString()),O({tokens:null===e||null===n?null:{token:e,refreshToken:n},shouldStore:!0})}else e()};n instanceof Promise?n.then(r):r(n);return}let n=void 0!==window?.location?new URLSearchParams(window.location.search).get("code"):null;if(V.current||n){if(n&&!V.current){V.current=!0;let e=new URL(window.location.href);e.searchParams.delete("code"),(async()=>{await m(e.pathname+e.search+e.hash),await F(void 0,{code:n}),V.current=!1})()}}else e()},[e,E]);let J=(0,l.useMemo)(()=>({signIn:F,signOut:U}),[F,U]),D=null!==p,q=(0,l.useMemo)(()=>({isLoading:_,isAuthenticated:D,fetchAccessToken:G}),[G,_,D]);return(0,r.jsx)(c.Provider,{value:q,children:(0,r.jsx)(s.Provider,{value:J,children:(0,r.jsx)(f.Provider,{value:p,children:b})})})}async function y(e,t){let n=window?.navigator?.locks;return void 0!==n?await n.request(e,t):await S(e,t)}function b(e){void 0===globalThis.__convexAuthMutexes&&(globalThis.__convexAuthMutexes={});let t=globalThis.__convexAuthMutexes[e];return void 0===t&&(globalThis.__convexAuthMutexes[e]={currentlyRunning:null,waiting:[]}),t=globalThis.__convexAuthMutexes[e]}function k(e,t){globalThis.__convexAuthMutexes[e]=t}async function _(e,t){let n=b(e);null===n.currentlyRunning?k(e,{currentlyRunning:t().finally(()=>{let t=b(e).waiting.shift();b(e).currentlyRunning=null,k(e,{...b(e),currentlyRunning:void 0===t?null:_(e,t)})}),waiting:[]}):k(e,{...n,waiting:[...n.waiting,t]})}async function S(e,t){return new Promise((n,r)=>{_(e,()=>t().then(e=>n(e)).catch(e=>r(e)))})}function x(e,t,n){window.addEventListener?.(e,t,n)}function C(e,t,n){window.removeEventListener?.(e,t,n)}},9181:(e,t,n)=>{"use strict";n.d(t,{ConvexAuthNextjsClientProvider:()=>u});var r=n(5155),l=n(2115),a=n(6593),o=n(4477);let i=(0,o.createServerReference)("00f913389bf07a0ce5471de0fecab54577e23d5f75",o.callServer,void 0,o.findSourceMapURL,"invalidateCache");function u(e){let{apiRoute:t,serverState:n,storage:o,storageNamespace:u,verbose:s,children:c}=e,d=(0,l.useCallback)(async(e,n)=>{let r=await fetch(null!=t?t:"/api/auth",{body:JSON.stringify({action:e,args:n}),method:"POST"});return await r.json()},[t]),f=(0,l.useMemo)(()=>({authenticatedCall:d,unauthenticatedCall:d,verbose:s}),[d,s]);return(0,r.jsx)(a.OJ,{client:f,serverState:n,onChange:i,storage:"undefined"==typeof window?void 0:"inMemory"===o?null:window.localStorage,storageNamespace:null!=u?u:function(e,t){if(void 0===e)throw Error("Missing environment variable `".concat(t,"`"));return e}("https://lovely-cormorant-474.convex.cloud","NEXT_PUBLIC_CONVEX_URL"),replaceURL:e=>{window.history.replaceState({},"",e)},children:c})}},9324:()=>{},9432:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}}},e=>{var t=t=>e(e.s=t);e.O(0,[261,54,441,684,358],()=>t(1480)),_N_E=e.O()}]);