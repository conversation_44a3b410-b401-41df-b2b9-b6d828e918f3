(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[24],{1175:(e,l,d)=>{Promise.resolve().then(d.bind(d,6767))},6767:(e,l,d)=>{"use strict";d.d(l,{default:()=>a});var s=d(5155),n=d(4054),r=d(6857);function a(e){let{preloaded:l}=e,d=(0,n.AN)(l),a=(0,n.n_)(r.F.djProfiles.createDjProfile);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex flex-col gap-4 bg-slate-200 dark:bg-slate-800 p-4 rounded-md",children:[(0,s.jsx)("h2",{className:"text-xl font-bold",children:"DJ Profiles Data"}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{children:["Total profiles: ",(null==d?void 0:d.length)||0]}),null==d?void 0:d.map(e=>(0,s.jsxs)("div",{className:"p-2 border rounded mb-2",children:[(0,s.jsx)("strong",{children:e.displayName}),(0,s.jsx)("span",{className:"ml-2 px-2 py-1 rounded text-xs ".concat(e.completedOnboarding?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:e.completedOnboarding?"Onboarded":"Pending"})]},e.id))]})]}),(0,s.jsx)("button",{className:"bg-foreground text-background px-4 py-2 rounded-md mx-auto",onClick:()=>{a({displayName:"Test DJ ".concat(Math.floor(1e3*Math.random()))})},children:"Create Test DJ Profile"})]})}},6857:(e,l,d)=>{"use strict";d.d(l,{F:()=>s});let s=d(3312).dp}},e=>{var l=l=>e(e.s=l);e.O(0,[54,312,441,684,358],()=>l(1175)),_N_E=e.O()}]);