(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[807],{3418:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var a=t(5155),r=t(4054),l=t(5695),i=t(2115),c=t(6857),x=t(7168),n=t(8482),d=t(8145),o=t(2784),h=t(9946);let m=(0,h.A)("hard-drive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]);var p=t(9869);let g=(0,h.A)("file-image",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["circle",{cx:"10",cy:"12",r:"2",key:"737tya"}],["path",{d:"m20 17-1.296-1.296a2.41 2.41 0 0 0-3.408 0L9 22",key:"wt3hpn"}]]);var u=t(7213),y=t(9074);let j=(0,h.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),N=(0,h.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);function f(e){let{purpose:s,showUpload:t=!0,maxFiles:l=10}=e,[h,f]=(0,i.useState)(s),b=(0,r.IT)(c.F.fileStorage.getUserFiles,{purpose:h,limit:l}),v=(0,r.IT)(c.F.fileStorage.getStorageStats),w=(0,r.n_)(c.F.fileStorage.deleteFile),k=async e=>{try{await w({fileId:e})}catch(e){console.error("Failed to delete file:",e)}},M=e=>{if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB"][s]},_=e=>new Date(e).toLocaleDateString(),B=e=>{switch(e){case"profile_picture":return"bg-blue-500/20 text-blue-400";case"sponsor_logo":return"bg-green-500/20 text-green-400";case"session_artwork":return"bg-purple-500/20 text-purple-400";default:return"bg-gray-500/20 text-gray-400"}},F=e=>{switch(e){case"profile_picture":return"Profile Picture";case"sponsor_logo":return"Sponsor Logo";case"session_artwork":return"Session Artwork";default:return"Other"}};return(0,a.jsxs)("div",{className:"space-y-6",children:[v&&(0,a.jsxs)(n.Zp,{className:"bg-gray-800/50 border-purple-500/20",children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsxs)(n.ZB,{className:"text-white flex items-center",children:[(0,a.jsx)(m,{className:"w-5 h-5 mr-2"}),"Storage Usage"]}),(0,a.jsx)(n.BT,{className:"text-gray-400",children:"Your file storage statistics"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-white",children:v.totalFiles}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Total Files"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-white",children:v.totalSizeMB}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"MB Used"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-white",children:v.maxSizeMB}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"MB Limit"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-white",children:[Math.round(v.totalSizeMB/v.maxSizeMB*100),"%"]}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Used"})]})]})})]}),t&&(0,a.jsxs)(n.Zp,{className:"bg-gray-800/50 border-purple-500/20",children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsxs)(n.ZB,{className:"text-white flex items-center",children:[(0,a.jsx)(p.A,{className:"w-5 h-5 mr-2"}),"Upload Files"]}),(0,a.jsx)(n.BT,{className:"text-gray-400",children:"Upload images for your profile, sessions, or sponsors"})]}),(0,a.jsxs)(n.Wu,{children:[!s&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"text-sm text-gray-400 mb-2 block",children:"File Purpose"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:["profile_picture","sponsor_logo","session_artwork","other"].map(e=>(0,a.jsx)(x.$,{variant:h===e?"default":"outline",size:"sm",onClick:()=>f(e),className:h===e?"bg-purple-600 hover:bg-purple-700":"border-gray-600 text-gray-300 hover:bg-gray-700",children:F(e)},e))})]}),(0,a.jsx)(o.e,{purpose:h||"other",onUploadComplete:(e,s)=>{console.log("File uploaded:",{fileId:e,url:s})},onUploadError:e=>console.error("Upload error:",e)})]})]}),(0,a.jsxs)(n.Zp,{className:"bg-gray-800/50 border-purple-500/20",children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsxs)(n.ZB,{className:"text-white flex items-center",children:[(0,a.jsx)(g,{className:"w-5 h-5 mr-2"}),"Your Files"]}),(0,a.jsx)(n.BT,{className:"text-gray-400",children:"Manage your uploaded files"})]}),(0,a.jsx)(n.Wu,{children:b&&b.length>0?(0,a.jsx)("div",{className:"space-y-4",children:b.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-700/50 rounded-lg border border-gray-600/30",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-600 rounded-lg flex items-center justify-center",children:e.url?(0,a.jsx)("img",{src:e.url,alt:e.fileName,className:"w-full h-full object-cover rounded-lg"}):(0,a.jsx)(u.A,{className:"w-6 h-6 text-gray-400"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"font-medium text-white truncate",children:e.fileName}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-400",children:[(0,a.jsx)("span",{children:M(e.fileSize)}),(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(y.A,{className:"w-3 h-3 mr-1"}),_(e.uploadedAt)]})]})]}),(0,a.jsx)(d.E,{className:B(e.purpose),children:F(e.purpose)})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[e.url&&(0,a.jsx)(x.$,{variant:"ghost",size:"sm",onClick:()=>window.open(e.url,"_blank"),className:"text-gray-400 hover:text-white",children:(0,a.jsx)(j,{className:"w-4 h-4"})}),(0,a.jsx)(x.$,{variant:"ghost",size:"sm",onClick:()=>k(e._id),className:"text-gray-400 hover:text-red-400",children:(0,a.jsx)(N,{className:"w-4 h-4"})})]})]},e._id))}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(g,{className:"w-12 h-12 text-gray-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-400 mb-2",children:"No files uploaded yet"}),(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"Upload your first file to get started"})]})})]})]})}let b=(0,h.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);function v(){let{isAuthenticated:e,isLoading:s}=(0,r.Z)(),t=(0,l.useRouter)();return((0,i.useEffect)(()=>{s||e||t.push("/signin")},[e,s,t]),s)?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-white text-lg",children:"Loading..."})}):e?(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900",children:[(0,a.jsx)("header",{className:"bg-gray-900/50 backdrop-blur-md border-b border-purple-500/20",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"flex justify-between items-center h-16",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)(x.$,{variant:"ghost",onClick:()=>t.push("/dashboard"),className:"text-gray-300 hover:text-white mr-4",children:[(0,a.jsx)(b,{className:"w-4 h-4 mr-2"}),"Back to Dashboard"]}),(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)(p.A,{className:"w-5 h-5 text-white"})}),(0,a.jsx)("h1",{className:"text-xl font-bold text-white",children:"File Upload Test"})]})})})}),(0,a.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-white mb-2",children:"File Upload Integration Test"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Test the file upload functionality including profile pictures, sponsor logos, and session artwork"})]}),(0,a.jsx)(f,{showUpload:!0,maxFiles:20})]})]}):null}},5900:(e,s,t)=>{Promise.resolve().then(t.bind(t,3418))}},e=>{var s=s=>e(e.s=s);e.O(0,[54,338,312,346,848,441,684,358],()=>s(5900)),_N_E=e.O()}]);