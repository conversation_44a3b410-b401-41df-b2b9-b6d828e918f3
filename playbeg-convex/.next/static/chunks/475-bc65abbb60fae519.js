"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[475],{227:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("music",[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]])},305:(e,t,n)=>{n.d(t,{BG:()=>a}),n(5155),n(1457),n(4054);var r=n(2115),l=n(6593);function a(){return(0,r.useContext)(l.eB)}},6593:(e,t,n)=>{n.d(t,{OJ:()=>k,eB:()=>s,As:()=>d});var r=n(5155),l=n(2115);let a=Object.prototype.toString,o=e=>"[object Error]"===a.call(e),i=new Set(["network error","Failed to fetch","NetworkError when attempting to fetch resource.","The Internet connection appears to be offline.","Load failed","Network request failed","fetch failed","terminated"]),u=[500,2e3],s=(0,l.createContext)(void 0),c=(0,l.createContext)(void 0);function d(){return(0,l.useContext)(c)}let h=(0,l.createContext)(null),f="__convexAuthOAuthVerifier",w="__convexAuthJWT",g="__convexAuthRefreshToken",v="__convexAuthServerStateFetchTime";function k({client:e,serverState:t,onChange:n,storage:a,storageNamespace:d,replaceURL:k,children:m}){let b=(0,l.useRef)(t?._state.token??null),[_,x]=(0,l.useState)(null===b.current),[A,p]=(0,l.useState)(b.current),I=e.verbose??!1,$=(0,l.useCallback)(t=>{I&&(console.debug(`${new Date().toISOString()} ${t}`),e.logger?.logVerbose(t))},[I]),{storageSet:R,storageGet:T,storageRemove:E,storageKey:M}=function(e,t){let n=function(){let[e,t]=(0,l.useState)({});return()=>({getItem:t=>e[t],setItem:(e,n)=>{t(t=>({...t,[e]:n}))},removeItem:e=>{t(t=>{let{[e]:n,...r}=t;return r})}})}(),r=(0,l.useMemo)(()=>e??n(),[e]),a=t.replace(/[^a-zA-Z0-9]/g,""),o=(0,l.useCallback)(e=>`${e}_${a}`,[t]),i=(0,l.useCallback)((e,t)=>r.setItem(o(e),t),[r,o]);return{storageSet:i,storageGet:(0,l.useCallback)(e=>r.getItem(o(e)),[r,o]),storageRemove:(0,l.useCallback)(e=>r.removeItem(o(e)),[r,o]),storageKey:o}}(a,d),[P,L]=(0,l.useState)(!1),j=(0,l.useCallback)(async e=>{let t;let r=null!==b.current;if(null===e.tokens)b.current=null,e.shouldStore&&(await E(w),await E(g)),t=null;else{let{token:n}=e.tokens;if(b.current=n,e.shouldStore){let{refreshToken:t}=e.tokens;await R(w,n),await R(g,t)}t=n}r!==(null!==t)&&await n?.(),p(t),x(!1)},[R,E]);(0,l.useEffect)(()=>{let e=async e=>{if(P)return e.preventDefault(),e.returnValue=!0,"Are you sure you want to leave? Your changes may not be saved."};return C("beforeunload",e),()=>{S("beforeunload",e)}}),(0,l.useEffect)(()=>{let e=e=>{(async()=>{if(e.storageArea===a&&e.key===M(w)){let t=e.newValue;$(`synced access token, is null: ${null===t}`),await j({shouldStore:!1,tokens:null===t?null:{token:t}})}})()};return C("storage",e),()=>S("storage",e)},[j]);let F=(0,l.useCallback)(async t=>{let n;let r=0;for(;r<u.length;)try{return await e.unauthenticatedCall("auth:signIn","code"in t?{params:{code:t.code},verifier:t.verifier}:t)}catch(t){if(n=t,!(t&&o(t)&&"TypeError"===t.name&&"string"==typeof t.message&&("Load failed"===t.message?void 0===t.stack:i.has(t.message))))break;let e=u[r]+100*Math.random();r++,$(`verifyCode failed with network error, retry ${r} of ${u.length} in ${e}ms`),await new Promise(t=>setTimeout(t,e))}throw n},[e]),O=(0,l.useCallback)(async e=>{let{tokens:t}=await F(e);return $(`retrieved tokens, is null: ${null===t}`),await j({shouldStore:!0,tokens:t??null}),null!==t},[e,j]),V=(0,l.useCallback)(async(t,n)=>{let r=n instanceof FormData?Array.from(n.entries()).reduce((e,[t,n])=>(e[t]=n,e),{}):n??{},l=await T(f)??void 0;await E(f);let a=await e.authenticatedCall("auth:signIn",{provider:t,params:r,verifier:l});if(void 0!==a.redirect){let e=new URL(a.redirect);return await R(f,a.verifier),void 0!==window.location&&(window.location.href=e.toString()),{signingIn:!1,redirect:e}}if(void 0!==a.tokens){let{tokens:e}=a;return $(`signed in and got tokens, is null: ${null===e}`),await j({shouldStore:!0,tokens:e}),{signingIn:null!==a.tokens}}return{signingIn:!1}},[e,j,T]),N=(0,l.useCallback)(async()=>{try{await e.authenticatedCall("auth:signOut")}catch(e){}$("signed out, erasing tokens"),await j({shouldStore:!0,tokens:null})},[j,e]),q=(0,l.useCallback)(async({forceRefreshToken:e})=>{if(e){let e=b.current;return await y(g,async()=>{let t=b.current;if(t!==e)return $(`returning synced token, is null: ${null===t}`),t;let n=await T(g)??null;return null!==n?(L(!0),await O({refreshToken:n}).finally(()=>{L(!1)}),$(`returning retrieved token, is null: ${null===t}`),b.current):(L(!1),$("returning null, there is no refresh token"),null)})}return b.current},[O,N,T]),B=(0,l.useRef)(!1);(0,l.useEffect)(()=>{if(void 0===a)throw Error("`localStorage` is not available in this environment, set the `storage` prop on `ConvexAuthProvider`!");let e=async()=>{let e=await T(w)??null;$(`retrieved token from storage, is null: ${null===e}`),await j({shouldStore:!1,tokens:null===e?null:{token:e}})};if(void 0!==t){let n=T(v),r=n=>{if(!n||t._timeFetched>+n){let{token:e,refreshToken:n}=t._state;R(v,t._timeFetched.toString()),j({tokens:null===e||null===n?null:{token:e,refreshToken:n},shouldStore:!0})}else e()};n instanceof Promise?n.then(r):r(n);return}let n=void 0!==window?.location?new URLSearchParams(window.location.search).get("code"):null;if(B.current||n){if(n&&!B.current){B.current=!0;let e=new URL(window.location.href);e.searchParams.delete("code"),(async()=>{await k(e.pathname+e.search+e.hash),await V(void 0,{code:n}),B.current=!1})()}}else e()},[e,T]);let D=(0,l.useMemo)(()=>({signIn:V,signOut:N}),[V,N]),U=null!==A,G=(0,l.useMemo)(()=>({isLoading:_,isAuthenticated:U,fetchAccessToken:q}),[q,_,U]);return(0,r.jsx)(c.Provider,{value:G,children:(0,r.jsx)(s.Provider,{value:D,children:(0,r.jsx)(h.Provider,{value:A,children:m})})})}async function y(e,t){let n=window?.navigator?.locks;return void 0!==n?await n.request(e,t):await x(e,t)}function m(e){void 0===globalThis.__convexAuthMutexes&&(globalThis.__convexAuthMutexes={});let t=globalThis.__convexAuthMutexes[e];return void 0===t&&(globalThis.__convexAuthMutexes[e]={currentlyRunning:null,waiting:[]}),t=globalThis.__convexAuthMutexes[e]}function b(e,t){globalThis.__convexAuthMutexes[e]=t}async function _(e,t){let n=m(e);null===n.currentlyRunning?b(e,{currentlyRunning:t().finally(()=>{let t=m(e).waiting.shift();m(e).currentlyRunning=null,b(e,{...m(e),currentlyRunning:void 0===t?null:_(e,t)})}),waiting:[]}):b(e,{...n,waiting:[...n.waiting,t]})}async function x(e,t){return new Promise((n,r)=>{_(e,()=>t().then(e=>n(e)).catch(e=>r(e)))})}function C(e,t,n){window.addEventListener?.(e,t,n)}function S(e,t,n){window.removeEventListener?.(e,t,n)}}}]);