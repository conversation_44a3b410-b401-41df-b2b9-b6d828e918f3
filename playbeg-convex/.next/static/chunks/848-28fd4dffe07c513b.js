"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[848],{2784:(e,r,t)=>{t.d(r,{e:()=>v});var a=t(5155),s=t(2115),l=t(6710),n=t(4054),i=t(6857),o=t(7168),d=t(8482),c=t(9104),u=t(3999);let f=s.forwardRef((e,r)=>{let{className:t,value:s,...l}=e;return(0,a.jsx)(c.bL,{ref:r,className:(0,u.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",t),...l,children:(0,a.jsx)(c.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})});f.displayName=c.bL.displayName;var p=t(7213),m=t(7434),x=t(9869),g=t(5339),h=t(4416);function v(e){let{purpose:r,onUploadComplete:t,onUploadError:c,maxSize:v=0xa00000,accept:y={"image/*":[".jpeg",".jpg",".png",".webp",".gif"]},className:b,disabled:N=!1}=e,[j,w]=(0,s.useState)([]),C=(0,n.n_)(i.F.fileStorage.generateUploadUrl),k=(0,n.n_)(i.F.fileStorage.storeFileMetadata),R=(0,s.useCallback)(async e=>{let a={file:e,progress:0,status:"uploading"};w(e=>[...e,a]);try{let a=await C(),s=new XMLHttpRequest;return new Promise((l,n)=>{s.upload.addEventListener("progress",r=>{if(r.lengthComputable){let t=Math.round(r.loaded/r.total*100);w(r=>r.map(r=>r.file===e?{...r,progress:t}:r))}}),s.addEventListener("load",async()=>{if(200===s.status)try{let a=JSON.parse(s.responseText).storageId;w(r=>r.map(r=>r.file===e?{...r,status:"processing",progress:100}:r));let n=await k({storageId:a,fileName:e.name,fileType:e.type,fileSize:e.size,purpose:r,description:"".concat(r," uploaded by user")}),i=URL.createObjectURL(e);w(r=>r.map(r=>r.file===e?{...r,status:"complete",url:i,fileId:n}:r)),null==t||t(n,i),l()}catch(e){throw Error("Failed to store file metadata")}else throw Error("Upload failed")}),s.addEventListener("error",()=>{n(Error("Upload failed"))});let i=new FormData;i.append("file",e),s.open("POST",a),s.send(i)})}catch(t){let r=t instanceof Error?t.message:"Upload failed";w(t=>t.map(t=>t.file===e?{...t,status:"error",error:r}:t)),null==c||c(r)}},[C,k,r,t,c]),E=(0,s.useCallback)(e=>{e.forEach(R)},[R]),{getRootProps:F,getInputProps:U,isDragActive:z}=(0,l.VB)({onDrop:E,accept:y,maxSize:v,disabled:N,multiple:!1}),_=e=>{w(r=>r.filter(r=>r.file!==e))},L=e=>e.startsWith("image/")?(0,a.jsx)(p.A,{className:"w-8 h-8"}):(0,a.jsx)(m.A,{className:"w-8 h-8"});return(0,a.jsxs)("div",{className:(0,u.cn)("space-y-4",b),children:[(0,a.jsx)(d.Zp,{className:(0,u.cn)("border-2 border-dashed transition-colors",z?"border-purple-500 bg-purple-50/10":"border-gray-600",N&&"opacity-50 cursor-not-allowed"),children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{...F(),className:(0,u.cn)("flex flex-col items-center justify-center space-y-4 text-center cursor-pointer",N&&"cursor-not-allowed"),children:[(0,a.jsx)("input",{...U()}),(0,a.jsx)(x.A,{className:(0,u.cn)("w-12 h-12",z?"text-purple-500":"text-gray-400")}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-medium text-white",children:z?"Drop the file here":"Drag & drop a file here"}),(0,a.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"or click to select a file"}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["Max size: ",Math.round(v/1048576),"MB"]})]}),(0,a.jsx)(o.$,{type:"button",variant:"outline",size:"sm",className:"border-gray-600 text-gray-300 hover:bg-gray-700",disabled:N,children:"Choose File"})]})})}),j.length>0&&(0,a.jsx)("div",{className:"space-y-3",children:j.map((e,r)=>(0,a.jsx)(d.Zp,{className:"bg-gray-800/50 border-gray-600",children:(0,a.jsx)(d.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"text-gray-400",children:L(e.file.type)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-white truncate",children:e.file.name}),(0,a.jsxs)("p",{className:"text-xs text-gray-400",children:[Math.round(e.file.size/1024)," KB"]}),"uploading"===e.status&&(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)(f,{value:e.progress,className:"h-2"}),(0,a.jsxs)("p",{className:"text-xs text-gray-400 mt-1",children:["Uploading... ",e.progress,"%"]})]}),"processing"===e.status&&(0,a.jsx)("p",{className:"text-xs text-blue-400 mt-1",children:"Processing..."}),"complete"===e.status&&(0,a.jsx)("p",{className:"text-xs text-green-400 mt-1",children:"Upload complete!"}),"error"===e.status&&(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[(0,a.jsx)(g.A,{className:"w-3 h-3 text-red-400 mr-1"}),(0,a.jsx)("p",{className:"text-xs text-red-400",children:e.error||"Upload failed"})]})]}),(0,a.jsx)(o.$,{variant:"ghost",size:"sm",onClick:()=>_(e.file),className:"text-gray-400 hover:text-white",children:(0,a.jsx)(h.A,{className:"w-4 h-4"})})]})})},r))})]})}},3999:(e,r,t)=>{t.d(r,{cn:()=>l});var a=t(2596),s=t(9688);function l(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}},6857:(e,r,t)=>{t.d(r,{F:()=>a});let a=t(3312).dp},7168:(e,r,t)=>{t.d(r,{$:()=>d});var a=t(5155),s=t(2115),l=t(4624),n=t(2085),i=t(3999);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",locked:"relative bg-gray-700/50 text-gray-400 cursor-not-allowed border border-gray-600/30"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,r)=>{let{className:t,variant:s,size:n,asChild:d=!1,...c}=e,u=d?l.DX:"button";return(0,a.jsx)(u,{className:(0,i.cn)(o({variant:s,size:n,className:t})),ref:r,...c})});d.displayName="Button"},8145:(e,r,t)=>{t.d(r,{E:()=>i});var a=t(5155);t(2115);var s=t(2085),l=t(3999);let n=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:r,variant:t,...s}=e;return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:t}),r),...s})}},8482:(e,r,t)=>{t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i});var a=t(5155),s=t(2115),l=t(3999);let n=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...s})});n.displayName="Card";let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...s})});i.displayName="CardHeader";let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...s})});o.displayName="CardTitle";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",t),...s})});d.displayName="CardDescription";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",t),...s})});c.displayName="CardContent",s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",t),...s})}).displayName="CardFooter"}}]);