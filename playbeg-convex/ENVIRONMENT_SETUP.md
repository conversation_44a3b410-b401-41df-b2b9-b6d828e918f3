# PlayBeg Convex Environment Setup

## Overview

This document outlines the environment configuration for the PlayBeg Convex migration project. We have three environments:

1. **Development** - Local development with hot reloading
2. **Staging** - Pre-production testing environment
3. **Production** - Live production environment

## Environment Configuration

### Development Environment

**Status**: ✅ **CONFIGURED**

- **Deployment**: `dev:lovely-cormorant-474`
- **URL**: `https://lovely-cormorant-474.convex.cloud`
- **Configuration**: `.env.local`
- **Usage**: `npm run dev`

### Staging Environment

**Status**: 🔄 **READY FOR CONFIGURATION**

- **Configuration File**: `.env.staging`
- **Deployment Script**: `scripts/deploy-staging.sh`
- **Usage**: `npm run deploy:staging`

**Required Environment Variables**:
```bash
CONVEX_DEPLOYMENT=staging:playbeg-staging
NEXT_PUBLIC_CONVEX_URL=https://playbeg-staging.convex.cloud
APPLE_MUSIC_DEVELOPER_TOKEN=staging_token_here
STRIPE_SECRET_KEY=sk_test_staging_key_here
STRIPE_WEBHOOK_SECRET=whsec_staging_webhook_secret_here
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_staging_key_here
CONVEX_SITE_URL=https://playbeg-staging.netlify.app
```

### Production Environment

**Status**: 🔄 **READY FOR CONFIGURATION**

- **Configuration File**: `.env.production`
- **Deployment Script**: `scripts/deploy-production.sh`
- **Usage**: `npm run deploy:production`

**Required Environment Variables**:
```bash
CONVEX_DEPLOYMENT=prod:playbeg-production
NEXT_PUBLIC_CONVEX_URL=https://playbeg-production.convex.cloud
APPLE_MUSIC_DEVELOPER_TOKEN=production_token_here
STRIPE_SECRET_KEY=sk_live_production_key_here
STRIPE_WEBHOOK_SECRET=whsec_production_webhook_secret_here
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_production_key_here
CONVEX_SITE_URL=https://playbeg.com
```

## Available Scripts

### Development
```bash
npm run dev              # Start development server with hot reloading
npm run dev:frontend     # Start only Next.js frontend
npm run dev:backend      # Start only Convex backend
```

### Building
```bash
npm run build            # Build for development
npm run build:staging    # Build for staging environment
npm run build:production # Build for production environment
```

### Deployment
```bash
npm run deploy:staging    # Deploy to staging environment
npm run deploy:production # Deploy to production environment
```

### Environment Management
```bash
npm run env:staging      # List staging environment variables
npm run env:production   # List production environment variables
```

## Setup Instructions

### 1. Development Environment (Already Complete)
The development environment is already configured and running.

### 2. Staging Environment Setup
1. Create staging deployment in Convex dashboard
2. Update `.env.staging` with actual staging values
3. Configure staging secrets: `convex env set --prod VARIABLE_NAME value`
4. Deploy: `npm run deploy:staging`

### 3. Production Environment Setup
1. Create production deployment in Convex dashboard
2. Update `.env.production` with actual production values
3. Configure production secrets: `convex env set --prod VARIABLE_NAME value`
4. Deploy: `npm run deploy:production`

## Security Notes

- Environment files (`.env.*`) contain sensitive information
- Never commit actual API keys or secrets to version control
- Use Convex environment variables for sensitive data
- Staging should use test/sandbox API keys
- Production should use live API keys

## Next Steps

1. ✅ Convex project created and configured
2. ✅ Development environment operational
3. ✅ Environment structure established
4. 🔄 Configure staging deployment (when ready)
5. 🔄 Configure production deployment (when ready)
6. 🔄 Set up CI/CD pipeline integration

## Dashboard Access

- **Development**: https://dashboard.convex.dev/d/lovely-cormorant-474
- **Project Management**: https://dashboard.convex.dev/t/djrobbieh/playbeg

## Support

For Convex-specific issues:
- Documentation: https://docs.convex.dev
- Community: https://convex.dev/community
- Support: <EMAIL>
