#!/bin/bash

# Deploy to Production Environment
# This script deploys the Convex backend to production

echo "🚀 Deploying PlayBeg to Production Environment..."

# Load production environment variables
if [ -f .env.production ]; then
    export $(cat .env.production | grep -v '^#' | xargs)
    echo "✅ Loaded production environment variables"
else
    echo "❌ .env.production file not found"
    exit 1
fi

# Deploy to production
echo "📦 Deploying Convex functions..."
npx convex deploy --prod

echo "✅ Production deployment completed!"
echo "🌐 Dashboard: https://dashboard.convex.dev/t/djrobbieh/playbeg"
