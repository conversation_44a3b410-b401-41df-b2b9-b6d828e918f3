#!/bin/bash

# Deploy to Staging Environment
# This script deploys the Convex backend to staging

echo "🚀 Deploying PlayBeg to Staging Environment..."

# Load staging environment variables
if [ -f .env.staging ]; then
    export $(cat .env.staging | grep -v '^#' | xargs)
    echo "✅ Loaded staging environment variables"
else
    echo "❌ .env.staging file not found"
    exit 1
fi

# Deploy to production (Convex uses 'deploy' for production, we'll configure it for staging)
echo "📦 Deploying Convex functions..."
npx convex deploy --prod

echo "✅ Staging deployment completed!"
echo "🌐 Dashboard: https://dashboard.convex.dev/t/djrobbieh/playbeg"
