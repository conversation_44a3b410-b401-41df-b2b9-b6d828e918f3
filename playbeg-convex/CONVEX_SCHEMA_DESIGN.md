# PlayBeg Convex Schema Design

## Overview

This document details the complete Convex schema design for the PlayBeg application, migrated from Supabase PostgreSQL to Convex's document-based architecture. The schema maintains full feature parity while leveraging Convex's real-time capabilities and optimized indexing.

## Schema Design Principles

### 1. Data Type Transformations
- **UUIDs** → `v.id("tableName")` for type-safe references
- **TIMESTAMPTZ** → `v.number()` (Unix timestamps in milliseconds)
- **TEXT[]** → `v.array(v.string())` for native array support
- **ENUMs** → `v.union()` with literal types for type safety
- **File URLs** → `v.id("_storage")` for Convex file storage

### 2. Relationship Mapping
- **Foreign Keys** → Convex Id references with proper typing
- **One-to-One** → Unique indexes on reference fields
- **One-to-Many** → Standard Id references with indexes
- **Many-to-Many** → Junction tables with compound indexes

### 3. Index Strategy
- **Query Performance** → Indexes on frequently queried fields
- **Real-time Updates** → Optimized for subscription patterns
- **Compound Indexes** → For complex filtering and sorting
- **Unique Constraints** → Enforced via unique indexes

## Core Collections

### 1. djProfiles
**Purpose**: DJ user profiles and onboarding status

```typescript
djProfiles: defineTable({
  userId: v.id("users"),                    // Link to Convex Auth user
  displayName: v.optional(v.string()),     // DJ display name
  completedOnboarding: v.boolean(),        // Onboarding status
  profilePictureStorageId: v.optional(v.id("_storage")), // Profile image
  lastLoginAt: v.optional(v.number()),     // Login tracking
  createdAt: v.number(),                   // Creation timestamp
  updatedAt: v.number(),                   // Last update timestamp
})
```

**Indexes**:
- `by_user` - Primary lookup by user ID
- `by_last_login` - Analytics and activity tracking
- `by_onboarding` - Filter incomplete onboarding

**Migration Notes**:
- Links to Convex Auth users instead of Supabase Auth
- Profile images stored in Convex file storage
- Maintains login tracking for analytics

### 2. sessions
**Purpose**: DJ event sessions with comprehensive configuration

```typescript
sessions: defineTable({
  djId: v.id("djProfiles"),               // Session owner
  name: v.string(),                       // Session name
  active: v.boolean(),                    // Active status
  acceptRequests: v.optional(v.boolean()), // Accept new requests
  autoApproval: v.optional(v.boolean()),  // Auto-approve requests
  blockedGenres: v.optional(v.array(v.string())), // Blocked genres
  durationMinutes: v.optional(v.number()), // Session duration
  maxRequestsPerTimeframe: v.optional(v.number()), // Rate limiting
  maxRequestsPerUser: v.optional(v.number()), // User limits
  enableIpLimiting: v.optional(v.boolean()), // IP-based limiting
  allowQuotaRequests: v.optional(v.boolean()), // Quota system
  sponsorHeader: v.optional(v.string()),  // Sponsor branding
  sponsorLogoStorageId: v.optional(v.id("_storage")), // Sponsor logo
  sponsorMessage: v.optional(v.string()), // Sponsor message
  sponsorUrl: v.optional(v.string()),     // Sponsor website
  timeframeMinutes: v.optional(v.number()), // Rate limit timeframe
  // Wedding Mode Configuration
  weddingModeEnabled: v.optional(v.boolean()),
  weddingCoupleNames: v.optional(v.array(v.string())),
  weddingDate: v.optional(v.string()),
  weddingHashtag: v.optional(v.string()),
  weddingTemplate: v.optional(v.string()),
  weddingPrimaryColor: v.optional(v.string()),
  weddingSecondaryColor: v.optional(v.string()),
  weddingCustomMessage: v.optional(v.string()),
  weddingShowIcons: v.optional(v.boolean()),
  weddingBorderStyle: v.optional(v.string()),
  weddingBackgroundPattern: v.optional(v.string()),
  createdAt: v.number(),
  updatedAt: v.number(),
})
```

**Indexes**:
- `by_dj` - Sessions by DJ
- `by_active` - Active sessions
- `by_created` - Chronological ordering
- `by_wedding_mode` - Wedding sessions
- `by_dj_active` - Active sessions by DJ (compound)

**Migration Notes**:
- Wedding couple names stored as array instead of separate fields
- Sponsor logos use Convex file storage
- All optional fields maintain backward compatibility

### 3. songRequests
**Purpose**: Individual song requests from audience members

```typescript
songRequests: defineTable({
  sessionId: v.id("sessions"),            // Parent session
  songTitle: v.string(),                  // Song title
  artistName: v.string(),                 // Artist name
  requesterName: v.string(),              // Requester display name
  requesterIp: v.optional(v.string()),    // IP for rate limiting
  appleMusicId: v.optional(v.string()),   // Apple Music track ID
  albumArtwork: v.optional(v.string()),   // Album artwork URL
  playlistId: v.optional(v.string()),     // Playlist reference
  appleMusicPlaylistId: v.optional(v.string()), // Apple Music playlist
  genre: v.optional(v.string()),          // Song genre
  status: v.union(                        // Request status
    v.literal("pending"),
    v.literal("approved"),
    v.literal("auto-approved"),
    v.literal("declined"),
    v.literal("played")
  ),
  addedToPlaylist: v.optional(v.boolean()), // Playlist status
  createdAt: v.number(),                  // Request timestamp
})
```

**Indexes**:
- `by_session` - Requests by session
- `by_status` - Filter by status
- `by_created` - Chronological ordering
- `by_session_status` - Session + status filtering
- `by_session_created` - Session chronological
- `by_requester_ip` - IP-based rate limiting
- `by_apple_music_id` - Apple Music integration

**Migration Notes**:
- Status enum converted to union type
- High-performance indexes for real-time updates
- IP tracking for rate limiting

### 4. djSubscriptions
**Purpose**: Subscription management and billing

```typescript
djSubscriptions: defineTable({
  djId: v.id("djProfiles"),              // Subscription owner
  planId: v.optional(v.id("subscriptionPlans")), // Current plan
  status: v.union(                       // Subscription status
    v.literal("active"),
    v.literal("canceled"),
    v.literal("past_due"),
    v.literal("unpaid"),
    v.literal("incomplete"),
    v.literal("incomplete_expired"),
    v.literal("trialing")
  ),
  currentPeriodStart: v.optional(v.number()), // Billing period start
  currentPeriodEnd: v.optional(v.number()),   // Billing period end
  cancelAtPeriodEnd: v.optional(v.boolean()), // Cancellation flag
  stripeCustomerId: v.optional(v.string()),   // Stripe customer ID
  stripeSubscriptionId: v.optional(v.string()), // Stripe subscription ID
  createdAt: v.number(),
  updatedAt: v.number(),
})
```

**Indexes**:
- `by_dj` - Subscriptions by DJ
- `by_status` - Filter by status
- `by_stripe_customer` - Stripe integration
- `by_stripe_subscription` - Stripe webhooks
- `by_plan` - Plan analytics
- `by_period_end` - Expiration tracking

**Migration Notes**:
- Expanded status enum for Stripe states
- Stripe integration fields maintained
- Period tracking for billing logic

### 5. subscriptionPlans
**Purpose**: Available pricing tiers and features

```typescript
subscriptionPlans: defineTable({
  name: v.string(),                      // Plan name
  description: v.optional(v.string()),   // Plan description
  priceAmount: v.number(),               // Price in cents
  priceCurrency: v.string(),             // Currency code
  durationHours: v.number(),             // Plan duration
  stripePriceId: v.string(),             // Stripe price ID
  active: v.boolean(),                   // Plan availability
  createdAt: v.number(),
  updatedAt: v.number(),
})
```

**Indexes**:
- `by_active` - Available plans
- `by_stripe_price` - Stripe integration
- `by_name` - Plan lookup

### 6. appleMusicTokens
**Purpose**: Secure Apple Music API token storage

```typescript
appleMusicTokens: defineTable({
  userId: v.id("users"),                 // Token owner
  appleMusicToken: v.string(),           // Encrypted token
  expiresAt: v.number(),                 // Expiration timestamp
  isValid: v.boolean(),                  // Token validity
  createdAt: v.number(),
  updatedAt: v.number(),
})
```

**Indexes**:
- `by_user` - Tokens by user
- `by_expires` - Expiration tracking
- `by_valid` - Valid tokens only
- `by_user_valid` - User + validity compound

### 7. blogPosts
**Purpose**: Content management system for blog

```typescript
blogPosts: defineTable({
  title: v.string(),                     // Post title
  slug: v.string(),                      // URL slug (unique)
  publishedDate: v.string(),             // Publication date
  author: v.string(),                    // Author name
  category: v.string(),                  // Post category
  excerpt: v.string(),                   // Post excerpt
  coverImageStorageId: v.optional(v.id("_storage")), // Cover image
  coverImageAlt: v.optional(v.string()), // Alt text
  tags: v.array(v.string()),             // Post tags
  content: v.string(),                   // Post content
  status: v.union(                       // Publication status
    v.literal("published"),
    v.literal("draft"),
    v.literal("archived")
  ),
  metaDescription: v.optional(v.string()), // SEO description
  ogImageStorageId: v.optional(v.id("_storage")), // OG image
  viewCount: v.number(),                 // View analytics
  featured: v.boolean(),                 // Featured flag
  createdAt: v.number(),
  updatedAt: v.number(),
})
```

**Indexes**:
- `by_slug` - URL lookup (unique)
- `by_status` - Publication status
- `by_category` - Category filtering
- `by_published_date` - Date ordering
- `by_featured` - Featured posts
- `by_author` - Author filtering
- `by_view_count` - Popular posts
- `by_status_featured` - Published + featured

### 8. playlists
**Purpose**: Apple Music playlist management

```typescript
playlists: defineTable({
  userId: v.id("users"),                 // Playlist owner
  name: v.string(),                      // Playlist name
  description: v.optional(v.string()),   // Playlist description
  appleMusicPlaylistId: v.optional(v.string()), // Apple Music ID
  active: v.optional(v.boolean()),       // Active status
  createdAt: v.number(),
  updatedAt: v.number(),
})
```

### 9. recentlyPlayedTracks
**Purpose**: Track history and analytics

```typescript
recentlyPlayedTracks: defineTable({
  sessionId: v.id("sessions"),           // Session reference
  songTitle: v.string(),                 // Track title
  artistName: v.string(),                // Artist name
  albumArtwork: v.optional(v.string()),  // Album artwork
  appleMusicId: v.optional(v.string()),  // Apple Music ID
  playlistId: v.optional(v.id("playlists")), // Playlist reference
  playedAt: v.number(),                  // Play timestamp
  createdAt: v.number(),
})
```

### 10. songs
**Purpose**: Song catalog and metadata

```typescript
songs: defineTable({
  title: v.string(),                     // Song title
  artist: v.string(),                    // Artist name
  album: v.optional(v.string()),         // Album name
  appleMusicId: v.optional(v.string()),  // Apple Music ID
  artworkUrl: v.optional(v.string()),    // Artwork URL
  createdAt: v.number(),
  updatedAt: v.number(),
})
```

## Validation Logic

### Wedding Template Validation
```typescript
const VALID_WEDDING_TEMPLATES = [
  "classic-elegance",
  "rustic-romance", 
  "modern-minimalist",
  "garden-party",
  "vintage-glam",
  "beach-destination"
];
```

### Request Status Validation
```typescript
const VALID_REQUEST_STATUSES = [
  "pending",
  "approved", 
  "auto-approved",
  "declined",
  "played"
];
```

### Subscription Status Validation
```typescript
const VALID_SUBSCRIPTION_STATUSES = [
  "active",
  "canceled",
  "past_due",
  "unpaid",
  "incomplete",
  "incomplete_expired",
  "trialing"
];
```

## Performance Optimizations

### High-Volume Collections
- **songRequests**: Optimized for real-time updates
- **recentlyPlayedTracks**: Efficient session-based queries
- **blogPosts**: SEO and content discovery optimization

### Real-time Subscriptions
- Session status changes
- Song request updates
- Subscription modifications
- Blog post publications

### Compound Indexes
- `by_dj_active` - Active sessions by DJ
- `by_session_status` - Request filtering
- `by_session_created` - Chronological requests
- `by_user_valid` - Valid tokens by user
- `by_status_featured` - Featured published posts

## Migration Considerations

### Data Integrity
- All foreign key relationships maintained via Id references
- Unique constraints enforced via indexes
- Validation logic moved to Convex functions

### Performance
- Optimized index strategy for query patterns
- Real-time subscription support
- Efficient filtering and sorting

### Security
- File storage integration for images
- Token encryption maintained
- User-based access control via functions

This schema design provides a solid foundation for the PlayBeg Convex migration while maintaining all existing functionality and improving real-time performance.
