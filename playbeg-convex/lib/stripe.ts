/**
 * Stripe Payment Integration
 * 
 * This module handles Stripe payment processing for PlayBeg's time-limited pass system.
 * Supports Free (20min/3 requests), 24-Hour ($9.99/100 requests), 
 * 48-Hour ($17.99/100 requests), and 7-Day ($49.99/100 requests) passes.
 */

import Stripe from 'stripe';

// Initialize Stripe with secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-05-28.basil',
});

// Pass configurations matching the subscription model
export const PASS_PLANS = {
  free: {
    id: 'free',
    name: 'Free Pass',
    description: '20 minutes or 3 requests',
    price: 0,
    currency: 'usd',
    durationHours: 0.33, // 20 minutes
    maxRequests: 3,
    stripePriceId: null,
  },
  '24hour': {
    id: '24hour',
    name: '24-Hour Pass',
    description: '24 hours of unlimited requests',
    price: 999, // $9.99 in cents
    currency: 'usd',
    durationHours: 24,
    maxRequests: 100,
    stripePriceId: process.env.STRIPE_24HOUR_PRICE_ID,
  },
  '48hour': {
    id: '48hour',
    name: '48-Hour Pass',
    description: '48 hours of unlimited requests',
    price: 1799, // $17.99 in cents
    currency: 'usd',
    durationHours: 48,
    maxRequests: 100,
    stripePriceId: process.env.STRIPE_48HOUR_PRICE_ID,
  },
  '7day': {
    id: '7day',
    name: '7-Day Pass',
    description: '7 days of unlimited requests',
    price: 4999, // $49.99 in cents
    currency: 'usd',
    durationHours: 168, // 7 days
    maxRequests: 100,
    stripePriceId: process.env.STRIPE_7DAY_PRICE_ID,
  },
} as const;

export type PassPlanId = keyof typeof PASS_PLANS;

/**
 * Create a Stripe Checkout Session for pass purchase
 */
export async function createCheckoutSession(
  planId: PassPlanId,
  userId: string,
  successUrl: string,
  cancelUrl: string
): Promise<Stripe.Checkout.Session> {
  const plan = PASS_PLANS[planId];
  
  if (!plan || planId === 'free') {
    throw new Error('Invalid plan for checkout');
  }

  if (!plan.stripePriceId) {
    throw new Error(`Stripe Price ID not configured for ${planId} plan`);
  }

  try {
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: plan.stripePriceId,
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: successUrl,
      cancel_url: cancelUrl,
      client_reference_id: userId,
      metadata: {
        planId,
        userId,
        durationHours: plan.durationHours.toString(),
        maxRequests: plan.maxRequests.toString(),
      },
    });

    return session;
  } catch (error) {
    console.error('Error creating Stripe checkout session:', error);
    throw new Error('Failed to create checkout session');
  }
}

/**
 * Retrieve a Stripe Checkout Session
 */
export async function getCheckoutSession(sessionId: string): Promise<Stripe.Checkout.Session> {
  try {
    return await stripe.checkout.sessions.retrieve(sessionId);
  } catch (error) {
    console.error('Error retrieving Stripe checkout session:', error);
    throw new Error('Failed to retrieve checkout session');
  }
}

/**
 * Create a Stripe Customer
 */
export async function createCustomer(
  email: string,
  name?: string,
  metadata?: Record<string, string>
): Promise<Stripe.Customer> {
  try {
    return await stripe.customers.create({
      email,
      name,
      metadata,
    });
  } catch (error) {
    console.error('Error creating Stripe customer:', error);
    throw new Error('Failed to create customer');
  }
}

/**
 * Get customer by ID
 */
export async function getCustomer(customerId: string): Promise<Stripe.Customer | null> {
  try {
    const customer = await stripe.customers.retrieve(customerId);
    return customer.deleted ? null : customer as Stripe.Customer;
  } catch (error) {
    console.error('Error retrieving Stripe customer:', error);
    return null;
  }
}

/**
 * Handle Stripe webhook events
 */
export async function handleWebhookEvent(
  body: string,
  signature: string
): Promise<Stripe.Event> {
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
  
  if (!webhookSecret) {
    throw new Error('Stripe webhook secret not configured');
  }

  try {
    return stripe.webhooks.constructEvent(body, signature, webhookSecret);
  } catch (error) {
    console.error('Error verifying Stripe webhook:', error);
    throw new Error('Invalid webhook signature');
  }
}

/**
 * Process successful payment
 */
export async function processSuccessfulPayment(
  checkoutSession: Stripe.Checkout.Session
): Promise<PassActivationData> {
  const { metadata, client_reference_id, amount_total } = checkoutSession;
  
  if (!metadata || !client_reference_id) {
    throw new Error('Missing required checkout session data');
  }

  const { planId, durationHours, maxRequests } = metadata;
  const plan = PASS_PLANS[planId as PassPlanId];
  
  if (!plan) {
    throw new Error(`Invalid plan ID: ${planId}`);
  }

  const now = Date.now();
  const expirationTime = now + (parseFloat(durationHours) * 60 * 60 * 1000);

  return {
    userId: client_reference_id,
    planId: planId as PassPlanId,
    planName: plan.name,
    durationHours: parseFloat(durationHours),
    maxRequests: parseInt(maxRequests),
    amountPaid: amount_total || 0,
    activatedAt: now,
    expiresAt: expirationTime,
    stripeSessionId: checkoutSession.id,
    stripePaymentIntentId: checkoutSession.payment_intent as string,
  };
}

/**
 * Format price for display
 */
export function formatPrice(amountInCents: number, currency: string = 'usd'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase(),
  }).format(amountInCents / 100);
}

/**
 * Get plan by ID
 */
export function getPlan(planId: PassPlanId) {
  return PASS_PLANS[planId];
}

/**
 * Get all available plans
 */
export function getAllPlans() {
  return Object.values(PASS_PLANS);
}

// Type definitions
export interface PassActivationData {
  userId: string;
  planId: PassPlanId;
  planName: string;
  durationHours: number;
  maxRequests: number;
  amountPaid: number;
  activatedAt: number;
  expiresAt: number;
  stripeSessionId: string;
  stripePaymentIntentId: string;
}

export { stripe };
