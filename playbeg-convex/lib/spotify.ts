/**
 * Spotify Web API Integration
 * 
 * This module handles Spotify API authentication and song search functionality
 * for PlayBeg's music validation and metadata retrieval.
 */

import SpotifyWebApi from 'spotify-web-api-node';

// Spotify API configuration
const spotifyApi = new SpotifyWebApi({
  clientId: process.env.SPOTIFY_CLIENT_ID,
  clientSecret: process.env.SPOTIFY_CLIENT_SECRET,
});

// Cache for access token
let accessToken: string | null = null;
let tokenExpirationTime: number = 0;

/**
 * Get Spotify access token using Client Credentials flow
 * This is suitable for server-side operations that don't require user authentication
 */
async function getAccessToken(): Promise<string> {
  // Return cached token if still valid
  if (accessToken && Date.now() < tokenExpirationTime) {
    return accessToken;
  }

  try {
    const data = await spotifyApi.clientCredentialsGrant();
    accessToken = data.body.access_token;
    tokenExpirationTime = Date.now() + (data.body.expires_in * 1000) - 60000; // Refresh 1 minute early
    
    spotifyApi.setAccessToken(accessToken);
    return accessToken;
  } catch (error) {
    console.error('Error getting Spotify access token:', error);
    throw new Error('Failed to authenticate with Spotify');
  }
}

/**
 * Search for tracks on Spotify
 */
export async function searchTracks(query: string, limit: number = 10): Promise<SpotifyTrack[]> {
  try {
    await getAccessToken();
    
    const searchResults = await spotifyApi.searchTracks(query, { limit });
    
    return searchResults.body.tracks?.items.map(track => ({
      id: track.id,
      name: track.name,
      artists: track.artists.map(artist => artist.name),
      album: track.album.name,
      albumArt: track.album.images[0]?.url || null,
      previewUrl: track.preview_url,
      duration: track.duration_ms,
      popularity: track.popularity,
      explicit: track.explicit,
      externalUrls: track.external_urls,
      uri: track.uri,
    })) || [];
  } catch (error) {
    console.error('Error searching Spotify tracks:', error);
    throw new Error('Failed to search tracks on Spotify');
  }
}

/**
 * Get track details by Spotify ID
 */
export async function getTrackById(trackId: string): Promise<SpotifyTrack | null> {
  try {
    await getAccessToken();
    
    const track = await spotifyApi.getTrack(trackId);
    
    return {
      id: track.body.id,
      name: track.body.name,
      artists: track.body.artists.map(artist => artist.name),
      album: track.body.album.name,
      albumArt: track.body.album.images[0]?.url || null,
      previewUrl: track.body.preview_url,
      duration: track.body.duration_ms,
      popularity: track.body.popularity,
      explicit: track.body.explicit,
      externalUrls: track.body.external_urls,
      uri: track.body.uri,
    };
  } catch (error) {
    console.error('Error getting Spotify track:', error);
    return null;
  }
}

/**
 * Validate a song request against Spotify catalog
 */
export async function validateSongRequest(songTitle: string, artistName: string): Promise<ValidationResult> {
  try {
    const query = `track:"${songTitle}" artist:"${artistName}"`;
    const results = await searchTracks(query, 5);
    
    if (results.length === 0) {
      // Try a broader search
      const broadQuery = `${songTitle} ${artistName}`;
      const broadResults = await searchTracks(broadQuery, 5);
      
      return {
        isValid: broadResults.length > 0,
        exactMatch: false,
        suggestions: broadResults.slice(0, 3),
        confidence: broadResults.length > 0 ? 'low' : 'none',
      };
    }
    
    // Check for exact or close matches
    const exactMatch = results.find(track => 
      track.name.toLowerCase() === songTitle.toLowerCase() &&
      track.artists.some(artist => artist.toLowerCase() === artistName.toLowerCase())
    );
    
    if (exactMatch) {
      return {
        isValid: true,
        exactMatch: true,
        matchedTrack: exactMatch,
        suggestions: [exactMatch],
        confidence: 'high',
      };
    }
    
    // Check for close matches
    const closeMatch = results.find(track =>
      track.name.toLowerCase().includes(songTitle.toLowerCase()) ||
      songTitle.toLowerCase().includes(track.name.toLowerCase())
    );
    
    return {
      isValid: true,
      exactMatch: false,
      matchedTrack: closeMatch,
      suggestions: results.slice(0, 3),
      confidence: closeMatch ? 'medium' : 'low',
    };
    
  } catch (error) {
    console.error('Error validating song request:', error);
    return {
      isValid: false,
      exactMatch: false,
      suggestions: [],
      confidence: 'none',
      error: 'Failed to validate with Spotify',
    };
  }
}

/**
 * Get popular tracks for suggestions
 */
export async function getPopularTracks(genre?: string, limit: number = 20): Promise<SpotifyTrack[]> {
  try {
    await getAccessToken();
    
    // Use search with popular terms or genre
    const query = genre ? `genre:"${genre}"` : 'year:2023-2024';
    const results = await spotifyApi.searchTracks(query, { limit });
    
    return results.body.tracks?.items.map(track => ({
      id: track.id,
      name: track.name,
      artists: track.artists.map(artist => artist.name),
      album: track.album.name,
      albumArt: track.album.images[0]?.url || null,
      previewUrl: track.preview_url,
      duration: track.duration_ms,
      popularity: track.popularity,
      explicit: track.explicit,
      externalUrls: track.external_urls,
      uri: track.uri,
    })).sort((a, b) => b.popularity - a.popularity) || [];
  } catch (error) {
    console.error('Error getting popular tracks:', error);
    return [];
  }
}

// Type definitions
export interface SpotifyTrack {
  id: string;
  name: string;
  artists: string[];
  album: string;
  albumArt: string | null;
  previewUrl: string | null;
  duration: number;
  popularity: number;
  explicit: boolean;
  externalUrls: any;
  uri: string;
}

export interface ValidationResult {
  isValid: boolean;
  exactMatch: boolean;
  matchedTrack?: SpotifyTrack;
  suggestions: SpotifyTrack[];
  confidence: 'high' | 'medium' | 'low' | 'none';
  error?: string;
}

// Initialize Spotify API
export { spotifyApi };
