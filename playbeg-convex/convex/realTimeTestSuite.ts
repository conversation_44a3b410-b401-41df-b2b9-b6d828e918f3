/**
 * Real-time Features Test Suite
 * 
 * This module provides comprehensive testing for real-time features including
 * optimized subscriptions, live analytics, notifications, and audience engagement
 * with workflow validation and performance testing.
 */

import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Test Query: Validate real-time operations
export const testRealTimeOperations = query({
  args: {},
  handler: async (ctx) => {
    const results = {
      timestamp: Date.now(),
      tests: [] as any[],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
      },
    };

    const userId = await getAuthUserId(ctx);
    
    // Test 1: Check authentication
    try {
      results.tests.push({
        name: "authentication_check",
        status: userId ? "passed" : "failed",
        result: userId ? `Authenticated as ${userId}` : "Not authenticated",
      });
      if (userId) results.summary.passed++;
      else results.summary.failed++;
    } catch (error) {
      results.tests.push({
        name: "authentication_check",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Test 2: Check notifications table
    try {
      const notifications = await ctx.db.query("notifications").take(5);
      results.tests.push({
        name: "notifications_table_query",
        status: "passed",
        result: `Found ${notifications.length} notifications`,
      });
      results.summary.passed++;
    } catch (error) {
      results.tests.push({
        name: "notifications_table_query",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Test 3: Check user interactions table
    try {
      const interactions = await ctx.db.query("userInteractions").take(5);
      results.tests.push({
        name: "user_interactions_table_query",
        status: "passed",
        result: `Found ${interactions.length} user interactions`,
      });
      results.summary.passed++;
    } catch (error) {
      results.tests.push({
        name: "user_interactions_table_query",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Test 4: Check song request votes table
    try {
      const votes = await ctx.db.query("songRequestVotes").take(5);
      results.tests.push({
        name: "song_request_votes_table_query",
        status: "passed",
        result: `Found ${votes.length} song request votes`,
      });
      results.summary.passed++;
    } catch (error) {
      results.tests.push({
        name: "song_request_votes_table_query",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Test 5: Check session reactions table
    try {
      const reactions = await ctx.db.query("sessionReactions").take(5);
      results.tests.push({
        name: "session_reactions_table_query",
        status: "passed",
        result: `Found ${reactions.length} session reactions`,
      });
      results.summary.passed++;
    } catch (error) {
      results.tests.push({
        name: "session_reactions_table_query",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    if (userId) {
      // Test 6: Check user notifications
      try {
        const userNotifications = await ctx.runQuery(api.realTimeNotifications.getUserNotifications, {
          limit: 5,
        });
        results.tests.push({
          name: "user_notifications_query",
          status: "passed",
          result: `Found ${userNotifications.length} user notifications`,
        });
        results.summary.passed++;
      } catch (error) {
        results.tests.push({
          name: "user_notifications_query",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }

      // Test 7: Check notification statistics
      try {
        const notificationStats = await ctx.runQuery(api.realTimeNotifications.getNotificationStatistics, {});
        results.tests.push({
          name: "notification_statistics",
          status: "passed",
          result: notificationStats,
        });
        results.summary.passed++;
      } catch (error) {
        results.tests.push({
          name: "notification_statistics",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    results.summary.total = results.tests.length;
    return results;
  },
});

// Test Mutation: Create test notification
export const createTestNotification = mutation({
  args: {
    notificationType: v.optional(v.union(
      v.literal("new_song_request"),
      v.literal("session_started"),
      v.literal("system_announcement")
    )),
    title: v.optional(v.string()),
    message: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const notificationType = args.notificationType || "system_announcement";
    const title = args.title || `Test Notification ${Date.now()}`;
    const message = args.message || "This is a test notification for development";

    const notificationResult = await ctx.runMutation(api.realTimeNotifications.createNotification, {
      userId,
      type: notificationType,
      title,
      message,
      priority: "normal",
    });

    return {
      ...notificationResult,
      notificationType,
      title,
      message,
    };
  },
});

// Test Mutation: Create test user interaction
export const createTestUserInteraction = mutation({
  args: {
    sessionId: v.id("sessions"),
    interactionType: v.optional(v.union(
      v.literal("session_joined"),
      v.literal("reaction_added"),
      v.literal("request_voted")
    )),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const interactionType = args.interactionType || "session_joined";

    const interactionResult = await ctx.runMutation(api.liveAnalytics.trackUserInteraction, {
      sessionId: args.sessionId,
      interactionType,
      metadata: {
        additionalData: { testInteraction: true },
      },
    });

    return {
      ...interactionResult,
      interactionType,
      sessionId: args.sessionId,
    };
  },
});

// Test Action: Real-time features workflow test
export const testRealTimeWorkflow = action({
  args: {
    includeNotifications: v.optional(v.boolean()),
    includeAnalytics: v.optional(v.boolean()),
    includeEngagement: v.optional(v.boolean()),
    includeSubscriptions: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const results = {
      timestamp: Date.now(),
      workflow: "real_time_features_integration",
      steps: [] as any[],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
      },
    };

    // Step 1: Validate operations
    try {
      const validation = await ctx.runQuery(api.realTimeTestSuite.testRealTimeOperations, {});
      results.steps.push({
        name: "validate_operations",
        status: validation.summary.failed === 0 ? "passed" : "failed",
        result: validation,
      });
      if (validation.summary.failed === 0) results.summary.passed++;
      else results.summary.failed++;
    } catch (error) {
      results.steps.push({
        name: "validate_operations",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Step 2: Test notifications (if requested)
    if (args.includeNotifications !== false) {
      try {
        const testNotification = await ctx.runMutation(api.realTimeTestSuite.createTestNotification, {
          notificationType: "system_announcement",
          title: "Test Workflow Notification",
          message: "Testing real-time notification system",
        });
        
        results.steps.push({
          name: "test_notifications",
          status: "passed",
          result: testNotification,
        });
        results.summary.passed++;
      } catch (error) {
        results.steps.push({
          name: "test_notifications",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    // Step 3: Test analytics (if requested)
    if (args.includeAnalytics !== false) {
      try {
        // Get active sessions for testing
        const activeSessions = await ctx.runQuery(api.realTimeSubscriptions.getActiveSessions, {
          limit: 1,
        });
        
        if (activeSessions.sessions.length > 0) {
          const sessionId = activeSessions.sessions[0].id;
          
          const analytics = await ctx.runQuery(api.liveAnalytics.getLiveSessionAnalytics, {
            sessionId,
          });
          
          results.steps.push({
            name: "test_analytics",
            status: "passed",
            result: {
              sessionId,
              hasAnalytics: !!analytics,
              metricsAvailable: analytics ? Object.keys(analytics).length : 0,
            },
          });
          results.summary.passed++;
        } else {
          results.steps.push({
            name: "test_analytics",
            status: "skipped",
            result: "No active sessions available for analytics testing",
          });
        }
      } catch (error) {
        results.steps.push({
          name: "test_analytics",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    // Step 4: Test engagement features (if requested)
    if (args.includeEngagement !== false) {
      try {
        const trendingSessions = await ctx.runQuery(api.audienceEngagement.getTrendingSessions, {
          limit: 5,
          timeWindow: "6hours",
        });
        
        results.steps.push({
          name: "test_engagement",
          status: "passed",
          result: {
            trendingSessionsCount: trendingSessions.sessions.length,
            timeWindow: trendingSessions.timeWindow,
            lastUpdated: trendingSessions.lastUpdated,
          },
        });
        results.summary.passed++;
      } catch (error) {
        results.steps.push({
          name: "test_engagement",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    // Step 5: Test real-time subscriptions (if requested)
    if (args.includeSubscriptions !== false) {
      try {
        const activeSessions = await ctx.runQuery(api.realTimeSubscriptions.getActiveSessions, {
          limit: 5,
        });
        
        results.steps.push({
          name: "test_subscriptions",
          status: "passed",
          result: {
            activeSessionsCount: activeSessions.sessions.length,
            lastUpdated: activeSessions.lastUpdated,
            hasRealTimeData: activeSessions.sessions.length > 0,
          },
        });
        results.summary.passed++;
      } catch (error) {
        results.steps.push({
          name: "test_subscriptions",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    results.summary.total = results.steps.length;
    return results;
  },
});

// Test Query: Real-time features statistics
export const getRealTimeStatistics = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get all notifications
    const allNotifications = await ctx.db.query("notifications").collect();
    
    // Get all user interactions
    const allInteractions = await ctx.db.query("userInteractions").collect();
    
    // Get all votes
    const allVotes = await ctx.db.query("songRequestVotes").collect();
    
    // Get all reactions
    const allReactions = await ctx.db.query("sessionReactions").collect();

    // Calculate time-based metrics
    const now = Date.now();
    const last24Hours = now - (24 * 60 * 60 * 1000);
    const lastHour = now - (60 * 60 * 1000);

    const recent24hNotifications = allNotifications.filter(n => n.createdAt > last24Hours);
    const recent1hInteractions = allInteractions.filter(i => i.timestamp > lastHour);
    const recent24hVotes = allVotes.filter(v => v.createdAt > last24Hours);
    const recent24hReactions = allReactions.filter(r => r.createdAt > last24Hours);

    // Count by type
    const notificationTypes: Record<string, number> = {};
    allNotifications.forEach(notification => {
      notificationTypes[notification.type] = (notificationTypes[notification.type] || 0) + 1;
    });

    const interactionTypes: Record<string, number> = {};
    allInteractions.forEach(interaction => {
      interactionTypes[interaction.interactionType] = (interactionTypes[interaction.interactionType] || 0) + 1;
    });

    const reactionTypes: Record<string, number> = {};
    allReactions.forEach(reaction => {
      reactionTypes[reaction.reactionType] = (reactionTypes[reaction.reactionType] || 0) + 1;
    });

    return {
      totals: {
        notifications: allNotifications.length,
        interactions: allInteractions.length,
        votes: allVotes.length,
        reactions: allReactions.length,
      },
      recent: {
        notifications24h: recent24hNotifications.length,
        interactions1h: recent1hInteractions.length,
        votes24h: recent24hVotes.length,
        reactions24h: recent24hReactions.length,
      },
      distributions: {
        notificationTypes,
        interactionTypes,
        reactionTypes,
      },
      engagement: {
        averageNotificationsPerUser: allNotifications.length > 0 
          ? Math.round((allNotifications.length / new Set(allNotifications.map(n => n.userId)).size) * 100) / 100
          : 0,
        averageInteractionsPerUser: allInteractions.length > 0 
          ? Math.round((allInteractions.length / new Set(allInteractions.map(i => i.userId)).size) * 100) / 100
          : 0,
      },
      lastUpdated: now,
    };
  },
});

// Test Query: Real-time features health check
export const realTimeFeaturesHealthCheck = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    
    const health = {
      timestamp: Date.now(),
      authentication: {
        isAuthenticated: !!userId,
        userId,
      },
      database: {
        notificationsTable: false,
        userInteractionsTable: false,
        songRequestVotesTable: false,
        sessionReactionsTable: false,
      },
      functionality: {
        canCreateNotifications: false,
        canTrackInteractions: false,
        canVoteOnRequests: false,
        canAddReactions: false,
      },
      statistics: {
        totalNotifications: 0,
        totalInteractions: 0,
        totalVotes: 0,
        totalReactions: 0,
      },
    };

    // Test database tables
    try {
      const notifications = await ctx.db.query("notifications").take(1);
      health.database.notificationsTable = true;
      health.statistics.totalNotifications = notifications.length;
    } catch (error) {
      console.log("Notifications table error:", error);
    }

    try {
      const interactions = await ctx.db.query("userInteractions").take(1);
      health.database.userInteractionsTable = true;
      health.statistics.totalInteractions = interactions.length;
    } catch (error) {
      console.log("User interactions table error:", error);
    }

    try {
      const votes = await ctx.db.query("songRequestVotes").take(1);
      health.database.songRequestVotesTable = true;
      health.statistics.totalVotes = votes.length;
    } catch (error) {
      console.log("Song request votes table error:", error);
    }

    try {
      const reactions = await ctx.db.query("sessionReactions").take(1);
      health.database.sessionReactionsTable = true;
      health.statistics.totalReactions = reactions.length;
    } catch (error) {
      console.log("Session reactions table error:", error);
    }

    // Test functionality if authenticated
    if (userId) {
      try {
        const testNotification = await ctx.runMutation(api.realTimeTestSuite.createTestNotification, {
          title: "Health Check",
          message: "Testing notification creation",
        });
        health.functionality.canCreateNotifications = !!testNotification.notificationId;
      } catch (error) {
        console.log("Create notification error:", error);
      }

      // Additional functionality tests would go here
    }

    return health;
  },
});
