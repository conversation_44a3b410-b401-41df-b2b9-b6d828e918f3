/**
 * Song Management
 * 
 * This module handles song CRUD operations that integrate with Apple Music,
 * including song metadata storage, search integration, and catalog management
 * with proper validation and caching.
 */

import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Query: Get song by Apple Music ID
export const getSongByAppleMusicId = query({
  args: {
    appleMusicId: v.string(),
  },
  handler: async (ctx, args) => {
    const song = await ctx.db
      .query("songs")
      .withIndex("by_apple_music_id", (q) => q.eq("appleMusicId", args.appleMusicId))
      .first();

    return song;
  },
});

// Query: Search songs in local database
export const searchSongsLocal = query({
  args: {
    query: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    if (args.query.trim().length === 0) {
      return [];
    }

    const limit = args.limit ?? 25;
    const searchTerm = args.query.toLowerCase().trim();

    // Get all songs and filter (in production, use full-text search)
    const allSongs = await ctx.db.query("songs").take(200);
    
    const filteredSongs = allSongs.filter(song => 
      song.title.toLowerCase().includes(searchTerm) ||
      song.artistName.toLowerCase().includes(searchTerm) ||
      song.albumName?.toLowerCase().includes(searchTerm) ||
      song.genre?.toLowerCase().includes(searchTerm)
    );

    return filteredSongs.slice(0, limit);
  },
});

// Query: Get popular songs
export const getPopularSongs = query({
  args: {
    limit: v.optional(v.number()),
    genre: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 50;
    
    let songs;
    if (args.genre) {
      songs = await ctx.db
        .query("songs")
        .withIndex("by_genre", (q) => q.eq("genre", args.genre))
        .order("desc") // Assuming newer songs are more popular
        .take(limit);
    } else {
      songs = await ctx.db
        .query("songs")
        .order("desc")
        .take(limit);
    }

    return songs;
  },
});

// Query: Get recently added songs
export const getRecentlyAddedSongs = query({
  args: {
    limit: v.optional(v.number()),
    daysSince: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 25;
    const daysSince = args.daysSince ?? 7;
    const cutoffTime = Date.now() - (daysSince * 24 * 60 * 60 * 1000);

    const songs = await ctx.db
      .query("songs")
      .filter((q) => q.gte(q.field("createdAt"), cutoffTime))
      .order("desc")
      .take(limit);

    return songs;
  },
});

// Mutation: Store song metadata
export const storeSongMetadata = mutation({
  args: {
    appleMusicId: v.string(),
    title: v.string(),
    artistName: v.string(),
    albumName: v.optional(v.string()),
    genre: v.optional(v.string()),
    duration: v.optional(v.number()),
    artworkUrl: v.optional(v.string()),
    releaseDate: v.optional(v.string()),
    isExplicit: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Validate required fields
    if (args.appleMusicId.trim().length === 0) {
      throw new Error("Apple Music ID is required");
    }

    if (args.title.trim().length === 0) {
      throw new Error("Song title is required");
    }

    if (args.artistName.trim().length === 0) {
      throw new Error("Artist name is required");
    }

    // Check if song already exists
    const existingSong = await ctx.db
      .query("songs")
      .withIndex("by_apple_music_id", (q) => q.eq("appleMusicId", args.appleMusicId))
      .first();

    const now = Date.now();

    if (existingSong) {
      // Update existing song
      await ctx.db.patch(existingSong._id, {
        title: args.title.trim(),
        artistName: args.artistName.trim(),
        albumName: args.albumName?.trim(),
        genre: args.genre?.trim(),
        duration: args.duration,
        artworkUrl: args.artworkUrl,
        releaseDate: args.releaseDate,
        isExplicit: args.isExplicit ?? false,
        updatedAt: now,
      });

      return {
        songId: existingSong._id,
        appleMusicId: args.appleMusicId,
        message: "Song metadata updated successfully",
        isNew: false,
      };
    } else {
      // Create new song
      const songId = await ctx.db.insert("songs", {
        appleMusicId: args.appleMusicId,
        title: args.title.trim(),
        artistName: args.artistName.trim(),
        albumName: args.albumName?.trim(),
        genre: args.genre?.trim(),
        duration: args.duration,
        artworkUrl: args.artworkUrl,
        releaseDate: args.releaseDate,
        isExplicit: args.isExplicit ?? false,
        createdAt: now,
        updatedAt: now,
      });

      return {
        songId,
        appleMusicId: args.appleMusicId,
        message: "Song metadata stored successfully",
        isNew: true,
      };
    }
  },
});

// Action: Search songs with Apple Music integration
export const searchSongsWithAppleMusic = action({
  args: {
    query: v.string(),
    limit: v.optional(v.number()),
    useLocalFirst: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    if (args.query.trim().length === 0) {
      throw new Error("Search query cannot be empty");
    }

    const limit = args.limit ?? 25;
    const useLocalFirst = args.useLocalFirst ?? true;

    let localResults: any[] = [];
    let appleMusicResults: any[] = [];

    // Search local database first if requested
    if (useLocalFirst) {
      localResults = await ctx.runQuery(api.songs.searchSongsLocal, {
        query: args.query,
        limit: Math.min(limit, 10), // Limit local results
      });
    }

    // Search Apple Music if we need more results or if local search is disabled
    const needMoreResults = localResults.length < limit;
    if (needMoreResults || !useLocalFirst) {
      try {
        const appleMusicSearch = await ctx.runAction(api.appleMusicApi.searchSongs, {
          query: args.query,
          limit: limit - localResults.length,
        });
        appleMusicResults = appleMusicSearch.results;

        // Store new songs in local database for future searches
        for (const song of appleMusicResults) {
          try {
            await ctx.runMutation(api.songs.storeSongMetadata, {
              appleMusicId: song.id,
              title: song.title,
              artistName: song.artist,
              albumName: song.album,
              genre: song.genre,
              duration: song.duration,
              artworkUrl: song.artwork,
              releaseDate: song.releaseDate,
              isExplicit: song.isExplicit,
            });
          } catch (error) {
            // Continue if individual song storage fails
            console.log(`Failed to store song ${song.id}:`, error);
          }
        }
      } catch (error) {
        // If Apple Music search fails, continue with local results only
        console.log("Apple Music search failed:", error);
      }
    }

    // Combine and deduplicate results
    const combinedResults = [...localResults];
    const localAppleMusicIds = new Set(localResults.map(song => song.appleMusicId));

    for (const appleSong of appleMusicResults) {
      if (!localAppleMusicIds.has(appleSong.id)) {
        combinedResults.push({
          _id: `temp_${appleSong.id}`,
          appleMusicId: appleSong.id,
          title: appleSong.title,
          artistName: appleSong.artist,
          albumName: appleSong.album,
          genre: appleSong.genre,
          duration: appleSong.duration,
          artworkUrl: appleSong.artwork,
          releaseDate: appleSong.releaseDate,
          isExplicit: appleSong.isExplicit,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      }
    }

    return {
      query: args.query,
      results: combinedResults.slice(0, limit),
      sources: {
        local: localResults.length,
        appleMusic: appleMusicResults.length,
        total: combinedResults.length,
      },
      searchedAt: Date.now(),
    };
  },
});

// Action: Get song details with Apple Music integration
export const getSongDetailsWithAppleMusic = action({
  args: {
    appleMusicId: v.string(),
    forceRefresh: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Check local database first
    const localSong = await ctx.runQuery(api.songs.getSongByAppleMusicId, {
      appleMusicId: args.appleMusicId,
    });

    // Return local data if available and not forcing refresh
    if (localSong && !args.forceRefresh) {
      return {
        song: localSong,
        source: "local",
        retrievedAt: Date.now(),
      };
    }

    // Get details from Apple Music
    try {
      const appleMusicDetails = await ctx.runAction(api.appleMusicApi.getSongDetails, {
        appleMusicId: args.appleMusicId,
      });

      // Store/update in local database
      const storeResult = await ctx.runMutation(api.songs.storeSongMetadata, {
        appleMusicId: args.appleMusicId,
        title: appleMusicDetails.song.title,
        artistName: appleMusicDetails.song.artist,
        albumName: appleMusicDetails.song.album,
        genre: appleMusicDetails.song.genre,
        duration: appleMusicDetails.song.duration,
        artworkUrl: appleMusicDetails.song.artwork,
        releaseDate: appleMusicDetails.song.releaseDate,
        isExplicit: appleMusicDetails.song.isExplicit,
      });

      // Get the updated song from database
      const updatedSong = await ctx.runQuery(api.songs.getSongByAppleMusicId, {
        appleMusicId: args.appleMusicId,
      });

      return {
        song: updatedSong,
        source: "apple_music",
        isNew: storeResult.isNew,
        retrievedAt: Date.now(),
      };
    } catch (error) {
      // If Apple Music fails, return local data if available
      if (localSong) {
        return {
          song: localSong,
          source: "local_fallback",
          error: "Apple Music unavailable, using cached data",
          retrievedAt: Date.now(),
        };
      }
      
      throw new Error(`Failed to get song details: ${error}`);
    }
  },
});

// Query: Get song statistics
export const getSongStatistics = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get total song count
    const allSongs = await ctx.db.query("songs").collect();
    
    // Count by genre
    const genreCounts: Record<string, number> = {};
    for (const song of allSongs) {
      if (song.genre) {
        genreCounts[song.genre] = (genreCounts[song.genre] || 0) + 1;
      }
    }

    // Get recent additions (last 7 days)
    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    const recentSongs = allSongs.filter(song => song.createdAt > sevenDaysAgo);

    // Top genres
    const topGenres = Object.entries(genreCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([genre, count]) => ({ genre, count }));

    return {
      total: allSongs.length,
      recentlyAdded: recentSongs.length,
      totalGenres: Object.keys(genreCounts).length,
      topGenres,
      explicitCount: allSongs.filter(song => song.isExplicit).length,
      withArtwork: allSongs.filter(song => song.artworkUrl).length,
      averageDuration: allSongs.length > 0 
        ? Math.round(allSongs.reduce((sum, song) => sum + (song.duration || 0), 0) / allSongs.length)
        : 0,
    };
  },
});
