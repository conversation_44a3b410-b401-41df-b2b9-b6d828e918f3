/**
 * File Upload Test Suite
 * 
 * This module provides comprehensive testing for file upload functionality,
 * including profile pictures, sponsor logos, validation, and integration
 * with user profiles and sessions.
 */

import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Test Query: Validate file upload operations
export const testFileUploadOperations = query({
  args: {},
  handler: async (ctx) => {
    const results = {
      timestamp: Date.now(),
      tests: [] as any[],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
      },
    };

    const userId = await getAuthUserId(ctx);
    
    // Test 1: Check authentication
    try {
      results.tests.push({
        name: "authentication_check",
        status: userId ? "passed" : "failed",
        result: userId ? `Authenticated as ${userId}` : "Not authenticated",
      });
      if (userId) results.summary.passed++;
      else results.summary.failed++;
    } catch (error) {
      results.tests.push({
        name: "authentication_check",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Test 2: Check file metadata table
    try {
      const fileMetadata = await ctx.db.query("fileMetadata").take(5);
      results.tests.push({
        name: "file_metadata_query",
        status: "passed",
        result: `Found ${fileMetadata.length} file metadata records`,
      });
      results.summary.passed++;
    } catch (error) {
      results.tests.push({
        name: "file_metadata_query",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Test 3: Check sponsor logos table
    try {
      const sponsorLogos = await ctx.db.query("sponsorLogos").take(5);
      results.tests.push({
        name: "sponsor_logos_query",
        status: "passed",
        result: `Found ${sponsorLogos.length} sponsor logo records`,
      });
      results.summary.passed++;
    } catch (error) {
      results.tests.push({
        name: "sponsor_logos_query",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    if (userId) {
      // Test 4: Check profile picture upload status
      try {
        const uploadStatus = await ctx.runQuery(api.profilePictures.getProfilePictureUploadStatus, {});
        results.tests.push({
          name: "profile_picture_upload_status",
          status: "passed",
          result: uploadStatus,
        });
        results.summary.passed++;
      } catch (error) {
        results.tests.push({
          name: "profile_picture_upload_status",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }

      // Test 5: Check user's files
      try {
        const userFiles = await ctx.runQuery(api.fileStorage.getUserFilesByType, {
          fileType: "profile_picture",
          limit: 5,
        });
        results.tests.push({
          name: "user_files_query",
          status: "passed",
          result: `Found ${userFiles.length} user files`,
        });
        results.summary.passed++;
      } catch (error) {
        results.tests.push({
          name: "user_files_query",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    results.summary.total = results.tests.length;
    return results;
  },
});

// Test Mutation: Create mock file metadata
export const createMockFileMetadata = mutation({
  args: {
    fileType: v.union(
      v.literal("profile_picture"),
      v.literal("sponsor_logo"),
      v.literal("session_image"),
      v.literal("other")
    ),
    fileName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const fileName = args.fileName || `test_${args.fileType}_${Date.now()}.jpg`;
    const now = Date.now();

    // Create a mock storage ID (this won't actually exist in storage)
    const mockStorageId = `mock_storage_${now}` as any;

    const fileMetadataId = await ctx.db.insert("fileMetadata", {
      storageId: mockStorageId,
      userId,
      fileName,
      fileType: args.fileType,
      mimeType: "image/jpeg",
      fileSize: 1024 * 100, // 100KB
      associatedId: undefined,
      uploadedAt: now,
      updatedAt: now,
    });

    return {
      fileMetadataId,
      storageId: mockStorageId,
      fileName,
      fileType: args.fileType,
      message: "Mock file metadata created successfully",
    };
  },
});

// Test Action: File upload workflow test
export const testFileUploadWorkflow = action({
  args: {
    includeProfilePicture: v.optional(v.boolean()),
    includeSponsorLogo: v.optional(v.boolean()),
    includeValidation: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const results = {
      timestamp: Date.now(),
      workflow: "file_upload_integration",
      steps: [] as any[],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
      },
    };

    // Step 1: Validate operations
    try {
      const validation = await ctx.runQuery(api.fileUploadTestSuite.testFileUploadOperations, {});
      results.steps.push({
        name: "validate_operations",
        status: validation.summary.failed === 0 ? "passed" : "failed",
        result: validation,
      });
      if (validation.summary.failed === 0) results.summary.passed++;
      else results.summary.failed++;
    } catch (error) {
      results.steps.push({
        name: "validate_operations",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Step 2: Test profile picture workflow (if requested)
    if (args.includeProfilePicture !== false) {
      try {
        // Get upload URL
        const uploadUrl = await ctx.runAction(api.profilePictures.getProfilePictureUploadUrl, {});
        
        results.steps.push({
          name: "profile_picture_upload_url",
          status: "passed",
          result: {
            hasUploadUrl: !!uploadUrl.uploadUrl,
            maxFileSize: uploadUrl.maxFileSize,
            allowedTypes: uploadUrl.allowedTypes,
          },
        });
        results.summary.passed++;

        // Test upload status
        const uploadStatus = await ctx.runQuery(api.profilePictures.getProfilePictureUploadStatus, {});
        
        results.steps.push({
          name: "profile_picture_upload_status",
          status: "passed",
          result: uploadStatus,
        });
        results.summary.passed++;

      } catch (error) {
        results.steps.push({
          name: "profile_picture_workflow",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    // Step 3: Test sponsor logo workflow (if requested)
    if (args.includeSponsorLogo !== false) {
      try {
        // Get upload URL
        const uploadUrl = await ctx.runAction(api.sponsorLogos.getSponsorLogoUploadUrl, {});
        
        results.steps.push({
          name: "sponsor_logo_upload_url",
          status: "passed",
          result: {
            hasUploadUrl: !!uploadUrl.uploadUrl,
            maxFileSize: uploadUrl.maxFileSize,
            allowedTypes: uploadUrl.allowedTypes,
          },
        });
        results.summary.passed++;

        // Test user's sponsor logos
        const userLogos = await ctx.runQuery(api.sponsorLogos.getUserSponsorLogos, {
          limit: 5,
        });
        
        results.steps.push({
          name: "user_sponsor_logos",
          status: "passed",
          result: {
            count: userLogos.length,
            hasLogos: userLogos.length > 0,
          },
        });
        results.summary.passed++;

      } catch (error) {
        results.steps.push({
          name: "sponsor_logo_workflow",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    // Step 4: Test file validation (if requested)
    if (args.includeValidation !== false) {
      try {
        // Create mock file metadata to test validation
        const mockFile = await ctx.runMutation(api.fileUploadTestSuite.createMockFileMetadata, {
          fileType: "profile_picture",
          fileName: "test_validation.jpg",
        });
        
        results.steps.push({
          name: "file_validation_test",
          status: "passed",
          result: mockFile,
        });
        results.summary.passed++;

      } catch (error) {
        results.steps.push({
          name: "file_validation_test",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    results.summary.total = results.steps.length;
    return results;
  },
});

// Test Query: File upload statistics
export const getFileUploadStatistics = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get all file metadata
    const allFiles = await ctx.db.query("fileMetadata").collect();
    
    // Get user's files
    const userFiles = allFiles.filter(file => file.userId === userId);

    // Count by file type
    const fileTypeCounts: Record<string, number> = {};
    for (const file of allFiles) {
      fileTypeCounts[file.fileType] = (fileTypeCounts[file.fileType] || 0) + 1;
    }

    // Count by MIME type
    const mimeTypeCounts: Record<string, number> = {};
    for (const file of allFiles) {
      mimeTypeCounts[file.mimeType] = (mimeTypeCounts[file.mimeType] || 0) + 1;
    }

    // Calculate total file sizes
    const totalSize = allFiles.reduce((sum, file) => sum + file.fileSize, 0);
    const userTotalSize = userFiles.reduce((sum, file) => sum + file.fileSize, 0);

    // Get recent uploads (last 7 days)
    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    const recentUploads = allFiles.filter(file => file.uploadedAt > sevenDaysAgo);

    return {
      total: allFiles.length,
      userTotal: userFiles.length,
      fileTypeCounts,
      mimeTypeCounts,
      totalSizeMB: Math.round(totalSize / 1024 / 1024 * 100) / 100,
      userTotalSizeMB: Math.round(userTotalSize / 1024 / 1024 * 100) / 100,
      recentUploads: recentUploads.length,
      averageFileSizeKB: allFiles.length > 0 
        ? Math.round(totalSize / allFiles.length / 1024 * 100) / 100
        : 0,
    };
  },
});

// Test Query: File upload health check
export const fileUploadHealthCheck = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    
    const health = {
      timestamp: Date.now(),
      authentication: {
        isAuthenticated: !!userId,
        userId,
      },
      database: {
        fileMetadataTable: false,
        sponsorLogosTable: false,
      },
      functionality: {
        canGenerateUploadUrl: false,
        canUploadProfilePicture: false,
        canUploadSponsorLogo: false,
      },
      statistics: {
        totalFiles: 0,
        totalSponsorLogos: 0,
      },
    };

    // Test database tables
    try {
      const fileMetadata = await ctx.db.query("fileMetadata").take(1);
      health.database.fileMetadataTable = true;
      health.statistics.totalFiles = fileMetadata.length;
    } catch (error) {
      console.log("File metadata table error:", error);
    }

    try {
      const sponsorLogos = await ctx.db.query("sponsorLogos").take(1);
      health.database.sponsorLogosTable = true;
      health.statistics.totalSponsorLogos = sponsorLogos.length;
    } catch (error) {
      console.log("Sponsor logos table error:", error);
    }

    // Test functionality if authenticated
    if (userId) {
      try {
        const uploadUrl = await ctx.runMutation(api.fileStorage.generateUploadUrl, {});
        health.functionality.canGenerateUploadUrl = !!uploadUrl;
      } catch (error) {
        console.log("Generate upload URL error:", error);
      }

      try {
        const profileStatus = await ctx.runQuery(api.profilePictures.getProfilePictureUploadStatus, {});
        health.functionality.canUploadProfilePicture = profileStatus.canUpload;
      } catch (error) {
        console.log("Profile picture upload status error:", error);
      }

      try {
        const logoUrl = await ctx.runAction(api.sponsorLogos.getSponsorLogoUploadUrl, {});
        health.functionality.canUploadSponsorLogo = !!logoUrl.uploadUrl;
      } catch (error) {
        console.log("Sponsor logo upload URL error:", error);
      }
    }

    return health;
  },
});
