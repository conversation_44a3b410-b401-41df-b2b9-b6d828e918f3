/**
 * Apple Music API Integration
 * 
 * This module handles Apple Music API operations including song search,
 * metadata retrieval, playlist management, and API response handling
 * with proper error handling and token validation.
 */

import { v } from "convex/values";
import { action, query } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Action: Search songs in Apple Music
export const searchSongs = action({
  args: {
    query: v.string(),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Validate search query
    if (args.query.trim().length === 0) {
      throw new Error("Search query cannot be empty");
    }

    if (args.query.length > 200) {
      throw new Error("Search query too long (max 200 characters)");
    }

    // Get user's Apple Music token
    let tokenData;
    try {
      tokenData = await ctx.runQuery(api.appleMusicTokens.getAppleMusicTokenForApi, {});
    } catch (error) {
      throw new Error("Valid Apple Music token required for search");
    }

    const limit = Math.min(args.limit ?? 25, 50); // Max 50 results
    const offset = args.offset ?? 0;

    // TODO: Integrate with actual Apple Music API
    // For now, return mock search results
    const mockResults = generateMockSearchResults(args.query, limit, offset);

    return {
      query: args.query,
      results: mockResults,
      pagination: {
        limit,
        offset,
        total: mockResults.length,
        hasMore: mockResults.length === limit,
      },
      searchedAt: Date.now(),
    };
  },
});

// Action: Get song details by Apple Music ID
export const getSongDetails = action({
  args: {
    appleMusicId: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Validate Apple Music ID format
    if (!args.appleMusicId || args.appleMusicId.trim().length === 0) {
      throw new Error("Apple Music ID is required");
    }

    // Get user's Apple Music token
    let tokenData;
    try {
      tokenData = await ctx.runQuery(api.appleMusicTokens.getAppleMusicTokenForApi, {});
    } catch (error) {
      throw new Error("Valid Apple Music token required");
    }

    // TODO: Integrate with actual Apple Music API
    // For now, return mock song details
    const mockSongDetails = generateMockSongDetails(args.appleMusicId);

    return {
      song: mockSongDetails,
      retrievedAt: Date.now(),
    };
  },
});

// Action: Get user's Apple Music playlists
export const getUserPlaylists = action({
  args: {
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get user's Apple Music token
    let tokenData;
    try {
      tokenData = await ctx.runQuery(api.appleMusicTokens.getAppleMusicTokenForApi, {});
    } catch (error) {
      throw new Error("Valid Apple Music token required");
    }

    const limit = Math.min(args.limit ?? 20, 50);
    const offset = args.offset ?? 0;

    // TODO: Integrate with actual Apple Music API
    // For now, return mock playlists
    const mockPlaylists = generateMockPlaylists(limit, offset);

    return {
      playlists: mockPlaylists,
      pagination: {
        limit,
        offset,
        total: mockPlaylists.length,
        hasMore: mockPlaylists.length === limit,
      },
      retrievedAt: Date.now(),
    };
  },
});

// Action: Get playlist details and tracks
export const getPlaylistDetails = action({
  args: {
    playlistId: v.string(),
    includeTracks: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    if (!args.playlistId || args.playlistId.trim().length === 0) {
      throw new Error("Playlist ID is required");
    }

    // Get user's Apple Music token
    let tokenData;
    try {
      tokenData = await ctx.runQuery(api.appleMusicTokens.getAppleMusicTokenForApi, {});
    } catch (error) {
      throw new Error("Valid Apple Music token required");
    }

    // TODO: Integrate with actual Apple Music API
    // For now, return mock playlist details
    const mockPlaylistDetails = generateMockPlaylistDetails(
      args.playlistId, 
      args.includeTracks ?? true
    );

    return {
      playlist: mockPlaylistDetails,
      retrievedAt: Date.now(),
    };
  },
});

// Action: Add song to Apple Music playlist
export const addSongToPlaylist = action({
  args: {
    playlistId: v.string(),
    songId: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    if (!args.playlistId || args.playlistId.trim().length === 0) {
      throw new Error("Playlist ID is required");
    }

    if (!args.songId || args.songId.trim().length === 0) {
      throw new Error("Song ID is required");
    }

    // Get user's Apple Music token
    let tokenData;
    try {
      tokenData = await ctx.runQuery(api.appleMusicTokens.getAppleMusicTokenForApi, {});
    } catch (error) {
      throw new Error("Valid Apple Music token required");
    }

    // TODO: Integrate with actual Apple Music API
    // For now, return mock success response
    return {
      success: true,
      playlistId: args.playlistId,
      songId: args.songId,
      message: "Song added to playlist successfully",
      addedAt: Date.now(),
    };
  },
});

// Action: Create new Apple Music playlist
export const createPlaylist = action({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    isPublic: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Validate playlist name
    if (!args.name || args.name.trim().length === 0) {
      throw new Error("Playlist name is required");
    }

    if (args.name.length > 100) {
      throw new Error("Playlist name cannot exceed 100 characters");
    }

    // Get user's Apple Music token
    let tokenData;
    try {
      tokenData = await ctx.runQuery(api.appleMusicTokens.getAppleMusicTokenForApi, {});
    } catch (error) {
      throw new Error("Valid Apple Music token required");
    }

    // TODO: Integrate with actual Apple Music API
    // For now, return mock playlist creation
    const mockPlaylist = {
      id: `pl_${Date.now()}`,
      name: args.name.trim(),
      description: args.description?.trim(),
      isPublic: args.isPublic ?? false,
      trackCount: 0,
      createdAt: Date.now(),
    };

    return {
      playlist: mockPlaylist,
      message: "Playlist created successfully",
    };
  },
});

// Query: Get Apple Music integration status
export const getAppleMusicIntegrationStatus = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return {
        isAuthenticated: false,
        hasToken: false,
        isTokenValid: false,
        canUseApi: false,
      };
    }

    const tokenStatus = await ctx.runQuery(api.appleMusicTokens.hasValidAppleMusicToken, {});

    return {
      isAuthenticated: true,
      hasToken: tokenStatus.hasToken,
      isTokenValid: tokenStatus.isValid,
      isTokenExpired: tokenStatus.isExpired,
      canUseApi: tokenStatus.isValid && !tokenStatus.isExpired,
      expiresAt: tokenStatus.expiresAt,
      expiresInHours: tokenStatus.expiresInHours,
    };
  },
});

// Mock data generators (replace with actual API calls in production)
function generateMockSearchResults(query: string, limit: number, offset: number) {
  const mockSongs = [
    {
      id: "1001",
      title: "Bohemian Rhapsody",
      artist: "Queen",
      album: "A Night at the Opera",
      duration: 355000, // milliseconds
      artwork: "https://example.com/artwork/queen-bohemian.jpg",
      genre: "Rock",
      releaseDate: "1975-10-31",
      isExplicit: false,
    },
    {
      id: "1002", 
      title: "Billie Jean",
      artist: "Michael Jackson",
      album: "Thriller",
      duration: 294000,
      artwork: "https://example.com/artwork/mj-billie-jean.jpg",
      genre: "Pop",
      releaseDate: "1983-01-02",
      isExplicit: false,
    },
    {
      id: "1003",
      title: "Hotel California",
      artist: "Eagles",
      album: "Hotel California",
      duration: 391000,
      artwork: "https://example.com/artwork/eagles-hotel.jpg",
      genre: "Rock",
      releaseDate: "1976-12-08",
      isExplicit: false,
    },
  ];

  // Filter by query and apply pagination
  const filtered = mockSongs.filter(song => 
    song.title.toLowerCase().includes(query.toLowerCase()) ||
    song.artist.toLowerCase().includes(query.toLowerCase()) ||
    song.album.toLowerCase().includes(query.toLowerCase())
  );

  return filtered.slice(offset, offset + limit);
}

function generateMockSongDetails(appleMusicId: string) {
  return {
    id: appleMusicId,
    title: "Sample Song",
    artist: "Sample Artist",
    album: "Sample Album",
    duration: 240000,
    artwork: "https://example.com/artwork/sample.jpg",
    genre: "Pop",
    releaseDate: "2024-01-01",
    isExplicit: false,
    previewUrl: "https://example.com/preview/sample.m4a",
    appleMusicUrl: `https://music.apple.com/song/${appleMusicId}`,
  };
}

function generateMockPlaylists(limit: number, offset: number) {
  const mockPlaylists = [
    {
      id: "pl_001",
      name: "My Favorites",
      description: "My favorite songs",
      trackCount: 25,
      isPublic: false,
      artwork: "https://example.com/playlist/favorites.jpg",
      createdAt: Date.now() - (7 * 24 * 60 * 60 * 1000), // 7 days ago
    },
    {
      id: "pl_002",
      name: "Party Mix",
      description: "Songs for parties",
      trackCount: 50,
      isPublic: true,
      artwork: "https://example.com/playlist/party.jpg",
      createdAt: Date.now() - (14 * 24 * 60 * 60 * 1000), // 14 days ago
    },
  ];

  return mockPlaylists.slice(offset, offset + limit);
}

function generateMockPlaylistDetails(playlistId: string, includeTracks: boolean) {
  const playlist = {
    id: playlistId,
    name: "Sample Playlist",
    description: "A sample playlist",
    trackCount: 3,
    isPublic: false,
    artwork: "https://example.com/playlist/sample.jpg",
    createdAt: Date.now() - (7 * 24 * 60 * 60 * 1000),
    tracks: includeTracks ? [
      {
        id: "1001",
        title: "Song 1",
        artist: "Artist 1",
        duration: 240000,
        addedAt: Date.now() - (24 * 60 * 60 * 1000),
      },
      {
        id: "1002",
        title: "Song 2", 
        artist: "Artist 2",
        duration: 180000,
        addedAt: Date.now() - (12 * 60 * 60 * 1000),
      },
    ] : undefined,
  };

  return playlist;
}
