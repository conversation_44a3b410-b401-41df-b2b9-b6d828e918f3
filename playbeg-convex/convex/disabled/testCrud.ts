/**
 * CRUD Operations Test Suite
 * 
 * This module contains test functions to validate all CRUD operations
 * for users and DJ profiles. These tests ensure proper authentication,
 * validation, and data integrity.
 */

import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Test Query: Comprehensive user and profile status check
export const testUserProfileStatus = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    
    const results = {
      timestamp: Date.now(),
      authentication: {
        isAuthenticated: !!userId,
        userId: userId,
      },
      user: null as any,
      djProfile: null as any,
      stats: {
        totalDjProfiles: 0,
        completedOnboarding: 0,
        recentlyActive: 0,
      },
    };

    // Get current user if authenticated
    if (userId) {
      const user = await ctx.db.get(userId);
      results.user = user;

      // Get DJ profile
      const djProfile = await ctx.db
        .query("djProfiles")
        .withIndex("by_user", (q) => q.eq("userId", userId))
        .first();
      results.djProfile = djProfile;
    }

    // Get overall stats
    const allProfiles = await ctx.db.query("djProfiles").collect();
    results.stats.totalDjProfiles = allProfiles.length;
    results.stats.completedOnboarding = allProfiles.filter(p => p.completedOnboarding).length;

    // Count recently active (last 7 days)
    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    results.stats.recentlyActive = allProfiles.filter(p => 
      p.lastLoginAt && p.lastLoginAt > sevenDaysAgo
    ).length;

    return results;
  },
});

// Test Mutation: Create test DJ profile with validation
export const testCreateDjProfile = mutation({
  args: {
    displayName: v.optional(v.string()),
    testValidation: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required for testing");
    }

    const results = {
      timestamp: Date.now(),
      testType: "createDjProfile",
      success: false,
      profileId: null as any,
      validationTests: [] as any[],
      error: null as any,
    };

    try {
      // Test validation if requested
      if (args.testValidation) {
        // Test empty display name
        try {
          await ctx.db.insert("djProfiles", {
            userId,
            displayName: "",
            completedOnboarding: false,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          });
          results.validationTests.push({
            test: "empty_display_name",
            passed: false,
            message: "Should have rejected empty display name",
          });
        } catch (error) {
          results.validationTests.push({
            test: "empty_display_name",
            passed: true,
            message: "Correctly rejected empty display name",
          });
        }

        // Test long display name
        const longName = "a".repeat(101);
        try {
          await ctx.db.insert("djProfiles", {
            userId,
            displayName: longName,
            completedOnboarding: false,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          });
          results.validationTests.push({
            test: "long_display_name",
            passed: false,
            message: "Should have rejected long display name",
          });
        } catch (error) {
          results.validationTests.push({
            test: "long_display_name",
            passed: true,
            message: "Correctly rejected long display name",
          });
        }
      }

      // Check if profile already exists
      const existingProfile = await ctx.db
        .query("djProfiles")
        .withIndex("by_user", (q) => q.eq("userId", userId))
        .first();

      if (existingProfile) {
        results.error = "DJ profile already exists";
        results.profileId = existingProfile._id;
        return results;
      }

      // Create profile with proper validation
      const displayName = args.displayName?.trim();
      if (displayName !== undefined) {
        if (displayName.length === 0) {
          throw new Error("Display name cannot be empty");
        }
        if (displayName.length > 100) {
          throw new Error("Display name cannot exceed 100 characters");
        }
      }

      const now = Date.now();
      const profileId = await ctx.db.insert("djProfiles", {
        userId,
        displayName,
        completedOnboarding: false,
        createdAt: now,
        updatedAt: now,
      });

      results.success = true;
      results.profileId = profileId;

    } catch (error) {
      results.error = error instanceof Error ? error.message : String(error);
    }

    return results;
  },
});

// Test Mutation: Update DJ profile with validation
export const testUpdateDjProfile = mutation({
  args: {
    profileId: v.id("djProfiles"),
    displayName: v.optional(v.string()),
    completedOnboarding: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required for testing");
    }

    const results = {
      timestamp: Date.now(),
      testType: "updateDjProfile",
      success: false,
      updatedFields: {} as any,
      error: null as any,
    };

    try {
      // Get existing profile and verify ownership
      const existingProfile = await ctx.db.get(args.profileId);
      if (!existingProfile) {
        throw new Error("DJ profile not found");
      }

      if (existingProfile.userId !== userId) {
        throw new Error("Access denied: You can only update your own profile");
      }

      // Validate display name if provided
      if (args.displayName !== undefined) {
        const trimmedName = args.displayName.trim();
        if (trimmedName.length === 0) {
          throw new Error("Display name cannot be empty");
        }
        if (trimmedName.length > 100) {
          throw new Error("Display name cannot exceed 100 characters");
        }
        results.updatedFields.displayName = trimmedName;
      }

      if (args.completedOnboarding !== undefined) {
        results.updatedFields.completedOnboarding = args.completedOnboarding;
      }

      // Prepare update object
      const updateData: any = {
        updatedAt: Date.now(),
        ...results.updatedFields,
      };

      await ctx.db.patch(args.profileId, updateData);
      results.success = true;

    } catch (error) {
      results.error = error instanceof Error ? error.message : String(error);
    }

    return results;
  },
});

// Test Action: Comprehensive CRUD test suite
export const runCrudTestSuite = action({
  args: {
    testLevel: v.union(v.literal("basic"), v.literal("comprehensive")),
  },
  handler: async (ctx, args) => {
    const results = {
      timestamp: Date.now(),
      testLevel: args.testLevel,
      tests: [] as any[],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
      },
    };

    // Test 1: Check user status
    try {
      const userStatus = await ctx.runQuery(api.users.checkUserStatus, {});
      results.tests.push({
        name: "checkUserStatus",
        passed: true,
        result: userStatus,
      });
    } catch (error) {
      results.tests.push({
        name: "checkUserStatus",
        passed: false,
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Test 2: Get current user with profile
    try {
      const userWithProfile = await ctx.runQuery(api.users.getCurrentUserWithProfile, {});
      results.tests.push({
        name: "getCurrentUserWithProfile",
        passed: true,
        result: userWithProfile,
      });
    } catch (error) {
      results.tests.push({
        name: "getCurrentUserWithProfile",
        passed: false,
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Test 3: List DJ profiles
    try {
      const djProfiles = await ctx.runQuery(api.djProfiles.listDjProfiles, { limit: 5 });
      results.tests.push({
        name: "listDjProfiles",
        passed: true,
        result: { count: djProfiles.length },
      });
    } catch (error) {
      results.tests.push({
        name: "listDjProfiles",
        passed: false,
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Test 4: Get user activity summary
    try {
      const activitySummary = await ctx.runQuery(api.users.getUserActivitySummary, {});
      results.tests.push({
        name: "getUserActivitySummary",
        passed: true,
        result: activitySummary,
      });
    } catch (error) {
      results.tests.push({
        name: "getUserActivitySummary",
        passed: false,
        error: error instanceof Error ? error.message : String(error),
      });
    }

    if (args.testLevel === "comprehensive") {
      // Test 5: Create DJ profile (if not exists)
      try {
        const createResult = await ctx.runMutation(api.testCrud.testCreateDjProfile, {
          displayName: "Test DJ Profile",
          testValidation: true,
        });
        results.tests.push({
          name: "testCreateDjProfile",
          passed: createResult.success || createResult.error === "DJ profile already exists",
          result: createResult,
        });
      } catch (error) {
        results.tests.push({
          name: "testCreateDjProfile",
          passed: false,
          error: error instanceof Error ? error.message : String(error),
        });
      }

      // Test 6: Update last login
      try {
        await ctx.runMutation(api.djProfiles.updateLastLogin, {});
        results.tests.push({
          name: "updateLastLogin",
          passed: true,
          result: "Login timestamp updated",
        });
      } catch (error) {
        results.tests.push({
          name: "updateLastLogin",
          passed: false,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    // Calculate summary
    results.summary.total = results.tests.length;
    results.summary.passed = results.tests.filter(t => t.passed).length;
    results.summary.failed = results.summary.total - results.summary.passed;

    return results;
  },
});

// Test Query: Validate schema relationships
export const testSchemaRelationships = query({
  args: {},
  handler: async (ctx) => {
    const results = {
      timestamp: Date.now(),
      relationships: [] as any[],
      integrity: {
        orphanedProfiles: 0,
        validRelationships: 0,
      },
    };

    // Check DJ profiles and their user relationships
    const djProfiles = await ctx.db.query("djProfiles").collect();
    
    for (const profile of djProfiles) {
      try {
        const user = await ctx.db.get(profile.userId);
        if (user) {
          results.relationships.push({
            profileId: profile._id,
            userId: profile.userId,
            valid: true,
            displayName: profile.displayName,
            onboarding: profile.completedOnboarding,
          });
          results.integrity.validRelationships++;
        } else {
          results.relationships.push({
            profileId: profile._id,
            userId: profile.userId,
            valid: false,
            error: "User not found",
          });
          results.integrity.orphanedProfiles++;
        }
      } catch (error) {
        results.relationships.push({
          profileId: profile._id,
          userId: profile.userId,
          valid: false,
          error: error instanceof Error ? error.message : String(error),
        });
        results.integrity.orphanedProfiles++;
      }
    }

    return results;
  },
});
