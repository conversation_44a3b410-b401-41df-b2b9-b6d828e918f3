/**
 * Live Session Analytics
 * 
 * This module provides real-time analytics for DJ sessions including
 * live metrics, audience engagement tracking, performance indicators,
 * and comprehensive session analytics with real-time updates.
 */

import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Query: Get comprehensive live session analytics
export const getLiveSessionAnalytics = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return null;
    }

    const now = Date.now();
    const sessionStart = session.startTime;
    const sessionDuration = session.active 
      ? now - sessionStart
      : (session.endTime || now) - sessionStart;

    // Get all requests for analysis
    const allRequests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    // Time-based analysis
    const timeWindows = {
      last5Minutes: now - (5 * 60 * 1000),
      last15Minutes: now - (15 * 60 * 1000),
      last30Minutes: now - (30 * 60 * 1000),
      lastHour: now - (60 * 60 * 1000),
    };

    const requestsByTimeWindow = {
      last5Minutes: allRequests.filter(r => r.createdAt > timeWindows.last5Minutes).length,
      last15Minutes: allRequests.filter(r => r.createdAt > timeWindows.last15Minutes).length,
      last30Minutes: allRequests.filter(r => r.createdAt > timeWindows.last30Minutes).length,
      lastHour: allRequests.filter(r => r.createdAt > timeWindows.lastHour).length,
    };

    // User engagement analysis
    const uniqueUsers = new Set(allRequests.map(r => r.userId));
    const userEngagement = Array.from(uniqueUsers).map(userId => {
      const userRequests = allRequests.filter(r => r.userId === userId);
      return {
        userId,
        requestCount: userRequests.length,
        firstRequest: Math.min(...userRequests.map(r => r.createdAt)),
        lastRequest: Math.max(...userRequests.map(r => r.createdAt)),
        timeSpan: Math.max(...userRequests.map(r => r.createdAt)) - Math.min(...userRequests.map(r => r.createdAt)),
      };
    });

    // Request status analysis
    const statusAnalysis = {
      pending: allRequests.filter(r => r.status === "pending").length,
      approved: allRequests.filter(r => r.status === "approved").length,
      rejected: allRequests.filter(r => r.status === "rejected").length,
      played: allRequests.filter(r => r.status === "played").length,
    };

    // Performance metrics
    const performanceMetrics = {
      requestsPerMinute: sessionDuration > 0 
        ? Math.round((allRequests.length / (sessionDuration / (1000 * 60))) * 100) / 100
        : 0,
      averageResponseTime: calculateAverageResponseTime(allRequests),
      approvalRate: allRequests.length > 0 
        ? Math.round((statusAnalysis.approved / allRequests.length) * 100)
        : 0,
      playRate: allRequests.length > 0 
        ? Math.round((statusAnalysis.played / allRequests.length) * 100)
        : 0,
    };

    // Engagement trends (hourly breakdown)
    const hourlyBreakdown = generateHourlyBreakdown(allRequests, sessionStart, now);

    return {
      sessionId: args.sessionId,
      sessionInfo: {
        title: session.title,
        isActive: session.active,
        startTime: sessionStart,
        duration: sessionDuration,
        isWeddingMode: session.isWeddingMode,
      },
      realTimeMetrics: {
        totalRequests: allRequests.length,
        uniqueUsers: uniqueUsers.size,
        requestsByTimeWindow,
        currentActivity: requestsByTimeWindow.last5Minutes,
      },
      engagement: {
        userEngagement: userEngagement.sort((a, b) => b.requestCount - a.requestCount).slice(0, 10),
        averageRequestsPerUser: uniqueUsers.size > 0 ? Math.round((allRequests.length / uniqueUsers.size) * 100) / 100 : 0,
        retentionRate: calculateRetentionRate(userEngagement, sessionDuration),
      },
      performance: performanceMetrics,
      statusDistribution: statusAnalysis,
      trends: {
        hourlyBreakdown,
        peakActivity: findPeakActivity(hourlyBreakdown),
      },
      lastUpdated: now,
    };
  },
});

// Query: Get real-time audience engagement metrics
export const getAudienceEngagementMetrics = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return null;
    }

    const now = Date.now();
    
    // Get recent requests for engagement analysis
    const recentRequests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .filter((q) => q.gte(q.field("createdAt"), now - (30 * 60 * 1000))) // Last 30 minutes
      .collect();

    // Get all requests for comparison
    const allRequests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    // Calculate engagement metrics
    const uniqueRecentUsers = new Set(recentRequests.map(r => r.userId)).size;
    const totalUniqueUsers = new Set(allRequests.map(r => r.userId)).size;

    // Activity levels
    const activityLevels = {
      veryHigh: recentRequests.length >= 20,
      high: recentRequests.length >= 10 && recentRequests.length < 20,
      medium: recentRequests.length >= 5 && recentRequests.length < 10,
      low: recentRequests.length >= 1 && recentRequests.length < 5,
      inactive: recentRequests.length === 0,
    };

    const currentActivityLevel = Object.keys(activityLevels).find(
      level => activityLevels[level as keyof typeof activityLevels]
    ) || "inactive";

    // User interaction patterns
    const userPatterns = analyzeUserPatterns(recentRequests);

    return {
      sessionId: args.sessionId,
      timeWindow: "30 minutes",
      engagement: {
        activeUsers: uniqueRecentUsers,
        totalUsers: totalUniqueUsers,
        activityLevel: currentActivityLevel,
        recentRequests: recentRequests.length,
        engagementRate: totalUniqueUsers > 0 
          ? Math.round((uniqueRecentUsers / totalUniqueUsers) * 100)
          : 0,
      },
      patterns: userPatterns,
      trends: {
        increasing: recentRequests.length > (allRequests.length / 2),
        peakTime: findRecentPeakTime(recentRequests),
      },
      lastUpdated: now,
    };
  },
});

// Query: Get session performance indicators
export const getSessionPerformanceIndicators = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return null;
    }

    const now = Date.now();
    const sessionDuration = session.active 
      ? now - session.startTime
      : (session.endTime || now) - session.startTime;

    // Get all requests
    const allRequests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    // Calculate key performance indicators
    const kpis = {
      totalRequests: allRequests.length,
      requestsPerHour: sessionDuration > 0 
        ? Math.round((allRequests.length / (sessionDuration / (1000 * 60 * 60))) * 100) / 100
        : 0,
      approvalRate: allRequests.length > 0 
        ? Math.round((allRequests.filter(r => r.status === "approved").length / allRequests.length) * 100)
        : 0,
      playRate: allRequests.length > 0 
        ? Math.round((allRequests.filter(r => r.status === "played").length / allRequests.length) * 100)
        : 0,
      averageResponseTime: calculateAverageResponseTime(allRequests),
      uniqueParticipants: new Set(allRequests.map(r => r.userId)).size,
    };

    // Performance benchmarks
    const benchmarks = {
      excellent: {
        requestsPerHour: 30,
        approvalRate: 80,
        playRate: 70,
        responseTime: 2 * 60 * 1000, // 2 minutes
      },
      good: {
        requestsPerHour: 20,
        approvalRate: 70,
        playRate: 60,
        responseTime: 5 * 60 * 1000, // 5 minutes
      },
      average: {
        requestsPerHour: 10,
        approvalRate: 60,
        playRate: 50,
        responseTime: 10 * 60 * 1000, // 10 minutes
      },
    };

    // Calculate performance score
    const performanceScore = calculatePerformanceScore(kpis, benchmarks);

    return {
      sessionId: args.sessionId,
      duration: sessionDuration,
      kpis,
      performance: {
        score: performanceScore,
        level: getPerformanceLevel(performanceScore),
        strengths: identifyStrengths(kpis, benchmarks),
        improvements: identifyImprovements(kpis, benchmarks),
      },
      benchmarks,
      lastUpdated: now,
    };
  },
});

// Mutation: Track user interaction event
export const trackUserInteraction = mutation({
  args: {
    sessionId: v.id("sessions"),
    interactionType: v.union(
      v.literal("request_submitted"),
      v.literal("session_joined"),
      v.literal("session_left"),
      v.literal("request_voted"),
      v.literal("message_sent"),
      v.literal("reaction_added")
    ),
    metadata: v.optional(v.object({
      requestId: v.optional(v.id("songRequests")),
      messageId: v.optional(v.string()),
      reactionType: v.optional(v.string()),
      additionalData: v.optional(v.any()),
    })),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const now = Date.now();
    
    // Store interaction event
    const interactionId = await ctx.db.insert("userInteractions", {
      userId,
      sessionId: args.sessionId,
      interactionType: args.interactionType,
      metadata: args.metadata,
      timestamp: now,
    });

    // Update session activity
    await ctx.runMutation(api.realTimeSubscriptions.updateSessionActivity, {
      sessionId: args.sessionId,
      activityType: "general_activity",
    });

    return {
      interactionId,
      timestamp: now,
      message: "User interaction tracked successfully",
    };
  },
});

// Helper functions
function calculateAverageResponseTime(requests: any[]): number {
  const responseTimes = requests
    .filter(r => r.status !== "pending" && r.updatedAt > r.createdAt)
    .map(r => r.updatedAt - r.createdAt);
  
  return responseTimes.length > 0 
    ? Math.round(responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length)
    : 0;
}

function calculateRetentionRate(userEngagement: any[], sessionDuration: number): number {
  if (userEngagement.length === 0) return 0;
  
  const retainedUsers = userEngagement.filter(user => 
    user.timeSpan > sessionDuration * 0.3 // Users active for at least 30% of session
  ).length;
  
  return Math.round((retainedUsers / userEngagement.length) * 100);
}

function generateHourlyBreakdown(requests: any[], sessionStart: number, now: number): any[] {
  const sessionDuration = now - sessionStart;
  const hours = Math.ceil(sessionDuration / (1000 * 60 * 60));
  
  const breakdown = [];
  for (let i = 0; i < hours; i++) {
    const hourStart = sessionStart + (i * 60 * 60 * 1000);
    const hourEnd = hourStart + (60 * 60 * 1000);
    
    const hourRequests = requests.filter(r => 
      r.createdAt >= hourStart && r.createdAt < hourEnd
    );
    
    breakdown.push({
      hour: i + 1,
      startTime: hourStart,
      endTime: Math.min(hourEnd, now),
      requests: hourRequests.length,
      uniqueUsers: new Set(hourRequests.map(r => r.userId)).size,
    });
  }
  
  return breakdown;
}

function findPeakActivity(hourlyBreakdown: any[]): any {
  if (hourlyBreakdown.length === 0) return null;
  
  return hourlyBreakdown.reduce((peak, hour) => 
    hour.requests > peak.requests ? hour : peak
  );
}

function analyzeUserPatterns(requests: any[]): any {
  const userActivity = new Map();
  
  requests.forEach(request => {
    const userId = request.userId;
    if (!userActivity.has(userId)) {
      userActivity.set(userId, []);
    }
    userActivity.get(userId).push(request.createdAt);
  });
  
  const patterns = {
    rapidRequesters: 0, // Users with multiple requests in short time
    steadyParticipants: 0, // Users with consistent activity
    oneTimeUsers: 0, // Users with single request
  };
  
  userActivity.forEach(timestamps => {
    if (timestamps.length === 1) {
      patterns.oneTimeUsers++;
    } else if (timestamps.length >= 3) {
      const timeSpan = Math.max(...timestamps) - Math.min(...timestamps);
      if (timeSpan < 10 * 60 * 1000) { // 10 minutes
        patterns.rapidRequesters++;
      } else {
        patterns.steadyParticipants++;
      }
    } else {
      patterns.steadyParticipants++;
    }
  });
  
  return patterns;
}

function findRecentPeakTime(requests: any[]): number | null {
  if (requests.length === 0) return null;
  
  // Group requests by 5-minute intervals
  const intervals = new Map();
  const intervalSize = 5 * 60 * 1000; // 5 minutes
  
  requests.forEach(request => {
    const interval = Math.floor(request.createdAt / intervalSize) * intervalSize;
    intervals.set(interval, (intervals.get(interval) || 0) + 1);
  });
  
  let peakInterval = 0;
  let peakCount = 0;
  
  intervals.forEach((count, interval) => {
    if (count > peakCount) {
      peakCount = count;
      peakInterval = interval;
    }
  });
  
  return peakInterval;
}

function calculatePerformanceScore(kpis: any, benchmarks: any): number {
  const scores = [];
  
  // Score each KPI against benchmarks
  if (kpis.requestsPerHour >= benchmarks.excellent.requestsPerHour) scores.push(100);
  else if (kpis.requestsPerHour >= benchmarks.good.requestsPerHour) scores.push(80);
  else if (kpis.requestsPerHour >= benchmarks.average.requestsPerHour) scores.push(60);
  else scores.push(40);
  
  if (kpis.approvalRate >= benchmarks.excellent.approvalRate) scores.push(100);
  else if (kpis.approvalRate >= benchmarks.good.approvalRate) scores.push(80);
  else if (kpis.approvalRate >= benchmarks.average.approvalRate) scores.push(60);
  else scores.push(40);
  
  if (kpis.playRate >= benchmarks.excellent.playRate) scores.push(100);
  else if (kpis.playRate >= benchmarks.good.playRate) scores.push(80);
  else if (kpis.playRate >= benchmarks.average.playRate) scores.push(60);
  else scores.push(40);
  
  return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
}

function getPerformanceLevel(score: number): string {
  if (score >= 90) return "excellent";
  if (score >= 75) return "good";
  if (score >= 60) return "average";
  return "needs_improvement";
}

function identifyStrengths(kpis: any, benchmarks: any): string[] {
  const strengths = [];
  
  if (kpis.requestsPerHour >= benchmarks.good.requestsPerHour) {
    strengths.push("High audience engagement");
  }
  if (kpis.approvalRate >= benchmarks.good.approvalRate) {
    strengths.push("Good request curation");
  }
  if (kpis.playRate >= benchmarks.good.playRate) {
    strengths.push("Effective playlist management");
  }
  if (kpis.averageResponseTime <= benchmarks.good.responseTime) {
    strengths.push("Quick response times");
  }
  
  return strengths;
}

function identifyImprovements(kpis: any, benchmarks: any): string[] {
  const improvements = [];
  
  if (kpis.requestsPerHour < benchmarks.average.requestsPerHour) {
    improvements.push("Increase audience engagement");
  }
  if (kpis.approvalRate < benchmarks.average.approvalRate) {
    improvements.push("Improve request approval rate");
  }
  if (kpis.playRate < benchmarks.average.playRate) {
    improvements.push("Play more approved requests");
  }
  if (kpis.averageResponseTime > benchmarks.average.responseTime) {
    improvements.push("Respond to requests faster");
  }
  
  return improvements;
}
