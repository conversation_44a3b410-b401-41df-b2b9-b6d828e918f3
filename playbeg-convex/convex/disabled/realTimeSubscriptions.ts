/**
 * Real-time Subscriptions Management
 * 
 * This module provides optimized real-time subscription patterns for sessions,
 * song requests, and live updates with efficient data synchronization,
 * connection management, and reduced latency.
 */

import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Query: Get live session data with optimized subscriptions
export const getLiveSessionData = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return null;
    }

    // Get DJ profile for session owner info
    const djProfile = await ctx.db.get(session.djId);
    
    // Get recent song requests (last 50 for performance)
    const recentRequests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .order("desc")
      .take(50);

    // Get session statistics
    const totalRequests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    // Calculate session duration
    const sessionDuration = session.active 
      ? Date.now() - session.startTime
      : (session.endTime || Date.now()) - session.startTime;

    return {
      session: {
        id: session._id,
        title: session.title,
        description: session.description,
        active: session.active,
        startTime: session.startTime,
        endTime: session.endTime,
        isWeddingMode: session.isWeddingMode,
        updatedAt: session.updatedAt,
      },
      dj: djProfile ? {
        id: djProfile._id,
        stageName: djProfile.stageName,
        profilePictureStorageId: djProfile.profilePictureStorageId,
      } : null,
      requests: {
        recent: recentRequests,
        total: totalRequests.length,
        pending: totalRequests.filter(r => r.status === "pending").length,
        approved: totalRequests.filter(r => r.status === "approved").length,
        played: totalRequests.filter(r => r.status === "played").length,
      },
      metrics: {
        duration: sessionDuration,
        requestsPerHour: sessionDuration > 0 
          ? Math.round((totalRequests.length / (sessionDuration / (1000 * 60 * 60))) * 100) / 100
          : 0,
        lastActivity: Math.max(
          session.updatedAt,
          ...recentRequests.map(r => r.createdAt)
        ),
      },
      lastUpdated: Date.now(),
    };
  },
});

// Query: Get live song requests with real-time updates
export const getLiveSongRequests = query({
  args: {
    sessionId: v.id("sessions"),
    limit: v.optional(v.number()),
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("played")
    )),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 25;
    
    let requestsQuery = ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId));

    if (args.status) {
      requestsQuery = requestsQuery.filter((q) => q.eq(q.field("status"), args.status));
    }

    const requests = await requestsQuery
      .order("desc")
      .take(limit);

    // Get requester information for each request
    const requestsWithUsers = await Promise.all(
      requests.map(async (request) => {
        const user = await ctx.db.get(request.userId);
        return {
          ...request,
          user: user ? {
            id: user._id,
            name: user.name,
            email: user.email,
          } : null,
        };
      })
    );

    return {
      requests: requestsWithUsers,
      total: requests.length,
      lastUpdated: Date.now(),
    };
  },
});

// Query: Get session participants and activity
export const getSessionParticipants = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    // Get all users who have made requests in this session
    const requests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    // Get unique user IDs
    const uniqueUserIds = [...new Set(requests.map(r => r.userId))];

    // Get user information
    const participants = await Promise.all(
      uniqueUserIds.map(async (userId) => {
        const user = await ctx.db.get(userId);
        const userRequests = requests.filter(r => r.userId === userId);
        
        return user ? {
          id: user._id,
          name: user.name,
          email: user.email,
          requestCount: userRequests.length,
          lastActivity: Math.max(...userRequests.map(r => r.createdAt)),
          firstActivity: Math.min(...userRequests.map(r => r.createdAt)),
        } : null;
      })
    );

    // Filter out null users and sort by activity
    const validParticipants = participants
      .filter(p => p !== null)
      .sort((a, b) => b!.lastActivity - a!.lastActivity);

    return {
      participants: validParticipants,
      totalParticipants: validParticipants.length,
      totalRequests: requests.length,
      lastUpdated: Date.now(),
    };
  },
});

// Query: Get real-time session metrics
export const getSessionMetrics = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return null;
    }

    // Get all requests for metrics calculation
    const allRequests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    // Calculate time-based metrics
    const now = Date.now();
    const sessionStart = session.startTime;
    const sessionDuration = session.active 
      ? now - sessionStart
      : (session.endTime || now) - sessionStart;

    // Calculate request metrics by time periods
    const last15Minutes = now - (15 * 60 * 1000);
    const lastHour = now - (60 * 60 * 1000);

    const recentRequests15min = allRequests.filter(r => r.createdAt > last15Minutes);
    const recentRequestsHour = allRequests.filter(r => r.createdAt > lastHour);

    // Calculate status distribution
    const statusCounts = {
      pending: allRequests.filter(r => r.status === "pending").length,
      approved: allRequests.filter(r => r.status === "approved").length,
      rejected: allRequests.filter(r => r.status === "rejected").length,
      played: allRequests.filter(r => r.status === "played").length,
    };

    // Calculate engagement metrics
    const uniqueUsers = new Set(allRequests.map(r => r.userId)).size;
    const averageRequestsPerUser = uniqueUsers > 0 ? allRequests.length / uniqueUsers : 0;

    return {
      sessionId: args.sessionId,
      isActive: session.active,
      duration: {
        total: sessionDuration,
        hours: Math.floor(sessionDuration / (1000 * 60 * 60)),
        minutes: Math.floor((sessionDuration % (1000 * 60 * 60)) / (1000 * 60)),
      },
      requests: {
        total: allRequests.length,
        last15Minutes: recentRequests15min.length,
        lastHour: recentRequestsHour.length,
        statusDistribution: statusCounts,
      },
      engagement: {
        uniqueUsers,
        averageRequestsPerUser: Math.round(averageRequestsPerUser * 100) / 100,
        requestsPerHour: sessionDuration > 0 
          ? Math.round((allRequests.length / (sessionDuration / (1000 * 60 * 60))) * 100) / 100
          : 0,
      },
      activity: {
        lastRequestTime: allRequests.length > 0 
          ? Math.max(...allRequests.map(r => r.createdAt))
          : sessionStart,
        sessionLastUpdated: session.updatedAt,
      },
      timestamp: now,
    };
  },
});

// Mutation: Update session activity timestamp
export const updateSessionActivity = mutation({
  args: {
    sessionId: v.id("sessions"),
    activityType: v.union(
      v.literal("dj_action"),
      v.literal("request_update"),
      v.literal("status_change"),
      v.literal("general_activity")
    ),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Verify DJ ownership for DJ actions
    if (args.activityType === "dj_action") {
      const djProfile = await ctx.db.get(session.djId);
      if (!djProfile || djProfile.userId !== userId) {
        throw new Error("Only the session DJ can perform DJ actions");
      }
    }

    // Update session activity timestamp
    await ctx.db.patch(args.sessionId, {
      updatedAt: Date.now(),
    });

    return {
      success: true,
      activityType: args.activityType,
      timestamp: Date.now(),
    };
  },
});

// Query: Get active sessions for real-time discovery
export const getActiveSessions = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 10;

    const activeSessions = await ctx.db
      .query("sessions")
      .withIndex("by_active", (q) => q.eq("active", true))
      .order("desc")
      .take(limit);

    // Get enhanced data for each session
    const sessionsWithData = await Promise.all(
      activeSessions.map(async (session) => {
        const djProfile = await ctx.db.get(session.djId);
        
        // Get request count
        const requests = await ctx.db
          .query("songRequests")
          .withIndex("by_session", (q) => q.eq("sessionId", session._id))
          .collect();

        // Get unique participants
        const uniqueUsers = new Set(requests.map(r => r.userId)).size;

        return {
          id: session._id,
          title: session.title,
          description: session.description,
          startTime: session.startTime,
          isWeddingMode: session.isWeddingMode,
          dj: djProfile ? {
            id: djProfile._id,
            stageName: djProfile.stageName,
            profilePictureStorageId: djProfile.profilePictureStorageId,
          } : null,
          metrics: {
            totalRequests: requests.length,
            participants: uniqueUsers,
            duration: Date.now() - session.startTime,
            lastActivity: Math.max(
              session.updatedAt,
              ...requests.map(r => r.createdAt)
            ),
          },
        };
      })
    );

    return {
      sessions: sessionsWithData,
      total: sessionsWithData.length,
      lastUpdated: Date.now(),
    };
  },
});
