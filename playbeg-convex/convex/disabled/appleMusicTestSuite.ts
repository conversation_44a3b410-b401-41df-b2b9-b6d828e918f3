/**
 * Apple Music Integration Test Suite
 * 
 * This module provides comprehensive testing for Apple Music integration,
 * token management, API operations, and playlist/song management with
 * proper validation and error handling.
 */

import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Test Query: Validate Apple Music operations
export const testAppleMusicOperations = query({
  args: {},
  handler: async (ctx) => {
    const results = {
      timestamp: Date.now(),
      tests: [] as any[],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
      },
    };

    const userId = await getAuthUserId(ctx);
    
    // Test 1: Check authentication
    try {
      results.tests.push({
        name: "authentication_check",
        status: userId ? "passed" : "failed",
        result: userId ? `Authenticated as ${userId}` : "Not authenticated",
      });
      if (userId) results.summary.passed++;
      else results.summary.failed++;
    } catch (error) {
      results.tests.push({
        name: "authentication_check",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Test 2: Check Apple Music tokens table
    try {
      const tokens = await ctx.db.query("appleMusicTokens").take(5);
      results.tests.push({
        name: "apple_music_tokens_query",
        status: "passed",
        result: `Found ${tokens.length} Apple Music tokens`,
      });
      results.summary.passed++;
    } catch (error) {
      results.tests.push({
        name: "apple_music_tokens_query",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Test 3: Check songs table
    try {
      const songs = await ctx.db.query("songs").take(5);
      results.tests.push({
        name: "songs_query",
        status: "passed",
        result: `Found ${songs.length} songs`,
      });
      results.summary.passed++;
    } catch (error) {
      results.tests.push({
        name: "songs_query",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Test 4: Check playlists table
    try {
      const playlists = await ctx.db.query("playlists").take(5);
      results.tests.push({
        name: "playlists_query",
        status: "passed",
        result: `Found ${playlists.length} playlists`,
      });
      results.summary.passed++;
    } catch (error) {
      results.tests.push({
        name: "playlists_query",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    if (userId) {
      // Test 5: Check user's Apple Music token status
      try {
        const tokenStatus = await ctx.runQuery(api.appleMusicTokens.hasValidAppleMusicToken, {});
        results.tests.push({
          name: "user_token_status",
          status: "passed",
          result: tokenStatus,
        });
        results.summary.passed++;
      } catch (error) {
        results.tests.push({
          name: "user_token_status",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }

      // Test 6: Check Apple Music integration status
      try {
        const integrationStatus = await ctx.runQuery(api.appleMusicApi.getAppleMusicIntegrationStatus, {});
        results.tests.push({
          name: "integration_status",
          status: "passed",
          result: integrationStatus,
        });
        results.summary.passed++;
      } catch (error) {
        results.tests.push({
          name: "integration_status",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    results.summary.total = results.tests.length;
    return results;
  },
});

// Test Mutation: Create test Apple Music token
export const createTestAppleMusicToken = mutation({
  args: {
    durationHours: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const durationHours = args.durationHours || 24; // 24 hours default
    const now = Date.now();
    const expiresAt = now + (durationHours * 60 * 60 * 1000);

    const result = await ctx.runMutation(api.appleMusicTokens.storeAppleMusicToken, {
      appleMusicToken: `test_token_${now}`,
      expiresAt,
    });

    return {
      ...result,
      durationHours,
      expiresAt,
      expiresInHours: Math.floor((expiresAt - now) / (60 * 60 * 1000)),
    };
  },
});

// Test Mutation: Create test song metadata
export const createTestSongMetadata = mutation({
  args: {
    count: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const count = Math.min(args.count || 3, 10); // Limit to 10 test songs
    
    const testSongs = [
      {
        appleMusicId: "test_1001",
        title: "Bohemian Rhapsody",
        artistName: "Queen",
        albumName: "A Night at the Opera",
        genre: "Rock",
        duration: 355000,
        artworkUrl: "https://example.com/artwork/queen.jpg",
        releaseDate: "1975-10-31",
        isExplicit: false,
      },
      {
        appleMusicId: "test_1002",
        title: "Billie Jean",
        artistName: "Michael Jackson",
        albumName: "Thriller",
        genre: "Pop",
        duration: 294000,
        artworkUrl: "https://example.com/artwork/mj.jpg",
        releaseDate: "1983-01-02",
        isExplicit: false,
      },
      {
        appleMusicId: "test_1003",
        title: "Hotel California",
        artistName: "Eagles",
        albumName: "Hotel California",
        genre: "Rock",
        duration: 391000,
        artworkUrl: "https://example.com/artwork/eagles.jpg",
        releaseDate: "1976-12-08",
        isExplicit: false,
      },
    ];

    const createdSongs = [];
    for (let i = 0; i < count; i++) {
      const song = testSongs[i % testSongs.length];
      
      try {
        const result = await ctx.runMutation(api.songs.storeSongMetadata, {
          appleMusicId: `${song.appleMusicId}_${i}`,
          title: song.title,
          artistName: song.artistName,
          albumName: song.albumName,
          genre: song.genre,
          duration: song.duration,
          artworkUrl: song.artworkUrl,
          releaseDate: song.releaseDate,
          isExplicit: song.isExplicit,
        });
        
        createdSongs.push(result);
      } catch (error) {
        console.log(`Failed to create test song ${i}:`, error);
      }
    }

    return {
      createdSongs,
      count: createdSongs.length,
      message: `Created ${createdSongs.length} test songs`,
    };
  },
});

// Test Mutation: Create test playlist
export const createTestPlaylist = mutation({
  args: {
    playlistName: v.optional(v.string()),
    withAppleMusicId: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const playlistName = args.playlistName || `Test Playlist ${Date.now()}`;
    const appleMusicId = args.withAppleMusicId ? `test_pl_${Date.now()}` : undefined;

    const result = await ctx.runMutation(api.playlists.createPlaylist, {
      name: playlistName,
      appleMusicId,
      description: "Test playlist for development",
      isPublic: false,
    });

    return {
      ...result,
      playlistName,
      appleMusicId,
      withAppleMusicId: !!appleMusicId,
    };
  },
});

// Test Action: Comprehensive Apple Music workflow test
export const testAppleMusicWorkflow = action({
  args: {
    includeTokenTest: v.optional(v.boolean()),
    includeSongTest: v.optional(v.boolean()),
    includePlaylistTest: v.optional(v.boolean()),
    includeApiTest: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const results = {
      timestamp: Date.now(),
      workflow: "apple_music_integration",
      steps: [] as any[],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
      },
    };

    // Step 1: Validate operations
    try {
      const validation = await ctx.runQuery(api.appleMusicTestSuite.testAppleMusicOperations, {});
      results.steps.push({
        name: "validate_operations",
        status: validation.summary.failed === 0 ? "passed" : "failed",
        result: validation,
      });
      if (validation.summary.failed === 0) results.summary.passed++;
      else results.summary.failed++;
    } catch (error) {
      results.steps.push({
        name: "validate_operations",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Step 2: Test token management (if requested)
    if (args.includeTokenTest !== false) {
      try {
        const tokenResult = await ctx.runMutation(api.appleMusicTestSuite.createTestAppleMusicToken, {
          durationHours: 48,
        });
        
        results.steps.push({
          name: "test_token_management",
          status: "passed",
          result: tokenResult,
        });
        results.summary.passed++;
      } catch (error) {
        results.steps.push({
          name: "test_token_management",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    // Step 3: Test song metadata (if requested)
    if (args.includeSongTest !== false) {
      try {
        const songResult = await ctx.runMutation(api.appleMusicTestSuite.createTestSongMetadata, {
          count: 3,
        });
        
        results.steps.push({
          name: "test_song_metadata",
          status: "passed",
          result: songResult,
        });
        results.summary.passed++;
      } catch (error) {
        results.steps.push({
          name: "test_song_metadata",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    // Step 4: Test playlist management (if requested)
    if (args.includePlaylistTest !== false) {
      try {
        const playlistResult = await ctx.runMutation(api.appleMusicTestSuite.createTestPlaylist, {
          playlistName: "Workflow Test Playlist",
          withAppleMusicId: true,
        });
        
        results.steps.push({
          name: "test_playlist_management",
          status: "passed",
          result: playlistResult,
        });
        results.summary.passed++;
      } catch (error) {
        results.steps.push({
          name: "test_playlist_management",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    // Step 5: Test API integration (if requested and token available)
    if (args.includeApiTest !== false) {
      try {
        // Check if we have a valid token first
        const tokenStatus = await ctx.runQuery(api.appleMusicTokens.hasValidAppleMusicToken, {});
        
        if (tokenStatus.isValid) {
          const searchResult = await ctx.runAction(api.appleMusicApi.searchSongs, {
            query: "test",
            limit: 5,
          });
          
          results.steps.push({
            name: "test_api_integration",
            status: "passed",
            result: {
              searchQuery: "test",
              resultsCount: searchResult.results.length,
              hasResults: searchResult.results.length > 0,
            },
          });
          results.summary.passed++;
        } else {
          results.steps.push({
            name: "test_api_integration",
            status: "skipped",
            result: "No valid Apple Music token available",
          });
        }
      } catch (error) {
        results.steps.push({
          name: "test_api_integration",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    results.summary.total = results.steps.length;
    return results;
  },
});

// Test Query: Apple Music integration health check
export const appleMusicHealthCheck = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    
    const health = {
      timestamp: Date.now(),
      authentication: {
        isAuthenticated: !!userId,
        userId,
      },
      database: {
        tokensTable: false,
        songsTable: false,
        playlistsTable: false,
      },
      integration: {
        hasValidToken: false,
        canUseApi: false,
        tokenExpired: true,
      },
      statistics: {
        totalTokens: 0,
        totalSongs: 0,
        totalPlaylists: 0,
      },
    };

    // Test database tables
    try {
      const tokens = await ctx.db.query("appleMusicTokens").take(1);
      health.database.tokensTable = true;
      health.statistics.totalTokens = tokens.length;
    } catch (error) {
      console.log("Tokens table error:", error);
    }

    try {
      const songs = await ctx.db.query("songs").take(1);
      health.database.songsTable = true;
      health.statistics.totalSongs = songs.length;
    } catch (error) {
      console.log("Songs table error:", error);
    }

    try {
      const playlists = await ctx.db.query("playlists").take(1);
      health.database.playlistsTable = true;
      health.statistics.totalPlaylists = playlists.length;
    } catch (error) {
      console.log("Playlists table error:", error);
    }

    // Test integration status if authenticated
    if (userId) {
      try {
        const tokenStatus = await ctx.runQuery(api.appleMusicTokens.hasValidAppleMusicToken, {});
        health.integration.hasValidToken = tokenStatus.hasToken;
        health.integration.canUseApi = tokenStatus.isValid;
        health.integration.tokenExpired = tokenStatus.isExpired;
      } catch (error) {
        console.log("Integration status error:", error);
      }
    }

    return health;
  },
});
