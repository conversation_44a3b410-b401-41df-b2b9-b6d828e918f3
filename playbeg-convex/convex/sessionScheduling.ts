/**
 * Session Scheduling System
 * 
 * Enables DJs to schedule future sessions with specific start times,
 * duration limits, and automated session lifecycle management.
 */

import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Schedule a new session
export const scheduleSession = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    scheduledStartTime: v.number(),
    scheduledDuration: v.number(), // in minutes
    templateId: v.optional(v.id("sessionTemplates")),
    sessionConfig: v.optional(v.any()),
    autoStart: v.optional(v.boolean()),
    autoEnd: v.optional(v.boolean()),
    reminderSettings: v.optional(v.object({
      sendReminders: v.boolean(),
      reminderTimes: v.array(v.number()), // minutes before start
      notifyFollowers: v.optional(v.boolean()),
    })),
    recurrence: v.optional(v.object({
      type: v.union(
        v.literal("none"),
        v.literal("daily"),
        v.literal("weekly"),
        v.literal("monthly"),
      ),
      interval: v.optional(v.number()), // every N days/weeks/months
      endDate: v.optional(v.number()),
      maxOccurrences: v.optional(v.number()),
    })),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required to schedule sessions");
    }

    // Verify user has a DJ profile
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      throw new Error("DJ profile required to schedule sessions");
    }

    // Validate scheduling time
    const now = Date.now();
    if (args.scheduledStartTime <= now) {
      throw new Error("Scheduled start time must be in the future");
    }

    // Check for scheduling conflicts
    const conflictingSessions = await ctx.db
      .query("scheduledSessions")
      .withIndex("by_user_time", (q) => 
        q.eq("userId", userId).gte("scheduledStartTime", now)
      )
      .filter((q) => 
        q.and(
          q.eq(q.field("status"), "scheduled"),
          q.lt(q.field("scheduledStartTime"), args.scheduledStartTime + (args.scheduledDuration * 60 * 1000)),
          q.gt(q.field("scheduledEndTime"), args.scheduledStartTime)
        )
      )
      .collect();

    if (conflictingSessions.length > 0) {
      throw new Error("Scheduling conflict detected with existing session");
    }

    const scheduledEndTime = args.scheduledStartTime + (args.scheduledDuration * 60 * 1000);

    // Create scheduled session
    const scheduledSessionId = await ctx.db.insert("scheduledSessions", {
      userId,
      djProfileId: djProfile._id,
      name: args.name,
      description: args.description,
      scheduledStartTime: args.scheduledStartTime,
      scheduledEndTime,
      scheduledDuration: args.scheduledDuration,
      templateId: args.templateId,
      sessionConfig: args.sessionConfig || {},
      autoStart: args.autoStart || false,
      autoEnd: args.autoEnd || true,
      reminderSettings: args.reminderSettings,
      recurrence: args.recurrence || { type: "none" },
      status: "scheduled",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Schedule reminders if enabled
    if (args.reminderSettings?.sendReminders && args.reminderSettings.reminderTimes) {
      for (const reminderMinutes of args.reminderSettings.reminderTimes) {
        const reminderTime = args.scheduledStartTime - (reminderMinutes * 60 * 1000);
        if (reminderTime > now) {
          await ctx.db.insert("scheduledReminders", {
            scheduledSessionId,
            userId,
            reminderTime,
            reminderType: "session_starting",
            minutesBefore: reminderMinutes,
            sent: false,
            createdAt: Date.now(),
          });
        }
      }
    }

    return scheduledSessionId;
  },
});

// Get user's scheduled sessions
export const getUserScheduledSessions = query({
  args: {
    status: v.optional(v.union(
      v.literal("scheduled"),
      v.literal("active"),
      v.literal("completed"),
      v.literal("cancelled"),
    )),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    let query = ctx.db
      .query("scheduledSessions")
      .withIndex("by_user", (q) => q.eq("userId", userId));

    if (args.status) {
      query = query.filter((q) => q.eq(q.field("status"), args.status));
    }

    if (args.startDate) {
      query = query.filter((q) => q.gte(q.field("scheduledStartTime"), args.startDate!));
    }

    if (args.endDate) {
      query = query.filter((q) => q.lte(q.field("scheduledStartTime"), args.endDate!));
    }

    const sessions = await query
      .order("asc") // Chronological order
      .take(args.limit || 50);

    // Get template details for sessions that use templates
    const sessionsWithTemplates = await Promise.all(
      sessions.map(async (session) => {
        if (session.templateId) {
          const template = await ctx.db.get(session.templateId);
          return {
            ...session,
            template: template ? {
              name: template.name,
              description: template.description,
            } : null,
          };
        }
        return { ...session, template: null };
      })
    );

    return sessionsWithTemplates;
  },
});

// Update a scheduled session
export const updateScheduledSession = mutation({
  args: {
    scheduledSessionId: v.id("scheduledSessions"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    scheduledStartTime: v.optional(v.number()),
    scheduledDuration: v.optional(v.number()),
    sessionConfig: v.optional(v.any()),
    autoStart: v.optional(v.boolean()),
    autoEnd: v.optional(v.boolean()),
    reminderSettings: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const scheduledSession = await ctx.db.get(args.scheduledSessionId);
    if (!scheduledSession) {
      throw new Error("Scheduled session not found");
    }

    if (scheduledSession.userId !== userId) {
      throw new Error("Access denied: You can only edit your own scheduled sessions");
    }

    if (scheduledSession.status !== "scheduled") {
      throw new Error("Cannot edit sessions that are not in scheduled status");
    }

    const updates: any = {
      updatedAt: Date.now(),
    };

    if (args.name !== undefined) updates.name = args.name;
    if (args.description !== undefined) updates.description = args.description;
    if (args.sessionConfig !== undefined) updates.sessionConfig = args.sessionConfig;
    if (args.autoStart !== undefined) updates.autoStart = args.autoStart;
    if (args.autoEnd !== undefined) updates.autoEnd = args.autoEnd;
    if (args.reminderSettings !== undefined) updates.reminderSettings = args.reminderSettings;

    if (args.scheduledStartTime !== undefined) {
      const now = Date.now();
      if (args.scheduledStartTime <= now) {
        throw new Error("Scheduled start time must be in the future");
      }
      updates.scheduledStartTime = args.scheduledStartTime;
    }

    if (args.scheduledDuration !== undefined) {
      updates.scheduledDuration = args.scheduledDuration;
      if (args.scheduledStartTime !== undefined) {
        updates.scheduledEndTime = args.scheduledStartTime + (args.scheduledDuration * 60 * 1000);
      } else {
        updates.scheduledEndTime = scheduledSession.scheduledStartTime + (args.scheduledDuration * 60 * 1000);
      }
    }

    await ctx.db.patch(args.scheduledSessionId, updates);

    return { success: true };
  },
});

// Cancel a scheduled session
export const cancelScheduledSession = mutation({
  args: {
    scheduledSessionId: v.id("scheduledSessions"),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const scheduledSession = await ctx.db.get(args.scheduledSessionId);
    if (!scheduledSession) {
      throw new Error("Scheduled session not found");
    }

    if (scheduledSession.userId !== userId) {
      throw new Error("Access denied: You can only cancel your own scheduled sessions");
    }

    if (scheduledSession.status !== "scheduled") {
      throw new Error("Can only cancel sessions in scheduled status");
    }

    await ctx.db.patch(args.scheduledSessionId, {
      status: "cancelled",
      cancellationReason: args.reason,
      cancelledAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Cancel any pending reminders
    const pendingReminders = await ctx.db
      .query("scheduledReminders")
      .withIndex("by_scheduled_session", (q) => q.eq("scheduledSessionId", args.scheduledSessionId))
      .filter((q) => q.eq(q.field("sent"), false))
      .collect();

    for (const reminder of pendingReminders) {
      await ctx.db.patch(reminder._id, { sent: true, cancelledAt: Date.now() });
    }

    return { success: true };
  },
});

// Start a scheduled session manually
export const startScheduledSession = mutation({
  args: {
    scheduledSessionId: v.id("scheduledSessions"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const scheduledSession = await ctx.db.get(args.scheduledSessionId);
    if (!scheduledSession) {
      throw new Error("Scheduled session not found");
    }

    if (scheduledSession.userId !== userId) {
      throw new Error("Access denied: You can only start your own scheduled sessions");
    }

    if (scheduledSession.status !== "scheduled") {
      throw new Error("Session is not in scheduled status");
    }

    // Create the actual session
    const sessionId = await ctx.db.insert("sessions", {
      userId,
      name: scheduledSession.name,
      description: scheduledSession.description,
      scheduledSessionId: args.scheduledSessionId,
      ...scheduledSession.sessionConfig,
      active: true,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Update scheduled session status
    await ctx.db.patch(args.scheduledSessionId, {
      status: "active",
      actualStartTime: Date.now(),
      sessionId,
      updatedAt: Date.now(),
    });

    return sessionId;
  },
});

// Get upcoming sessions (for dashboard)
export const getUpcomingSessions = query({
  args: {
    limit: v.optional(v.number()),
    hoursAhead: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    const now = Date.now();
    const maxTime = now + ((args.hoursAhead || 24) * 60 * 60 * 1000);

    const upcomingSessions = await ctx.db
      .query("scheduledSessions")
      .withIndex("by_user_time", (q) => 
        q.eq("userId", userId).gte("scheduledStartTime", now)
      )
      .filter((q) => 
        q.and(
          q.eq(q.field("status"), "scheduled"),
          q.lte(q.field("scheduledStartTime"), maxTime)
        )
      )
      .order("asc")
      .take(args.limit || 10);

    return upcomingSessions.map(session => ({
      ...session,
      timeUntilStart: session.scheduledStartTime - now,
      formattedStartTime: new Date(session.scheduledStartTime).toLocaleString(),
    }));
  },
});
