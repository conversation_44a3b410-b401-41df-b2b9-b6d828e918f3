/**
 * PlayBeg Convex Validation Utilities
 * 
 * This module contains validation logic that was previously handled by
 * PostgreSQL constraints in the Supabase implementation. These validations
 * ensure data integrity and business rule compliance in the Convex environment.
 */

// Wedding Template Validation
export const VALID_WEDDING_TEMPLATES = [
  "classic-elegance",
  "rustic-romance",
  "modern-minimalist",
  "garden-party",
  "vintage-glam",
  "beach-destination",
] as const;

export type WeddingTemplate = typeof VALID_WEDDING_TEMPLATES[number];

export function isValidWeddingTemplate(template: string): template is WeddingTemplate {
  return VALID_WEDDING_TEMPLATES.includes(template as WeddingTemplate);
}

// Wedding Border Style Validation
export const VALID_WEDDING_BORDER_STYLES = [
  "elegant-frame",
  "floral-border",
  "geometric-pattern",
  "none",
] as const;

export type WeddingBorderStyle = typeof VALID_WEDDING_BORDER_STYLES[number];

export function isValidWeddingBorderStyle(style: string): style is WeddingBorderStyle {
  return VALID_WEDDING_BORDER_STYLES.includes(style as WeddingBorderStyle);
}

// Wedding Background Pattern Validation
export const VALID_WEDDING_BACKGROUND_PATTERNS = [
  "lace",
  "watercolor",
  "subtle-texture",
  "none",
] as const;

export type WeddingBackgroundPattern = typeof VALID_WEDDING_BACKGROUND_PATTERNS[number];

export function isValidWeddingBackgroundPattern(pattern: string): pattern is WeddingBackgroundPattern {
  return VALID_WEDDING_BACKGROUND_PATTERNS.includes(pattern as WeddingBackgroundPattern);
}

// Request Status Validation
export const VALID_REQUEST_STATUSES = [
  "pending",
  "approved",
  "auto-approved",
  "declined",
  "played",
] as const;

export type RequestStatus = typeof VALID_REQUEST_STATUSES[number];

export function isValidRequestStatus(status: string): status is RequestStatus {
  return VALID_REQUEST_STATUSES.includes(status as RequestStatus);
}

// Subscription Status Validation
export const VALID_SUBSCRIPTION_STATUSES = [
  "active",
  "canceled",
  "past_due",
  "unpaid",
  "incomplete",
  "incomplete_expired",
  "trialing",
] as const;

export type SubscriptionStatus = typeof VALID_SUBSCRIPTION_STATUSES[number];

export function isValidSubscriptionStatus(status: string): status is SubscriptionStatus {
  return VALID_SUBSCRIPTION_STATUSES.includes(status as SubscriptionStatus);
}

// Blog Post Status Validation
export const VALID_BLOG_POST_STATUSES = [
  "published",
  "draft",
  "archived",
] as const;

export type BlogPostStatus = typeof VALID_BLOG_POST_STATUSES[number];

export function isValidBlogPostStatus(status: string): status is BlogPostStatus {
  return VALID_BLOG_POST_STATUSES.includes(status as BlogPostStatus);
}

// Currency Validation
export const VALID_CURRENCIES = [
  "usd",
  "eur",
  "gbp",
  "cad",
  "aud",
] as const;

export type Currency = typeof VALID_CURRENCIES[number];

export function isValidCurrency(currency: string): currency is Currency {
  return VALID_CURRENCIES.includes(currency as Currency);
}

// Email Validation
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// URL Validation
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// Color Validation (Hex colors)
export function isValidHexColor(color: string): boolean {
  const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  return hexColorRegex.test(color);
}

// Slug Validation (URL-safe strings)
export function isValidSlug(slug: string): boolean {
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugRegex.test(slug);
}

// Session Validation
export interface SessionValidationOptions {
  maxDurationMinutes?: number;
  maxRequestsPerTimeframe?: number;
  maxRequestsPerUser?: number;
  maxTimeframeMinutes?: number;
}

export function validateSessionConfiguration(
  config: {
    durationMinutes?: number;
    maxRequestsPerTimeframe?: number;
    maxRequestsPerUser?: number;
    timeframeMinutes?: number;
    weddingTemplate?: string;
    weddingBorderStyle?: string;
    weddingBackgroundPattern?: string;
    weddingPrimaryColor?: string;
    weddingSecondaryColor?: string;
  },
  options: SessionValidationOptions = {}
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Duration validation
  if (config.durationMinutes !== undefined) {
    if (config.durationMinutes <= 0) {
      errors.push("Duration must be positive");
    }
    if (options.maxDurationMinutes && config.durationMinutes > options.maxDurationMinutes) {
      errors.push(`Duration cannot exceed ${options.maxDurationMinutes} minutes`);
    }
  }

  // Request limits validation
  if (config.maxRequestsPerTimeframe !== undefined && config.maxRequestsPerTimeframe <= 0) {
    errors.push("Max requests per timeframe must be positive");
  }

  if (config.maxRequestsPerUser !== undefined && config.maxRequestsPerUser <= 0) {
    errors.push("Max requests per user must be positive");
  }

  if (config.timeframeMinutes !== undefined && config.timeframeMinutes <= 0) {
    errors.push("Timeframe minutes must be positive");
  }

  // Wedding configuration validation
  if (config.weddingTemplate && !isValidWeddingTemplate(config.weddingTemplate)) {
    errors.push(`Invalid wedding template: ${config.weddingTemplate}`);
  }

  if (config.weddingBorderStyle && !isValidWeddingBorderStyle(config.weddingBorderStyle)) {
    errors.push(`Invalid wedding border style: ${config.weddingBorderStyle}`);
  }

  if (config.weddingBackgroundPattern && !isValidWeddingBackgroundPattern(config.weddingBackgroundPattern)) {
    errors.push(`Invalid wedding background pattern: ${config.weddingBackgroundPattern}`);
  }

  if (config.weddingPrimaryColor && !isValidHexColor(config.weddingPrimaryColor)) {
    errors.push(`Invalid wedding primary color: ${config.weddingPrimaryColor}`);
  }

  if (config.weddingSecondaryColor && !isValidHexColor(config.weddingSecondaryColor)) {
    errors.push(`Invalid wedding secondary color: ${config.weddingSecondaryColor}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Subscription Plan Validation
export function validateSubscriptionPlan(plan: {
  name: string;
  priceAmount: number;
  priceCurrency: string;
  durationHours: number;
  stripePriceId: string;
}): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!plan.name || plan.name.trim().length === 0) {
    errors.push("Plan name is required");
  }

  if (plan.priceAmount < 0) {
    errors.push("Price amount cannot be negative");
  }

  if (!isValidCurrency(plan.priceCurrency)) {
    errors.push(`Invalid currency: ${plan.priceCurrency}`);
  }

  if (plan.durationHours <= 0) {
    errors.push("Duration hours must be positive");
  }

  if (!plan.stripePriceId || plan.stripePriceId.trim().length === 0) {
    errors.push("Stripe price ID is required");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Blog Post Validation
export function validateBlogPost(post: {
  title: string;
  slug: string;
  author: string;
  category: string;
  excerpt: string;
  content: string;
  status: string;
  tags: string[];
}): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!post.title || post.title.trim().length === 0) {
    errors.push("Title is required");
  }

  if (!post.slug || !isValidSlug(post.slug)) {
    errors.push("Valid slug is required");
  }

  if (!post.author || post.author.trim().length === 0) {
    errors.push("Author is required");
  }

  if (!post.category || post.category.trim().length === 0) {
    errors.push("Category is required");
  }

  if (!post.excerpt || post.excerpt.trim().length === 0) {
    errors.push("Excerpt is required");
  }

  if (!post.content || post.content.trim().length === 0) {
    errors.push("Content is required");
  }

  if (!isValidBlogPostStatus(post.status)) {
    errors.push(`Invalid blog post status: ${post.status}`);
  }

  if (!Array.isArray(post.tags)) {
    errors.push("Tags must be an array");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Song Request Validation
export function validateSongRequest(request: {
  songTitle: string;
  artistName: string;
  requesterName: string;
  status: string;
}): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!request.songTitle || request.songTitle.trim().length === 0) {
    errors.push("Song title is required");
  }

  if (!request.artistName || request.artistName.trim().length === 0) {
    errors.push("Artist name is required");
  }

  if (!request.requesterName || request.requesterName.trim().length === 0) {
    errors.push("Requester name is required");
  }

  if (!isValidRequestStatus(request.status)) {
    errors.push(`Invalid request status: ${request.status}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Generic validation error class
export class ValidationError extends Error {
  constructor(public errors: string[]) {
    super(`Validation failed: ${errors.join(", ")}`);
    this.name = "ValidationError";
  }
}

// Helper function to throw validation errors
export function throwIfInvalid(validation: { isValid: boolean; errors: string[] }): void {
  if (!validation.isValid) {
    throw new ValidationError(validation.errors);
  }
}
