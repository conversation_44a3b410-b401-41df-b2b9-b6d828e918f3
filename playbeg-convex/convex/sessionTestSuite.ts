/**
 * Session and Song Request Test Suite
 * 
 * This module provides comprehensive testing for session and song request
 * CRUD operations, real-time subscriptions, and workflow validation.
 */

import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Test Query: Validate session operations
export const testSessionOperations = query({
  args: {},
  handler: async (ctx) => {
    const results = {
      timestamp: Date.now(),
      tests: [] as any[],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
      },
    };

    const userId = await getAuthUserId(ctx);
    
    // Test 1: Check authentication
    try {
      results.tests.push({
        name: "authentication_check",
        status: userId ? "passed" : "failed",
        result: userId ? `Authenticated as ${userId}` : "Not authenticated",
      });
      if (userId) results.summary.passed++;
      else results.summary.failed++;
    } catch (error) {
      results.tests.push({
        name: "authentication_check",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    if (!userId) {
      results.summary.total = 1;
      return results;
    }

    // Test 2: Check DJ profile exists
    try {
      const djProfile = await ctx.db
        .query("djProfiles")
        .withIndex("by_user", (q) => q.eq("userId", userId))
        .first();

      results.tests.push({
        name: "dj_profile_check",
        status: djProfile ? "passed" : "failed",
        result: djProfile ? "DJ profile found" : "No DJ profile",
        profileId: djProfile?._id,
      });
      if (djProfile) results.summary.passed++;
      else results.summary.failed++;
    } catch (error) {
      results.tests.push({
        name: "dj_profile_check",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Test 3: Check existing sessions
    try {
      const sessions = await ctx.db
        .query("sessions")
        .take(5);

      results.tests.push({
        name: "sessions_query",
        status: "passed",
        result: `Found ${sessions.length} sessions`,
      });
      results.summary.passed++;
    } catch (error) {
      results.tests.push({
        name: "sessions_query",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Test 4: Check song requests
    try {
      const requests = await ctx.db
        .query("songRequests")
        .take(5);

      results.tests.push({
        name: "song_requests_query",
        status: "passed",
        result: `Found ${requests.length} song requests`,
      });
      results.summary.passed++;
    } catch (error) {
      results.tests.push({
        name: "song_requests_query",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    results.summary.total = results.tests.length;
    return results;
  },
});

// Test Mutation: Create test session
export const createTestSession = mutation({
  args: {
    sessionName: v.optional(v.string()),
    weddingMode: v.optional(v.boolean()),
    autoApproval: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      throw new Error("DJ profile required to create test session");
    }

    const sessionName = args.sessionName || `Test Session ${Date.now()}`;
    const now = Date.now();

    const sessionData = {
      djId: djProfile._id,
      name: sessionName,
      active: false,
      acceptRequests: true,
      autoApproval: args.autoApproval ?? false,
      weddingModeEnabled: args.weddingMode ?? false,
      createdAt: now,
      updatedAt: now,
    };

    // Add wedding mode configuration if enabled
    if (args.weddingMode) {
      Object.assign(sessionData, {
        weddingCoupleNames: ["Test Bride", "Test Groom"],
        weddingDate: "2024-06-15",
        weddingHashtag: "#TestWedding2024",
        weddingTemplate: "classic-elegance",
        weddingPrimaryColor: "#D4AF37",
        weddingSecondaryColor: "#F5F5DC",
        weddingCustomMessage: "Welcome to our test wedding!",
        weddingShowIcons: true,
        weddingBorderStyle: "elegant-frame",
        weddingBackgroundPattern: "none",
      });
    }

    const sessionId = await ctx.db.insert("sessions", sessionData);

    return {
      sessionId,
      sessionName,
      weddingMode: args.weddingMode ?? false,
      autoApproval: args.autoApproval ?? false,
      message: "Test session created successfully",
    };
  },
});

// Test Mutation: Create test song requests
export const createTestSongRequests = mutation({
  args: {
    sessionId: v.id("sessions"),
    count: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    const count = Math.min(args.count ?? 3, 10); // Limit to 10 test requests
    const testSongs = [
      { title: "Bohemian Rhapsody", artist: "Queen", requester: "Test User 1", genre: "Rock" },
      { title: "Billie Jean", artist: "Michael Jackson", requester: "Test User 2", genre: "Pop" },
      { title: "Hotel California", artist: "Eagles", requester: "Test User 3", genre: "Rock" },
      { title: "Dancing Queen", artist: "ABBA", requester: "Test User 4", genre: "Pop" },
      { title: "Sweet Child O' Mine", artist: "Guns N' Roses", requester: "Test User 5", genre: "Rock" },
      { title: "I Will Always Love You", artist: "Whitney Houston", requester: "Test User 6", genre: "R&B" },
      { title: "Imagine", artist: "John Lennon", requester: "Test User 7", genre: "Pop" },
      { title: "Stairway to Heaven", artist: "Led Zeppelin", requester: "Test User 8", genre: "Rock" },
      { title: "Like a Prayer", artist: "Madonna", requester: "Test User 9", genre: "Pop" },
      { title: "Smells Like Teen Spirit", artist: "Nirvana", requester: "Test User 10", genre: "Grunge" },
    ];

    const createdRequests = [];
    const now = Date.now();

    for (let i = 0; i < count; i++) {
      const song = testSongs[i % testSongs.length];
      
      let status: "pending" | "auto-approved" = "pending";
      if (session.autoApproval) {
        status = "auto-approved";
      }

      const requestId = await ctx.db.insert("songRequests", {
        sessionId: args.sessionId,
        songTitle: song.title,
        artistName: song.artist,
        requesterName: song.requester,
        genre: song.genre,
        status,
        addedToPlaylist: false,
        createdAt: now + i, // Slight time offset for ordering
      });

      createdRequests.push({
        requestId,
        songTitle: song.title,
        artistName: song.artist,
        status,
      });
    }

    return {
      sessionId: args.sessionId,
      createdRequests,
      count: createdRequests.length,
      message: `Created ${createdRequests.length} test song requests`,
    };
  },
});

// Test Action: Comprehensive session workflow test
export const testSessionWorkflow = action({
  args: {
    includeWeddingMode: v.optional(v.boolean()),
    includeRealtime: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const results = {
      timestamp: Date.now(),
      workflow: "session_and_requests",
      steps: [] as any[],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
      },
    };

    // Step 1: Validate operations
    try {
      const validation = await ctx.runQuery(api.sessionTestSuite.testSessionOperations, {});
      results.steps.push({
        name: "validate_operations",
        status: validation.summary.failed === 0 ? "passed" : "failed",
        result: validation,
      });
      if (validation.summary.failed === 0) results.summary.passed++;
      else results.summary.failed++;
    } catch (error) {
      results.steps.push({
        name: "validate_operations",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Step 2: Create test session
    try {
      const sessionResult = await ctx.runMutation(api.sessionTestSuite.createTestSession, {
        sessionName: "Workflow Test Session",
        weddingMode: args.includeWeddingMode ?? false,
        autoApproval: false,
      });
      
      results.steps.push({
        name: "create_test_session",
        status: "passed",
        result: sessionResult,
      });
      results.summary.passed++;

      const sessionId = sessionResult.sessionId;

      // Step 3: Create test song requests
      try {
        const requestsResult = await ctx.runMutation(api.sessionTestSuite.createTestSongRequests, {
          sessionId,
          count: 5,
        });
        
        results.steps.push({
          name: "create_test_requests",
          status: "passed",
          result: requestsResult,
        });
        results.summary.passed++;

        // Step 4: Test session activation
        try {
          await ctx.runMutation(api.sessions.toggleSessionActive, {
            sessionId,
            active: true,
          });
          
          results.steps.push({
            name: "activate_session",
            status: "passed",
            result: "Session activated successfully",
          });
          results.summary.passed++;

          // Step 5: Test real-time subscriptions (if requested)
          if (args.includeRealtime) {
            try {
              const realtimeData = await ctx.runQuery(api.realtime.subscribeToDjDashboard, {
                sessionId,
              });
              
              results.steps.push({
                name: "test_realtime_subscription",
                status: "passed",
                result: realtimeData,
              });
              results.summary.passed++;
            } catch (error) {
              results.steps.push({
                name: "test_realtime_subscription",
                status: "failed",
                error: String(error),
              });
              results.summary.failed++;
            }
          }

          // Step 6: Test request status updates
          try {
            const firstRequest = requestsResult.createdRequests[0];
            await ctx.runMutation(api.songRequests.updateRequestStatus, {
              requestId: firstRequest.requestId,
              status: "approved",
            });
            
            results.steps.push({
              name: "update_request_status",
              status: "passed",
              result: "Request status updated successfully",
            });
            results.summary.passed++;
          } catch (error) {
            results.steps.push({
              name: "update_request_status",
              status: "failed",
              error: String(error),
            });
            results.summary.failed++;
          }

          // Step 7: Clean up - deactivate session
          try {
            await ctx.runMutation(api.sessions.toggleSessionActive, {
              sessionId,
              active: false,
            });
            
            results.steps.push({
              name: "deactivate_session",
              status: "passed",
              result: "Session deactivated successfully",
            });
            results.summary.passed++;
          } catch (error) {
            results.steps.push({
              name: "deactivate_session",
              status: "failed",
              error: String(error),
            });
            results.summary.failed++;
          }

        } catch (error) {
          results.steps.push({
            name: "activate_session",
            status: "failed",
            error: String(error),
          });
          results.summary.failed++;
        }

      } catch (error) {
        results.steps.push({
          name: "create_test_requests",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }

    } catch (error) {
      results.steps.push({
        name: "create_test_session",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    results.summary.total = results.steps.length;
    return results;
  },
});
