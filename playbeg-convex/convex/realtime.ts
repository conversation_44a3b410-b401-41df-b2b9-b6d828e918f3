/**
 * Real-time Subscription Patterns
 * 
 * This module provides real-time subscription patterns for live session
 * and song request updates, enabling the real-time DJ experience.
 */

import { v } from "convex/values";
import { query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Real-time subscription: Session status updates
export const subscribeToSessionStatus = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return null;
    }

    // Return session status information for real-time updates
    return {
      sessionId: args.sessionId,
      active: session.active,
      acceptRequests: session.acceptRequests,
      name: session.name,
      updatedAt: session.updatedAt,
      weddingModeEnabled: session.weddingModeEnabled,
    };
  },
});

// Real-time subscription: Live song requests for public view
export const subscribeToLiveRequests = query({
  args: {
    sessionId: v.id("sessions"),
    includeDeclined: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session || !session.active) {
      return [];
    }

    // Get recent requests for live display
    const statuses = args.includeDeclined 
      ? ["pending", "approved", "auto-approved", "declined", "played"]
      : ["pending", "approved", "auto-approved", "played"];

    const requests = await ctx.db
      .query("songRequests")
      .withIndex("by_session_created", (q) => q.eq("sessionId", args.sessionId))
      .order("desc")
      .take(20);

    return requests
      .filter(request => statuses.includes(request.status))
      .map(request => ({
        id: request._id,
        songTitle: request.songTitle,
        artistName: request.artistName,
        requesterName: request.requesterName,
        status: request.status,
        createdAt: request.createdAt,
        albumArtwork: request.albumArtwork,
        genre: request.genre,
      }));
  },
});

// Real-time subscription: DJ dashboard updates
export const subscribeToDjDashboard = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Verify ownership
    const djProfile = await ctx.db.get(session.djId);
    if (!djProfile || djProfile.userId !== userId) {
      throw new Error("Access denied: You can only view your own session dashboard");
    }

    // Get pending requests count
    const pendingRequests = await ctx.db
      .query("songRequests")
      .withIndex("by_session_status", (q) => 
        q.eq("sessionId", args.sessionId).eq("status", "pending")
      )
      .collect();

    // Get recent activity (last 10 minutes)
    const tenMinutesAgo = Date.now() - (10 * 60 * 1000);
    const recentRequests = await ctx.db
      .query("songRequests")
      .withIndex("by_session_created", (q) => q.eq("sessionId", args.sessionId))
      .filter((q) => q.gte(q.field("createdAt"), tenMinutesAgo))
      .collect();

    // Get total stats
    const allRequests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    const stats = {
      pending: pendingRequests.length,
      recentActivity: recentRequests.length,
      total: allRequests.length,
      approved: allRequests.filter(r => r.status === "approved" || r.status === "auto-approved").length,
      played: allRequests.filter(r => r.status === "played").length,
      declined: allRequests.filter(r => r.status === "declined").length,
    };

    return {
      sessionId: args.sessionId,
      sessionName: session.name,
      sessionActive: session.active,
      acceptRequests: session.acceptRequests,
      autoApproval: session.autoApproval,
      stats,
      lastUpdated: Date.now(),
    };
  },
});

// Real-time subscription: Session request queue for DJ
export const subscribeToRequestQueue = query({
  args: {
    sessionId: v.id("sessions"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Verify ownership
    const djProfile = await ctx.db.get(session.djId);
    if (!djProfile || djProfile.userId !== userId) {
      throw new Error("Access denied: You can only view your own request queue");
    }

    const limit = args.limit ?? 50;

    // Get pending requests (oldest first for FIFO)
    const pendingRequests = await ctx.db
      .query("songRequests")
      .withIndex("by_session_status", (q) => 
        q.eq("sessionId", args.sessionId).eq("status", "pending")
      )
      .order("asc")
      .take(limit);

    // Get recently approved/played requests for context
    const recentProcessed = await ctx.db
      .query("songRequests")
      .withIndex("by_session_created", (q) => q.eq("sessionId", args.sessionId))
      .filter((q) => 
        q.or(
          q.eq(q.field("status"), "approved"),
          q.eq(q.field("status"), "auto-approved"),
          q.eq(q.field("status"), "played")
        )
      )
      .order("desc")
      .take(10);

    return {
      pending: pendingRequests,
      recentProcessed,
      queueLength: pendingRequests.length,
      lastUpdated: Date.now(),
    };
  },
});

// Real-time subscription: Wedding mode display
export const subscribeToWeddingDisplay = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session || !session.active || !session.weddingModeEnabled) {
      return null;
    }

    // Get recent approved/played requests for wedding display
    const recentRequests = await ctx.db
      .query("songRequests")
      .withIndex("by_session_created", (q) => q.eq("sessionId", args.sessionId))
      .filter((q) => 
        q.or(
          q.eq(q.field("status"), "approved"),
          q.eq(q.field("status"), "auto-approved"),
          q.eq(q.field("status"), "played")
        )
      )
      .order("desc")
      .take(15);

    return {
      sessionId: args.sessionId,
      weddingConfig: {
        coupleNames: session.weddingCoupleNames,
        date: session.weddingDate,
        hashtag: session.weddingHashtag,
        template: session.weddingTemplate,
        primaryColor: session.weddingPrimaryColor,
        secondaryColor: session.weddingSecondaryColor,
        customMessage: session.weddingCustomMessage,
        showIcons: session.weddingShowIcons,
        borderStyle: session.weddingBorderStyle,
        backgroundPattern: session.weddingBackgroundPattern,
      },
      sponsorConfig: {
        header: session.sponsorHeader,
        message: session.sponsorMessage,
        url: session.sponsorUrl,
      },
      recentRequests: recentRequests.map(request => ({
        songTitle: request.songTitle,
        artistName: request.artistName,
        requesterName: request.requesterName,
        status: request.status,
        createdAt: request.createdAt,
      })),
      lastUpdated: Date.now(),
    };
  },
});

// Real-time subscription: Session activity feed
export const subscribeToSessionActivity = query({
  args: {
    sessionId: v.id("sessions"),
    includeAll: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session || !session.active) {
      return [];
    }

    const limit = args.includeAll ? 100 : 20;
    
    // Get recent requests for activity feed
    const requests = await ctx.db
      .query("songRequests")
      .withIndex("by_session_created", (q) => q.eq("sessionId", args.sessionId))
      .order("desc")
      .take(limit);

    // Create activity feed entries
    const activities = requests.map(request => ({
      id: request._id,
      type: "song_request" as const,
      timestamp: request.createdAt,
      data: {
        songTitle: request.songTitle,
        artistName: request.artistName,
        requesterName: request.requesterName,
        status: request.status,
      },
    }));

    // Add session status changes (simplified - in real implementation, 
    // you'd track these in a separate activities table)
    activities.push({
      id: `session_${session._id}`,
      type: "session_update" as const,
      timestamp: session.updatedAt,
      data: {
        sessionName: session.name,
        active: session.active,
        acceptRequests: session.acceptRequests,
      },
    });

    return activities
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  },
});

// Real-time subscription: Public session info
export const subscribeToPublicSessionInfo = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session || !session.active) {
      return null;
    }

    // Get basic stats for public display
    const totalRequests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    const recentRequests = totalRequests
      .filter(r => r.status !== "declined")
      .sort((a, b) => b.createdAt - a.createdAt)
      .slice(0, 10);

    return {
      sessionId: args.sessionId,
      sessionName: session.name,
      acceptRequests: session.acceptRequests,
      weddingMode: session.weddingModeEnabled,
      stats: {
        totalRequests: totalRequests.length,
        recentCount: recentRequests.length,
      },
      recentRequests: recentRequests.map(request => ({
        songTitle: request.songTitle,
        artistName: request.artistName,
        requesterName: request.requesterName,
        status: request.status,
        createdAt: request.createdAt,
      })),
      lastUpdated: Date.now(),
    };
  },
});
