/**
 * User Operations
 * 
 * This module handles user-related operations that work with Convex Auth
 * and integrate with DJ profiles for the complete user experience.
 */

import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Query: Get current authenticated user
export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const user = await ctx.db.get(userId);
    return user;
  },
});

// Query: Get current user with DJ profile
export const getCurrentUserWithProfile = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const user = await ctx.db.get(userId);
    if (!user) {
      return null;
    }

    // Get DJ profile if it exists
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    return {
      user,
      djProfile,
      hasDjProfile: !!djProfile,
      onboardingComplete: djProfile?.completedOnboarding ?? false,
    };
  },
});

// Query: Get user by ID (admin only or self)
export const getUserById = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const currentUserId = await getAuthUserId(ctx);
    if (!currentUserId) {
      throw new Error("Authentication required");
    }

    // Users can only access their own data
    if (currentUserId !== args.userId) {
      throw new Error("Access denied: You can only access your own user data");
    }

    const user = await ctx.db.get(args.userId);
    return user;
  },
});

// Query: Check if user exists and has DJ profile
export const checkUserStatus = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    
    if (!userId) {
      return {
        isAuthenticated: false,
        userExists: false,
        hasDjProfile: false,
        onboardingComplete: false,
      };
    }

    const user = await ctx.db.get(userId);
    if (!user) {
      return {
        isAuthenticated: true,
        userExists: false,
        hasDjProfile: false,
        onboardingComplete: false,
      };
    }

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    return {
      isAuthenticated: true,
      userExists: true,
      hasDjProfile: !!djProfile,
      onboardingComplete: djProfile?.completedOnboarding ?? false,
      userId: userId,
      djProfileId: djProfile?._id,
    };
  },
});

// Query: Get user profile summary (public information)
export const getUserProfileSummary = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    if (!user) {
      return null;
    }

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    // Return only public information
    return {
      userId: args.userId,
      email: user.email,
      name: user.name,
      djProfile: djProfile ? {
        id: djProfile._id,
        displayName: djProfile.displayName,
        completedOnboarding: djProfile.completedOnboarding,
        createdAt: djProfile.createdAt,
      } : null,
    };
  },
});

// Mutation: Initialize user setup (called after first login)
export const initializeUserSetup = mutation({
  args: {
    createDjProfile: v.optional(v.boolean()),
    displayName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const user = await ctx.db.get(userId);
    if (!user) {
      throw new Error("User not found");
    }

    // Check if DJ profile already exists
    const existingProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (existingProfile) {
      return {
        success: true,
        message: "User already has a DJ profile",
        djProfileId: existingProfile._id,
        onboardingComplete: existingProfile.completedOnboarding,
      };
    }

    // Create DJ profile if requested
    if (args.createDjProfile) {
      const now = Date.now();
      const djProfileId = await ctx.db.insert("djProfiles", {
        userId,
        displayName: args.displayName?.trim(),
        completedOnboarding: false,
        createdAt: now,
        updatedAt: now,
      });

      return {
        success: true,
        message: "DJ profile created successfully",
        djProfileId,
        onboardingComplete: false,
      };
    }

    return {
      success: true,
      message: "User setup initialized",
      djProfileId: null,
      onboardingComplete: false,
    };
  },
});

// Mutation: Complete user onboarding
export const completeOnboarding = mutation({
  args: {},
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get user's DJ profile
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      throw new Error("DJ profile not found. Please create a DJ profile first.");
    }

    if (djProfile.completedOnboarding) {
      return {
        success: true,
        message: "Onboarding already completed",
        alreadyCompleted: true,
      };
    }

    // Update DJ profile to mark onboarding as complete
    await ctx.db.patch(djProfile._id, {
      completedOnboarding: true,
      updatedAt: Date.now(),
    });

    return {
      success: true,
      message: "Onboarding completed successfully",
      alreadyCompleted: false,
    };
  },
});

// Mutation: Update user preferences (if we add user preferences later)
export const updateUserPreferences = mutation({
  args: {
    preferences: v.object({
      emailNotifications: v.optional(v.boolean()),
      theme: v.optional(v.string()),
      language: v.optional(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // For now, we'll store preferences in the DJ profile
    // In the future, we might create a separate user preferences table
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      throw new Error("DJ profile required to update preferences");
    }

    // Store preferences as a simple object for now
    // This would be expanded based on actual requirements
    await ctx.db.patch(djProfile._id, {
      updatedAt: Date.now(),
      // preferences: args.preferences, // Would add this field to schema
    });

    return {
      success: true,
      message: "User preferences updated successfully",
    };
  },
});

// Query: Get user activity summary
export const getUserActivitySummary = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      return null;
    }

    // Get session count
    const sessions = await ctx.db
      .query("sessions")
      .withIndex("by_dj", (q) => q.eq("djId", djProfile._id))
      .collect();

    // Get active sessions count
    const activeSessions = await ctx.db
      .query("sessions")
      .withIndex("by_dj_active", (q) => 
        q.eq("djId", djProfile._id).eq("active", true)
      )
      .collect();

    // Get total song requests across all sessions
    let totalRequests = 0;
    for (const session of sessions) {
      const requests = await ctx.db
        .query("songRequests")
        .withIndex("by_session", (q) => q.eq("sessionId", session._id))
        .collect();
      totalRequests += requests.length;
    }

    return {
      djProfile: {
        id: djProfile._id,
        displayName: djProfile.displayName,
        completedOnboarding: djProfile.completedOnboarding,
        lastLoginAt: djProfile.lastLoginAt,
        createdAt: djProfile.createdAt,
      },
      stats: {
        totalSessions: sessions.length,
        activeSessions: activeSessions.length,
        totalSongRequests: totalRequests,
      },
    };
  },
});

// Mutation: Delete user account (with all associated data)
export const deleteUserAccount = mutation({
  args: {
    confirmDelete: v.boolean(),
    deleteReason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    if (!args.confirmDelete) {
      throw new Error("Delete confirmation required");
    }

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get DJ profile
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (djProfile) {
      // Check for active sessions
      const activeSessions = await ctx.db
        .query("sessions")
        .withIndex("by_dj_active", (q) => 
          q.eq("djId", djProfile._id).eq("active", true)
        )
        .first();

      if (activeSessions) {
        throw new Error("Cannot delete account with active sessions. Please end all sessions first.");
      }

      // Delete DJ profile (this will cascade to sessions and requests)
      await ctx.db.delete(djProfile._id);
    }

    // Delete Apple Music tokens
    const tokens = await ctx.db
      .query("appleMusicTokens")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();

    for (const token of tokens) {
      await ctx.db.delete(token._id);
    }

    // Note: The actual user record in Convex Auth would need to be handled
    // through the auth system, not directly through the database

    return {
      success: true,
      message: "User account and associated data deleted successfully",
      deletedAt: Date.now(),
    };
  },
});

// Test function for creating users (development/testing only)
export const createTestUser = mutation({
  args: {
    email: v.string(),
    name: v.string(),
    profilePictureUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // This is a test function - in production, users are created via auth
    const userId = await ctx.db.insert("users", {
      email: args.email,
      name: args.name,
      image: args.profilePictureUrl,
      isAnonymous: false,
    });
    return userId;
  },
});
