/**
 * Billing and Stripe Integration
 * 
 * This module handles billing operations, Stripe integration, and
 * subscription lifecycle management including webhooks and payments.
 */

import { v } from "convex/values";
import { action, mutation } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Action: Create Stripe checkout session
export const createCheckoutSession = action({
  args: {
    planId: v.id("subscriptionPlans"),
    successUrl: v.string(),
    cancelUrl: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get user's DJ profile
    const djProfile = await ctx.runQuery(api.djProfiles.getCurrentDjProfile, {});
    if (!djProfile) {
      throw new Error("DJ profile required to create subscription");
    }

    // Get subscription plan
    const plan = await ctx.runQuery(api.subscriptionPlans.getSubscriptionPlanById, {
      planId: args.planId,
    });

    if (!plan) {
      throw new Error("Subscription plan not found");
    }

    // Check if user already has an active subscription
    const existingSubscription = await ctx.runQuery(api.djSubscriptions.getCurrentUserSubscription, {});
    if (existingSubscription?.subscription && existingSubscription.isActive) {
      throw new Error("You already have an active subscription");
    }

    // TODO: Integrate with actual Stripe API
    // For now, return a mock checkout session
    const mockCheckoutSession = {
      id: `cs_mock_${Date.now()}`,
      url: `https://checkout.stripe.com/pay/mock_session_${Date.now()}`,
      planId: args.planId,
      planName: plan.name,
      amount: plan.priceAmount,
      currency: plan.priceCurrency,
    };

    return {
      checkoutSession: mockCheckoutSession,
      message: "Checkout session created successfully",
    };
  },
});

// Action: Create Stripe customer portal session
export const createCustomerPortalSession = action({
  args: {
    returnUrl: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get user's subscription
    const subscription = await ctx.runQuery(api.djSubscriptions.getCurrentUserSubscription, {});
    if (!subscription?.subscription) {
      throw new Error("No subscription found");
    }

    if (!subscription.subscription.stripeCustomerId) {
      throw new Error("No Stripe customer ID found");
    }

    // TODO: Integrate with actual Stripe API
    // For now, return a mock portal session
    const mockPortalSession = {
      url: `https://billing.stripe.com/p/session_mock_${Date.now()}`,
      customerId: subscription.subscription.stripeCustomerId,
    };

    return {
      portalSession: mockPortalSession,
      message: "Customer portal session created successfully",
    };
  },
});

// Mutation: Handle Stripe webhook
export const handleStripeWebhook = mutation({
  args: {
    eventType: v.string(),
    eventData: v.any(),
  },
  handler: async (ctx, args) => {
    // TODO: Add webhook signature verification for production
    
    switch (args.eventType) {
      case "customer.subscription.created":
        return await handleSubscriptionCreated(ctx, args.eventData);
      
      case "customer.subscription.updated":
        return await handleSubscriptionUpdated(ctx, args.eventData);
      
      case "customer.subscription.deleted":
        return await handleSubscriptionDeleted(ctx, args.eventData);
      
      case "invoice.payment_succeeded":
        return await handlePaymentSucceeded(ctx, args.eventData);
      
      case "invoice.payment_failed":
        return await handlePaymentFailed(ctx, args.eventData);
      
      default:
        console.log(`Unhandled webhook event: ${args.eventType}`);
        return { success: true, message: "Event ignored" };
    }
  },
});

// Helper: Handle subscription created webhook
async function handleSubscriptionCreated(ctx: any, eventData: any) {
  const subscription = eventData.object;
  
  // Find the subscription plan by Stripe price ID
  const plan = await ctx.runQuery(api.subscriptionPlans.getSubscriptionPlanByStripeId, {
    stripePriceId: subscription.items.data[0].price.id,
  });

  if (!plan) {
    throw new Error("Subscription plan not found for Stripe price ID");
  }

  // Create subscription in our database
  await ctx.runMutation(api.djSubscriptions.createSubscription, {
    planId: plan._id,
    stripeCustomerId: subscription.customer,
    stripeSubscriptionId: subscription.id,
    currentPeriodStart: subscription.current_period_start * 1000,
    currentPeriodEnd: subscription.current_period_end * 1000,
  });

  return {
    success: true,
    message: "Subscription created successfully",
  };
}

// Helper: Handle subscription updated webhook
async function handleSubscriptionUpdated(ctx: any, eventData: any) {
  const subscription = eventData.object;
  
  await ctx.runMutation(api.djSubscriptions.updateSubscriptionStatus, {
    stripeSubscriptionId: subscription.id,
    status: subscription.status,
    currentPeriodStart: subscription.current_period_start * 1000,
    currentPeriodEnd: subscription.current_period_end * 1000,
    cancelAtPeriodEnd: subscription.cancel_at_period_end,
  });

  return {
    success: true,
    message: "Subscription updated successfully",
  };
}

// Helper: Handle subscription deleted webhook
async function handleSubscriptionDeleted(ctx: any, eventData: any) {
  const subscription = eventData.object;
  
  await ctx.runMutation(api.djSubscriptions.updateSubscriptionStatus, {
    stripeSubscriptionId: subscription.id,
    status: "canceled",
  });

  return {
    success: true,
    message: "Subscription canceled successfully",
  };
}

// Helper: Handle payment succeeded webhook
async function handlePaymentSucceeded(ctx: any, eventData: any) {
  const invoice = eventData.object;
  
  if (invoice.subscription) {
    // Update subscription status to active
    await ctx.runMutation(api.djSubscriptions.updateSubscriptionStatus, {
      stripeSubscriptionId: invoice.subscription,
      status: "active",
    });
  }

  return {
    success: true,
    message: "Payment processed successfully",
  };
}

// Helper: Handle payment failed webhook
async function handlePaymentFailed(ctx: any, eventData: any) {
  const invoice = eventData.object;
  
  if (invoice.subscription) {
    // Update subscription status to past_due
    await ctx.runMutation(api.djSubscriptions.updateSubscriptionStatus, {
      stripeSubscriptionId: invoice.subscription,
      status: "past_due",
    });
  }

  return {
    success: true,
    message: "Payment failure processed",
  };
}

// Action: Get billing summary
export const getBillingSummary = action({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get subscription status
    const subscriptionStatus = await ctx.runQuery(api.djSubscriptions.checkSubscriptionStatus, {});
    
    // Get subscription summary
    const subscriptionSummary = await ctx.runQuery(api.featureAccess.getUserSubscriptionSummary, {});

    // Get available plans
    const availablePlans = await ctx.runQuery(api.subscriptionPlans.getActiveSubscriptionPlans, {});

    return {
      subscription: subscriptionStatus,
      usage: subscriptionSummary.currentUsage,
      availablePlans,
      billing: {
        nextBillingDate: subscriptionStatus.subscription?.currentPeriodEnd,
        daysRemaining: subscriptionStatus.daysRemaining,
        canUpgrade: !subscriptionStatus.isActive || subscriptionStatus.planName === "Free",
        canDowngrade: false, // Event-based model doesn't support downgrades
      },
    };
  },
});

// Action: Preview plan change
export const previewPlanChange = action({
  args: {
    newPlanId: v.id("subscriptionPlans"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get current subscription
    const currentSubscription = await ctx.runQuery(api.djSubscriptions.getCurrentUserSubscription, {});
    
    // Get new plan details
    const newPlan = await ctx.runQuery(api.subscriptionPlans.getSubscriptionPlanById, {
      planId: args.newPlanId,
    });

    if (!newPlan) {
      throw new Error("New plan not found");
    }

    // Calculate prorated amount (simplified calculation)
    let proratedAmount = newPlan.priceAmount;
    let changeType = "upgrade";

    if (currentSubscription?.plan) {
      if (newPlan.priceAmount < currentSubscription.plan.priceAmount) {
        changeType = "downgrade";
      }

      // Calculate prorated amount based on remaining time
      if (currentSubscription.daysRemaining && currentSubscription.plan.durationHours) {
        const remainingRatio = currentSubscription.daysRemaining / (currentSubscription.plan.durationHours / 24);
        const currentPlanCredit = Math.round(currentSubscription.plan.priceAmount * remainingRatio);
        proratedAmount = Math.max(0, newPlan.priceAmount - currentPlanCredit);
      }
    }

    return {
      currentPlan: currentSubscription?.plan,
      newPlan,
      changeType,
      proratedAmount,
      effectiveDate: Date.now(),
      planChanges: {
        durationMinutes: {
          current: currentSubscription?.plan ? currentSubscription.plan.durationMinutes : 20,
          new: newPlan.details.durationMinutes,
        },
        maxRequestsPerSession: {
          current: currentSubscription?.plan ? currentSubscription.plan.maxRequestsPerSession : 3,
          new: newPlan.details.maxRequestsPerSession,
        },
      },
    };
  },
});

// Note: Feature-tier logic removed in favor of simple event-based passes
