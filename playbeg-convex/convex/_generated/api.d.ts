/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as appleMusicApi from "../appleMusicApi.js";
import type * as appleMusicTestSuite from "../appleMusicTestSuite.js";
import type * as appleMusicTokens from "../appleMusicTokens.js";
import type * as audienceEngagement from "../audienceEngagement.js";
import type * as auth from "../auth.js";
import type * as billing from "../billing.js";
import type * as djProfiles from "../djProfiles.js";
import type * as djSubscriptions from "../djSubscriptions.js";
import type * as featureAccess from "../featureAccess.js";
import type * as fileStorage from "../fileStorage.js";
import type * as fileUploadTestSuite from "../fileUploadTestSuite.js";
import type * as http from "../http.js";
import type * as liveAnalytics from "../liveAnalytics.js";
import type * as myFunctions from "../myFunctions.js";
import type * as playlists from "../playlists.js";
import type * as profilePictures from "../profilePictures.js";
import type * as realTimeNotifications from "../realTimeNotifications.js";
import type * as realTimeSubscriptions from "../realTimeSubscriptions.js";
import type * as realTimeTestSuite from "../realTimeTestSuite.js";
import type * as realtime from "../realtime.js";
import type * as sessionTestSuite from "../sessionTestSuite.js";
import type * as sessions from "../sessions.js";
import type * as songRequests from "../songRequests.js";
import type * as songs from "../songs.js";
import type * as sponsorLogos from "../sponsorLogos.js";
import type * as subscriptionPlans from "../subscriptionPlans.js";
import type * as subscriptionTestSuite from "../subscriptionTestSuite.js";
import type * as testCrud from "../testCrud.js";
import type * as users from "../users.js";
import type * as validation from "../validation.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  appleMusicApi: typeof appleMusicApi;
  appleMusicTestSuite: typeof appleMusicTestSuite;
  appleMusicTokens: typeof appleMusicTokens;
  audienceEngagement: typeof audienceEngagement;
  auth: typeof auth;
  billing: typeof billing;
  djProfiles: typeof djProfiles;
  djSubscriptions: typeof djSubscriptions;
  featureAccess: typeof featureAccess;
  fileStorage: typeof fileStorage;
  fileUploadTestSuite: typeof fileUploadTestSuite;
  http: typeof http;
  liveAnalytics: typeof liveAnalytics;
  myFunctions: typeof myFunctions;
  playlists: typeof playlists;
  profilePictures: typeof profilePictures;
  realTimeNotifications: typeof realTimeNotifications;
  realTimeSubscriptions: typeof realTimeSubscriptions;
  realTimeTestSuite: typeof realTimeTestSuite;
  realtime: typeof realtime;
  sessionTestSuite: typeof sessionTestSuite;
  sessions: typeof sessions;
  songRequests: typeof songRequests;
  songs: typeof songs;
  sponsorLogos: typeof sponsorLogos;
  subscriptionPlans: typeof subscriptionPlans;
  subscriptionTestSuite: typeof subscriptionTestSuite;
  testCrud: typeof testCrud;
  users: typeof users;
  validation: typeof validation;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
