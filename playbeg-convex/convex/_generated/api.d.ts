/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as appleMusicTokens from "../appleMusicTokens.js";
import type * as audienceEngagement from "../audienceEngagement.js";
import type * as auth from "../auth.js";
import type * as djProfiles from "../djProfiles.js";
import type * as djSubscriptions from "../djSubscriptions.js";
import type * as fileStorage from "../fileStorage.js";
import type * as http from "../http.js";
import type * as liveAnalytics from "../liveAnalytics.js";
import type * as myFunctions from "../myFunctions.js";
import type * as notifications from "../notifications.js";
import type * as realTimeConnections from "../realTimeConnections.js";
import type * as sessions from "../sessions.js";
import type * as songRequests from "../songRequests.js";
import type * as users from "../users.js";
import type * as validation from "../validation.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  appleMusicTokens: typeof appleMusicTokens;
  audienceEngagement: typeof audienceEngagement;
  auth: typeof auth;
  djProfiles: typeof djProfiles;
  djSubscriptions: typeof djSubscriptions;
  fileStorage: typeof fileStorage;
  http: typeof http;
  liveAnalytics: typeof liveAnalytics;
  myFunctions: typeof myFunctions;
  notifications: typeof notifications;
  realTimeConnections: typeof realTimeConnections;
  sessions: typeof sessions;
  songRequests: typeof songRequests;
  users: typeof users;
  validation: typeof validation;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
