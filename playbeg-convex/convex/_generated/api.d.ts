/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as analytics from "../analytics.js";
import type * as appleMusicTokens from "../appleMusicTokens.js";
import type * as audienceEngagement from "../audienceEngagement.js";
import type * as auth from "../auth.js";
import type * as blog from "../blog.js";
import type * as djProfiles from "../djProfiles.js";
import type * as djSubscriptions from "../djSubscriptions.js";
import type * as enhancedSessionControls from "../enhancedSessionControls.js";
import type * as fileStorage from "../fileStorage.js";
import type * as http from "../http.js";
import type * as liveAnalytics from "../liveAnalytics.js";
import type * as mobile from "../mobile.js";
import type * as myFunctions from "../myFunctions.js";
import type * as notifications from "../notifications.js";
import type * as payments from "../payments.js";
import type * as performance from "../performance.js";
import type * as realTimeConnections from "../realTimeConnections.js";
import type * as search from "../search.js";
import type * as security from "../security.js";
import type * as sessionAnalytics from "../sessionAnalytics.js";
import type * as sessionCollaboration from "../sessionCollaboration.js";
import type * as sessionScheduling from "../sessionScheduling.js";
import type * as sessionTemplates from "../sessionTemplates.js";
import type * as sessions from "../sessions.js";
import type * as songRequests from "../songRequests.js";
import type * as spotify from "../spotify.js";
import type * as testData from "../testData.js";
import type * as users from "../users.js";
import type * as validation from "../validation.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  analytics: typeof analytics;
  appleMusicTokens: typeof appleMusicTokens;
  audienceEngagement: typeof audienceEngagement;
  auth: typeof auth;
  blog: typeof blog;
  djProfiles: typeof djProfiles;
  djSubscriptions: typeof djSubscriptions;
  enhancedSessionControls: typeof enhancedSessionControls;
  fileStorage: typeof fileStorage;
  http: typeof http;
  liveAnalytics: typeof liveAnalytics;
  mobile: typeof mobile;
  myFunctions: typeof myFunctions;
  notifications: typeof notifications;
  payments: typeof payments;
  performance: typeof performance;
  realTimeConnections: typeof realTimeConnections;
  search: typeof search;
  security: typeof security;
  sessionAnalytics: typeof sessionAnalytics;
  sessionCollaboration: typeof sessionCollaboration;
  sessionScheduling: typeof sessionScheduling;
  sessionTemplates: typeof sessionTemplates;
  sessions: typeof sessions;
  songRequests: typeof songRequests;
  spotify: typeof spotify;
  testData: typeof testData;
  users: typeof users;
  validation: typeof validation;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
