/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as appleMusicTokens from "../appleMusicTokens.js";
import type * as auth from "../auth.js";
import type * as disabled_appleMusic<PERSON>pi from "../disabled/appleMusicApi.js";
import type * as disabled_appleMusicTestSuite from "../disabled/appleMusicTestSuite.js";
import type * as disabled_audienceEngagement from "../disabled/audienceEngagement.js";
import type * as disabled_billing from "../disabled/billing.js";
import type * as disabled_featureAccess from "../disabled/featureAccess.js";
import type * as disabled_fileStorage from "../disabled/fileStorage.js";
import type * as disabled_fileUploadTestSuite from "../disabled/fileUploadTestSuite.js";
import type * as disabled_liveAnalytics from "../disabled/liveAnalytics.js";
import type * as disabled_playlists from "../disabled/playlists.js";
import type * as disabled_profilePictures from "../disabled/profilePictures.js";
import type * as disabled_realTimeNotifications from "../disabled/realTimeNotifications.js";
import type * as disabled_realTimeSubscriptions from "../disabled/realTimeSubscriptions.js";
import type * as disabled_realTimeTestSuite from "../disabled/realTimeTestSuite.js";
import type * as disabled_realtime from "../disabled/realtime.js";
import type * as disabled_sessionTestSuite from "../disabled/sessionTestSuite.js";
import type * as disabled_songs from "../disabled/songs.js";
import type * as disabled_sponsorLogos from "../disabled/sponsorLogos.js";
import type * as disabled_subscriptionPlans from "../disabled/subscriptionPlans.js";
import type * as disabled_subscriptionTestSuite from "../disabled/subscriptionTestSuite.js";
import type * as disabled_testCrud from "../disabled/testCrud.js";
import type * as djProfiles from "../djProfiles.js";
import type * as djSubscriptions from "../djSubscriptions.js";
import type * as http from "../http.js";
import type * as myFunctions from "../myFunctions.js";
import type * as sessions from "../sessions.js";
import type * as songRequests from "../songRequests.js";
import type * as users from "../users.js";
import type * as validation from "../validation.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  appleMusicTokens: typeof appleMusicTokens;
  auth: typeof auth;
  "disabled/appleMusicApi": typeof disabled_appleMusicApi;
  "disabled/appleMusicTestSuite": typeof disabled_appleMusicTestSuite;
  "disabled/audienceEngagement": typeof disabled_audienceEngagement;
  "disabled/billing": typeof disabled_billing;
  "disabled/featureAccess": typeof disabled_featureAccess;
  "disabled/fileStorage": typeof disabled_fileStorage;
  "disabled/fileUploadTestSuite": typeof disabled_fileUploadTestSuite;
  "disabled/liveAnalytics": typeof disabled_liveAnalytics;
  "disabled/playlists": typeof disabled_playlists;
  "disabled/profilePictures": typeof disabled_profilePictures;
  "disabled/realTimeNotifications": typeof disabled_realTimeNotifications;
  "disabled/realTimeSubscriptions": typeof disabled_realTimeSubscriptions;
  "disabled/realTimeTestSuite": typeof disabled_realTimeTestSuite;
  "disabled/realtime": typeof disabled_realtime;
  "disabled/sessionTestSuite": typeof disabled_sessionTestSuite;
  "disabled/songs": typeof disabled_songs;
  "disabled/sponsorLogos": typeof disabled_sponsorLogos;
  "disabled/subscriptionPlans": typeof disabled_subscriptionPlans;
  "disabled/subscriptionTestSuite": typeof disabled_subscriptionTestSuite;
  "disabled/testCrud": typeof disabled_testCrud;
  djProfiles: typeof djProfiles;
  djSubscriptions: typeof djSubscriptions;
  http: typeof http;
  myFunctions: typeof myFunctions;
  sessions: typeof sessions;
  songRequests: typeof songRequests;
  users: typeof users;
  validation: typeof validation;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
