/**
 * Sponsor Logo Management
 * 
 * This module handles sponsor logo operations including upload, management,
 * session association, and retrieval with proper validation and access control.
 */

import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Query: Get sponsor logos for a session
export const getSponsorLogosBySessionId = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    // Get session to verify it exists
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return [];
    }

    // Get sponsor logos associated with this session
    const sponsorLogos = await ctx.db
      .query("fileMetadata")
      .withIndex("by_type_associated", (q) => 
        q.eq("fileType", "sponsor_logo").eq("associatedId", args.sessionId)
      )
      .collect();

    // Get URLs for each logo
    const logosWithUrls = await Promise.all(
      sponsorLogos.map(async (logo) => {
        const url = await ctx.storage.getUrl(logo.storageId);
        return {
          ...logo,
          url,
        };
      })
    );

    return logosWithUrls;
  },
});

// Query: Get user's sponsor logos
export const getUserSponsorLogos = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    const limit = args.limit ?? 20;

    const sponsorLogos = await ctx.db
      .query("fileMetadata")
      .withIndex("by_user_type", (q) => 
        q.eq("userId", userId).eq("fileType", "sponsor_logo")
      )
      .order("desc")
      .take(limit);

    // Get URLs for each logo
    const logosWithUrls = await Promise.all(
      sponsorLogos.map(async (logo) => {
        const url = await ctx.storage.getUrl(logo.storageId);
        return {
          ...logo,
          url,
        };
      })
    );

    return logosWithUrls;
  },
});

// Action: Upload sponsor logo
export const uploadSponsorLogo = action({
  args: {
    storageId: v.id("_storage"),
    fileName: v.string(),
    mimeType: v.string(),
    fileSize: v.number(),
    sessionId: v.optional(v.id("sessions")),
    sponsorName: v.optional(v.string()),
    sponsorUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // If sessionId provided, verify ownership
    if (args.sessionId) {
      const session = await ctx.runQuery(api.sessions.getSessionById, {
        sessionId: args.sessionId,
      });
      
      if (!session) {
        throw new Error("Session not found");
      }
    }

    // Store file metadata
    const fileResult = await ctx.runMutation(api.fileStorage.storeFileMetadata, {
      storageId: args.storageId,
      fileName: args.fileName,
      fileType: "sponsor_logo",
      mimeType: args.mimeType,
      fileSize: args.fileSize,
      associatedId: args.sessionId,
    });

    // Create sponsor logo record with additional metadata
    const now = Date.now();
    const sponsorLogoId = await ctx.runMutation(api.sponsorLogos.createSponsorLogoRecord, {
      fileMetadataId: fileResult.fileMetadataId,
      storageId: args.storageId,
      sessionId: args.sessionId,
      sponsorName: args.sponsorName,
      sponsorUrl: args.sponsorUrl,
    });

    return {
      ...fileResult,
      sponsorLogoId,
      message: "Sponsor logo uploaded successfully",
    };
  },
});

// Mutation: Create sponsor logo record
export const createSponsorLogoRecord = mutation({
  args: {
    fileMetadataId: v.id("fileMetadata"),
    storageId: v.id("_storage"),
    sessionId: v.optional(v.id("sessions")),
    sponsorName: v.optional(v.string()),
    sponsorUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const now = Date.now();
    const sponsorLogoId = await ctx.db.insert("sponsorLogos", {
      fileMetadataId: args.fileMetadataId,
      storageId: args.storageId,
      userId,
      sessionId: args.sessionId,
      sponsorName: args.sponsorName?.trim(),
      sponsorUrl: args.sponsorUrl?.trim(),
      active: true,
      createdAt: now,
      updatedAt: now,
    });

    return sponsorLogoId;
  },
});

// Mutation: Update sponsor logo metadata
export const updateSponsorLogoMetadata = mutation({
  args: {
    sponsorLogoId: v.id("sponsorLogos"),
    sponsorName: v.optional(v.string()),
    sponsorUrl: v.optional(v.string()),
    active: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const sponsorLogo = await ctx.db.get(args.sponsorLogoId);
    if (!sponsorLogo) {
      throw new Error("Sponsor logo not found");
    }

    if (sponsorLogo.userId !== userId) {
      throw new Error("Access denied: You can only update your own sponsor logos");
    }

    const updateData: any = {
      updatedAt: Date.now(),
    };

    if (args.sponsorName !== undefined) {
      updateData.sponsorName = args.sponsorName?.trim();
    }

    if (args.sponsorUrl !== undefined) {
      updateData.sponsorUrl = args.sponsorUrl?.trim();
    }

    if (args.active !== undefined) {
      updateData.active = args.active;
    }

    await ctx.db.patch(args.sponsorLogoId, updateData);

    return {
      sponsorLogoId: args.sponsorLogoId,
      message: "Sponsor logo metadata updated successfully",
    };
  },
});

// Mutation: Delete sponsor logo
export const deleteSponsorLogo = mutation({
  args: {
    sponsorLogoId: v.id("sponsorLogos"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const sponsorLogo = await ctx.db.get(args.sponsorLogoId);
    if (!sponsorLogo) {
      throw new Error("Sponsor logo not found");
    }

    if (sponsorLogo.userId !== userId) {
      throw new Error("Access denied: You can only delete your own sponsor logos");
    }

    // Delete file from storage
    try {
      await ctx.runMutation(api.fileStorage.deleteFile, {
        storageId: sponsorLogo.storageId,
      });
    } catch (error) {
      // Continue even if file deletion fails
      console.log("Failed to delete sponsor logo file:", error);
    }

    // Delete sponsor logo record
    await ctx.db.delete(args.sponsorLogoId);

    return {
      success: true,
      message: "Sponsor logo deleted successfully",
    };
  },
});

// Action: Get sponsor logo upload URL
export const getSponsorLogoUploadUrl = action({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Generate upload URL
    const uploadUrl = await ctx.runMutation(api.fileStorage.generateUploadUrl, {});

    return {
      uploadUrl,
      maxFileSize: 2 * 1024 * 1024, // 2MB
      allowedTypes: ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"],
      instructions: "Upload sponsor logo (max 2MB, JPEG/PNG/GIF/WebP)",
    };
  },
});

// Query: Get sponsor logo by ID
export const getSponsorLogoById = query({
  args: {
    sponsorLogoId: v.id("sponsorLogos"),
  },
  handler: async (ctx, args) => {
    const sponsorLogo = await ctx.db.get(args.sponsorLogoId);
    if (!sponsorLogo) {
      return null;
    }

    // Get file URL
    const url = await ctx.storage.getUrl(sponsorLogo.storageId);

    // Get file metadata
    const fileMetadata = await ctx.db.get(sponsorLogo.fileMetadataId);

    return {
      ...sponsorLogo,
      url,
      fileMetadata,
    };
  },
});

// Mutation: Associate sponsor logo with session
export const associateSponsorLogoWithSession = mutation({
  args: {
    sponsorLogoId: v.id("sponsorLogos"),
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Verify sponsor logo ownership
    const sponsorLogo = await ctx.db.get(args.sponsorLogoId);
    if (!sponsorLogo) {
      throw new Error("Sponsor logo not found");
    }

    if (sponsorLogo.userId !== userId) {
      throw new Error("Access denied: You can only manage your own sponsor logos");
    }

    // Verify session ownership
    const session = await ctx.runQuery(api.sessions.getSessionById, {
      sessionId: args.sessionId,
    });

    if (!session) {
      throw new Error("Session not found");
    }

    // Update sponsor logo with session association
    await ctx.db.patch(args.sponsorLogoId, {
      sessionId: args.sessionId,
      updatedAt: Date.now(),
    });

    // Update file metadata association
    await ctx.db.patch(sponsorLogo.fileMetadataId, {
      associatedId: args.sessionId,
      updatedAt: Date.now(),
    });

    return {
      sponsorLogoId: args.sponsorLogoId,
      sessionId: args.sessionId,
      message: "Sponsor logo associated with session successfully",
    };
  },
});

// Query: Get sponsor logo statistics
export const getSponsorLogoStatistics = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get all sponsor logos
    const sponsorLogos = await ctx.db
      .query("sponsorLogos")
      .collect();

    // Get user's sponsor logos
    const userSponsorLogos = sponsorLogos.filter(logo => logo.userId === userId);

    // Count active vs inactive
    const activeLogos = sponsorLogos.filter(logo => logo.active);
    const inactiveLogos = sponsorLogos.filter(logo => !logo.active);

    // Count associated with sessions
    const associatedLogos = sponsorLogos.filter(logo => logo.sessionId);

    return {
      total: sponsorLogos.length,
      userTotal: userSponsorLogos.length,
      active: activeLogos.length,
      inactive: inactiveLogos.length,
      associatedWithSessions: associatedLogos.length,
      unassociated: sponsorLogos.length - associatedLogos.length,
    };
  },
});
