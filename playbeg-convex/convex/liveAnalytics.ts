/**
 * Live Session Analytics
 * 
 * Real-time analytics for DJ sessions including listener metrics,
 * engagement rates, request patterns, and session performance data.
 */

import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Record analytics event
export const recordAnalyticsEvent = mutation({
  args: {
    sessionId: v.id("sessions"),
    eventType: v.union(
      v.literal("user_joined"),
      v.literal("user_left"),
      v.literal("song_requested"),
      v.literal("song_approved"),
      v.literal("song_declined"),
      v.literal("song_played"),
      v.literal("reaction_added"),
      v.literal("chat_message"),
      v.literal("vote_cast"),
    ),
    eventData: v.optional(v.object({
      userId: v.optional(v.id("users")),
      songRequestId: v.optional(v.id("songRequests")),
      reactionType: v.optional(v.string()),
      messageContent: v.optional(v.string()),
      voteValue: v.optional(v.number()),
      metadata: v.optional(v.any()),
    })),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    
    const eventId = await ctx.db.insert("analyticsEvents", {
      sessionId: args.sessionId,
      eventType: args.eventType,
      eventData: args.eventData,
      userId: userId || undefined,
      timestamp: Date.now(),
    });

    return eventId;
  },
});

// Get real-time session analytics
export const getLiveSessionAnalytics = query({
  args: {
    sessionId: v.id("sessions"),
    timeRange: v.optional(v.union(
      v.literal("last_hour"),
      v.literal("last_day"),
      v.literal("session_start"),
    )),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return null;
    }

    // Calculate time range
    let startTime: number;
    const now = Date.now();
    
    switch (args.timeRange) {
      case "last_hour":
        startTime = now - (60 * 60 * 1000);
        break;
      case "last_day":
        startTime = now - (24 * 60 * 60 * 1000);
        break;
      case "session_start":
      default:
        startTime = session.createdAt;
        break;
    }

    // Get analytics events in time range
    const events = await ctx.db
      .query("analyticsEvents")
      .withIndex("by_session_time", (q) => 
        q.eq("sessionId", args.sessionId).gte("timestamp", startTime)
      )
      .collect();

    // Get active connections
    const activeConnections = await ctx.db
      .query("sessionConnections")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    // Get song requests in time range
    const songRequests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .filter((q) => q.gte(q.field("createdAt"), startTime))
      .collect();

    // Calculate metrics
    const totalEvents = events.length;
    const uniqueUsers = new Set(events.map(e => e.userId).filter(Boolean)).size;
    const currentListeners = activeConnections.filter(c => c.userRole === "audience").length;
    const totalRequests = songRequests.length;
    const approvedRequests = songRequests.filter(r => r.status === "approved").length;
    const playedRequests = songRequests.filter(r => r.status === "played").length;

    // Event type breakdown
    const eventBreakdown = events.reduce((acc, event) => {
      acc[event.eventType] = (acc[event.eventType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Hourly activity (last 24 hours)
    const hourlyActivity = [];
    for (let i = 23; i >= 0; i--) {
      const hourStart = now - (i * 60 * 60 * 1000);
      const hourEnd = hourStart + (60 * 60 * 1000);
      const hourEvents = events.filter(e => e.timestamp >= hourStart && e.timestamp < hourEnd);
      
      hourlyActivity.push({
        hour: new Date(hourStart).getHours(),
        timestamp: hourStart,
        eventCount: hourEvents.length,
        uniqueUsers: new Set(hourEvents.map(e => e.userId).filter(Boolean)).size,
      });
    }

    // Request approval rate
    const approvalRate = totalRequests > 0 ? (approvedRequests / totalRequests) * 100 : 0;
    const playRate = totalRequests > 0 ? (playedRequests / totalRequests) * 100 : 0;

    // Session duration
    const sessionDuration = now - session.createdAt;
    const sessionDurationHours = sessionDuration / (1000 * 60 * 60);

    // Engagement rate (events per user per hour)
    const engagementRate = uniqueUsers > 0 && sessionDurationHours > 0 
      ? totalEvents / (uniqueUsers * sessionDurationHours) 
      : 0;

    return {
      sessionId: args.sessionId,
      timeRange: args.timeRange || "session_start",
      startTime,
      endTime: now,
      
      // Current metrics
      currentListeners,
      totalConnections: activeConnections.length,
      
      // Activity metrics
      totalEvents,
      uniqueUsers,
      engagementRate: Math.round(engagementRate * 100) / 100,
      
      // Request metrics
      totalRequests,
      approvedRequests,
      playedRequests,
      approvalRate: Math.round(approvalRate * 100) / 100,
      playRate: Math.round(playRate * 100) / 100,
      
      // Session metrics
      sessionDuration,
      sessionDurationFormatted: formatDuration(sessionDuration),
      
      // Breakdowns
      eventBreakdown,
      hourlyActivity,
      
      lastUpdated: now,
    };
  },
});

// Get engagement trends
export const getEngagementTrends = query({
  args: {
    sessionId: v.id("sessions"),
    intervalMinutes: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return null;
    }

    const intervalMs = (args.intervalMinutes || 5) * 60 * 1000; // Default 5 minutes
    const now = Date.now();
    const sessionStart = session.createdAt;

    // Get all events for the session
    const events = await ctx.db
      .query("analyticsEvents")
      .withIndex("by_session_time", (q) => 
        q.eq("sessionId", args.sessionId).gte("timestamp", sessionStart)
      )
      .collect();

    // Create time intervals
    const intervals = [];
    for (let time = sessionStart; time < now; time += intervalMs) {
      const intervalEnd = Math.min(time + intervalMs, now);
      const intervalEvents = events.filter(e => 
        e.timestamp >= time && e.timestamp < intervalEnd
      );

      intervals.push({
        startTime: time,
        endTime: intervalEnd,
        eventCount: intervalEvents.length,
        uniqueUsers: new Set(intervalEvents.map(e => e.userId).filter(Boolean)).size,
        songRequests: intervalEvents.filter(e => e.eventType === "song_requested").length,
        reactions: intervalEvents.filter(e => e.eventType === "reaction_added").length,
        chatMessages: intervalEvents.filter(e => e.eventType === "chat_message").length,
      });
    }

    return {
      sessionId: args.sessionId,
      intervalMinutes: args.intervalMinutes || 5,
      intervals,
      totalIntervals: intervals.length,
      lastUpdated: now,
    };
  },
});

// Get top audience members by engagement
export const getTopAudienceMembers = query({
  args: {
    sessionId: v.id("sessions"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 10;

    // Get all events for the session
    const events = await ctx.db
      .query("analyticsEvents")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    // Group by user and count engagement
    const userEngagement = events.reduce((acc, event) => {
      if (!event.userId) return acc;
      
      if (!acc[event.userId]) {
        acc[event.userId] = {
          userId: event.userId,
          totalEvents: 0,
          songRequests: 0,
          reactions: 0,
          chatMessages: 0,
          votes: 0,
        };
      }

      acc[event.userId].totalEvents++;
      
      switch (event.eventType) {
        case "song_requested":
          acc[event.userId].songRequests++;
          break;
        case "reaction_added":
          acc[event.userId].reactions++;
          break;
        case "chat_message":
          acc[event.userId].chatMessages++;
          break;
        case "vote_cast":
          acc[event.userId].votes++;
          break;
      }

      return acc;
    }, {} as Record<string, any>);

    // Sort by total events and get top users
    const topUsers = Object.values(userEngagement)
      .sort((a: any, b: any) => b.totalEvents - a.totalEvents)
      .slice(0, limit);

    // Get user details
    const topUsersWithDetails = await Promise.all(
      topUsers.map(async (userStats: any) => {
        const user = await ctx.db.get(userStats.userId);
        return {
          ...userStats,
          user: user ? {
            _id: user._id,
            name: user.name,
            email: user.email,
          } : null,
        };
      })
    );

    return topUsersWithDetails;
  },
});

// Helper function to format duration
function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}
