/**
 * Session Analytics Dashboard
 * 
 * Comprehensive analytics for historical session performance, audience engagement,
 * popular song requests, and DJ performance metrics over time.
 */

import { v } from "convex/values";
import { query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Get comprehensive session analytics for a DJ
export const getDJSessionAnalytics = query({
  args: {
    timeRange: v.optional(v.union(
      v.literal("last_week"),
      v.literal("last_month"),
      v.literal("last_3_months"),
      v.literal("last_year"),
      v.literal("all_time"),
    )),
    includeScheduled: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    // Calculate time range
    const now = Date.now();
    let startTime: number;
    
    switch (args.timeRange) {
      case "last_week":
        startTime = now - (7 * 24 * 60 * 60 * 1000);
        break;
      case "last_month":
        startTime = now - (30 * 24 * 60 * 60 * 1000);
        break;
      case "last_3_months":
        startTime = now - (90 * 24 * 60 * 60 * 1000);
        break;
      case "last_year":
        startTime = now - (365 * 24 * 60 * 60 * 1000);
        break;
      case "all_time":
      default:
        startTime = 0;
        break;
    }

    // Get sessions in time range
    const sessions = await ctx.db
      .query("sessions")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .filter((q) => q.gte(q.field("createdAt"), startTime))
      .collect();

    // Get scheduled sessions if requested
    let scheduledSessions: any[] = [];
    if (args.includeScheduled) {
      scheduledSessions = await ctx.db
        .query("scheduledSessions")
        .withIndex("by_user", (q) => q.eq("userId", userId))
        .filter((q) => q.gte(q.field("createdAt"), startTime))
        .collect();
    }

    // Get analytics events for these sessions
    const sessionIds = sessions.map(s => s._id);
    const analyticsEvents = await Promise.all(
      sessionIds.map(async (sessionId) => {
        return await ctx.db
          .query("analyticsEvents")
          .withIndex("by_session", (q) => q.eq("sessionId", sessionId))
          .collect();
      })
    );

    const allEvents = analyticsEvents.flat();

    // Get song requests for these sessions
    const songRequests = await Promise.all(
      sessionIds.map(async (sessionId) => {
        return await ctx.db
          .query("songRequests")
          .withIndex("by_session", (q) => q.eq("sessionId", sessionId))
          .collect();
      })
    );

    const allSongRequests = songRequests.flat();

    // Calculate metrics
    const totalSessions = sessions.length;
    const activeSessions = sessions.filter(s => s.active).length;
    const completedSessions = sessions.filter(s => !s.active).length;
    
    const totalDuration = sessions.reduce((sum, session) => {
      if (!session.active && session.endedAt) {
        return sum + (session.endedAt - session.createdAt);
      }
      return sum;
    }, 0);

    const averageSessionDuration = completedSessions > 0 ? totalDuration / completedSessions : 0;

    const totalRequests = allSongRequests.length;
    const approvedRequests = allSongRequests.filter(r => r.status === "approved" || r.status === "played").length;
    const playedRequests = allSongRequests.filter(r => r.status === "played").length;
    
    const approvalRate = totalRequests > 0 ? (approvedRequests / totalRequests) * 100 : 0;
    const playRate = totalRequests > 0 ? (playedRequests / totalRequests) * 100 : 0;

    // Audience engagement metrics
    const totalEvents = allEvents.length;
    const uniqueUsers = new Set(allEvents.map(e => e.userId).filter(Boolean)).size;
    const averageEventsPerSession = totalSessions > 0 ? totalEvents / totalSessions : 0;

    // Popular genres analysis
    const genreCount = allSongRequests.reduce((acc, request) => {
      if (request.genre) {
        acc[request.genre] = (acc[request.genre] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const topGenres = Object.entries(genreCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([genre, count]) => ({ genre, count }));

    // Session performance over time
    const sessionsByMonth = sessions.reduce((acc, session) => {
      const month = new Date(session.createdAt).toISOString().slice(0, 7); // YYYY-MM
      if (!acc[month]) {
        acc[month] = {
          month,
          sessionCount: 0,
          totalRequests: 0,
          totalDuration: 0,
          uniqueListeners: new Set(),
        };
      }
      acc[month].sessionCount++;
      
      const sessionRequests = allSongRequests.filter(r => r.sessionId === session._id);
      acc[month].totalRequests += sessionRequests.length;
      
      if (!session.active && session.endedAt) {
        acc[month].totalDuration += (session.endedAt - session.createdAt);
      }

      const sessionEvents = allEvents.filter(e => e.sessionId === session._id);
      sessionEvents.forEach(event => {
        if (event.userId) {
          acc[month].uniqueListeners.add(event.userId);
        }
      });

      return acc;
    }, {} as Record<string, any>);

    const monthlyStats = Object.values(sessionsByMonth).map((month: any) => ({
      ...month,
      uniqueListeners: month.uniqueListeners.size,
      averageDuration: month.sessionCount > 0 ? month.totalDuration / month.sessionCount : 0,
    }));

    // Top requested songs
    const songCount = allSongRequests.reduce((acc, request) => {
      const key = `${request.songTitle} - ${request.artistName}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const topSongs = Object.entries(songCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([song, count]) => ({ song, count }));

    // Peak performance metrics
    const peakListeners = Math.max(...sessions.map(session => {
      const sessionEvents = allEvents.filter(e => e.sessionId === session._id);
      return new Set(sessionEvents.map(e => e.userId).filter(Boolean)).size;
    }), 0);

    const peakRequests = Math.max(...sessions.map(session => {
      return allSongRequests.filter(r => r.sessionId === session._id).length;
    }), 0);

    return {
      timeRange: args.timeRange || "all_time",
      startTime,
      endTime: now,
      
      // Session metrics
      totalSessions,
      activeSessions,
      completedSessions,
      scheduledSessions: scheduledSessions.length,
      averageSessionDuration: Math.round(averageSessionDuration / (1000 * 60)), // in minutes
      
      // Request metrics
      totalRequests,
      approvedRequests,
      playedRequests,
      approvalRate: Math.round(approvalRate * 100) / 100,
      playRate: Math.round(playRate * 100) / 100,
      
      // Engagement metrics
      totalEvents,
      uniqueUsers,
      averageEventsPerSession: Math.round(averageEventsPerSession * 100) / 100,
      peakListeners,
      peakRequests,
      
      // Content analysis
      topGenres,
      topSongs,
      
      // Trends
      monthlyStats: monthlyStats.sort((a, b) => a.month.localeCompare(b.month)),
      
      lastUpdated: now,
    };
  },
});

// Get session comparison analytics
export const getSessionComparison = query({
  args: {
    sessionIds: v.array(v.id("sessions")),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const sessionComparisons = await Promise.all(
      args.sessionIds.map(async (sessionId) => {
        const session = await ctx.db.get(sessionId);
        if (!session || session.userId !== userId) {
          return null;
        }

        // Get analytics for this session
        const events = await ctx.db
          .query("analyticsEvents")
          .withIndex("by_session", (q) => q.eq("sessionId", sessionId))
          .collect();

        const requests = await ctx.db
          .query("songRequests")
          .withIndex("by_session", (q) => q.eq("sessionId", sessionId))
          .collect();

        const duration = session.endedAt ? session.endedAt - session.createdAt : Date.now() - session.createdAt;
        const uniqueListeners = new Set(events.map(e => e.userId).filter(Boolean)).size;
        const approvedRequests = requests.filter(r => r.status === "approved" || r.status === "played").length;

        return {
          sessionId,
          name: session.name,
          createdAt: session.createdAt,
          duration: Math.round(duration / (1000 * 60)), // in minutes
          totalRequests: requests.length,
          approvedRequests,
          approvalRate: requests.length > 0 ? (approvedRequests / requests.length) * 100 : 0,
          uniqueListeners,
          totalEvents: events.length,
          engagementRate: uniqueListeners > 0 ? events.length / uniqueListeners : 0,
        };
      })
    );

    return sessionComparisons.filter(Boolean);
  },
});

// Get audience retention analytics
export const getAudienceRetentionAnalytics = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session || session.userId !== userId) {
      throw new Error("Session not found or access denied");
    }

    // Get connection events for this session
    const connections = await ctx.db
      .query("sessionConnections")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    // Get analytics events
    const events = await ctx.db
      .query("analyticsEvents")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    // Calculate retention metrics
    const sessionDuration = session.endedAt ? session.endedAt - session.createdAt : Date.now() - session.createdAt;
    const intervalMinutes = 5; // 5-minute intervals
    const totalIntervals = Math.ceil(sessionDuration / (intervalMinutes * 60 * 1000));

    const retentionData = [];
    for (let i = 0; i < totalIntervals; i++) {
      const intervalStart = session.createdAt + (i * intervalMinutes * 60 * 1000);
      const intervalEnd = intervalStart + (intervalMinutes * 60 * 1000);

      const activeConnections = connections.filter(conn => 
        conn.joinedAt <= intervalEnd && 
        (!conn.leftAt || conn.leftAt >= intervalStart)
      );

      const intervalEvents = events.filter(event => 
        event.timestamp >= intervalStart && event.timestamp < intervalEnd
      );

      retentionData.push({
        interval: i,
        startTime: intervalStart,
        endTime: intervalEnd,
        activeUsers: activeConnections.length,
        events: intervalEvents.length,
        engagementScore: activeConnections.length > 0 ? intervalEvents.length / activeConnections.length : 0,
      });
    }

    return {
      sessionId: args.sessionId,
      sessionName: session.name,
      sessionDuration: Math.round(sessionDuration / (1000 * 60)), // in minutes
      totalIntervals,
      intervalMinutes,
      retentionData,
      peakAudience: Math.max(...retentionData.map(d => d.activeUsers), 0),
      averageAudience: retentionData.length > 0 ? 
        Math.round(retentionData.reduce((sum, d) => sum + d.activeUsers, 0) / retentionData.length) : 0,
      lastUpdated: Date.now(),
    };
  },
});
