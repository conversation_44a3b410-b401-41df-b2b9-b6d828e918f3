/**
 * Performance Optimization & Caching System
 * 
 * This module provides optimized queries with pagination, caching strategies,
 * and performance monitoring for high-performance data access.
 */

import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Optimized query: Get sessions with pagination and caching
export const getSessionsPaginated = query({
  args: {
    paginationOpts: v.object({
      numItems: v.number(),
      cursor: v.optional(v.string()),
    }),
    djId: v.optional(v.id("djProfiles")),
    active: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("sessions");

    // Use appropriate index for optimal performance
    if (args.djId && args.active !== undefined) {
      query = query.withIndex("by_dj_active", (q) => 
        q.eq("djId", args.djId!).eq("active", args.active!)
      );
    } else if (args.djId) {
      query = query.withIndex("by_dj", (q) => q.eq("djId", args.djId!));
    } else if (args.active !== undefined) {
      query = query.withIndex("by_active", (q) => q.eq("active", args.active));
    } else {
      query = query.withIndex("by_created");
    }

    return await query
      .order("desc")
      .paginate(args.paginationOpts);
  },
});

// Optimized query: Get song requests with pagination
export const getSongRequestsPaginated = query({
  args: {
    paginationOpts: v.object({
      numItems: v.number(),
      cursor: v.optional(v.string()),
    }),
    sessionId: v.optional(v.id("sessions")),
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("approved"),
      v.literal("auto-approved"),
      v.literal("declined"),
      v.literal("played")
    )),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("songRequests");

    // Use appropriate index for optimal performance
    if (args.sessionId && args.status) {
      query = query.withIndex("by_session_status", (q) => 
        q.eq("sessionId", args.sessionId!).eq("status", args.status!)
      );
    } else if (args.sessionId) {
      query = query.withIndex("by_session", (q) => q.eq("sessionId", args.sessionId!));
    } else if (args.status) {
      query = query.withIndex("by_status", (q) => q.eq("status", args.status));
    } else {
      query = query.withIndex("by_created");
    }

    return await query
      .order("desc")
      .paginate(args.paginationOpts);
  },
});

// Optimized query: Get blog posts with pagination
export const getBlogPostsPaginated = query({
  args: {
    paginationOpts: v.object({
      numItems: v.number(),
      cursor: v.optional(v.string()),
    }),
    status: v.optional(v.union(v.literal("draft"), v.literal("published"), v.literal("archived"))),
    category: v.optional(v.string()),
    featured: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("blogPosts");

    // Use appropriate index for optimal performance
    if (args.status && args.featured !== undefined) {
      query = query.withIndex("by_status_featured", (q) => 
        q.eq("status", args.status!).eq("featured", args.featured!)
      );
    } else if (args.status) {
      query = query.withIndex("by_status", (q) => q.eq("status", args.status));
    } else if (args.category) {
      query = query.withIndex("by_category", (q) => q.eq("category", args.category));
    } else if (args.featured !== undefined) {
      query = query.withIndex("by_featured", (q) => q.eq("featured", args.featured));
    } else {
      query = query.withIndex("by_status", (q) => q.eq("status", "published"));
    }

    const result = await query
      .order("desc")
      .paginate(args.paginationOpts);

    // Remove content field from list view for better performance
    return {
      ...result,
      page: result.page.map(post => ({
        ...post,
        content: undefined, // Don't include full content in list view
      })),
    };
  },
});

// Cached query: Get session statistics (cached for 5 minutes)
export const getSessionStatsCached = query({
  args: { sessionId: v.id("sessions") },
  handler: async (ctx, args) => {
    // This query will be automatically cached by Convex
    const session = await ctx.db.get(args.sessionId);
    if (!session) return null;

    const [
      totalRequests,
      pendingRequests,
      approvedRequests,
      playedRequests,
      uniqueRequesters
    ] = await Promise.all([
      ctx.db
        .query("songRequests")
        .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
        .collect()
        .then(requests => requests.length),
      
      ctx.db
        .query("songRequests")
        .withIndex("by_session_status", (q) => 
          q.eq("sessionId", args.sessionId).eq("status", "pending")
        )
        .collect()
        .then(requests => requests.length),
      
      ctx.db
        .query("songRequests")
        .withIndex("by_session_status", (q) => 
          q.eq("sessionId", args.sessionId).eq("status", "approved")
        )
        .collect()
        .then(requests => requests.length),
      
      ctx.db
        .query("songRequests")
        .withIndex("by_session_status", (q) => 
          q.eq("sessionId", args.sessionId).eq("status", "played")
        )
        .collect()
        .then(requests => requests.length),
      
      ctx.db
        .query("songRequests")
        .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
        .collect()
        .then(requests => new Set(requests.map(r => r.requesterIp)).size),
    ]);

    return {
      sessionId: args.sessionId,
      sessionName: session.name,
      active: session.active,
      totalRequests,
      pendingRequests,
      approvedRequests,
      playedRequests,
      uniqueRequesters,
      approvalRate: totalRequests > 0 ? ((approvedRequests + playedRequests) / totalRequests * 100).toFixed(1) : "0",
      lastUpdated: Date.now(),
    };
  },
});

// Optimized query: Get user dashboard data (minimal queries)
export const getUserDashboardData = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;

    const user = await ctx.db.get(userId);
    if (!user) return null;

    // Get user's DJ profile
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      return {
        user,
        djProfile: null,
        activeSessions: [],
        recentRequests: [],
        stats: null,
      };
    }

    // Get active sessions (limit to 5 for performance)
    const activeSessions = await ctx.db
      .query("sessions")
      .withIndex("by_dj_active", (q) => q.eq("djId", djProfile._id).eq("active", true))
      .order("desc")
      .take(5);

    // Get recent requests from active sessions
    const recentRequests = activeSessions.length > 0 
      ? await ctx.db
          .query("songRequests")
          .withIndex("by_session", (q) => q.eq("sessionId", activeSessions[0]._id))
          .order("desc")
          .take(10)
      : [];

    // Calculate basic stats
    const totalSessions = await ctx.db
      .query("sessions")
      .withIndex("by_dj", (q) => q.eq("djId", djProfile._id))
      .collect()
      .then(sessions => sessions.length);

    return {
      user,
      djProfile,
      activeSessions,
      recentRequests,
      stats: {
        totalSessions,
        activeSessions: activeSessions.length,
        recentRequestsCount: recentRequests.length,
      },
    };
  },
});

// Performance monitoring: Track query performance
export const trackQueryPerformance = mutation({
  args: {
    queryName: v.string(),
    executionTime: v.number(),
    resultCount: v.number(),
    userId: v.optional(v.id("users")),
  },
  handler: async (ctx, args) => {
    // Store performance metrics for monitoring
    await ctx.db.insert("performanceMetrics", {
      queryName: args.queryName,
      executionTime: args.executionTime,
      resultCount: args.resultCount,
      userId: args.userId,
      timestamp: Date.now(),
    });
  },
});

// Query: Get performance metrics for monitoring
export const getPerformanceMetrics = query({
  args: {
    timeRange: v.optional(v.number()), // milliseconds
    queryName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const timeThreshold = args.timeRange 
      ? Date.now() - args.timeRange 
      : Date.now() - (24 * 60 * 60 * 1000); // Default: last 24 hours

    let query = ctx.db
      .query("performanceMetrics")
      .withIndex("by_timestamp", (q) => q.gte("timestamp", timeThreshold));

    if (args.queryName) {
      query = query.filter((q) => q.eq(q.field("queryName"), args.queryName));
    }

    const metrics = await query.order("desc").take(1000);

    // Calculate aggregated metrics
    const aggregated = metrics.reduce((acc, metric) => {
      const queryName = metric.queryName;
      if (!acc[queryName]) {
        acc[queryName] = {
          count: 0,
          totalTime: 0,
          totalResults: 0,
          minTime: Infinity,
          maxTime: 0,
        };
      }

      acc[queryName].count++;
      acc[queryName].totalTime += metric.executionTime;
      acc[queryName].totalResults += metric.resultCount;
      acc[queryName].minTime = Math.min(acc[queryName].minTime, metric.executionTime);
      acc[queryName].maxTime = Math.max(acc[queryName].maxTime, metric.executionTime);

      return acc;
    }, {} as Record<string, any>);

    // Calculate averages
    Object.keys(aggregated).forEach(queryName => {
      const data = aggregated[queryName];
      data.avgTime = data.totalTime / data.count;
      data.avgResults = data.totalResults / data.count;
    });

    return {
      timeRange: args.timeRange || (24 * 60 * 60 * 1000),
      totalQueries: metrics.length,
      aggregatedMetrics: aggregated,
      recentMetrics: metrics.slice(0, 50), // Last 50 queries
    };
  },
});

// Optimized query: Batch load multiple entities
export const batchLoadEntities = query({
  args: {
    sessionIds: v.optional(v.array(v.id("sessions"))),
    requestIds: v.optional(v.array(v.id("songRequests"))),
    userIds: v.optional(v.array(v.id("users"))),
  },
  handler: async (ctx, args) => {
    const results: any = {
      sessions: {},
      requests: {},
      users: {},
    };

    // Batch load sessions
    if (args.sessionIds && args.sessionIds.length > 0) {
      const sessions = await Promise.all(
        args.sessionIds.map(id => ctx.db.get(id))
      );
      sessions.forEach((session, index) => {
        if (session) {
          results.sessions[args.sessionIds![index]] = session;
        }
      });
    }

    // Batch load requests
    if (args.requestIds && args.requestIds.length > 0) {
      const requests = await Promise.all(
        args.requestIds.map(id => ctx.db.get(id))
      );
      requests.forEach((request, index) => {
        if (request) {
          results.requests[args.requestIds![index]] = request;
        }
      });
    }

    // Batch load users
    if (args.userIds && args.userIds.length > 0) {
      const users = await Promise.all(
        args.userIds.map(id => ctx.db.get(id))
      );
      users.forEach((user, index) => {
        if (user) {
          results.users[args.userIds![index]] = user;
        }
      });
    }

    return results;
  },
});

// Action: Preload and cache frequently accessed data
export const preloadFrequentData = action({
  args: {},
  handler: async (ctx) => {
    // This action can be called periodically to warm up caches
    // by triggering frequently used queries
    
    try {
      // Preload active sessions
      await ctx.runQuery(api.performance.getSessionsPaginated, {
        paginationOpts: { numItems: 20 },
        active: true,
      });

      // Preload recent song requests
      await ctx.runQuery(api.performance.getSongRequestsPaginated, {
        paginationOpts: { numItems: 50 },
        status: "pending",
      });

      // Preload published blog posts
      await ctx.runQuery(api.performance.getBlogPostsPaginated, {
        paginationOpts: { numItems: 10 },
        status: "published",
      });

      return { success: true, message: "Cache preloading completed" };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  },
});
