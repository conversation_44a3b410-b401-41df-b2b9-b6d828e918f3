/**
 * Security utilities for IP-based access control
 */

// Get allowed IPs from environment variables
export const getAllowedIPs = (): string[] => {
  const envIPs = process.env.ALLOWED_IP_ADDRESSES || "127.0.0.1,::1";
  return envIPs.split(",").map(ip => ip.trim());
};

// Check if request is from allowed IP
export const isIPAllowed = (clientIP: string): boolean => {
  const allowedIPs = getAllowedIPs();
  
  // In development mode, be more permissive
  if (process.env.NODE_ENV === "development") {
    return true; // Allow all in development
  }
  
  // Check if IP is in whitelist
  return allowedIPs.some(allowedIP => 
    clientIP === allowedIP || clientIP.includes(allowedIP)
  );
};

// Extract client IP from request headers
export const getClientIP = (request: Request): string => {
  return request.headers.get("x-forwarded-for") || 
         request.headers.get("x-real-ip") || 
         request.headers.get("cf-connecting-ip") || // Cloudflare
         "unknown";
};

// Security middleware for HTTP actions
export const checkIPAccess = (request: Request): boolean => {
  const clientIP = getClientIP(request);
  console.log("Request from IP:", clientIP);
  
  return isIPAllowed(clientIP);
};
