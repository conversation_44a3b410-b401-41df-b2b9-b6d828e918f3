/**
 * Enhanced Security & Validation System
 * 
 * This module provides comprehensive security measures including
 * rate limiting, input validation, and access control.
 */

import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Rate limiting configuration
const RATE_LIMITS = {
  songRequest: { maxRequests: 10, windowMs: 60 * 1000 }, // 10 requests per minute
  sessionCreation: { maxRequests: 5, windowMs: 60 * 60 * 1000 }, // 5 sessions per hour
  fileUpload: { maxRequests: 20, windowMs: 60 * 60 * 1000 }, // 20 uploads per hour
  apiCall: { maxRequests: 100, windowMs: 60 * 1000 }, // 100 API calls per minute
};

// Mutation: Track rate limiting
export const trackRateLimit = mutation({
  args: {
    action: v.string(),
    identifier: v.string(), // IP address or user ID
    metadata: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    await ctx.db.insert("rateLimitTracking", {
      action: args.action,
      identifier: args.identifier,
      timestamp: now,
      metadata: args.metadata,
    });

    return { success: true, timestamp: now };
  },
});

// Query: Check rate limit
export const checkRateLimit = query({
  args: {
    action: v.string(),
    identifier: v.string(),
  },
  handler: async (ctx, args) => {
    const limit = RATE_LIMITS[args.action as keyof typeof RATE_LIMITS];
    if (!limit) {
      return { allowed: true, remaining: Infinity };
    }

    const windowStart = Date.now() - limit.windowMs;
    
    const recentRequests = await ctx.db
      .query("rateLimitTracking")
      .withIndex("by_action_identifier", (q) => 
        q.eq("action", args.action).eq("identifier", args.identifier)
      )
      .filter((q) => q.gte(q.field("timestamp"), windowStart))
      .collect();

    const requestCount = recentRequests.length;
    const allowed = requestCount < limit.maxRequests;
    const remaining = Math.max(0, limit.maxRequests - requestCount);

    return {
      allowed,
      remaining,
      resetTime: windowStart + limit.windowMs,
      limit: limit.maxRequests,
    };
  },
});

// Validation functions
export const validateSongRequest = mutation({
  args: {
    sessionId: v.id("sessions"),
    songTitle: v.string(),
    artistName: v.string(),
    requesterName: v.string(),
    requesterIp: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const errors: string[] = [];

    // Validate session exists and is active
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      errors.push("Session not found");
    } else if (!session.active) {
      errors.push("Session is not active");
    } else if (!session.acceptRequests) {
      errors.push("Session is not accepting requests");
    }

    // Validate input lengths and content
    if (!args.songTitle.trim() || args.songTitle.length > 200) {
      errors.push("Song title must be between 1 and 200 characters");
    }

    if (!args.artistName.trim() || args.artistName.length > 200) {
      errors.push("Artist name must be between 1 and 200 characters");
    }

    if (!args.requesterName.trim() || args.requesterName.length > 100) {
      errors.push("Requester name must be between 1 and 100 characters");
    }

    // Check for inappropriate content (basic filter)
    const inappropriateWords = ['spam', 'test123', 'admin', 'system'];
    const content = `${args.songTitle} ${args.artistName} ${args.requesterName}`.toLowerCase();
    
    if (inappropriateWords.some(word => content.includes(word))) {
      errors.push("Request contains inappropriate content");
    }

    // Check rate limiting if IP provided
    if (args.requesterIp && session) {
      const rateCheck = await ctx.runQuery(api.security.checkRateLimit, {
        action: "songRequest",
        identifier: args.requesterIp,
      });

      if (!rateCheck.allowed) {
        errors.push("Rate limit exceeded. Please wait before making another request.");
      }

      // Check session-specific limits
      if (session.maxRequestsPerUser) {
        const userRequests = await ctx.db
          .query("songRequests")
          .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
          .filter((q) => q.eq(q.field("requesterIp"), args.requesterIp!))
          .collect();

        if (userRequests.length >= session.maxRequestsPerUser) {
          errors.push(`Maximum ${session.maxRequestsPerUser} requests per user exceeded`);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },
});

// Mutation: Validate session creation
export const validateSessionCreation = mutation({
  args: {
    name: v.string(),
    djId: v.id("djProfiles"),
    config: v.any(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return { isValid: false, errors: ["Authentication required"] };
    }

    const errors: string[] = [];

    // Validate DJ profile ownership
    const djProfile = await ctx.db.get(args.djId);
    if (!djProfile) {
      errors.push("DJ profile not found");
    } else if (djProfile.userId !== userId) {
      errors.push("Unauthorized: DJ profile does not belong to current user");
    }

    // Validate session name
    if (!args.name.trim() || args.name.length > 100) {
      errors.push("Session name must be between 1 and 100 characters");
    }

    // Check for duplicate session names for this DJ
    if (djProfile) {
      const existingSessions = await ctx.db
        .query("sessions")
        .withIndex("by_dj", (q) => q.eq("djId", args.djId))
        .filter((q) => q.eq(q.field("name"), args.name.trim()))
        .collect();

      if (existingSessions.length > 0) {
        errors.push("A session with this name already exists");
      }
    }

    // Validate configuration
    if (args.config) {
      if (args.config.maxRequestsPerUser && args.config.maxRequestsPerUser < 1) {
        errors.push("Max requests per user must be at least 1");
      }

      if (args.config.durationMinutes && args.config.durationMinutes < 1) {
        errors.push("Session duration must be at least 1 minute");
      }

      if (args.config.maxAudienceSize && args.config.maxAudienceSize < 1) {
        errors.push("Max audience size must be at least 1");
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },
});

// Query: Security audit log
export const getSecurityAuditLog = query({
  args: {
    timeRange: v.optional(v.number()),
    eventType: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // TODO: Add admin role check here

    const timeThreshold = args.timeRange 
      ? Date.now() - args.timeRange 
      : Date.now() - (24 * 60 * 60 * 1000); // Default: last 24 hours

    let query = ctx.db
      .query("securityEvents")
      .withIndex("by_timestamp", (q) => q.gte("timestamp", timeThreshold));

    if (args.eventType) {
      query = query.filter((q) => q.eq(q.field("eventType"), args.eventType));
    }

    const events = await query
      .order("desc")
      .take(args.limit || 100);

    return {
      events,
      totalCount: events.length,
      timeRange: args.timeRange || (24 * 60 * 60 * 1000),
    };
  },
});

// Mutation: Log security event
export const logSecurityEvent = mutation({
  args: {
    eventType: v.string(),
    severity: v.union(v.literal("low"), v.literal("medium"), v.literal("high"), v.literal("critical")),
    description: v.string(),
    metadata: v.optional(v.any()),
    ipAddress: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);

    await ctx.db.insert("securityEvents", {
      eventType: args.eventType,
      severity: args.severity,
      description: args.description,
      metadata: args.metadata,
      userId,
      ipAddress: args.ipAddress,
      timestamp: Date.now(),
    });

    // If critical event, could trigger alerts here
    if (args.severity === "critical") {
      console.warn(`CRITICAL SECURITY EVENT: ${args.description}`, {
        userId,
        eventType: args.eventType,
        metadata: args.metadata,
      });
    }

    return { success: true };
  },
});

// Query: Get user permissions
export const getUserPermissions = query({
  args: { userId: v.optional(v.id("users")) },
  handler: async (ctx, args) => {
    const currentUserId = await getAuthUserId(ctx);
    if (!currentUserId) {
      throw new Error("Authentication required");
    }

    const targetUserId = args.userId || currentUserId;
    const user = await ctx.db.get(targetUserId);
    
    if (!user) {
      return null;
    }

    // Get DJ profile if exists
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", targetUserId))
      .first();

    // Basic permissions for all authenticated users
    const permissions = {
      canCreateSessions: !!djProfile,
      canManageSessions: !!djProfile,
      canViewAnalytics: !!djProfile,
      canUploadFiles: true,
      canMakeRequests: true,
      canVoteOnRequests: true,
      canReact: true,
      canChat: true,
      isAdmin: false, // TODO: Implement admin role system
      isDJ: !!djProfile,
    };

    // Enhanced permissions for DJs
    if (djProfile) {
      permissions.canViewAnalytics = true;
      permissions.canManageSessions = true;
    }

    return {
      userId: targetUserId,
      permissions,
      djProfile,
    };
  },
});

// Action: Sanitize user input
export const sanitizeInput = action({
  args: {
    input: v.string(),
    type: v.union(v.literal("text"), v.literal("html"), v.literal("url")),
  },
  handler: async (ctx, args) => {
    let sanitized = args.input;

    switch (args.type) {
      case "text":
        // Remove potentially dangerous characters
        sanitized = sanitized
          .replace(/[<>]/g, '') // Remove HTML brackets
          .replace(/javascript:/gi, '') // Remove javascript: URLs
          .replace(/on\w+=/gi, '') // Remove event handlers
          .trim();
        break;

      case "html":
        // Basic HTML sanitization (in production, use a proper library)
        sanitized = sanitized
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
          .replace(/javascript:/gi, '')
          .replace(/on\w+=/gi, '');
        break;

      case "url":
        // Validate and sanitize URLs
        try {
          const url = new URL(sanitized);
          if (!['http:', 'https:'].includes(url.protocol)) {
            sanitized = '';
          }
        } catch {
          sanitized = '';
        }
        break;
    }

    return {
      original: args.input,
      sanitized,
      isModified: args.input !== sanitized,
    };
  },
});

// Query: Check content moderation
export const checkContentModeration = query({
  args: {
    content: v.string(),
    type: v.union(v.literal("song_request"), v.literal("chat_message"), v.literal("session_name")),
  },
  handler: async (ctx, args) => {
    // Basic content moderation rules
    const blockedWords = [
      'spam', 'scam', 'hack', 'cheat', 'exploit',
      // Add more as needed
    ];

    const suspiciousPatterns = [
      /(.)\1{10,}/, // Repeated characters
      /[A-Z]{20,}/, // Excessive caps
      /\b\d{4}[-\s]\d{4}[-\s]\d{4}[-\s]\d{4}\b/, // Credit card patterns
    ];

    const content = args.content.toLowerCase();
    const flags: string[] = [];

    // Check for blocked words
    blockedWords.forEach(word => {
      if (content.includes(word)) {
        flags.push(`Contains blocked word: ${word}`);
      }
    });

    // Check for suspicious patterns
    suspiciousPatterns.forEach((pattern, index) => {
      if (pattern.test(args.content)) {
        flags.push(`Suspicious pattern detected (${index + 1})`);
      }
    });

    // Check length limits based on type
    const lengthLimits = {
      song_request: 200,
      chat_message: 500,
      session_name: 100,
    };

    if (args.content.length > lengthLimits[args.type]) {
      flags.push(`Content exceeds maximum length (${lengthLimits[args.type]} characters)`);
    }

    return {
      content: args.content,
      type: args.type,
      isAllowed: flags.length === 0,
      flags,
      riskScore: flags.length * 25, // Simple risk scoring
    };
  },
});
