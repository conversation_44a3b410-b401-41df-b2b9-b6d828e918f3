/**
 * Session Templates System
 * 
 * Allows DJs to create, save, and reuse session configurations including
 * settings, blocked genres, request limits, auto-approval rules, and branding.
 */

import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Create a new session template
export const createSessionTemplate = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    templateConfig: v.object({
      // Basic session settings
      acceptRequests: v.optional(v.boolean()),
      autoApproval: v.optional(v.boolean()),
      maxRequestsPerTimeframe: v.optional(v.number()),
      timeframeMinutes: v.optional(v.number()),
      durationMinutes: v.optional(v.number()),
      
      // Content filtering
      blockedGenres: v.optional(v.array(v.string())),
      allowedGenres: v.optional(v.array(v.string())),
      explicitContentAllowed: v.optional(v.boolean()),
      
      // Audience management
      maxAudienceSize: v.optional(v.number()),
      requireApprovalToJoin: v.optional(v.boolean()),
      allowAnonymousRequests: v.optional(v.boolean()),
      
      // Geographic restrictions
      allowedCountries: v.optional(v.array(v.string())),
      blockedCountries: v.optional(v.array(v.string())),
      
      // Branding and customization
      sessionBranding: v.optional(v.object({
        primaryColor: v.optional(v.string()),
        secondaryColor: v.optional(v.string()),
        logoStorageId: v.optional(v.id("_storage")),
        backgroundImageStorageId: v.optional(v.id("_storage")),
        customMessage: v.optional(v.string()),
      })),
      
      // Advanced features
      enableVoting: v.optional(v.boolean()),
      enableChat: v.optional(v.boolean()),
      enableReactions: v.optional(v.boolean()),
      moderationLevel: v.optional(v.union(
        v.literal("none"),
        v.literal("basic"),
        v.literal("strict"),
      )),
    }),
    isPublic: v.optional(v.boolean()),
    tags: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required to create session template");
    }

    // Verify user has a DJ profile
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      throw new Error("DJ profile required to create session templates");
    }

    // Create the template
    const templateId = await ctx.db.insert("sessionTemplates", {
      userId,
      djProfileId: djProfile._id,
      name: args.name,
      description: args.description,
      templateConfig: args.templateConfig,
      isPublic: args.isPublic || false,
      tags: args.tags || [],
      usageCount: 0,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      isActive: true,
    });

    return templateId;
  },
});

// Get user's session templates
export const getUserSessionTemplates = query({
  args: {
    includePublic: v.optional(v.boolean()),
    tags: v.optional(v.array(v.string())),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    let query = ctx.db
      .query("sessionTemplates")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .filter((q) => q.eq(q.field("isActive"), true));

    const userTemplates = await query
      .order("desc")
      .take(args.limit || 50);

    let allTemplates = userTemplates;

    // Include public templates if requested
    if (args.includePublic) {
      const publicTemplates = await ctx.db
        .query("sessionTemplates")
        .withIndex("by_public", (q) => q.eq("isPublic", true))
        .filter((q) => 
          q.and(
            q.eq(q.field("isActive"), true),
            q.neq(q.field("userId"), userId)
          )
        )
        .order("desc")
        .take(20);

      allTemplates = [...userTemplates, ...publicTemplates];
    }

    // Filter by tags if specified
    if (args.tags && args.tags.length > 0) {
      allTemplates = allTemplates.filter(template => 
        args.tags!.some(tag => template.tags.includes(tag))
      );
    }

    // Get creator details for public templates
    const templatesWithCreators = await Promise.all(
      allTemplates.map(async (template) => {
        if (template.userId !== userId) {
          const creator = await ctx.db.get(template.userId);
          const creatorDjProfile = await ctx.db.get(template.djProfileId);
          return {
            ...template,
            creator: {
              name: creator?.name || "Unknown",
              djName: creatorDjProfile?.displayName || "Unknown DJ",
            },
          };
        }
        return { ...template, creator: null };
      })
    );

    return templatesWithCreators;
  },
});

// Get a specific session template
export const getSessionTemplate = query({
  args: {
    templateId: v.id("sessionTemplates"),
  },
  handler: async (ctx, args) => {
    const template = await ctx.db.get(args.templateId);
    if (!template || !template.isActive) {
      return null;
    }

    const userId = await getAuthUserId(ctx);
    
    // Check access permissions
    if (!template.isPublic && template.userId !== userId) {
      throw new Error("Access denied to private template");
    }

    // Get creator details
    const creator = await ctx.db.get(template.userId);
    const creatorDjProfile = await ctx.db.get(template.djProfileId);

    return {
      ...template,
      creator: {
        name: creator?.name || "Unknown",
        djName: creatorDjProfile?.displayName || "Unknown DJ",
      },
    };
  },
});

// Update a session template
export const updateSessionTemplate = mutation({
  args: {
    templateId: v.id("sessionTemplates"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    templateConfig: v.optional(v.any()),
    isPublic: v.optional(v.boolean()),
    tags: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const template = await ctx.db.get(args.templateId);
    if (!template) {
      throw new Error("Template not found");
    }

    if (template.userId !== userId) {
      throw new Error("Access denied: You can only edit your own templates");
    }

    const updates: any = {
      updatedAt: Date.now(),
    };

    if (args.name !== undefined) updates.name = args.name;
    if (args.description !== undefined) updates.description = args.description;
    if (args.templateConfig !== undefined) updates.templateConfig = args.templateConfig;
    if (args.isPublic !== undefined) updates.isPublic = args.isPublic;
    if (args.tags !== undefined) updates.tags = args.tags;

    await ctx.db.patch(args.templateId, updates);

    return { success: true };
  },
});

// Delete a session template
export const deleteSessionTemplate = mutation({
  args: {
    templateId: v.id("sessionTemplates"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const template = await ctx.db.get(args.templateId);
    if (!template) {
      throw new Error("Template not found");
    }

    if (template.userId !== userId) {
      throw new Error("Access denied: You can only delete your own templates");
    }

    // Soft delete
    await ctx.db.patch(args.templateId, {
      isActive: false,
      deletedAt: Date.now(),
    });

    return { success: true };
  },
});

// Use a template to create a session
export const createSessionFromTemplate = mutation({
  args: {
    templateId: v.id("sessionTemplates"),
    sessionName: v.string(),
    overrides: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const template = await ctx.db.get(args.templateId);
    if (!template || !template.isActive) {
      throw new Error("Template not found or inactive");
    }

    // Check access permissions
    if (!template.isPublic && template.userId !== userId) {
      throw new Error("Access denied to private template");
    }

    // Merge template config with overrides
    const sessionConfig = {
      ...template.templateConfig,
      ...args.overrides,
    };

    // Create session using the template configuration
    const sessionId = await ctx.db.insert("sessions", {
      userId,
      name: args.sessionName,
      templateId: args.templateId,
      ...sessionConfig,
      active: false, // Start inactive, DJ can activate when ready
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Increment template usage count
    await ctx.db.patch(args.templateId, {
      usageCount: template.usageCount + 1,
    });

    return sessionId;
  },
});

// Get popular public templates
export const getPopularTemplates = query({
  args: {
    limit: v.optional(v.number()),
    category: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const templates = await ctx.db
      .query("sessionTemplates")
      .withIndex("by_public", (q) => q.eq("isPublic", true))
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("desc")
      .take(args.limit || 20);

    // Sort by usage count
    const sortedTemplates = templates.sort((a, b) => b.usageCount - a.usageCount);

    // Get creator details
    const templatesWithCreators = await Promise.all(
      sortedTemplates.map(async (template) => {
        const creator = await ctx.db.get(template.userId);
        const creatorDjProfile = await ctx.db.get(template.djProfileId);
        return {
          ...template,
          creator: {
            name: creator?.name || "Unknown",
            djName: creatorDjProfile?.displayName || "Unknown DJ",
          },
        };
      })
    );

    return templatesWithCreators;
  },
});
