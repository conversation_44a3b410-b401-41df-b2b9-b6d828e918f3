/**
 * Subscription Plan Management
 * 
 * This module handles all database operations for subscription plans including
 * creation, reading, updating, and deletion with Stripe integration and
 * feature access control.
 */

import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { validateSubscriptionPlan, isValidCurrency } from "./validation";

// Query: Get all active subscription plans (public)
export const getActiveSubscriptionPlans = query({
  args: {},
  handler: async (ctx) => {
    const plans = await ctx.db
      .query("subscriptionPlans")
      .withIndex("by_active", (q) => q.eq("active", true))
      .order("asc") // Order by price (assuming lower prices first)
      .collect();

    return plans.map(plan => ({
      id: plan._id,
      name: plan.name,
      description: plan.description,
      priceAmount: plan.priceAmount,
      priceCurrency: plan.priceCurrency,
      durationMinutes: plan.durationMinutes,
      maxRequestsPerSession: plan.maxRequestsPerSession,
      stripePriceId: plan.stripePriceId,
      details: getPlanDetails(plan.durationMinutes, plan.maxRequestsPerSession),
    }));
  },
});

// Query: Get subscription plan by ID
export const getSubscriptionPlanById = query({
  args: {
    planId: v.id("subscriptionPlans"),
  },
  handler: async (ctx, args) => {
    const plan = await ctx.db.get(args.planId);
    if (!plan) {
      return null;
    }

    return {
      ...plan,
      details: getPlanDetails(plan.durationMinutes, plan.maxRequestsPerSession),
    };
  },
});

// Query: Get subscription plan by Stripe price ID
export const getSubscriptionPlanByStripeId = query({
  args: {
    stripePriceId: v.string(),
  },
  handler: async (ctx, args) => {
    const plan = await ctx.db
      .query("subscriptionPlans")
      .withIndex("by_stripe_price", (q) => q.eq("stripePriceId", args.stripePriceId))
      .first();

    if (!plan) {
      return null;
    }

    return {
      ...plan,
      details: getPlanDetails(plan.durationMinutes, plan.maxRequestsPerSession),
    };
  },
});

// Query: Get all subscription plans (admin only)
export const getAllSubscriptionPlans = query({
  args: {
    includeInactive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // TODO: Add admin role check when admin system is implemented
    // For now, any authenticated user can view all plans for development

    let plans;
    if (args.includeInactive) {
      plans = await ctx.db.query("subscriptionPlans").collect();
    } else {
      plans = await ctx.db
        .query("subscriptionPlans")
        .withIndex("by_active", (q) => q.eq("active", true))
        .collect();
    }

    return plans.map(plan => ({
      ...plan,
      details: getPlanDetails(plan.durationMinutes, plan.maxRequestsPerSession),
    }));
  },
});

// Mutation: Create subscription plan (admin only)
export const createSubscriptionPlan = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    priceAmount: v.number(),
    priceCurrency: v.string(),
    durationMinutes: v.number(),
    maxRequestsPerSession: v.union(v.number(), v.literal("unlimited")),
    stripePriceId: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required to create subscription plan");
    }

    // TODO: Add admin role check when admin system is implemented
    // For now, any authenticated user can create plans for development

    // Validate subscription plan data
    if (args.name.trim().length === 0) {
      throw new Error("Plan name is required");
    }
    if (args.priceAmount < 0) {
      throw new Error("Price amount cannot be negative");
    }
    if (args.durationMinutes <= 0) {
      throw new Error("Duration minutes must be positive");
    }
    if (typeof args.maxRequestsPerSession === "number" && args.maxRequestsPerSession <= 0) {
      throw new Error("Max requests per session must be positive");
    }

    // Check if plan with same name already exists
    const existingPlan = await ctx.db
      .query("subscriptionPlans")
      .withIndex("by_name", (q) => q.eq("name", args.name.trim()))
      .first();

    if (existingPlan) {
      throw new Error("Subscription plan with this name already exists");
    }

    // Check if Stripe price ID is already used
    const existingStripePrice = await ctx.db
      .query("subscriptionPlans")
      .withIndex("by_stripe_price", (q) => q.eq("stripePriceId", args.stripePriceId))
      .first();

    if (existingStripePrice) {
      throw new Error("Stripe price ID is already in use");
    }

    const now = Date.now();
    const planId = await ctx.db.insert("subscriptionPlans", {
      name: args.name.trim(),
      description: args.description?.trim(),
      priceAmount: args.priceAmount,
      priceCurrency: args.priceCurrency.toLowerCase(),
      durationMinutes: args.durationMinutes,
      maxRequestsPerSession: args.maxRequestsPerSession,
      stripePriceId: args.stripePriceId,
      active: true,
      createdAt: now,
      updatedAt: now,
    });

    return {
      planId,
      message: "Subscription plan created successfully",
    };
  },
});

// Mutation: Update subscription plan (admin only)
export const updateSubscriptionPlan = mutation({
  args: {
    planId: v.id("subscriptionPlans"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    priceAmount: v.optional(v.number()),
    priceCurrency: v.optional(v.string()),
    durationMinutes: v.optional(v.number()),
    maxRequestsPerSession: v.optional(v.union(v.number(), v.literal("unlimited"))),
    stripePriceId: v.optional(v.string()),
    active: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // TODO: Add admin role check when admin system is implemented

    const existingPlan = await ctx.db.get(args.planId);
    if (!existingPlan) {
      throw new Error("Subscription plan not found");
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: Date.now(),
    };

    // Validate and add fields
    if (args.name !== undefined) {
      if (args.name.trim().length === 0) {
        throw new Error("Plan name cannot be empty");
      }
      
      // Check for duplicate name (excluding current plan)
      const duplicateName = await ctx.db
        .query("subscriptionPlans")
        .withIndex("by_name", (q) => q.eq("name", args.name.trim()))
        .filter((q) => q.neq(q.field("_id"), args.planId))
        .first();

      if (duplicateName) {
        throw new Error("Plan name already exists");
      }

      updateData.name = args.name.trim();
    }

    if (args.description !== undefined) {
      updateData.description = args.description?.trim();
    }

    if (args.priceAmount !== undefined) {
      if (args.priceAmount < 0) {
        throw new Error("Price amount cannot be negative");
      }
      updateData.priceAmount = args.priceAmount;
    }

    if (args.priceCurrency !== undefined) {
      if (!isValidCurrency(args.priceCurrency)) {
        throw new Error(`Invalid currency: ${args.priceCurrency}`);
      }
      updateData.priceCurrency = args.priceCurrency.toLowerCase();
    }

    if (args.durationMinutes !== undefined) {
      if (args.durationMinutes <= 0) {
        throw new Error("Duration minutes must be positive");
      }
      updateData.durationMinutes = args.durationMinutes;
    }

    if (args.maxRequestsPerSession !== undefined) {
      if (typeof args.maxRequestsPerSession === "number" && args.maxRequestsPerSession <= 0) {
        throw new Error("Max requests per session must be positive");
      }
      updateData.maxRequestsPerSession = args.maxRequestsPerSession;
    }

    if (args.stripePriceId !== undefined) {
      // Check for duplicate Stripe price ID (excluding current plan)
      const duplicateStripeId = await ctx.db
        .query("subscriptionPlans")
        .withIndex("by_stripe_price", (q) => q.eq("stripePriceId", args.stripePriceId))
        .filter((q) => q.neq(q.field("_id"), args.planId))
        .first();

      if (duplicateStripeId) {
        throw new Error("Stripe price ID already in use");
      }

      updateData.stripePriceId = args.stripePriceId;
    }

    if (args.active !== undefined) {
      updateData.active = args.active;
    }

    await ctx.db.patch(args.planId, updateData);

    return {
      planId: args.planId,
      message: "Subscription plan updated successfully",
    };
  },
});

// Mutation: Delete subscription plan (admin only)
export const deleteSubscriptionPlan = mutation({
  args: {
    planId: v.id("subscriptionPlans"),
    confirmDelete: v.boolean(),
  },
  handler: async (ctx, args) => {
    if (!args.confirmDelete) {
      throw new Error("Delete confirmation required");
    }

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // TODO: Add admin role check when admin system is implemented

    const plan = await ctx.db.get(args.planId);
    if (!plan) {
      throw new Error("Subscription plan not found");
    }

    // Check if plan is in use by any subscriptions
    const activeSubscriptions = await ctx.db
      .query("djSubscriptions")
      .withIndex("by_plan", (q) => q.eq("planId", args.planId))
      .filter((q) => q.neq(q.field("status"), "canceled"))
      .first();

    if (activeSubscriptions) {
      throw new Error("Cannot delete plan with active subscriptions. Deactivate the plan instead.");
    }

    await ctx.db.delete(args.planId);

    return {
      success: true,
      message: "Subscription plan deleted successfully",
    };
  },
});

// Mutation: Initialize the four standard event-based plans
export const initializeStandardPlans = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // TODO: Add admin role check when admin system is implemented

    const standardPlans = [
      {
        name: "Free",
        description: "Free 20-minute session with basic features",
        priceAmount: 0,
        priceCurrency: "usd",
        durationMinutes: 20,
        maxRequestsPerSession: 3,
        stripePriceId: "price_free_plan",
      },
      {
        name: "24-Hour Pass",
        description: "24-hour access with 100 song requests",
        priceAmount: 999, // $9.99
        priceCurrency: "usd",
        durationMinutes: 1440, // 24 hours
        maxRequestsPerSession: 100,
        stripePriceId: "price_24_hour_pass",
      },
      {
        name: "48-Hour Pass",
        description: "48-hour access with 100 song requests",
        priceAmount: 1799, // $17.99
        priceCurrency: "usd",
        durationMinutes: 2880, // 48 hours
        maxRequestsPerSession: 100,
        stripePriceId: "price_48_hour_pass",
      },
      {
        name: "7-Day Pass",
        description: "7-day access with 100 song requests",
        priceAmount: 4999, // $49.99
        priceCurrency: "usd",
        durationMinutes: 10080, // 7 days
        maxRequestsPerSession: 100,
        stripePriceId: "price_7_day_pass",
      },
    ];

    const results = [];
    const now = Date.now();

    for (const plan of standardPlans) {
      // Check if plan already exists
      const existingPlan = await ctx.db
        .query("subscriptionPlans")
        .withIndex("by_name", (q) => q.eq("name", plan.name))
        .first();

      if (existingPlan) {
        results.push({
          name: plan.name,
          status: "exists",
          planId: existingPlan._id,
        });
        continue;
      }

      try {
        const planId = await ctx.db.insert("subscriptionPlans", {
          name: plan.name,
          description: plan.description,
          priceAmount: plan.priceAmount,
          priceCurrency: plan.priceCurrency,
          durationMinutes: plan.durationMinutes,
          maxRequestsPerSession: plan.maxRequestsPerSession,
          stripePriceId: plan.stripePriceId,
          active: true,
          createdAt: now,
          updatedAt: now,
        });

        results.push({
          name: plan.name,
          status: "created",
          planId,
        });
      } catch (error) {
        results.push({
          name: plan.name,
          status: "failed",
          error: String(error),
        });
      }
    }

    return {
      results,
      message: `Initialized ${results.filter(r => r.status === "created").length} new plans`,
    };
  },
});

// Helper function to get plan details (simplified for event-based model)
function getPlanDetails(durationMinutes: number, maxRequestsPerSession: number | "unlimited") {
  return {
    durationMinutes,
    maxRequestsPerSession,
    durationHours: Math.round(durationMinutes / 60 * 100) / 100, // For display purposes
    durationDays: Math.round(durationMinutes / (60 * 24) * 100) / 100, // For display purposes
  };
}
