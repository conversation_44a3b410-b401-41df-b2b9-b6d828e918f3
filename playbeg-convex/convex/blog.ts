/**
 * Blog Management Convex Functions
 * 
 * This module provides comprehensive blog management functionality including
 * CRUD operations, content management, and public blog API.
 */

import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Query: Get all published blog posts (public)
export const getPublishedPosts = query({
  args: {
    limit: v.optional(v.number()),
    category: v.optional(v.string()),
    featured: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("blogPosts")
      .withIndex("by_published", (q) => q.eq("status", "published"));

    if (args.category) {
      query = query.filter((q) => q.eq(q.field("category"), args.category));
    }

    if (args.featured !== undefined) {
      query = query.filter((q) => q.eq(q.field("featured"), args.featured));
    }

    const posts = await query
      .order("desc")
      .take(args.limit || 50);

    return posts.map(post => ({
      ...post,
      // Don't expose full content in list view
      content: undefined,
    }));
  },
});

// Query: Get blog post by slug (public)
export const getPostBySlug = query({
  args: { slug: v.string() },
  handler: async (ctx, args) => {
    const post = await ctx.db
      .query("blogPosts")
      .withIndex("by_slug", (q) => q.eq("slug", args.slug))
      .first();

    if (!post || post.status !== "published") {
      return null;
    }

    // Increment view count
    await ctx.db.patch(post._id, {
      viewCount: post.viewCount + 1,
      updatedAt: Date.now(),
    });

    return post;
  },
});

// Query: Get blog categories (public)
export const getCategories = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query("blogCategories")
      .order("desc")
      .collect();
  },
});

// Query: Get featured posts (public)
export const getFeaturedPosts = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    const posts = await ctx.db
      .query("blogPosts")
      .withIndex("by_featured", (q) => q.eq("featured", true))
      .filter((q) => q.eq(q.field("status"), "published"))
      .order("desc")
      .take(args.limit || 3);

    return posts.map(post => ({
      ...post,
      content: undefined, // Don't expose full content
    }));
  },
});

// Query: Get posts by category (public)
export const getPostsByCategory = query({
  args: {
    category: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const posts = await ctx.db
      .query("blogPosts")
      .withIndex("by_category", (q) => q.eq("category", args.category))
      .filter((q) => q.eq(q.field("status"), "published"))
      .order("desc")
      .take(args.limit || 20);

    return posts.map(post => ({
      ...post,
      content: undefined,
    }));
  },
});

// Admin Queries (require authentication)

// Query: Get all posts for admin (including drafts)
export const getAllPostsAdmin = query({
  args: {
    status: v.optional(v.union(v.literal("draft"), v.literal("published"), v.literal("archived"))),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    let query = ctx.db.query("blogPosts");

    if (args.status) {
      query = query.withIndex("by_status", (q) => q.eq("status", args.status));
    }

    const posts = await query
      .order("desc")
      .take(args.limit || 100);

    return posts.map(post => ({
      ...post,
      content: undefined, // Don't include full content in list
    }));
  },
});

// Query: Get post by ID for editing
export const getPostById = query({
  args: { postId: v.id("blogPosts") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    return await ctx.db.get(args.postId);
  },
});

// Mutations

// Mutation: Create blog post
export const createPost = mutation({
  args: {
    title: v.string(),
    slug: v.string(),
    content: v.string(),
    excerpt: v.string(),
    author: v.string(),
    category: v.string(),
    tags: v.array(v.string()),
    coverImageUrl: v.optional(v.string()),
    coverImageAlt: v.optional(v.string()),
    metaDescription: v.optional(v.string()),
    status: v.union(v.literal("draft"), v.literal("published")),
    featured: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Check if slug already exists
    const existingPost = await ctx.db
      .query("blogPosts")
      .withIndex("by_slug", (q) => q.eq("slug", args.slug))
      .first();

    if (existingPost) {
      throw new Error("A post with this slug already exists");
    }

    const now = Date.now();
    const postId = await ctx.db.insert("blogPosts", {
      title: args.title.trim(),
      slug: args.slug.trim(),
      content: args.content,
      excerpt: args.excerpt.trim(),
      author: args.author.trim(),
      authorId: userId,
      category: args.category,
      tags: args.tags,
      coverImageUrl: args.coverImageUrl,
      coverImageAlt: args.coverImageAlt,
      metaDescription: args.metaDescription,
      status: args.status,
      featured: args.featured || false,
      viewCount: 0,
      publishedAt: args.status === "published" ? now : undefined,
      createdAt: now,
      updatedAt: now,
    });

    return postId;
  },
});

// Mutation: Update blog post
export const updatePost = mutation({
  args: {
    postId: v.id("blogPosts"),
    title: v.optional(v.string()),
    slug: v.optional(v.string()),
    content: v.optional(v.string()),
    excerpt: v.optional(v.string()),
    author: v.optional(v.string()),
    category: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    coverImageUrl: v.optional(v.string()),
    coverImageAlt: v.optional(v.string()),
    metaDescription: v.optional(v.string()),
    status: v.optional(v.union(v.literal("draft"), v.literal("published"), v.literal("archived"))),
    featured: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const post = await ctx.db.get(args.postId);
    if (!post) {
      throw new Error("Post not found");
    }

    // Check slug uniqueness if changing
    if (args.slug && args.slug !== post.slug) {
      const existingPost = await ctx.db
        .query("blogPosts")
        .withIndex("by_slug", (q) => q.eq("slug", args.slug))
        .first();

      if (existingPost) {
        throw new Error("A post with this slug already exists");
      }
    }

    const updates: any = {
      updatedAt: Date.now(),
    };

    // Only update provided fields
    if (args.title !== undefined) updates.title = args.title.trim();
    if (args.slug !== undefined) updates.slug = args.slug.trim();
    if (args.content !== undefined) updates.content = args.content;
    if (args.excerpt !== undefined) updates.excerpt = args.excerpt.trim();
    if (args.author !== undefined) updates.author = args.author.trim();
    if (args.category !== undefined) updates.category = args.category;
    if (args.tags !== undefined) updates.tags = args.tags;
    if (args.coverImageUrl !== undefined) updates.coverImageUrl = args.coverImageUrl;
    if (args.coverImageAlt !== undefined) updates.coverImageAlt = args.coverImageAlt;
    if (args.metaDescription !== undefined) updates.metaDescription = args.metaDescription;
    if (args.featured !== undefined) updates.featured = args.featured;

    // Handle status changes
    if (args.status !== undefined) {
      updates.status = args.status;
      
      // Set publishedAt when publishing for the first time
      if (args.status === "published" && !post.publishedAt) {
        updates.publishedAt = Date.now();
      }
    }

    await ctx.db.patch(args.postId, updates);
    return args.postId;
  },
});

// Mutation: Delete blog post
export const deletePost = mutation({
  args: { postId: v.id("blogPosts") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const post = await ctx.db.get(args.postId);
    if (!post) {
      throw new Error("Post not found");
    }

    await ctx.db.delete(args.postId);
    return true;
  },
});

// Mutation: Create or update category
export const upsertCategory = mutation({
  args: {
    name: v.string(),
    slug: v.string(),
    description: v.optional(v.string()),
    color: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const existingCategory = await ctx.db
      .query("blogCategories")
      .withIndex("by_slug", (q) => q.eq("slug", args.slug))
      .first();

    const now = Date.now();

    if (existingCategory) {
      // Update existing category
      await ctx.db.patch(existingCategory._id, {
        name: args.name.trim(),
        description: args.description,
        color: args.color,
        updatedAt: now,
      });
      return existingCategory._id;
    } else {
      // Create new category
      return await ctx.db.insert("blogCategories", {
        name: args.name.trim(),
        slug: args.slug.trim(),
        description: args.description,
        color: args.color,
        postCount: 0,
        createdAt: now,
        updatedAt: now,
      });
    }
  },
});
