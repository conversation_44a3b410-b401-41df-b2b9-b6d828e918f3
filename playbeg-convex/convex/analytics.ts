/**
 * Advanced Analytics System
 * 
 * This module provides comprehensive analytics functionality including
 * session analytics, user engagement metrics, and business intelligence.
 */

import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Query: Get comprehensive session analytics
export const getSessionAnalytics = query({
  args: {
    sessionId: v.optional(v.id("sessions")),
    djId: v.optional(v.id("djProfiles")),
    timeRange: v.optional(v.number()), // milliseconds
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const timeThreshold = args.timeRange 
      ? Date.now() - args.timeRange 
      : Date.now() - (7 * 24 * 60 * 60 * 1000); // Default: last 7 days

    let sessionsQuery = ctx.db.query("sessions");
    
    if (args.sessionId) {
      const session = await ctx.db.get(args.sessionId);
      if (!session) return null;
      
      // Get detailed analytics for specific session
      const [requests, connections, reactions, votes] = await Promise.all([
        ctx.db
          .query("songRequests")
          .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId!))
          .collect(),
        
        ctx.db
          .query("sessionConnections")
          .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId!))
          .collect(),
        
        ctx.db
          .query("sessionReactions")
          .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId!))
          .collect(),
        
        ctx.db
          .query("songRequestVotes")
          .collect()
          .then(votes => votes.filter(vote => 
            requests.some(req => req._id === vote.requestId)
          )),
      ]);

      // Calculate metrics
      const totalRequests = requests.length;
      const approvedRequests = requests.filter(r => r.status === "approved" || r.status === "played").length;
      const uniqueRequesters = new Set(requests.map(r => r.requesterIp)).size;
      const totalConnections = connections.length;
      const uniqueUsers = new Set(connections.map(c => c.userId)).size;
      const totalReactions = reactions.length;
      const totalVotes = votes.length;

      // Request status breakdown
      const statusBreakdown = requests.reduce((acc, req) => {
        acc[req.status] = (acc[req.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Hourly activity
      const hourlyActivity = requests.reduce((acc, req) => {
        const hour = new Date(req.createdAt).getHours();
        acc[hour] = (acc[hour] || 0) + 1;
        return acc;
      }, {} as Record<number, number>);

      // Top genres
      const genreCount = requests.reduce((acc, req) => {
        if (req.genre) {
          acc[req.genre] = (acc[req.genre] || 0) + 1;
        }
        return acc;
      }, {} as Record<string, number>);

      return {
        session,
        metrics: {
          totalRequests,
          approvedRequests,
          approvalRate: totalRequests > 0 ? (approvedRequests / totalRequests * 100).toFixed(1) : "0",
          uniqueRequesters,
          totalConnections,
          uniqueUsers,
          totalReactions,
          totalVotes,
          avgRequestsPerUser: uniqueRequesters > 0 ? (totalRequests / uniqueRequesters).toFixed(1) : "0",
        },
        breakdowns: {
          statusBreakdown,
          hourlyActivity,
          topGenres: Object.entries(genreCount)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .map(([genre, count]) => ({ genre, count })),
        },
        timeline: requests
          .sort((a, b) => a.createdAt - b.createdAt)
          .map(req => ({
            timestamp: req.createdAt,
            type: 'request',
            data: { status: req.status, song: req.songTitle, artist: req.artistName },
          })),
      };
    } else {
      // Get overview analytics for DJ or all sessions
      let sessions;
      if (args.djId) {
        sessions = await ctx.db
          .query("sessions")
          .withIndex("by_dj", (q) => q.eq("djId", args.djId))
          .filter((q) => q.gte(q.field("createdAt"), timeThreshold))
          .collect();
      } else {
        sessions = await ctx.db
          .query("sessions")
          .withIndex("by_created")
          .filter((q) => q.gte(q.field("createdAt"), timeThreshold))
          .collect();
      }

      // Get all requests for these sessions
      const sessionIds = sessions.map(s => s._id);
      const allRequests = await Promise.all(
        sessionIds.map(sessionId =>
          ctx.db
            .query("songRequests")
            .withIndex("by_session", (q) => q.eq("sessionId", sessionId))
            .collect()
        )
      ).then(results => results.flat());

      // Calculate overview metrics
      const totalSessions = sessions.length;
      const activeSessions = sessions.filter(s => s.active).length;
      const totalRequests = allRequests.length;
      const avgRequestsPerSession = totalSessions > 0 ? (totalRequests / totalSessions).toFixed(1) : "0";

      // Daily activity
      const dailyActivity = sessions.reduce((acc, session) => {
        const date = new Date(session.createdAt).toISOString().split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return {
        overview: {
          totalSessions,
          activeSessions,
          totalRequests,
          avgRequestsPerSession,
          timeRange: args.timeRange || (7 * 24 * 60 * 60 * 1000),
        },
        dailyActivity: Object.entries(dailyActivity)
          .sort(([a], [b]) => a.localeCompare(b))
          .map(([date, count]) => ({ date, count })),
        topSessions: sessions
          .sort((a, b) => b.createdAt - a.createdAt)
          .slice(0, 10)
          .map(session => ({
            id: session._id,
            name: session.name,
            createdAt: session.createdAt,
            active: session.active,
            requestCount: allRequests.filter(r => r.sessionId === session._id).length,
          })),
      };
    }
  },
});

// Query: Get user engagement analytics
export const getUserEngagementAnalytics = query({
  args: {
    timeRange: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const timeThreshold = args.timeRange 
      ? Date.now() - args.timeRange 
      : Date.now() - (30 * 24 * 60 * 60 * 1000); // Default: last 30 days

    // Get user activity data
    const [connections, requests, reactions, votes] = await Promise.all([
      ctx.db
        .query("sessionConnections")
        .withIndex("by_user", (q) => q.eq("userId", userId))
        .filter((q) => q.gte(q.field("joinedAt"), timeThreshold))
        .collect(),
      
      ctx.db
        .query("songRequests")
        .filter((q) => q.gte(q.field("createdAt"), timeThreshold))
        .collect(),
      
      ctx.db
        .query("sessionReactions")
        .withIndex("by_user", (q) => q.eq("userId", userId))
        .filter((q) => q.gte(q.field("createdAt"), timeThreshold))
        .collect(),
      
      ctx.db
        .query("songRequestVotes")
        .withIndex("by_user", (q) => q.eq("userId", userId))
        .filter((q) => q.gte(q.field("createdAt"), timeThreshold))
        .collect(),
    ]);

    // Calculate engagement metrics
    const totalSessions = new Set(connections.map(c => c.sessionId)).size;
    const totalTimeSpent = connections.reduce((sum, conn) => {
      const endTime = conn.leftAt || Date.now();
      return sum + (endTime - conn.joinedAt);
    }, 0);
    const avgSessionTime = connections.length > 0 ? totalTimeSpent / connections.length : 0;

    // Activity by day
    const dailyActivity = connections.reduce((acc, conn) => {
      const date = new Date(conn.joinedAt).toISOString().split('T')[0];
      acc[date] = (acc[date] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      metrics: {
        totalSessions,
        totalConnections: connections.length,
        totalReactions: reactions.length,
        totalVotes: votes.length,
        avgSessionTime: Math.round(avgSessionTime / (1000 * 60)), // minutes
        totalTimeSpent: Math.round(totalTimeSpent / (1000 * 60 * 60)), // hours
      },
      dailyActivity: Object.entries(dailyActivity)
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([date, count]) => ({ date, count })),
      reactionBreakdown: reactions.reduce((acc, reaction) => {
        acc[reaction.reactionType] = (acc[reaction.reactionType] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
    };
  },
});

// Query: Get platform-wide analytics (admin only)
export const getPlatformAnalytics = query({
  args: {
    timeRange: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // TODO: Add admin role check here
    
    const timeThreshold = args.timeRange 
      ? Date.now() - args.timeRange 
      : Date.now() - (30 * 24 * 60 * 60 * 1000); // Default: last 30 days

    const [users, djProfiles, sessions, requests, subscriptions] = await Promise.all([
      ctx.db
        .query("users")
        .filter((q) => q.gte(q.field("_creationTime"), timeThreshold))
        .collect(),
      
      ctx.db
        .query("djProfiles")
        .filter((q) => q.gte(q.field("createdAt"), timeThreshold))
        .collect(),
      
      ctx.db
        .query("sessions")
        .filter((q) => q.gte(q.field("createdAt"), timeThreshold))
        .collect(),
      
      ctx.db
        .query("songRequests")
        .filter((q) => q.gte(q.field("createdAt"), timeThreshold))
        .collect(),
      
      ctx.db
        .query("djSubscriptions")
        .filter((q) => q.gte(q.field("createdAt"), timeThreshold))
        .collect(),
    ]);

    // Calculate platform metrics
    const totalUsers = users.length;
    const totalDJs = djProfiles.length;
    const totalSessions = sessions.length;
    const activeSessions = sessions.filter(s => s.active).length;
    const totalRequests = requests.length;
    const activeSubscriptions = subscriptions.filter(s => s.status === "active").length;

    // Growth metrics
    const userGrowth = users.reduce((acc, user) => {
      const date = new Date(user._creationTime).toISOString().split('T')[0];
      acc[date] = (acc[date] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const sessionGrowth = sessions.reduce((acc, session) => {
      const date = new Date(session.createdAt).toISOString().split('T')[0];
      acc[date] = (acc[date] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      metrics: {
        totalUsers,
        totalDJs,
        totalSessions,
        activeSessions,
        totalRequests,
        activeSubscriptions,
        djConversionRate: totalUsers > 0 ? (totalDJs / totalUsers * 100).toFixed(1) : "0",
        avgRequestsPerSession: totalSessions > 0 ? (totalRequests / totalSessions).toFixed(1) : "0",
      },
      growth: {
        userGrowth: Object.entries(userGrowth)
          .sort(([a], [b]) => a.localeCompare(b))
          .map(([date, count]) => ({ date, count })),
        sessionGrowth: Object.entries(sessionGrowth)
          .sort(([a], [b]) => a.localeCompare(b))
          .map(([date, count]) => ({ date, count })),
      },
      subscriptionBreakdown: subscriptions.reduce((acc, sub) => {
        acc[sub.status] = (acc[sub.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
    };
  },
});

// Mutation: Track custom analytics event
export const trackAnalyticsEvent = mutation({
  args: {
    eventType: v.string(),
    eventData: v.optional(v.any()),
    sessionId: v.optional(v.id("sessions")),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    
    await ctx.db.insert("analyticsEvents", {
      sessionId: args.sessionId || ("" as any), // Temporary fix for required field
      eventType: args.eventType as any,
      eventData: args.eventData,
      userId,
      timestamp: Date.now(),
    });
  },
});

// Query: Get user session statistics (for dashboard)
export const getUserSessionStats = query({
  args: {
    userId: v.id("users"),
    timeRange: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const currentUserId = await getAuthUserId(ctx);
    if (!currentUserId) {
      throw new Error("Authentication required");
    }

    // Only allow users to query their own stats
    if (currentUserId !== args.userId) {
      throw new Error("Unauthorized");
    }

    const timeThreshold = args.timeRange
      ? Date.now() - args.timeRange
      : Date.now() - (30 * 24 * 60 * 60 * 1000); // Default: last 30 days

    // Get user's sessions
    const userSessions = await ctx.db
      .query("sessions")
      .withIndex("by_dj", (q) => q.eq("djId", args.userId))
      .filter((q) => q.gte(q.field("createdAt"), timeThreshold))
      .collect();

    // Get requests for user's sessions
    const sessionIds = userSessions.map(s => s._id);
    const allRequests = await Promise.all(
      sessionIds.map(sessionId =>
        ctx.db
          .query("songRequests")
          .withIndex("by_session", (q) => q.eq("sessionId", sessionId))
          .filter((q) => q.gte(q.field("createdAt"), timeThreshold))
          .collect()
      )
    );

    const totalRequests = allRequests.flat().length;
    const activeSessions = userSessions.filter(s => s.active).length;

    return {
      totalSessions: userSessions.length,
      activeSessions,
      totalRequests,
      avgRequestsPerSession: userSessions.length > 0 ? (totalRequests / userSessions.length).toFixed(1) : "0",
    };
  },
});

// Query: Get real-time session metrics
export const getRealtimeSessionMetrics = query({
  args: { sessionId: v.id("sessions") },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) return null;

    const now = Date.now();
    const fiveMinutesAgo = now - (5 * 60 * 1000);

    const [recentRequests, activeConnections, recentReactions] = await Promise.all([
      ctx.db
        .query("songRequests")
        .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
        .filter((q) => q.gte(q.field("createdAt"), fiveMinutesAgo))
        .collect(),

      ctx.db
        .query("sessionConnections")
        .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
        .filter((q) => q.eq(q.field("isActive"), true))
        .collect(),

      ctx.db
        .query("sessionReactions")
        .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
        .filter((q) => q.gte(q.field("createdAt"), fiveMinutesAgo))
        .collect(),
    ]);

    return {
      sessionId: args.sessionId,
      sessionName: session.name,
      active: session.active,
      realtimeMetrics: {
        activeUsers: activeConnections.length,
        recentRequests: recentRequests.length,
        recentReactions: recentReactions.length,
        requestsPerMinute: recentRequests.length / 5,
        lastActivity: Math.max(
          ...recentRequests.map(r => r.createdAt),
          ...recentReactions.map(r => r.createdAt),
          0
        ),
      },
      timestamp: now,
    };
  },
});
