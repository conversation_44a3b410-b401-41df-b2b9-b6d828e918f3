import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// PlayBeg Sample Functions
// These are example functions to test the new schema

// Sample query to list DJ profiles
export const listDjProfiles = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 10;
    const profiles = await ctx.db
      .query("djProfiles")
      .order("desc")
      .take(limit);

    const userId = await getAuthUserId(ctx);
    const user = userId === null ? null : await ctx.db.get(userId);

    return {
      viewer: user?.email ?? null,
      profiles: profiles.map(profile => ({
        id: profile._id,
        displayName: profile.displayName,
        completedOnboarding: profile.completedOnboarding,
        createdAt: profile.createdAt,
      })),
    };
  },
});

// Sample query to list active sessions
export const listActiveSessions = query({
  args: {},
  handler: async (ctx) => {
    const sessions = await ctx.db
      .query("sessions")
      .withIndex("by_active", (q) => q.eq("active", true))
      .collect();

    return sessions.map(session => ({
      id: session._id,
      name: session.name,
      djId: session.djId,
      acceptRequests: session.acceptRequests,
      weddingModeEnabled: session.weddingModeEnabled,
      createdAt: session.createdAt,
    }));
  },
});

// Sample mutation to create a DJ profile
export const createDjProfile = mutation({
  args: {
    displayName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Must be authenticated to create DJ profile");
    }

    // Check if profile already exists
    const existingProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (existingProfile) {
      throw new Error("DJ profile already exists for this user");
    }

    const now = Date.now();
    const profileId = await ctx.db.insert("djProfiles", {
      userId,
      displayName: args.displayName,
      completedOnboarding: false,
      createdAt: now,
      updatedAt: now,
    });

    console.log("Created DJ profile with id:", profileId);
    return profileId;
  },
});

// Sample action to demonstrate schema validation
export const validateSchemaAction = action({
  args: {
    testType: v.string(),
  },
  handler: async (ctx, args): Promise<{
    testType: string;
    profileCount: number;
    sessionCount: number;
    timestamp: number;
  }> => {
    console.log(`Running schema validation test: ${args.testType}`);

    // Test basic queries
    const profileResult = await ctx.runQuery(api.myFunctions.listDjProfiles, { limit: 1 });
    const sessionResult = await ctx.runQuery(api.myFunctions.listActiveSessions, {});

    return {
      testType: args.testType,
      profileCount: profileResult.profiles.length,
      sessionCount: sessionResult.length,
      timestamp: Date.now(),
    };
  },
});
