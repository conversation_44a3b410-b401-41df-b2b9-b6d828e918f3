/**
 * Load Testing & Performance Validation System
 * 
 * This module provides comprehensive testing functionality including
 * load testing, performance benchmarking, and system validation.
 */

import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Mutation: Create test data for load testing
export const createTestData = mutation({
  args: {
    testType: v.string(),
    dataCount: v.number(),
    testSessionId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const testResults = [];
    const startTime = Date.now();

    try {
      switch (args.testType) {
        case "sessions":
          // Create test sessions
          for (let i = 0; i < args.dataCount; i++) {
            const djProfile = await ctx.db
              .query("djProfiles")
              .withIndex("by_user", (q) => q.eq("userId", userId))
              .first();

            if (djProfile) {
              const sessionId = await ctx.db.insert("sessions", {
                djId: djProfile._id,
                name: `Load Test Session ${i + 1}`,
                active: i % 2 === 0, // Alternate active/inactive
                acceptRequests: true,
                autoApproval: false,
                maxRequestsPerUser: 10,
                createdAt: Date.now(),
                updatedAt: Date.now(),
              });
              testResults.push(sessionId);
            }
          }
          break;

        case "song_requests":
          // Create test song requests
          const sessions = await ctx.db
            .query("sessions")
            .withIndex("by_active", (q) => q.eq("active", true))
            .take(5);

          if (sessions.length > 0) {
            for (let i = 0; i < args.dataCount; i++) {
              const randomSession = sessions[i % sessions.length];
              const requestId = await ctx.db.insert("songRequests", {
                sessionId: randomSession._id,
                songTitle: `Test Song ${i + 1}`,
                artistName: `Test Artist ${i + 1}`,
                requesterName: `Test User ${i + 1}`,
                requesterIp: `192.168.1.${(i % 254) + 1}`,
                status: ["pending", "approved", "declined"][i % 3] as any,
                createdAt: Date.now(),
              });
              testResults.push(requestId);
            }
          }
          break;

        case "connections":
          // Create test session connections
          const activeSessions = await ctx.db
            .query("sessions")
            .withIndex("by_active", (q) => q.eq("active", true))
            .take(3);

          if (activeSessions.length > 0) {
            for (let i = 0; i < args.dataCount; i++) {
              const randomSession = activeSessions[i % activeSessions.length];
              const connectionId = await ctx.db.insert("sessionConnections", {
                sessionId: randomSession._id,
                userId: userId,
                joinedAt: Date.now(),
                isActive: true,
                userAgent: `LoadTest-Agent-${i}`,
                ipAddress: `192.168.1.${(i % 254) + 1}`,
              });
              testResults.push(connectionId);
            }
          }
          break;

        default:
          throw new Error(`Unknown test type: ${args.testType}`);
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Log test performance
      await ctx.db.insert("testResults", {
        testType: args.testType,
        testSessionId: args.testSessionId || `test-${Date.now()}`,
        operation: "create_test_data",
        dataCount: args.dataCount,
        duration,
        throughput: args.dataCount / (duration / 1000), // items per second
        success: true,
        timestamp: Date.now(),
        metadata: {
          createdItems: testResults.length,
          avgTimePerItem: duration / args.dataCount,
        },
      });

      return {
        success: true,
        itemsCreated: testResults.length,
        duration,
        throughput: args.dataCount / (duration / 1000),
        testSessionId: args.testSessionId,
      };

    } catch (error: any) {
      const endTime = Date.now();
      const duration = endTime - startTime;

      await ctx.db.insert("testResults", {
        testType: args.testType,
        testSessionId: args.testSessionId || `test-${Date.now()}`,
        operation: "create_test_data",
        dataCount: args.dataCount,
        duration,
        throughput: 0,
        success: false,
        timestamp: Date.now(),
        metadata: {
          error: error.message,
          createdItems: testResults.length,
        },
      });

      throw error;
    }
  },
});

// Action: Run load test scenario
export const runLoadTestScenario = action({
  args: {
    scenarioName: v.string(),
    concurrentUsers: v.number(),
    duration: v.number(), // seconds
    operations: v.array(v.object({
      type: v.string(),
      weight: v.number(), // percentage
      params: v.any(),
    })),
  },
  handler: async (ctx, args) => {
    const testSessionId = `load-test-${Date.now()}`;
    const startTime = Date.now();
    const results = [];

    console.log(`Starting load test: ${args.scenarioName} with ${args.concurrentUsers} concurrent users for ${args.duration}s`);

    try {
      // Simulate concurrent users
      const userPromises = [];
      
      for (let userId = 0; userId < args.concurrentUsers; userId++) {
        const userPromise = simulateUser(ctx, {
          userId,
          testSessionId,
          duration: args.duration,
          operations: args.operations,
        });
        userPromises.push(userPromise);
      }

      // Wait for all users to complete
      const userResults = await Promise.allSettled(userPromises);
      
      const endTime = Date.now();
      const totalDuration = endTime - startTime;

      // Analyze results
      const successfulUsers = userResults.filter(r => r.status === 'fulfilled').length;
      const failedUsers = userResults.filter(r => r.status === 'rejected').length;

      const summary = {
        testSessionId,
        scenarioName: args.scenarioName,
        concurrentUsers: args.concurrentUsers,
        duration: totalDuration,
        successfulUsers,
        failedUsers,
        successRate: (successfulUsers / args.concurrentUsers) * 100,
        timestamp: Date.now(),
      };

      console.log(`Load test completed:`, summary);
      return summary;

    } catch (error: any) {
      console.error(`Load test failed:`, error);
      throw error;
    }
  },
});

// Helper function to simulate a single user
async function simulateUser(ctx: any, config: any) {
  const { userId, testSessionId, duration, operations } = config;
  const endTime = Date.now() + (duration * 1000);
  const userResults = [];

  while (Date.now() < endTime) {
    // Select random operation based on weights
    const randomOp = selectRandomOperation(operations);
    
    try {
      const opStartTime = Date.now();
      
      switch (randomOp.type) {
        case "create_session":
          await ctx.runMutation(api.testing.createTestData, {
            testType: "sessions",
            dataCount: 1,
            testSessionId,
          });
          break;

        case "submit_request":
          await ctx.runMutation(api.testing.createTestData, {
            testType: "song_requests",
            dataCount: 1,
            testSessionId,
          });
          break;

        case "query_sessions":
          await ctx.runQuery(api.sessions.getUserSessions, {});
          break;

        case "query_requests":
          await ctx.runQuery(api.songRequests.getSessionRequests, {
            sessionId: randomOp.params?.sessionId || "test-session",
          });
          break;

        default:
          console.warn(`Unknown operation type: ${randomOp.type}`);
      }

      const opDuration = Date.now() - opStartTime;
      userResults.push({
        operation: randomOp.type,
        duration: opDuration,
        success: true,
      });

    } catch (error: any) {
      const opDuration = Date.now() - opStartTime;
      userResults.push({
        operation: randomOp.type,
        duration: opDuration,
        success: false,
        error: error.message,
      });
    }

    // Small delay between operations
    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
  }

  return userResults;
}

// Helper function to select random operation based on weights
function selectRandomOperation(operations: any[]) {
  const totalWeight = operations.reduce((sum, op) => sum + op.weight, 0);
  let random = Math.random() * totalWeight;
  
  for (const operation of operations) {
    random -= operation.weight;
    if (random <= 0) {
      return operation;
    }
  }
  
  return operations[0]; // Fallback
}

// Query: Get load test results
export const getLoadTestResults = query({
  args: {
    testSessionId: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    let query = ctx.db.query("testResults");
    
    if (args.testSessionId) {
      query = query.withIndex("by_test_session", (q) => 
        q.eq("testSessionId", args.testSessionId)
      );
    } else {
      query = query.withIndex("by_timestamp");
    }

    const results = await query
      .order("desc")
      .take(args.limit || 50);

    // Calculate aggregated metrics
    const metrics = results.reduce((acc, result) => {
      acc.totalTests++;
      acc.totalDuration += result.duration;
      acc.totalThroughput += result.throughput;
      
      if (result.success) {
        acc.successfulTests++;
      } else {
        acc.failedTests++;
      }

      // Track by test type
      if (!acc.byType[result.testType]) {
        acc.byType[result.testType] = {
          count: 0,
          avgDuration: 0,
          avgThroughput: 0,
          successRate: 0,
        };
      }
      
      acc.byType[result.testType].count++;
      
      return acc;
    }, {
      totalTests: 0,
      successfulTests: 0,
      failedTests: 0,
      totalDuration: 0,
      totalThroughput: 0,
      byType: {} as Record<string, any>,
    });

    // Calculate averages
    if (metrics.totalTests > 0) {
      metrics.avgDuration = metrics.totalDuration / metrics.totalTests;
      metrics.avgThroughput = metrics.totalThroughput / metrics.totalTests;
      metrics.successRate = (metrics.successfulTests / metrics.totalTests) * 100;

      // Calculate averages by type
      Object.keys(metrics.byType).forEach(type => {
        const typeData = metrics.byType[type];
        const typeResults = results.filter(r => r.testType === type);
        
        typeData.avgDuration = typeResults.reduce((sum, r) => sum + r.duration, 0) / typeResults.length;
        typeData.avgThroughput = typeResults.reduce((sum, r) => sum + r.throughput, 0) / typeResults.length;
        typeData.successRate = (typeResults.filter(r => r.success).length / typeResults.length) * 100;
      });
    }

    return {
      results,
      metrics,
      testSessionId: args.testSessionId,
    };
  },
});

// Mutation: Clean up test data
export const cleanupTestData = mutation({
  args: {
    testSessionId: v.optional(v.string()),
    olderThan: v.optional(v.number()), // timestamp
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    let deletedCount = 0;
    const threshold = args.olderThan || (Date.now() - (24 * 60 * 60 * 1000)); // 24 hours ago

    try {
      // Clean up test sessions
      const testSessions = await ctx.db
        .query("sessions")
        .filter((q) => q.and(
          q.lt(q.field("createdAt"), threshold),
          q.like(q.field("name"), "Load Test Session%")
        ))
        .collect();

      for (const session of testSessions) {
        await ctx.db.delete(session._id);
        deletedCount++;
      }

      // Clean up test requests
      const testRequests = await ctx.db
        .query("songRequests")
        .filter((q) => q.and(
          q.lt(q.field("createdAt"), threshold),
          q.like(q.field("songTitle"), "Test Song%")
        ))
        .collect();

      for (const request of testRequests) {
        await ctx.db.delete(request._id);
        deletedCount++;
      }

      // Clean up test connections
      const testConnections = await ctx.db
        .query("sessionConnections")
        .filter((q) => q.and(
          q.lt(q.field("joinedAt"), threshold),
          q.like(q.field("userAgent"), "LoadTest-Agent%")
        ))
        .collect();

      for (const connection of testConnections) {
        await ctx.db.delete(connection._id);
        deletedCount++;
      }

      return {
        success: true,
        deletedCount,
        threshold,
      };

    } catch (error: any) {
      throw new Error(`Cleanup failed: ${error.message}`);
    }
  },
});
