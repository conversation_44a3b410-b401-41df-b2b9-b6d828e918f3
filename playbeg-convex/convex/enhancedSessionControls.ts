/**
 * Enhanced Session Controls
 * 
 * Advanced session features including pause/resume, audience capacity limits,
 * geographic restrictions, custom branding, and enhanced session management.
 */

import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Pause or resume a session
export const toggleSessionPause = mutation({
  args: {
    sessionId: v.id("sessions"),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Check permissions
    const hasPermission = session.userId === userId || 
      await checkCollaborationPermission(ctx, { sessionId: args.sessionId, action: "control_playback" });

    if (!hasPermission) {
      throw new Error("Access denied: You don't have permission to control this session");
    }

    const isPaused = session.pausedAt !== undefined;
    const now = Date.now();

    if (isPaused) {
      // Resume session
      const pauseDuration = now - (session.pausedAt || 0);
      await ctx.db.patch(args.sessionId, {
        pausedAt: undefined,
        pauseReason: undefined,
        totalPausedTime: (session.totalPausedTime || 0) + pauseDuration,
        updatedAt: now,
      });

      // Record analytics event
      await ctx.db.insert("analyticsEvents", {
        sessionId: args.sessionId,
        eventType: "session_resumed",
        eventData: {
          userId,
          pauseDuration,
          metadata: { reason: args.reason },
        },
        userId,
        timestamp: now,
      });

      // Notify collaborators
      await notifyCollaborators(ctx, args.sessionId, "Session Resumed", 
        `Session "${session.name}" has been resumed`);

    } else {
      // Pause session
      await ctx.db.patch(args.sessionId, {
        pausedAt: now,
        pauseReason: args.reason,
        updatedAt: now,
      });

      // Record analytics event
      await ctx.db.insert("analyticsEvents", {
        sessionId: args.sessionId,
        eventType: "session_paused",
        eventData: {
          userId,
          metadata: { reason: args.reason },
        },
        userId,
        timestamp: now,
      });

      // Notify collaborators
      await notifyCollaborators(ctx, args.sessionId, "Session Paused", 
        `Session "${session.name}" has been paused${args.reason ? `: ${args.reason}` : ''}`);
    }

    return { success: true, isPaused: !isPaused };
  },
});

// Update session capacity limits
export const updateSessionCapacity = mutation({
  args: {
    sessionId: v.id("sessions"),
    maxAudienceSize: v.optional(v.number()),
    requireApprovalToJoin: v.optional(v.boolean()),
    allowAnonymousRequests: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Check permissions
    const hasPermission = session.userId === userId || 
      await checkCollaborationPermission(ctx, { sessionId: args.sessionId, action: "edit_settings" });

    if (!hasPermission) {
      throw new Error("Access denied: You don't have permission to edit this session");
    }

    const updates: any = { updatedAt: Date.now() };

    if (args.maxAudienceSize !== undefined) {
      updates.maxAudienceSize = args.maxAudienceSize;
    }
    if (args.requireApprovalToJoin !== undefined) {
      updates.requireApprovalToJoin = args.requireApprovalToJoin;
    }
    if (args.allowAnonymousRequests !== undefined) {
      updates.allowAnonymousRequests = args.allowAnonymousRequests;
    }

    await ctx.db.patch(args.sessionId, updates);

    return { success: true };
  },
});

// Update geographic restrictions
export const updateGeographicRestrictions = mutation({
  args: {
    sessionId: v.id("sessions"),
    allowedCountries: v.optional(v.array(v.string())),
    blockedCountries: v.optional(v.array(v.string())),
    restrictionMode: v.optional(v.union(
      v.literal("none"),
      v.literal("allowlist"),
      v.literal("blocklist"),
    )),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Check permissions
    const hasPermission = session.userId === userId || 
      await checkCollaborationPermission(ctx, { sessionId: args.sessionId, action: "edit_settings" });

    if (!hasPermission) {
      throw new Error("Access denied: You don't have permission to edit this session");
    }

    const updates: any = { updatedAt: Date.now() };

    if (args.allowedCountries !== undefined) {
      updates.allowedCountries = args.allowedCountries;
    }
    if (args.blockedCountries !== undefined) {
      updates.blockedCountries = args.blockedCountries;
    }
    if (args.restrictionMode !== undefined) {
      updates.restrictionMode = args.restrictionMode;
    }

    await ctx.db.patch(args.sessionId, updates);

    return { success: true };
  },
});

// Update session branding
export const updateSessionBranding = mutation({
  args: {
    sessionId: v.id("sessions"),
    branding: v.object({
      primaryColor: v.optional(v.string()),
      secondaryColor: v.optional(v.string()),
      logoStorageId: v.optional(v.id("_storage")),
      backgroundImageStorageId: v.optional(v.id("_storage")),
      customMessage: v.optional(v.string()),
      customCSS: v.optional(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Check permissions
    const hasPermission = session.userId === userId || 
      await checkCollaborationPermission(ctx, { sessionId: args.sessionId, action: "edit_settings" });

    if (!hasPermission) {
      throw new Error("Access denied: You don't have permission to edit this session");
    }

    await ctx.db.patch(args.sessionId, {
      sessionBranding: args.branding,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Get session status and controls
export const getSessionControls = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return null;
    }

    // Check if user has access
    const isOwner = session.userId === userId;
    const collaboration = await ctx.db
      .query("sessionCollaborators")
      .withIndex("by_session_user", (q) => 
        q.eq("sessionId", args.sessionId).eq("userId", userId)
      )
      .filter((q) => q.eq(q.field("status"), "active"))
      .first();

    if (!isOwner && !collaboration) {
      throw new Error("Access denied: You don't have permission to view this session");
    }

    // Get current audience count
    const activeConnections = await ctx.db
      .query("sessionConnections")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    const currentAudienceSize = activeConnections.filter(c => c.userRole === "audience").length;

    // Get user permissions
    const permissions = isOwner ? {
      canManageRequests: true,
      canControlPlayback: true,
      canModerateChat: true,
      canInviteOthers: true,
      canEditSessionSettings: true,
      canViewAnalytics: true,
    } : collaboration?.permissions || {};

    return {
      sessionId: args.sessionId,
      name: session.name,
      active: session.active,
      isPaused: session.pausedAt !== undefined,
      pausedAt: session.pausedAt,
      pauseReason: session.pauseReason,
      totalPausedTime: session.totalPausedTime || 0,
      
      // Capacity controls
      currentAudienceSize,
      maxAudienceSize: session.maxAudienceSize,
      requireApprovalToJoin: session.requireApprovalToJoin,
      allowAnonymousRequests: session.allowAnonymousRequests,
      
      // Geographic restrictions
      allowedCountries: session.allowedCountries,
      blockedCountries: session.blockedCountries,
      restrictionMode: session.restrictionMode,
      
      // Branding
      sessionBranding: session.sessionBranding,
      
      // User permissions
      isOwner,
      permissions,
      
      // Session timing
      createdAt: session.createdAt,
      updatedAt: session.updatedAt,
      sessionDuration: Date.now() - session.createdAt,
      activeDuration: (Date.now() - session.createdAt) - (session.totalPausedTime || 0),
    };
  },
});

// Kick a user from the session
export const kickUserFromSession = mutation({
  args: {
    sessionId: v.id("sessions"),
    targetUserId: v.id("users"),
    reason: v.optional(v.string()),
    banDuration: v.optional(v.number()), // in minutes, 0 for permanent
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Check permissions
    const hasPermission = session.userId === userId || 
      await checkCollaborationPermission(ctx, { sessionId: args.sessionId, action: "moderate_chat" });

    if (!hasPermission) {
      throw new Error("Access denied: You don't have permission to moderate this session");
    }

    // Can't kick the session owner
    if (args.targetUserId === session.userId) {
      throw new Error("Cannot kick the session owner");
    }

    // Remove user's active connection
    const activeConnection = await ctx.db
      .query("sessionConnections")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .filter((q) => 
        q.and(
          q.eq(q.field("userId"), args.targetUserId),
          q.eq(q.field("isActive"), true)
        )
      )
      .first();

    if (activeConnection) {
      await ctx.db.patch(activeConnection._id, {
        isActive: false,
        leftAt: Date.now(),
        kickedBy: userId,
        kickReason: args.reason,
      });
    }

    // Create ban record if specified
    if (args.banDuration !== undefined) {
      const banExpiresAt = args.banDuration === 0 ? undefined : 
        Date.now() + (args.banDuration * 60 * 1000);

      await ctx.db.insert("sessionBans", {
        sessionId: args.sessionId,
        userId: args.targetUserId,
        bannedBy: userId,
        reason: args.reason,
        bannedAt: Date.now(),
        expiresAt: banExpiresAt,
        isActive: true,
      });
    }

    // Record analytics event
    await ctx.db.insert("analyticsEvents", {
      sessionId: args.sessionId,
      eventType: "user_kicked",
      eventData: {
        userId,
        targetUserId: args.targetUserId,
        metadata: { 
          reason: args.reason,
          banDuration: args.banDuration,
        },
      },
      userId,
      timestamp: Date.now(),
    });

    return { success: true };
  },
});

// Helper function to check collaboration permissions
async function checkCollaborationPermission(ctx: any, args: { sessionId: any, action: string }) {
  const userId = await getAuthUserId(ctx);
  if (!userId) return false;

  const collaboration = await ctx.db
    .query("sessionCollaborators")
    .withIndex("by_session_user", (q: any) =>
      q.eq("sessionId", args.sessionId).eq("userId", userId)
    )
    .filter((q: any) => q.eq(q.field("status"), "active"))
    .first();

  if (!collaboration) return false;

  const permissions = collaboration.permissions;
  switch (args.action) {
    case "manage_requests": return permissions.canManageRequests || false;
    case "control_playback": return permissions.canControlPlayback || false;
    case "moderate_chat": return permissions.canModerateChat || false;
    case "invite_others": return permissions.canInviteOthers || false;
    case "edit_settings": return permissions.canEditSessionSettings || false;
    case "view_analytics": return permissions.canViewAnalytics || false;
    default: return false;
  }
}

// Helper function to notify collaborators
async function notifyCollaborators(ctx: any, sessionId: any, title: string, message: string) {
  const collaborators = await ctx.db
    .query("sessionCollaborators")
    .withIndex("by_session", (q: any) => q.eq("sessionId", sessionId))
    .filter((q: any) => q.eq(q.field("status"), "active"))
    .collect();

  for (const collaborator of collaborators) {
    await ctx.db.insert("notifications", {
      recipientId: collaborator.userId,
      type: "session_status",
      title,
      message,
      priority: "medium",
      actionData: {
        sessionId,
        actionType: "view_session",
        actionUrl: `/dashboard/sessions/${sessionId}`,
      },
      isRead: false,
      isDelivered: false,
      createdAt: Date.now(),
    });
  }
}
