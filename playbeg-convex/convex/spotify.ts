/**
 * Spotify Integration Convex Functions
 *
 * This module provides Convex functions for Spotify API integration,
 * including song search, validation, and metadata retrieval.
 */

import { v } from "convex/values";
import { action, query, mutation } from "./_generated/server";
import { api } from "./_generated/api";

// Action: Search tracks on Spotify
export const searchTracks = action({
  args: {
    query: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Import Spotify functions dynamically to avoid Node.js dependencies in Convex
    const { searchTracks } = await import("../lib/spotify");
    
    try {
      const results = await searchTracks(args.query, args.limit || 10);
      return {
        success: true,
        tracks: results,
        query: args.query,
      };
    } catch (error: any) {
      console.error('Spotify search error:', error);
      return {
        success: false,
        error: error.message || 'Failed to search tracks',
        tracks: [],
        query: args.query,
      };
    }
  },
});

// Action: Validate song request with Spotify
export const validateSongRequest = action({
  args: {
    songTitle: v.string(),
    artistName: v.string(),
  },
  handler: async (ctx, args) => {
    const { validateSongRequest } = await import("../lib/spotify");
    
    try {
      const validation = await validateSongRequest(args.songTitle, args.artistName);
      return {
        success: true,
        validation,
        songTitle: args.songTitle,
        artistName: args.artistName,
      };
    } catch (error: any) {
      console.error('Spotify validation error:', error);
      return {
        success: false,
        error: error.message || 'Failed to validate song',
        validation: {
          isValid: false,
          exactMatch: false,
          suggestions: [],
          confidence: 'none' as const,
        },
        songTitle: args.songTitle,
        artistName: args.artistName,
      };
    }
  },
});

// Action: Get track by Spotify ID
export const getTrackById = action({
  args: {
    trackId: v.string(),
  },
  handler: async (ctx, args) => {
    const { getTrackById } = await import("../lib/spotify");
    
    try {
      const track = await getTrackById(args.trackId);
      return {
        success: true,
        track,
        trackId: args.trackId,
      };
    } catch (error: any) {
      console.error('Spotify get track error:', error);
      return {
        success: false,
        error: error.message || 'Failed to get track',
        track: null,
        trackId: args.trackId,
      };
    }
  },
});

// Action: Get popular tracks
export const getPopularTracks = action({
  args: {
    genre: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const { getPopularTracks } = await import("../lib/spotify");
    
    try {
      const tracks = await getPopularTracks(args.genre, args.limit || 20);
      return {
        success: true,
        tracks,
        genre: args.genre,
      };
    } catch (error: any) {
      console.error('Spotify popular tracks error:', error);
      return {
        success: false,
        error: error.message || 'Failed to get popular tracks',
        tracks: [],
        genre: args.genre,
      };
    }
  },
});

// Query: Get cached song metadata from database
export const getCachedSongMetadata = query({
  args: {
    songTitle: v.string(),
    artistName: v.string(),
  },
  handler: async (ctx, args) => {
    // Look for existing song in our database
    const existingSong = await ctx.db
      .query("songs")
      .withIndex("by_title_artist", (q) => 
        q.eq("title", args.songTitle).eq("artist", args.artistName)
      )
      .first();

    return existingSong;
  },
});

// Mutation: Cache song metadata
export const cacheSongMetadata = mutation({
  args: {
    spotifyTrack: v.object({
      id: v.string(),
      name: v.string(),
      artists: v.array(v.string()),
      album: v.string(),
      albumArt: v.optional(v.string()),
      previewUrl: v.optional(v.string()),
      duration: v.number(),
      popularity: v.number(),
      explicit: v.boolean(),
      uri: v.string(),
    }),
  },
  handler: async (ctx, args) => {
    const track = args.spotifyTrack;

    try {
      // Check if song already exists
      const existingSong = await ctx.db
        .query("songs")
        .withIndex("by_title_artist", (q) =>
          q.eq("title", track.name).eq("artist", track.artists[0])
        )
        .first();

      if (existingSong) {
        // Update existing song with Spotify data
        await ctx.db.patch(existingSong._id, {
          appleMusicId: track.id, // Store Spotify ID in appleMusicId field for now
          artworkUrl: track.albumArt || undefined,
          album: track.album,
          updatedAt: Date.now(),
        });
        return existingSong._id;
      } else {
        // Create new song entry
        const songId = await ctx.db.insert("songs", {
          title: track.name,
          artist: track.artists[0],
          album: track.album,
          appleMusicId: track.id, // Store Spotify ID
          artworkUrl: track.albumArt || undefined,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
        return songId;
      }
    } catch (error: any) {
      console.error('Error caching song metadata:', error);
      throw new Error('Failed to cache song metadata');
    }
  },
});

// Action: Enhanced song request with Spotify validation
export const createValidatedSongRequest = action({
  args: {
    sessionId: v.id("sessions"),
    songTitle: v.string(),
    artistName: v.string(),
    requesterName: v.string(),
    requesterIp: v.optional(v.string()),
    spotifyTrackId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      // First validate with Spotify if no track ID provided
      let spotifyTrack = null;
      if (args.spotifyTrackId) {
        const trackResult = await ctx.runAction(api.spotify.getTrackById, {
          trackId: args.spotifyTrackId,
        });
        spotifyTrack = trackResult.track;
      } else {
        const validationResult = await ctx.runAction(api.spotify.validateSongRequest, {
          songTitle: args.songTitle,
          artistName: args.artistName,
        });

        if (validationResult.validation.matchedTrack) {
          spotifyTrack = validationResult.validation.matchedTrack;
        }
      }

      // Cache song metadata if we have Spotify data
      if (spotifyTrack) {
        await ctx.runMutation(api.spotify.cacheSongMetadata, {
          spotifyTrack,
        });
      }

      // Create the song request with enhanced metadata
      const requestResult = await ctx.runMutation(api.songRequests.createSongRequest, {
        sessionId: args.sessionId,
        songTitle: spotifyTrack?.name || args.songTitle,
        artistName: spotifyTrack?.artists[0] || args.artistName,
        requesterName: args.requesterName,
        requesterIp: args.requesterIp,
        appleMusicId: spotifyTrack?.id,
        albumArtwork: spotifyTrack?.albumArt || undefined,
        genre: undefined, // Could be enhanced with Spotify genre data
      });

      return {
        success: true,
        request: requestResult,
        spotifyData: spotifyTrack,
      };
    } catch (error: any) {
      console.error('Error creating validated song request:', error);
      return {
        success: false,
        error: error.message || 'Failed to create song request',
      };
    }
  },
});
