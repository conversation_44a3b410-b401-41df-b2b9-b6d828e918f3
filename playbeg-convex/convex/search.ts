/**
 * Advanced Search & Filtering System
 * 
 * This module provides comprehensive search functionality across all major
 * data types including sessions, song requests, blog posts, and more.
 */

import { v } from "convex/values";
import { query, action } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Query: Search sessions with advanced filtering
export const searchSessions = query({
  args: {
    searchQuery: v.optional(v.string()),
    djId: v.optional(v.id("djProfiles")),
    active: v.optional(v.boolean()),
    weddingMode: v.optional(v.boolean()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 20;

    if (args.searchQuery && args.searchQuery.trim().length > 0) {
      // Use search index for text search
      let searchQuery = ctx.db
        .query("sessions")
        .withSearchIndex("search_sessions", (q) => {
          let query = q.search("name", args.searchQuery!);
          
          if (args.djId) {
            query = query.eq("djId", args.djId);
          }
          if (args.active !== undefined) {
            query = query.eq("active", args.active);
          }
          if (args.weddingMode !== undefined) {
            query = query.eq("weddingModeEnabled", args.weddingMode);
          }
          
          return query;
        });

      return await searchQuery.take(limit);
    } else {
      // Use regular index for non-text filtering
      let query = ctx.db.query("sessions");

      if (args.djId) {
        if (args.active !== undefined) {
          query = query.withIndex("by_dj_active", (q) => 
            q.eq("djId", args.djId!).eq("active", args.active!)
          );
        } else {
          query = query.withIndex("by_dj", (q) => q.eq("djId", args.djId!));
        }
      } else if (args.active !== undefined) {
        query = query.withIndex("by_active", (q) => q.eq("active", args.active));
      } else {
        query = query.withIndex("by_created");
      }

      let results = await query.order("desc").take(limit);

      // Apply additional filters
      if (args.weddingMode !== undefined) {
        results = results.filter(session => session.weddingModeEnabled === args.weddingMode);
      }

      return results;
    }
  },
});

// Query: Search song requests with advanced filtering
export const searchSongRequests = query({
  args: {
    searchQuery: v.optional(v.string()),
    sessionId: v.optional(v.id("sessions")),
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("approved"),
      v.literal("auto-approved"),
      v.literal("declined"),
      v.literal("played")
    )),
    artistName: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;

    if (args.searchQuery && args.searchQuery.trim().length > 0) {
      // Use search index for text search
      let searchQuery = ctx.db
        .query("songRequests")
        .withSearchIndex("search_song_requests", (q) => {
          let query = q.search("songTitle", args.searchQuery!);
          
          if (args.sessionId) {
            query = query.eq("sessionId", args.sessionId);
          }
          if (args.status) {
            query = query.eq("status", args.status);
          }
          if (args.artistName) {
            query = query.eq("artistName", args.artistName);
          }
          
          return query;
        });

      return await searchQuery.take(limit);
    } else {
      // Use regular index for non-text filtering
      let query = ctx.db.query("songRequests");

      if (args.sessionId && args.status) {
        query = query.withIndex("by_session_status", (q) => 
          q.eq("sessionId", args.sessionId!).eq("status", args.status!)
        );
      } else if (args.sessionId) {
        query = query.withIndex("by_session", (q) => q.eq("sessionId", args.sessionId!));
      } else if (args.status) {
        query = query.withIndex("by_status", (q) => q.eq("status", args.status));
      } else {
        query = query.withIndex("by_created");
      }

      let results = await query.order("desc").take(limit);

      // Apply additional filters
      if (args.artistName) {
        results = results.filter(request => 
          request.artistName.toLowerCase().includes(args.artistName!.toLowerCase())
        );
      }

      return results;
    }
  },
});

// Query: Search blog posts with advanced filtering
export const searchBlogPosts = query({
  args: {
    searchQuery: v.optional(v.string()),
    status: v.optional(v.union(v.literal("draft"), v.literal("published"), v.literal("archived"))),
    category: v.optional(v.string()),
    author: v.optional(v.string()),
    featured: v.optional(v.boolean()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 20;

    if (args.searchQuery && args.searchQuery.trim().length > 0) {
      // Use search index for text search
      let searchQuery = ctx.db
        .query("blogPosts")
        .withSearchIndex("search_blog_posts", (q) => {
          let query = q.search("title", args.searchQuery!);
          
          if (args.status) {
            query = query.eq("status", args.status);
          }
          if (args.category) {
            query = query.eq("category", args.category);
          }
          if (args.author) {
            query = query.eq("author", args.author);
          }
          if (args.featured !== undefined) {
            query = query.eq("featured", args.featured);
          }
          
          return query;
        });

      return await searchQuery.take(limit);
    } else {
      // Use regular index for non-text filtering
      let query = ctx.db.query("blogPosts");

      if (args.status && args.featured !== undefined) {
        query = query.withIndex("by_status_featured", (q) => 
          q.eq("status", args.status!).eq("featured", args.featured!)
        );
      } else if (args.status) {
        query = query.withIndex("by_status", (q) => q.eq("status", args.status));
      } else if (args.category) {
        query = query.withIndex("by_category", (q) => q.eq("category", args.category));
      } else if (args.author) {
        query = query.withIndex("by_author", (q) => q.eq("author", args.author));
      } else if (args.featured !== undefined) {
        query = query.withIndex("by_featured", (q) => q.eq("featured", args.featured));
      } else {
        query = query.withIndex("by_status", (q) => q.eq("status", "published"));
      }

      return await query.order("desc").take(limit);
    }
  },
});

// Query: Global search across multiple content types
export const globalSearch = query({
  args: {
    searchQuery: v.string(),
    contentTypes: v.optional(v.array(v.union(
      v.literal("sessions"),
      v.literal("songRequests"),
      v.literal("blogPosts")
    ))),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    const limit = args.limit || 10;
    const contentTypes = args.contentTypes || ["sessions", "songRequests", "blogPosts"];
    
    const results: any = {
      sessions: [],
      songRequests: [],
      blogPosts: [],
      totalResults: 0,
    };

    // Search sessions
    if (contentTypes.includes("sessions")) {
      const sessions = await ctx.db
        .query("sessions")
        .withSearchIndex("search_sessions", (q) => 
          q.search("name", args.searchQuery)
        )
        .take(limit);
      
      results.sessions = sessions.map(session => ({
        ...session,
        type: "session",
        title: session.name,
        description: `DJ Session ${session.active ? "(Active)" : "(Inactive)"}`,
      }));
    }

    // Search song requests (only if user is authenticated)
    if (contentTypes.includes("songRequests") && userId) {
      const songRequests = await ctx.db
        .query("songRequests")
        .withSearchIndex("search_song_requests", (q) => 
          q.search("songTitle", args.searchQuery)
        )
        .take(limit);
      
      results.songRequests = songRequests.map(request => ({
        ...request,
        type: "songRequest",
        title: `${request.songTitle} by ${request.artistName}`,
        description: `Requested by ${request.requesterName} - ${request.status}`,
      }));
    }

    // Search blog posts (public content)
    if (contentTypes.includes("blogPosts")) {
      const blogPosts = await ctx.db
        .query("blogPosts")
        .withSearchIndex("search_blog_posts", (q) => 
          q.search("title", args.searchQuery).eq("status", "published")
        )
        .take(limit);
      
      results.blogPosts = blogPosts.map(post => ({
        ...post,
        type: "blogPost",
        title: post.title,
        description: post.excerpt,
      }));
    }

    results.totalResults = results.sessions.length + results.songRequests.length + results.blogPosts.length;

    return results;
  },
});

// Query: Get search suggestions/autocomplete
export const getSearchSuggestions = query({
  args: {
    query: v.string(),
    type: v.union(v.literal("sessions"), v.literal("songRequests"), v.literal("blogPosts")),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 5;
    
    if (args.query.length < 2) {
      return [];
    }

    switch (args.type) {
      case "sessions":
        const sessions = await ctx.db
          .query("sessions")
          .withSearchIndex("search_sessions", (q) => 
            q.search("name", args.query)
          )
          .take(limit);
        
        return sessions.map(session => ({
          id: session._id,
          text: session.name,
          type: "session",
        }));

      case "songRequests":
        const requests = await ctx.db
          .query("songRequests")
          .withSearchIndex("search_song_requests", (q) => 
            q.search("songTitle", args.query)
          )
          .take(limit);
        
        return requests.map(request => ({
          id: request._id,
          text: `${request.songTitle} by ${request.artistName}`,
          type: "songRequest",
        }));

      case "blogPosts":
        const posts = await ctx.db
          .query("blogPosts")
          .withSearchIndex("search_blog_posts", (q) => 
            q.search("title", args.query).eq("status", "published")
          )
          .take(limit);
        
        return posts.map(post => ({
          id: post._id,
          text: post.title,
          type: "blogPost",
        }));

      default:
        return [];
    }
  },
});

// Query: Advanced filtering options for each content type
export const getFilterOptions = query({
  args: {
    type: v.union(v.literal("sessions"), v.literal("songRequests"), v.literal("blogPosts")),
  },
  handler: async (ctx, args) => {
    switch (args.type) {
      case "sessions":
        // Get unique filter values for sessions
        const sessions = await ctx.db.query("sessions").collect();
        const djIds = [...new Set(sessions.map(s => s.djId))];
        
        return {
          statuses: [
            { value: true, label: "Active" },
            { value: false, label: "Inactive" },
          ],
          weddingModes: [
            { value: true, label: "Wedding Mode" },
            { value: false, label: "Regular Mode" },
          ],
          djCount: djIds.length,
        };

      case "songRequests":
        const requests = await ctx.db.query("songRequests").collect();
        const statuses = [...new Set(requests.map(r => r.status))];
        const artists = [...new Set(requests.map(r => r.artistName))].slice(0, 20);
        
        return {
          statuses: statuses.map(status => ({ value: status, label: status })),
          artists: artists.map(artist => ({ value: artist, label: artist })),
        };

      case "blogPosts":
        const posts = await ctx.db.query("blogPosts").collect();
        const categories = [...new Set(posts.map(p => p.category))];
        const authors = [...new Set(posts.map(p => p.author))];
        
        return {
          statuses: [
            { value: "published", label: "Published" },
            { value: "draft", label: "Draft" },
            { value: "archived", label: "Archived" },
          ],
          categories: categories.map(cat => ({ value: cat, label: cat })),
          authors: authors.map(author => ({ value: author, label: author })),
          featured: [
            { value: true, label: "Featured" },
            { value: false, label: "Regular" },
          ],
        };

      default:
        return {};
    }
  },
});
