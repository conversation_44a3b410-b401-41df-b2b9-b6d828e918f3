import { httpRouter } from "convex/server";
import { httpAction } from "./_generated/server";
import { auth } from "./auth";
import { checkIPAccess, getClientIP } from "./security";

const http = httpRouter();

// IP whitelist for development - only allow your PC
const getAllowedIPs = () => {
  const envIPs = process.env.ALLOWED_IP_ADDRESSES || "127.0.0.1,::1";
  return envIPs.split(",").map(ip => ip.trim());
};

// Security middleware to check IP
const ipWhitelistMiddleware = httpAction(async (ctx, request) => {
  const clientIP = getClientIP(request);
  const isAllowed = checkIPAccess(request);

  if (!isAllowed) {
    return new Response(`Access denied - IP ${clientIP} not whitelisted`, {
      status: 403,
      headers: { "Content-Type": "text/plain" }
    });
  }

  return new Response(`Access allowed for IP: ${clientIP}`, {
    status: 200,
    headers: { "Content-Type": "text/plain" }
  });
});

// Add IP check route
http.route({
  path: "/check-access",
  method: "GET",
  handler: ipWhitelistMiddleware,
});

auth.addHttpRoutes(http);

export default http;
