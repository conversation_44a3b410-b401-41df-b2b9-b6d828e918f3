/**
 * Playlist Management
 * 
 * This module handles playlist CRUD operations that integrate with Apple Music,
 * including playlist creation, synchronization, and management with proper
 * authentication and validation.
 */

import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Query: Get current user's playlists
export const getCurrentUserPlaylists = query({
  args: {
    activeOnly: v.optional(v.boolean()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    const limit = args.limit ?? 50;
    
    if (args.activeOnly) {
      return await ctx.db
        .query("playlists")
        .withIndex("by_user_active", (q) => 
          q.eq("userId", userId).eq("active", true)
        )
        .order("desc")
        .take(limit);
    } else {
      return await ctx.db
        .query("playlists")
        .withIndex("by_user", (q) => q.eq("userId", userId))
        .order("desc")
        .take(limit);
    }
  },
});

// Query: Get playlist by ID with ownership check
export const getPlaylistById = query({
  args: {
    playlistId: v.id("playlists"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const playlist = await ctx.db.get(args.playlistId);
    if (!playlist) {
      return null;
    }

    // Verify ownership
    if (playlist.userId !== userId) {
      throw new Error("Access denied: You can only access your own playlists");
    }

    return playlist;
  },
});

// Query: Get playlist by Apple Music ID
export const getPlaylistByAppleMusicId = query({
  args: {
    appleMusicId: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const playlist = await ctx.db
      .query("playlists")
      .withIndex("by_apple_music_id", (q) => q.eq("appleMusicId", args.appleMusicId))
      .filter((q) => q.eq(q.field("userId"), userId))
      .first();

    return playlist;
  },
});

// Mutation: Create playlist
export const createPlaylist = mutation({
  args: {
    name: v.string(),
    appleMusicId: v.optional(v.string()),
    description: v.optional(v.string()),
    isPublic: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required to create playlist");
    }

    // Validate playlist name
    if (args.name.trim().length === 0) {
      throw new Error("Playlist name is required");
    }

    if (args.name.length > 100) {
      throw new Error("Playlist name cannot exceed 100 characters");
    }

    // Check if playlist with same Apple Music ID already exists
    if (args.appleMusicId) {
      const existingPlaylist = await ctx.db
        .query("playlists")
        .withIndex("by_apple_music_id", (q) => q.eq("appleMusicId", args.appleMusicId))
        .filter((q) => q.eq(q.field("userId"), userId))
        .first();

      if (existingPlaylist) {
        throw new Error("Playlist with this Apple Music ID already exists");
      }
    }

    const now = Date.now();
    const playlistId = await ctx.db.insert("playlists", {
      userId,
      name: args.name.trim(),
      appleMusicId: args.appleMusicId,
      description: args.description?.trim(),
      isPublic: args.isPublic ?? false,
      active: true,
      createdAt: now,
      updatedAt: now,
    });

    return {
      playlistId,
      message: "Playlist created successfully",
    };
  },
});

// Mutation: Update playlist
export const updatePlaylist = mutation({
  args: {
    playlistId: v.id("playlists"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    isPublic: v.optional(v.boolean()),
    active: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const playlist = await ctx.db.get(args.playlistId);
    if (!playlist) {
      throw new Error("Playlist not found");
    }

    // Verify ownership
    if (playlist.userId !== userId) {
      throw new Error("Access denied: You can only update your own playlists");
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: Date.now(),
    };

    if (args.name !== undefined) {
      if (args.name.trim().length === 0) {
        throw new Error("Playlist name cannot be empty");
      }
      if (args.name.length > 100) {
        throw new Error("Playlist name cannot exceed 100 characters");
      }
      updateData.name = args.name.trim();
    }

    if (args.description !== undefined) {
      updateData.description = args.description?.trim();
    }

    if (args.isPublic !== undefined) {
      updateData.isPublic = args.isPublic;
    }

    if (args.active !== undefined) {
      updateData.active = args.active;
    }

    await ctx.db.patch(args.playlistId, updateData);

    return {
      playlistId: args.playlistId,
      message: "Playlist updated successfully",
    };
  },
});

// Mutation: Delete playlist
export const deletePlaylist = mutation({
  args: {
    playlistId: v.id("playlists"),
    confirmDelete: v.boolean(),
  },
  handler: async (ctx, args) => {
    if (!args.confirmDelete) {
      throw new Error("Delete confirmation required");
    }

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const playlist = await ctx.db.get(args.playlistId);
    if (!playlist) {
      throw new Error("Playlist not found");
    }

    // Verify ownership
    if (playlist.userId !== userId) {
      throw new Error("Access denied: You can only delete your own playlists");
    }

    await ctx.db.delete(args.playlistId);

    return {
      success: true,
      message: "Playlist deleted successfully",
    };
  },
});

// Action: Sync playlists from Apple Music
export const syncPlaylistsFromAppleMusic = action({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Check if user has valid Apple Music token
    const tokenStatus = await ctx.runQuery(api.appleMusicTokens.hasValidAppleMusicToken, {});
    if (!tokenStatus.isValid) {
      throw new Error("Valid Apple Music token required for playlist sync");
    }

    // Get playlists from Apple Music
    const appleMusicPlaylists = await ctx.runAction(api.appleMusicApi.getUserPlaylists, {
      limit: 50,
    });

    const syncResults = {
      total: appleMusicPlaylists.playlists.length,
      created: 0,
      updated: 0,
      skipped: 0,
      errors: [] as string[],
    };

    for (const applePlaylist of appleMusicPlaylists.playlists) {
      try {
        // Check if playlist already exists in our database
        const existingPlaylist = await ctx.runQuery(api.playlists.getPlaylistByAppleMusicId, {
          appleMusicId: applePlaylist.id,
        });

        if (existingPlaylist) {
          // Update existing playlist
          await ctx.runMutation(api.playlists.updatePlaylist, {
            playlistId: existingPlaylist._id,
            name: applePlaylist.name,
            description: applePlaylist.description,
            isPublic: applePlaylist.isPublic,
          });
          syncResults.updated++;
        } else {
          // Create new playlist
          await ctx.runMutation(api.playlists.createPlaylist, {
            name: applePlaylist.name,
            appleMusicId: applePlaylist.id,
            description: applePlaylist.description,
            isPublic: applePlaylist.isPublic,
          });
          syncResults.created++;
        }
      } catch (error) {
        syncResults.errors.push(`Failed to sync playlist "${applePlaylist.name}": ${error}`);
        syncResults.skipped++;
      }
    }

    return {
      syncResults,
      message: `Sync completed: ${syncResults.created} created, ${syncResults.updated} updated, ${syncResults.skipped} skipped`,
      syncedAt: Date.now(),
    };
  },
});

// Action: Create playlist in Apple Music and sync to database
export const createAppleMusicPlaylist = action({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    isPublic: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Check if user has valid Apple Music token
    const tokenStatus = await ctx.runQuery(api.appleMusicTokens.hasValidAppleMusicToken, {});
    if (!tokenStatus.isValid) {
      throw new Error("Valid Apple Music token required to create playlist");
    }

    // Create playlist in Apple Music
    const appleMusicResult = await ctx.runAction(api.appleMusicApi.createPlaylist, {
      name: args.name,
      description: args.description,
      isPublic: args.isPublic,
    });

    // Create playlist in our database
    const playlistResult = await ctx.runMutation(api.playlists.createPlaylist, {
      name: args.name,
      appleMusicId: appleMusicResult.playlist.id,
      description: args.description,
      isPublic: args.isPublic,
    });

    return {
      playlist: {
        id: playlistResult.playlistId,
        appleMusicId: appleMusicResult.playlist.id,
        name: args.name,
        description: args.description,
        isPublic: args.isPublic ?? false,
      },
      message: "Playlist created in Apple Music and synced to database",
      createdAt: Date.now(),
    };
  },
});

// Query: Get playlist sync status
export const getPlaylistSyncStatus = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return {
        isAuthenticated: false,
        canSync: false,
        totalPlaylists: 0,
        syncedPlaylists: 0,
      };
    }

    // Check Apple Music token status
    const tokenStatus = await ctx.runQuery(api.appleMusicTokens.hasValidAppleMusicToken, {});

    // Get user's playlists
    const allPlaylists = await ctx.db
      .query("playlists")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();

    const syncedPlaylists = allPlaylists.filter(p => p.appleMusicId).length;

    return {
      isAuthenticated: true,
      canSync: tokenStatus.isValid,
      hasValidToken: tokenStatus.isValid,
      tokenExpired: tokenStatus.isExpired,
      totalPlaylists: allPlaylists.length,
      syncedPlaylists,
      unsyncedPlaylists: allPlaylists.length - syncedPlaylists,
      lastSyncAvailable: false, // TODO: Track last sync time
    };
  },
});
