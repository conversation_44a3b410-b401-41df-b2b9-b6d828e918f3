/**
 * Subscription Management Test Suite
 * 
 * This module provides comprehensive testing for subscription management,
 * billing operations, feature access control, and Stripe integration.
 */

import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Test Query: Validate subscription operations
export const testSubscriptionOperations = query({
  args: {},
  handler: async (ctx) => {
    const results = {
      timestamp: Date.now(),
      tests: [] as any[],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
      },
    };

    const userId = await getAuthUserId(ctx);
    
    // Test 1: Check authentication
    try {
      results.tests.push({
        name: "authentication_check",
        status: userId ? "passed" : "failed",
        result: userId ? `Authenticated as ${userId}` : "Not authenticated",
      });
      if (userId) results.summary.passed++;
      else results.summary.failed++;
    } catch (error) {
      results.tests.push({
        name: "authentication_check",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Test 2: Check subscription plans
    try {
      const plans = await ctx.db.query("subscriptionPlans").take(5);
      results.tests.push({
        name: "subscription_plans_query",
        status: "passed",
        result: `Found ${plans.length} subscription plans`,
      });
      results.summary.passed++;
    } catch (error) {
      results.tests.push({
        name: "subscription_plans_query",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Test 3: Check DJ subscriptions
    try {
      const subscriptions = await ctx.db.query("djSubscriptions").take(5);
      results.tests.push({
        name: "dj_subscriptions_query",
        status: "passed",
        result: `Found ${subscriptions.length} DJ subscriptions`,
      });
      results.summary.passed++;
    } catch (error) {
      results.tests.push({
        name: "dj_subscriptions_query",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    if (userId) {
      // Test 4: Check current user subscription status
      try {
        const djProfile = await ctx.db
          .query("djProfiles")
          .withIndex("by_user", (q) => q.eq("userId", userId))
          .first();

        results.tests.push({
          name: "user_dj_profile_check",
          status: djProfile ? "passed" : "failed",
          result: djProfile ? "DJ profile found" : "No DJ profile",
          profileId: djProfile?._id,
        });
        if (djProfile) results.summary.passed++;
        else results.summary.failed++;

        if (djProfile) {
          const subscription = await ctx.db
            .query("djSubscriptions")
            .withIndex("by_dj", (q) => q.eq("djId", djProfile._id))
            .first();

          results.tests.push({
            name: "user_subscription_check",
            status: "passed",
            result: subscription ? "Subscription found" : "No subscription",
            subscriptionId: subscription?._id,
            subscriptionStatus: subscription?.status,
          });
          results.summary.passed++;
        }
      } catch (error) {
        results.tests.push({
          name: "user_subscription_check",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }
    }

    results.summary.total = results.tests.length;
    return results;
  },
});

// Test Mutation: Create test subscription plan (updated for event-based model)
export const createTestSubscriptionPlan = mutation({
  args: {
    planName: v.optional(v.string()),
    priceAmount: v.optional(v.number()),
    durationMinutes: v.optional(v.number()),
    maxRequestsPerSession: v.optional(v.union(v.number(), v.literal("unlimited"))),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const planName = args.planName || `Test Plan ${Date.now()}`;
    const priceAmount = args.priceAmount || 999; // $9.99
    const durationMinutes = args.durationMinutes || 1440; // 24 hours
    const maxRequestsPerSession = args.maxRequestsPerSession || 100;

    const now = Date.now();
    const planId = await ctx.db.insert("subscriptionPlans", {
      name: planName,
      description: "Test subscription plan for development",
      priceAmount,
      priceCurrency: "usd",
      durationMinutes,
      maxRequestsPerSession,
      stripePriceId: `price_test_${now}`,
      active: true,
      createdAt: now,
      updatedAt: now,
    });

    return {
      planId,
      planName,
      priceAmount,
      durationMinutes,
      maxRequestsPerSession,
      message: "Test subscription plan created successfully",
    };
  },
});

// Test Mutation: Create test subscription
export const createTestSubscription = mutation({
  args: {
    planId: v.id("subscriptionPlans"),
    durationDays: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      throw new Error("DJ profile required to create test subscription");
    }

    // Check if subscription already exists
    const existingSubscription = await ctx.db
      .query("djSubscriptions")
      .withIndex("by_dj", (q) => q.eq("djId", djProfile._id))
      .first();

    if (existingSubscription) {
      return {
        subscriptionId: existingSubscription._id,
        message: "Subscription already exists",
        existing: true,
      };
    }

    const plan = await ctx.db.get(args.planId);
    if (!plan) {
      throw new Error("Subscription plan not found");
    }

    const now = Date.now();
    const durationMs = (args.durationDays || 30) * 24 * 60 * 60 * 1000;
    const endTime = now + durationMs;

    const subscriptionId = await ctx.db.insert("djSubscriptions", {
      djId: djProfile._id,
      planId: args.planId,
      status: "active",
      currentPeriodStart: now,
      currentPeriodEnd: endTime,
      cancelAtPeriodEnd: false,
      stripeCustomerId: `cus_test_${now}`,
      stripeSubscriptionId: `sub_test_${now}`,
      createdAt: now,
      updatedAt: now,
    });

    return {
      subscriptionId,
      planId: args.planId,
      planName: plan.name,
      durationDays: args.durationDays || 30,
      message: "Test subscription created successfully",
      existing: false,
    };
  },
});

// Test Action: Comprehensive subscription workflow test
export const testSubscriptionWorkflow = action({
  args: {
    includeFeatureAccess: v.optional(v.boolean()),
    includeBilling: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const results = {
      timestamp: Date.now(),
      workflow: "subscription_management",
      steps: [] as any[],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
      },
    };

    // Step 1: Validate operations
    try {
      const validation = await ctx.runQuery(api.subscriptionTestSuite.testSubscriptionOperations, {});
      results.steps.push({
        name: "validate_operations",
        status: validation.summary.failed === 0 ? "passed" : "failed",
        result: validation,
      });
      if (validation.summary.failed === 0) results.summary.passed++;
      else results.summary.failed++;
    } catch (error) {
      results.steps.push({
        name: "validate_operations",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    // Step 2: Create test subscription plan
    try {
      const planResult = await ctx.runMutation(api.subscriptionTestSuite.createTestSubscriptionPlan, {
        planName: "Workflow Test Plan",
        priceAmount: 1999, // $19.99
        durationHours: 168, // 1 week
      });
      
      results.steps.push({
        name: "create_test_plan",
        status: "passed",
        result: planResult,
      });
      results.summary.passed++;

      const planId = planResult.planId;

      // Step 3: Create test subscription
      try {
        const subscriptionResult = await ctx.runMutation(api.subscriptionTestSuite.createTestSubscription, {
          planId,
          durationDays: 7,
        });
        
        results.steps.push({
          name: "create_test_subscription",
          status: "passed",
          result: subscriptionResult,
        });
        results.summary.passed++;

        // Step 4: Test subscription status check
        try {
          const statusCheck = await ctx.runQuery(api.djSubscriptions.checkSubscriptionStatus, {});
          
          results.steps.push({
            name: "check_subscription_status",
            status: "passed",
            result: statusCheck,
          });
          results.summary.passed++;

          // Step 5: Test feature access (if requested)
          if (args.includeFeatureAccess) {
            try {
              const featureCheck = await ctx.runQuery(api.featureAccess.canCreateSession, {});
              
              results.steps.push({
                name: "test_feature_access",
                status: "passed",
                result: featureCheck,
              });
              results.summary.passed++;
            } catch (error) {
              results.steps.push({
                name: "test_feature_access",
                status: "failed",
                error: String(error),
              });
              results.summary.failed++;
            }
          }

          // Step 6: Test billing summary (if requested)
          if (args.includeBilling) {
            try {
              const billingSummary = await ctx.runAction(api.billing.getBillingSummary, {});
              
              results.steps.push({
                name: "test_billing_summary",
                status: "passed",
                result: billingSummary,
              });
              results.summary.passed++;
            } catch (error) {
              results.steps.push({
                name: "test_billing_summary",
                status: "failed",
                error: String(error),
              });
              results.summary.failed++;
            }
          }

        } catch (error) {
          results.steps.push({
            name: "check_subscription_status",
            status: "failed",
            error: String(error),
          });
          results.summary.failed++;
        }

      } catch (error) {
        results.steps.push({
          name: "create_test_subscription",
          status: "failed",
          error: String(error),
        });
        results.summary.failed++;
      }

    } catch (error) {
      results.steps.push({
        name: "create_test_plan",
        status: "failed",
        error: String(error),
      });
      results.summary.failed++;
    }

    results.summary.total = results.steps.length;
    return results;
  },
});

// Test Query: Feature access validation
export const testFeatureAccess = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return {
        isAuthenticated: false,
        tests: [],
      };
    }

    const tests = [];

    // Test session creation access
    try {
      const canCreateSession = await ctx.runQuery(api.featureAccess.canCreateSession, {});
      tests.push({
        feature: "create_session",
        status: "passed",
        result: canCreateSession,
      });
    } catch (error) {
      tests.push({
        feature: "create_session",
        status: "failed",
        error: String(error),
      });
    }

    // Test wedding mode access
    try {
      const canUseWeddingMode = await ctx.runQuery(api.featureAccess.canUseWeddingMode, {});
      tests.push({
        feature: "wedding_mode",
        status: "passed",
        result: canUseWeddingMode,
      });
    } catch (error) {
      tests.push({
        feature: "wedding_mode",
        status: "failed",
        error: String(error),
      });
    }

    // Test sponsor branding access
    try {
      const canUseSponsorBranding = await ctx.runQuery(api.featureAccess.canUseSponsorBranding, {});
      tests.push({
        feature: "sponsor_branding",
        status: "passed",
        result: canUseSponsorBranding,
      });
    } catch (error) {
      tests.push({
        feature: "sponsor_branding",
        status: "failed",
        error: String(error),
      });
    }

    // Test feature summary
    try {
      const featureSummary = await ctx.runQuery(api.featureAccess.getUserFeatureSummary, {});
      tests.push({
        feature: "feature_summary",
        status: "passed",
        result: featureSummary,
      });
    } catch (error) {
      tests.push({
        feature: "feature_summary",
        status: "failed",
        error: String(error),
      });
    }

    return {
      isAuthenticated: true,
      userId,
      tests,
      summary: {
        total: tests.length,
        passed: tests.filter(t => t.status === "passed").length,
        failed: tests.filter(t => t.status === "failed").length,
      },
    };
  },
});

// Test Mutation: Initialize and validate standard event-based plans
export const testStandardPlans = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Initialize standard plans
    const initResult = await ctx.runMutation(api.subscriptionPlans.initializeStandardPlans, {});

    // Validate each plan
    const expectedPlans = [
      { name: "Free", durationMinutes: 20, maxRequestsPerSession: 3, priceAmount: 0 },
      { name: "24-Hour Pass", durationMinutes: 1440, maxRequestsPerSession: 100, priceAmount: 999 },
      { name: "48-Hour Pass", durationMinutes: 2880, maxRequestsPerSession: 100, priceAmount: 1799 },
      { name: "7-Day Pass", durationMinutes: 10080, maxRequestsPerSession: 100, priceAmount: 4999 },
    ];

    const validationResults = [];

    for (const expectedPlan of expectedPlans) {
      const plan = await ctx.db
        .query("subscriptionPlans")
        .withIndex("by_name", (q) => q.eq("name", expectedPlan.name))
        .first();

      if (!plan) {
        validationResults.push({
          name: expectedPlan.name,
          status: "missing",
          error: "Plan not found",
        });
        continue;
      }

      const isValid =
        plan.durationMinutes === expectedPlan.durationMinutes &&
        plan.maxRequestsPerSession === expectedPlan.maxRequestsPerSession &&
        plan.priceAmount === expectedPlan.priceAmount;

      validationResults.push({
        name: expectedPlan.name,
        status: isValid ? "valid" : "invalid",
        expected: expectedPlan,
        actual: {
          durationMinutes: plan.durationMinutes,
          maxRequestsPerSession: plan.maxRequestsPerSession,
          priceAmount: plan.priceAmount,
        },
      });
    }

    return {
      initResult,
      validationResults,
      allValid: validationResults.every(r => r.status === "valid"),
      message: "Standard plans test completed",
    };
  },
});
