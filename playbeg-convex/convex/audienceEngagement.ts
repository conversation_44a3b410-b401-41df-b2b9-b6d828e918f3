/**
 * Live Audience Engagement Features
 * 
 * This module provides real-time audience engagement capabilities including
 * voting and rating systems, live reactions, audience interaction tracking,
 * and session discovery features for enhanced user participation.
 */

import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Query: Get song request votes
export const getSongRequestVotes = query({
  args: {
    requestId: v.id("songRequests"),
  },
  handler: async (ctx, args) => {
    const votes = await ctx.db
      .query("songRequestVotes")
      .withIndex("by_request", (q) => q.eq("requestId", args.requestId))
      .collect();

    const upvotes = votes.filter(v => v.voteType === "upvote").length;
    const downvotes = votes.filter(v => v.voteType === "downvote").length;
    const totalVotes = votes.length;
    const score = upvotes - downvotes;

    return {
      requestId: args.requestId,
      upvotes,
      downvotes,
      totalVotes,
      score,
      votes: votes.map(vote => ({
        id: vote._id,
        userId: vote.userId,
        voteType: vote.voteType,
        createdAt: vote.createdAt,
      })),
    };
  },
});

// Mutation: Vote on song request
export const voteOnSongRequest = mutation({
  args: {
    requestId: v.id("songRequests"),
    voteType: v.union(v.literal("upvote"), v.literal("downvote")),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required to vote");
    }

    // Check if user already voted on this request
    const existingVote = await ctx.db
      .query("songRequestVotes")
      .withIndex("by_request_user", (q) => 
        q.eq("requestId", args.requestId).eq("userId", userId)
      )
      .first();

    const now = Date.now();

    if (existingVote) {
      // Update existing vote
      await ctx.db.patch(existingVote._id, {
        voteType: args.voteType,
        updatedAt: now,
      });

      return {
        voteId: existingVote._id,
        action: "updated",
        voteType: args.voteType,
        message: "Vote updated successfully",
      };
    } else {
      // Create new vote
      const voteId = await ctx.db.insert("songRequestVotes", {
        requestId: args.requestId,
        userId,
        voteType: args.voteType,
        createdAt: now,
        updatedAt: now,
      });

      return {
        voteId,
        action: "created",
        voteType: args.voteType,
        message: "Vote cast successfully",
      };
    }
  },
});

// Mutation: Remove vote from song request
export const removeVoteFromSongRequest = mutation({
  args: {
    requestId: v.id("songRequests"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const existingVote = await ctx.db
      .query("songRequestVotes")
      .withIndex("by_request_user", (q) => 
        q.eq("requestId", args.requestId).eq("userId", userId)
      )
      .first();

    if (!existingVote) {
      return {
        success: true,
        message: "No vote to remove",
      };
    }

    await ctx.db.delete(existingVote._id);

    return {
      success: true,
      message: "Vote removed successfully",
    };
  },
});

// Query: Get session reactions
export const getSessionReactions = query({
  args: {
    sessionId: v.id("sessions"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 50;

    const reactions = await ctx.db
      .query("sessionReactions")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .order("desc")
      .take(limit);

    // Count reactions by type
    const reactionCounts: Record<string, number> = {};
    reactions.forEach(reaction => {
      reactionCounts[reaction.reactionType] = (reactionCounts[reaction.reactionType] || 0) + 1;
    });

    // Get recent reactions (last 5 minutes)
    const recentTime = Date.now() - (5 * 60 * 1000);
    const recentReactions = reactions.filter(r => r.createdAt > recentTime);

    return {
      sessionId: args.sessionId,
      reactions: reactions.map(reaction => ({
        id: reaction._id,
        userId: reaction.userId,
        reactionType: reaction.reactionType,
        createdAt: reaction.createdAt,
        timeAgo: getTimeAgo(reaction.createdAt),
      })),
      reactionCounts,
      recentReactions: recentReactions.length,
      totalReactions: reactions.length,
      lastUpdated: Date.now(),
    };
  },
});

// Mutation: Add session reaction
export const addSessionReaction = mutation({
  args: {
    sessionId: v.id("sessions"),
    reactionType: v.union(
      v.literal("like"),
      v.literal("love"),
      v.literal("fire"),
      v.literal("party"),
      v.literal("clap"),
      v.literal("wow"),
      v.literal("dance")
    ),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required to react");
    }

    // Verify session exists
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    const now = Date.now();
    const reactionId = await ctx.db.insert("sessionReactions", {
      sessionId: args.sessionId,
      userId,
      reactionType: args.reactionType,
      createdAt: now,
    });

    // Track user interaction
    await ctx.runMutation(api.liveAnalytics.trackUserInteraction, {
      sessionId: args.sessionId,
      interactionType: "reaction_added",
      metadata: {
        reactionType: args.reactionType,
        additionalData: { reactionId },
      },
    });

    return {
      reactionId,
      reactionType: args.reactionType,
      message: "Reaction added successfully",
    };
  },
});

// Query: Get trending sessions
export const getTrendingSessions = query({
  args: {
    limit: v.optional(v.number()),
    timeWindow: v.optional(v.union(
      v.literal("1hour"),
      v.literal("6hours"),
      v.literal("24hours")
    )),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 10;
    const timeWindow = args.timeWindow ?? "6hours";
    
    // Calculate time threshold
    const timeThresholds = {
      "1hour": 60 * 60 * 1000,
      "6hours": 6 * 60 * 60 * 1000,
      "24hours": 24 * 60 * 60 * 1000,
    };
    
    const cutoffTime = Date.now() - timeThresholds[timeWindow];

    // Get active sessions
    const activeSessions = await ctx.db
      .query("sessions")
      .withIndex("by_active", (q) => q.eq("active", true))
      .collect();

    // Calculate trending scores for each session
    const sessionScores = await Promise.all(
      activeSessions.map(async (session) => {
        // Get recent requests
        const recentRequests = await ctx.db
          .query("songRequests")
          .withIndex("by_session", (q) => q.eq("sessionId", session._id))
          .filter((q) => q.gte(q.field("createdAt"), cutoffTime))
          .collect();

        // Get recent reactions
        const recentReactions = await ctx.db
          .query("sessionReactions")
          .withIndex("by_session", (q) => q.eq("sessionId", session._id))
          .filter((q) => q.gte(q.field("createdAt"), cutoffTime))
          .collect();

        // Get recent votes
        const recentVotes = await ctx.db
          .query("songRequestVotes")
          .filter((q) => q.gte(q.field("createdAt"), cutoffTime))
          .collect();

        const sessionVotes = recentVotes.filter(async (vote) => {
          const request = await ctx.db.get(vote.requestId);
          return request?.sessionId === session._id;
        });

        // Calculate trending score
        const requestScore = recentRequests.length * 2;
        const reactionScore = recentReactions.length * 1;
        const voteScore = sessionVotes.length * 0.5;
        const uniqueUsers = new Set([
          ...recentRequests.map(r => r.userId),
          ...recentReactions.map(r => r.userId),
        ]).size;
        const userScore = uniqueUsers * 3;

        const trendingScore = requestScore + reactionScore + voteScore + userScore;

        return {
          session,
          trendingScore,
          metrics: {
            recentRequests: recentRequests.length,
            recentReactions: recentReactions.length,
            recentVotes: sessionVotes.length,
            uniqueUsers,
          },
        };
      })
    );

    // Sort by trending score and get top sessions
    const trendingSessions = sessionScores
      .filter(s => s.trendingScore > 0)
      .sort((a, b) => b.trendingScore - a.trendingScore)
      .slice(0, limit);

    // Get DJ information for each session
    const sessionsWithDjInfo = await Promise.all(
      trendingSessions.map(async ({ session, trendingScore, metrics }) => {
        const djProfile = await ctx.db.get(session.djId);
        
        return {
          id: session._id,
          title: session.title,
          description: session.description,
          startTime: session.startTime,
          isWeddingMode: session.isWeddingMode,
          trendingScore,
          metrics,
          dj: djProfile ? {
            id: djProfile._id,
            stageName: djProfile.stageName,
            profilePictureStorageId: djProfile.profilePictureStorageId,
          } : null,
        };
      })
    );

    return {
      sessions: sessionsWithDjInfo,
      timeWindow,
      lastUpdated: Date.now(),
    };
  },
});

// Query: Get session engagement summary
export const getSessionEngagementSummary = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return null;
    }

    // Get all requests
    const requests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    // Get all votes
    const allVotes = await ctx.db
      .query("songRequestVotes")
      .collect();
    
    const sessionVotes = allVotes.filter(async (vote) => {
      const request = await ctx.db.get(vote.requestId);
      return request?.sessionId === args.sessionId;
    });

    // Get all reactions
    const reactions = await ctx.db
      .query("sessionReactions")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    // Calculate engagement metrics
    const uniqueRequesters = new Set(requests.map(r => r.userId)).size;
    const uniqueReactors = new Set(reactions.map(r => r.userId)).size;
    const totalUniqueUsers = new Set([
      ...requests.map(r => r.userId),
      ...reactions.map(r => r.userId),
    ]).size;

    // Calculate engagement rate
    const sessionDuration = session.active 
      ? Date.now() - session.startTime
      : (session.endTime || Date.now()) - session.startTime;
    
    const engagementRate = sessionDuration > 0 
      ? Math.round(((requests.length + reactions.length) / (sessionDuration / (1000 * 60))) * 100) / 100
      : 0;

    return {
      sessionId: args.sessionId,
      engagement: {
        totalRequests: requests.length,
        totalReactions: reactions.length,
        totalVotes: sessionVotes.length,
        uniqueRequesters,
        uniqueReactors,
        totalUniqueUsers,
        engagementRate, // interactions per minute
      },
      activity: {
        lastRequestTime: requests.length > 0 
          ? Math.max(...requests.map(r => r.createdAt))
          : null,
        lastReactionTime: reactions.length > 0 
          ? Math.max(...reactions.map(r => r.createdAt))
          : null,
        sessionDuration,
      },
      distribution: {
        requestsByStatus: {
          pending: requests.filter(r => r.status === "pending").length,
          approved: requests.filter(r => r.status === "approved").length,
          rejected: requests.filter(r => r.status === "rejected").length,
          played: requests.filter(r => r.status === "played").length,
        },
        reactionsByType: reactions.reduce((acc, reaction) => {
          acc[reaction.reactionType] = (acc[reaction.reactionType] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
      },
      lastUpdated: Date.now(),
    };
  },
});

// Action: Get live session discovery feed
export const getLiveSessionDiscoveryFeed = action({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 20;

    // Get trending sessions
    const trendingSessions = await ctx.runQuery(api.audienceEngagement.getTrendingSessions, {
      limit: Math.floor(limit / 2),
      timeWindow: "6hours",
    });

    // Get recently started sessions
    const recentSessions = await ctx.runQuery(api.realTimeSubscriptions.getActiveSessions, {
      limit: Math.floor(limit / 2),
    });

    // Combine and deduplicate
    const allSessions = [...trendingSessions.sessions, ...recentSessions.sessions];
    const uniqueSessions = allSessions.filter((session, index, self) => 
      index === self.findIndex(s => s.id === session.id)
    );

    // Sort by a combination of trending score and recency
    const scoredSessions = uniqueSessions.map(session => {
      const trendingScore = 'trendingScore' in session ? session.trendingScore : 0;
      const recencyScore = (Date.now() - session.startTime) / (1000 * 60 * 60); // Hours ago
      const combinedScore = trendingScore + Math.max(0, 10 - recencyScore); // Boost recent sessions
      
      return {
        ...session,
        discoveryScore: combinedScore,
      };
    });

    const discoveryFeed = scoredSessions
      .sort((a, b) => b.discoveryScore - a.discoveryScore)
      .slice(0, limit);

    return {
      sessions: discoveryFeed,
      feedType: "discovery",
      lastUpdated: Date.now(),
    };
  },
});

// Helper function to calculate time ago
function getTimeAgo(timestamp: number): string {
  const now = Date.now();
  const diff = now - timestamp;
  
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  
  if (seconds < 60) return `${seconds}s ago`;
  if (minutes < 60) return `${minutes}m ago`;
  return `${hours}h ago`;
}
