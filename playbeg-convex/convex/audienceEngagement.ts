/**
 * Audience Engagement Features
 * 
 * Interactive features for audience participation including voting on song requests,
 * real-time reactions, chat messages, and engagement analytics.
 */

import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Cast a vote on a song request
export const voteOnSongRequest = mutation({
  args: {
    songRequestId: v.id("songRequests"),
    voteType: v.union(v.literal("upvote"), v.literal("downvote")),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required to vote");
    }

    const songRequest = await ctx.db.get(args.songRequestId);
    if (!songRequest) {
      throw new Error("Song request not found");
    }

    // Check if user already voted on this request
    const existingVote = await ctx.db
      .query("songRequestVotes")
      .withIndex("by_user_request", (q) =>
        q.eq("userId", userId).eq("requestId", args.songRequestId)
      )
      .first();

    if (existingVote) {
      // Update existing vote
      await ctx.db.patch(existingVote._id, {
        voteType: args.voteType,
        updatedAt: Date.now(),
      });

      // Record analytics event
      await ctx.db.insert("analyticsEvents", {
        sessionId: songRequest.sessionId,
        eventType: "vote_cast",
        eventData: {
          userId,
          songRequestId: args.songRequestId,
          voteValue: args.voteType === "upvote" ? 1 : -1,
          metadata: { action: "updated" },
        },
        userId,
        timestamp: Date.now(),
      });

      return existingVote._id;
    } else {
      // Create new vote
      const voteId = await ctx.db.insert("songRequestVotes", {
        requestId: args.songRequestId,
        userId,
        voteType: args.voteType,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      // Record analytics event
      await ctx.db.insert("analyticsEvents", {
        sessionId: songRequest.sessionId,
        eventType: "vote_cast",
        eventData: {
          userId,
          songRequestId: args.songRequestId,
          voteValue: args.voteType === "upvote" ? 1 : -1,
          metadata: { action: "created" },
        },
        userId,
        timestamp: Date.now(),
      });

      return voteId;
    }
  },
});

// Remove a vote
export const removeVote = mutation({
  args: {
    songRequestId: v.id("songRequests"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const vote = await ctx.db
      .query("songRequestVotes")
      .withIndex("by_user_request", (q) =>
        q.eq("userId", userId).eq("requestId", args.songRequestId)
      )
      .first();

    if (vote) {
      await ctx.db.delete(vote._id);
      return { success: true };
    }

    return { success: false, message: "Vote not found" };
  },
});

// Get vote counts for song requests
export const getSongRequestVotes = query({
  args: {
    songRequestIds: v.array(v.id("songRequests")),
  },
  handler: async (ctx, args) => {
    const voteCounts = await Promise.all(
      args.songRequestIds.map(async (songRequestId) => {
        const votes = await ctx.db
          .query("songRequestVotes")
          .withIndex("by_request", (q) => q.eq("requestId", songRequestId))
          .collect();

        const upvotes = votes.filter(v => v.voteType === "upvote").length;
        const downvotes = votes.filter(v => v.voteType === "downvote").length;
        const score = upvotes - downvotes;

        return {
          songRequestId,
          upvotes,
          downvotes,
          totalVotes: votes.length,
          score,
        };
      })
    );

    return voteCounts;
  },
});

// Add a reaction to a session
export const addReaction = mutation({
  args: {
    sessionId: v.id("sessions"),
    reactionType: v.union(
      v.literal("🔥"), // fire
      v.literal("❤️"), // heart
      v.literal("👏"), // clap
      v.literal("🎵"), // musical note
      v.literal("🎉"), // party
      v.literal("😍"), // heart eyes
      v.literal("🤘"), // rock on
      v.literal("💯"), // 100
    ),
    targetType: v.optional(v.union(
      v.literal("session"),
      v.literal("song_request"),
    )),
    targetId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required to react");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session || !session.active) {
      throw new Error("Session not found or inactive");
    }

    // Create reaction
    const reactionId = await ctx.db.insert("sessionReactions", {
      sessionId: args.sessionId,
      userId,
      reactionType: args.reactionType,
      targetType: args.targetType || "session",
      targetId: args.targetId,
      createdAt: Date.now(),
    });

    // Record analytics event
    await ctx.db.insert("analyticsEvents", {
      sessionId: args.sessionId,
      eventType: "reaction_added",
      eventData: {
        userId,
        reactionType: args.reactionType,
        metadata: {
          targetType: args.targetType,
          targetId: args.targetId,
        },
      },
      userId,
      timestamp: Date.now(),
    });

    return reactionId;
  },
});

// Get recent reactions for a session
export const getSessionReactions = query({
  args: {
    sessionId: v.id("sessions"),
    limit: v.optional(v.number()),
    targetType: v.optional(v.union(
      v.literal("session"),
      v.literal("song_request"),
    )),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("sessionReactions")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId));

    if (args.targetType) {
      query = query.filter((q) => q.eq(q.field("targetType"), args.targetType));
    }

    const reactions = await query
      .order("desc")
      .take(args.limit || 50);

    // Get user details for each reaction
    const reactionsWithUsers = await Promise.all(
      reactions.map(async (reaction) => {
        const user = await ctx.db.get(reaction.userId);
        return {
          ...reaction,
          user: user ? {
            _id: user._id,
            name: user.name,
          } : null,
          timeAgo: formatTimeAgo(Date.now() - reaction.createdAt),
        };
      })
    );

    return reactionsWithUsers;
  },
});

// Get reaction summary for a session
export const getReactionSummary = query({
  args: {
    sessionId: v.id("sessions"),
    timeRange: v.optional(v.union(
      v.literal("last_hour"),
      v.literal("last_day"),
      v.literal("session_start"),
    )),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return null;
    }

    // Calculate time range
    let startTime: number;
    const now = Date.now();
    
    switch (args.timeRange) {
      case "last_hour":
        startTime = now - (60 * 60 * 1000);
        break;
      case "last_day":
        startTime = now - (24 * 60 * 60 * 1000);
        break;
      case "session_start":
      default:
        startTime = session.createdAt;
        break;
    }

    const reactions = await ctx.db
      .query("sessionReactions")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .filter((q) => q.gte(q.field("createdAt"), startTime))
      .collect();

    // Count reactions by type
    const reactionCounts = reactions.reduce((acc, reaction) => {
      acc[reaction.reactionType] = (acc[reaction.reactionType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Get top reactions
    const topReactions = Object.entries(reactionCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([type, count]) => ({ type, count }));

    return {
      sessionId: args.sessionId,
      timeRange: args.timeRange || "session_start",
      totalReactions: reactions.length,
      uniqueReactors: new Set(reactions.map(r => r.userId)).size,
      reactionCounts,
      topReactions,
      lastUpdated: now,
    };
  },
});

// Send a chat message (simple implementation)
export const sendChatMessage = mutation({
  args: {
    sessionId: v.id("sessions"),
    message: v.string(),
    messageType: v.optional(v.union(
      v.literal("text"),
      v.literal("emoji"),
      v.literal("system"),
    )),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required to chat");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session || !session.active) {
      throw new Error("Session not found or inactive");
    }

    // Validate message length
    if (args.message.length > 500) {
      throw new Error("Message too long (max 500 characters)");
    }

    // Create chat message
    const messageId = await ctx.db.insert("chatMessages", {
      sessionId: args.sessionId,
      userId,
      message: args.message,
      messageType: args.messageType || "text",
      createdAt: Date.now(),
    });

    // Record analytics event
    await ctx.db.insert("analyticsEvents", {
      sessionId: args.sessionId,
      eventType: "chat_message",
      eventData: {
        userId,
        messageContent: args.message.substring(0, 100), // Truncate for analytics
        metadata: {
          messageType: args.messageType,
          messageLength: args.message.length,
        },
      },
      userId,
      timestamp: Date.now(),
    });

    return messageId;
  },
});

// Get chat messages for a session
export const getChatMessages = query({
  args: {
    sessionId: v.id("sessions"),
    limit: v.optional(v.number()),
    since: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("chatMessages")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId));

    if (args.since !== undefined) {
      query = query.filter((q) => q.gte(q.field("createdAt"), args.since!));
    }

    const messages = await query
      .order("desc")
      .take(args.limit || 50);

    // Get user details for each message
    const messagesWithUsers = await Promise.all(
      messages.map(async (message) => {
        const user = await ctx.db.get(message.userId);
        return {
          ...message,
          user: user ? {
            _id: user._id,
            name: user.name,
          } : null,
          timeAgo: formatTimeAgo(Date.now() - message.createdAt),
        };
      })
    );

    return messagesWithUsers.reverse(); // Return in chronological order
  },
});

// Helper function to format time ago
function formatTimeAgo(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}h ago`;
  } else if (minutes > 0) {
    return `${minutes}m ago`;
  } else {
    return `${seconds}s ago`;
  }
}
