/**
 * Session CRUD Operations
 * 
 * This module handles all database operations for DJ sessions including
 * creation, reading, updating, and deletion with proper authentication,
 * wedding mode configuration, and sponsor settings.
 */

import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { 
  isValidWeddingTemplate, 
  isValidWeddingBorderStyle, 
  isValidWeddingBackgroundPattern,
  validateSessionConfiguration 
} from "./validation";

// Query: Get session by ID with ownership check
export const getSessionById = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return null;
    }

    // Get DJ profile to verify ownership
    const djProfile = await ctx.db.get(session.djId);
    if (!djProfile || djProfile.userId !== userId) {
      throw new Error("Access denied: You can only access your own sessions");
    }

    return session;
  },
});

// Query: Get current user's sessions
export const getCurrentUserSessions = query({
  args: {
    activeOnly: v.optional(v.boolean()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    // Get user's DJ profile
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      return [];
    }

    const limit = args.limit ?? 50;
    
    if (args.activeOnly) {
      return await ctx.db
        .query("sessions")
        .withIndex("by_dj_active", (q) => 
          q.eq("djId", djProfile._id).eq("active", true)
        )
        .take(limit);
    } else {
      return await ctx.db
        .query("sessions")
        .withIndex("by_dj", (q) => q.eq("djId", djProfile._id))
        .order("desc")
        .take(limit);
    }
  },
});

// Query: Get active sessions (public)
export const getActiveSessions = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 20;
    
    const sessions = await ctx.db
      .query("sessions")
      .withIndex("by_active", (q) => q.eq("active", true))
      .take(limit);

    // Return public information only
    return sessions.map(session => ({
      id: session._id,
      name: session.name,
      djId: session.djId,
      acceptRequests: session.acceptRequests,
      weddingModeEnabled: session.weddingModeEnabled,
      sponsorHeader: session.sponsorHeader,
      createdAt: session.createdAt,
    }));
  },
});

// Query: Get session with request statistics
export const getSessionWithStats = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Verify ownership
    const djProfile = await ctx.db.get(session.djId);
    if (!djProfile || djProfile.userId !== userId) {
      throw new Error("Access denied: You can only access your own sessions");
    }

    // Get request statistics
    const allRequests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    const stats = {
      total: allRequests.length,
      pending: allRequests.filter(r => r.status === "pending").length,
      approved: allRequests.filter(r => r.status === "approved").length,
      autoApproved: allRequests.filter(r => r.status === "auto-approved").length,
      declined: allRequests.filter(r => r.status === "declined").length,
      played: allRequests.filter(r => r.status === "played").length,
    };

    return {
      session,
      stats,
    };
  },
});

// Mutation: Create new session
export const createSession = mutation({
  args: {
    name: v.string(),
    acceptRequests: v.optional(v.boolean()),
    autoApproval: v.optional(v.boolean()),
    blockedGenres: v.optional(v.array(v.string())),
    durationMinutes: v.optional(v.number()),
    maxRequestsPerTimeframe: v.optional(v.number()),
    maxRequestsPerUser: v.optional(v.number()),
    enableIpLimiting: v.optional(v.boolean()),
    allowQuotaRequests: v.optional(v.boolean()),
    timeframeMinutes: v.optional(v.number()),
    // Wedding mode fields
    weddingModeEnabled: v.optional(v.boolean()),
    weddingCoupleNames: v.optional(v.array(v.string())),
    weddingDate: v.optional(v.string()),
    weddingHashtag: v.optional(v.string()),
    weddingTemplate: v.optional(v.string()),
    weddingPrimaryColor: v.optional(v.string()),
    weddingSecondaryColor: v.optional(v.string()),
    weddingCustomMessage: v.optional(v.string()),
    weddingShowIcons: v.optional(v.boolean()),
    weddingBorderStyle: v.optional(v.string()),
    weddingBackgroundPattern: v.optional(v.string()),
    // Sponsor fields
    sponsorHeader: v.optional(v.string()),
    sponsorMessage: v.optional(v.string()),
    sponsorUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required to create session");
    }

    // Get user's DJ profile
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      throw new Error("DJ profile required to create session");
    }

    if (!djProfile.completedOnboarding) {
      throw new Error("Please complete onboarding before creating sessions");
    }

    // Validate session name
    if (args.name.trim().length === 0) {
      throw new Error("Session name is required");
    }
    if (args.name.length > 100) {
      throw new Error("Session name cannot exceed 100 characters");
    }

    // Validate session configuration
    const validation = validateSessionConfiguration({
      durationMinutes: args.durationMinutes,
      maxRequestsPerTimeframe: args.maxRequestsPerTimeframe,
      maxRequestsPerUser: args.maxRequestsPerUser,
      timeframeMinutes: args.timeframeMinutes,
      weddingTemplate: args.weddingTemplate,
      weddingBorderStyle: args.weddingBorderStyle,
      weddingBackgroundPattern: args.weddingBackgroundPattern,
      weddingPrimaryColor: args.weddingPrimaryColor,
      weddingSecondaryColor: args.weddingSecondaryColor,
    });

    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(", ")}`);
    }

    // Check for existing active sessions (limit to prevent abuse)
    const activeSessions = await ctx.db
      .query("sessions")
      .withIndex("by_dj_active", (q) => 
        q.eq("djId", djProfile._id).eq("active", true)
      )
      .collect();

    if (activeSessions.length >= 5) {
      throw new Error("Maximum of 5 active sessions allowed. Please end some sessions first.");
    }

    const now = Date.now();
    const sessionId = await ctx.db.insert("sessions", {
      djId: djProfile._id,
      name: args.name.trim(),
      active: false, // Sessions start inactive
      acceptRequests: args.acceptRequests ?? true,
      autoApproval: args.autoApproval ?? false,
      blockedGenres: args.blockedGenres,
      durationMinutes: args.durationMinutes,
      maxRequestsPerTimeframe: args.maxRequestsPerTimeframe,
      maxRequestsPerUser: args.maxRequestsPerUser,
      enableIpLimiting: args.enableIpLimiting ?? false,
      allowQuotaRequests: args.allowQuotaRequests ?? true,
      timeframeMinutes: args.timeframeMinutes ?? 60,
      // Wedding mode
      weddingModeEnabled: args.weddingModeEnabled ?? false,
      weddingCoupleNames: args.weddingCoupleNames,
      weddingDate: args.weddingDate,
      weddingHashtag: args.weddingHashtag,
      weddingTemplate: args.weddingTemplate,
      weddingPrimaryColor: args.weddingPrimaryColor,
      weddingSecondaryColor: args.weddingSecondaryColor,
      weddingCustomMessage: args.weddingCustomMessage,
      weddingShowIcons: args.weddingShowIcons ?? true,
      weddingBorderStyle: args.weddingBorderStyle,
      weddingBackgroundPattern: args.weddingBackgroundPattern,
      // Sponsor settings
      sponsorHeader: args.sponsorHeader,
      sponsorMessage: args.sponsorMessage,
      sponsorUrl: args.sponsorUrl,
      createdAt: now,
      updatedAt: now,
    });

    return sessionId;
  },
});

// Mutation: Update session
export const updateSession = mutation({
  args: {
    sessionId: v.id("sessions"),
    name: v.optional(v.string()),
    acceptRequests: v.optional(v.boolean()),
    autoApproval: v.optional(v.boolean()),
    blockedGenres: v.optional(v.array(v.string())),
    durationMinutes: v.optional(v.number()),
    maxRequestsPerTimeframe: v.optional(v.number()),
    maxRequestsPerUser: v.optional(v.number()),
    enableIpLimiting: v.optional(v.boolean()),
    allowQuotaRequests: v.optional(v.boolean()),
    timeframeMinutes: v.optional(v.number()),
    // Wedding mode fields
    weddingModeEnabled: v.optional(v.boolean()),
    weddingCoupleNames: v.optional(v.array(v.string())),
    weddingDate: v.optional(v.string()),
    weddingHashtag: v.optional(v.string()),
    weddingTemplate: v.optional(v.string()),
    weddingPrimaryColor: v.optional(v.string()),
    weddingSecondaryColor: v.optional(v.string()),
    weddingCustomMessage: v.optional(v.string()),
    weddingShowIcons: v.optional(v.boolean()),
    weddingBorderStyle: v.optional(v.string()),
    weddingBackgroundPattern: v.optional(v.string()),
    // Sponsor fields
    sponsorHeader: v.optional(v.string()),
    sponsorMessage: v.optional(v.string()),
    sponsorUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Verify ownership
    const djProfile = await ctx.db.get(session.djId);
    if (!djProfile || djProfile.userId !== userId) {
      throw new Error("Access denied: You can only update your own sessions");
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: Date.now(),
    };

    // Validate and add fields
    if (args.name !== undefined) {
      if (args.name.trim().length === 0) {
        throw new Error("Session name cannot be empty");
      }
      if (args.name.length > 100) {
        throw new Error("Session name cannot exceed 100 characters");
      }
      updateData.name = args.name.trim();
    }

    // Add all optional fields
    const optionalFields = [
      'acceptRequests', 'autoApproval', 'blockedGenres', 'durationMinutes',
      'maxRequestsPerTimeframe', 'maxRequestsPerUser', 'enableIpLimiting',
      'allowQuotaRequests', 'timeframeMinutes', 'weddingModeEnabled',
      'weddingCoupleNames', 'weddingDate', 'weddingHashtag', 'weddingTemplate',
      'weddingPrimaryColor', 'weddingSecondaryColor', 'weddingCustomMessage',
      'weddingShowIcons', 'weddingBorderStyle', 'weddingBackgroundPattern',
      'sponsorHeader', 'sponsorMessage', 'sponsorUrl'
    ];

    for (const field of optionalFields) {
      if (args[field as keyof typeof args] !== undefined) {
        updateData[field] = args[field as keyof typeof args];
      }
    }

    // Validate configuration if relevant fields are being updated
    if (Object.keys(updateData).some(key => 
      ['durationMinutes', 'maxRequestsPerTimeframe', 'maxRequestsPerUser', 
       'timeframeMinutes', 'weddingTemplate', 'weddingBorderStyle', 
       'weddingBackgroundPattern', 'weddingPrimaryColor', 'weddingSecondaryColor'].includes(key)
    )) {
      const validation = validateSessionConfiguration(updateData);
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(", ")}`);
      }
    }

    await ctx.db.patch(args.sessionId, updateData);
    return args.sessionId;
  },
});

// Mutation: Toggle session active status
export const toggleSessionActive = mutation({
  args: {
    sessionId: v.id("sessions"),
    active: v.boolean(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Verify ownership
    const djProfile = await ctx.db.get(session.djId);
    if (!djProfile || djProfile.userId !== userId) {
      throw new Error("Access denied: You can only control your own sessions");
    }

    await ctx.db.patch(args.sessionId, {
      active: args.active,
      updatedAt: Date.now(),
    });

    return {
      sessionId: args.sessionId,
      active: args.active,
      message: args.active ? "Session activated" : "Session deactivated",
    };
  },
});

// Mutation: Delete session
export const deleteSession = mutation({
  args: {
    sessionId: v.id("sessions"),
    confirmDelete: v.boolean(),
  },
  handler: async (ctx, args) => {
    if (!args.confirmDelete) {
      throw new Error("Delete confirmation required");
    }

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Verify ownership
    const djProfile = await ctx.db.get(session.djId);
    if (!djProfile || djProfile.userId !== userId) {
      throw new Error("Access denied: You can only delete your own sessions");
    }

    // Check if session is active
    if (session.active) {
      throw new Error("Cannot delete active session. Please deactivate first.");
    }

    // Get all song requests for this session
    const requests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    // Delete all song requests first
    for (const request of requests) {
      await ctx.db.delete(request._id);
    }

    // Delete the session
    await ctx.db.delete(args.sessionId);

    return {
      success: true,
      message: `Session deleted along with ${requests.length} song requests`,
      deletedRequests: requests.length,
    };
  },
});

// Query: Get session for public access (no authentication required)
export const getPublicSession = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session || !session.active) {
      return null;
    }

    // Return only public information
    return {
      id: session._id,
      name: session.name,
      acceptRequests: session.acceptRequests,
      weddingModeEnabled: session.weddingModeEnabled,
      weddingCoupleNames: session.weddingCoupleNames,
      weddingDate: session.weddingDate,
      weddingHashtag: session.weddingHashtag,
      weddingTemplate: session.weddingTemplate,
      weddingPrimaryColor: session.weddingPrimaryColor,
      weddingSecondaryColor: session.weddingSecondaryColor,
      weddingCustomMessage: session.weddingCustomMessage,
      weddingShowIcons: session.weddingShowIcons,
      weddingBorderStyle: session.weddingBorderStyle,
      weddingBackgroundPattern: session.weddingBackgroundPattern,
      sponsorHeader: session.sponsorHeader,
      sponsorMessage: session.sponsorMessage,
      sponsorUrl: session.sponsorUrl,
      createdAt: session.createdAt,
    };
  },
});
