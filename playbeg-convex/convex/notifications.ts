/**
 * Real-time Notifications System
 * 
 * Handles instant notifications for DJs about song requests, session events,
 * audience interactions, and system alerts with real-time delivery.
 */

import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Create a notification
export const createNotification = mutation({
  args: {
    recipientId: v.id("users"),
    type: v.union(
      v.literal("song_request"),
      v.literal("session_status"),
      v.literal("audience_interaction"),
      v.literal("system_alert"),
      v.literal("engagement_milestone"),
    ),
    title: v.string(),
    message: v.string(),
    priority: v.union(
      v.literal("low"),
      v.literal("medium"),
      v.literal("high"),
      v.literal("urgent"),
    ),
    actionData: v.optional(v.object({
      sessionId: v.optional(v.id("sessions")),
      songRequestId: v.optional(v.id("songRequests")),
      actionType: v.optional(v.string()),
      actionUrl: v.optional(v.string()),
      metadata: v.optional(v.any()),
    })),
    expiresAt: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const notificationId = await ctx.db.insert("notifications", {
      recipientId: args.recipientId,
      type: args.type,
      title: args.title,
      message: args.message,
      priority: args.priority,
      actionData: args.actionData,
      isRead: false,
      isDelivered: false,
      createdAt: Date.now(),
      expiresAt: args.expiresAt,
    });

    return notificationId;
  },
});

// Get user's notifications
export const getUserNotifications = query({
  args: {
    limit: v.optional(v.number()),
    includeRead: v.optional(v.boolean()),
    type: v.optional(v.union(
      v.literal("song_request"),
      v.literal("session_status"),
      v.literal("audience_interaction"),
      v.literal("system_alert"),
      v.literal("engagement_milestone"),
    )),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    let query = ctx.db
      .query("notifications")
      .withIndex("by_recipient", (q) => q.eq("recipientId", userId));

    // Filter by type if specified
    if (args.type) {
      query = query.filter((q) => q.eq(q.field("type"), args.type));
    }

    // Filter by read status if specified
    if (!args.includeRead) {
      query = query.filter((q) => q.eq(q.field("isRead"), false));
    }

    // Filter out expired notifications
    const now = Date.now();
    query = query.filter((q) => 
      q.or(
        q.eq(q.field("expiresAt"), undefined),
        q.gt(q.field("expiresAt"), now)
      )
    );

    const notifications = await query
      .order("desc")
      .take(args.limit || 50);

    return notifications;
  },
});

// Get unread notification count
export const getUnreadCount = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return 0;
    }

    const unreadCount = await ctx.db
      .query("notifications")
      .withIndex("by_recipient", (q) => q.eq("recipientId", userId))
      .filter((q) => q.eq(q.field("isRead"), false))
      .collect();

    return unreadCount.length;
  },
});

// Mark notification as read
export const markNotificationRead = mutation({
  args: {
    notificationId: v.id("notifications"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const notification = await ctx.db.get(args.notificationId);
    if (!notification) {
      throw new Error("Notification not found");
    }

    if (notification.recipientId !== userId) {
      throw new Error("Access denied");
    }

    await ctx.db.patch(args.notificationId, {
      isRead: true,
      readAt: Date.now(),
    });

    return { success: true };
  },
});

// Mark all notifications as read
export const markAllNotificationsRead = mutation({
  args: {
    type: v.optional(v.union(
      v.literal("song_request"),
      v.literal("session_status"),
      v.literal("audience_interaction"),
      v.literal("system_alert"),
      v.literal("engagement_milestone"),
    )),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    let query = ctx.db
      .query("notifications")
      .withIndex("by_recipient", (q) => q.eq("recipientId", userId))
      .filter((q) => q.eq(q.field("isRead"), false));

    if (args.type) {
      query = query.filter((q) => q.eq(q.field("type"), args.type));
    }

    const unreadNotifications = await query.collect();

    for (const notification of unreadNotifications) {
      await ctx.db.patch(notification._id, {
        isRead: true,
        readAt: Date.now(),
      });
    }

    return { 
      success: true, 
      markedCount: unreadNotifications.length 
    };
  },
});

// Get notification counts by type
export const getNotificationCounts = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const now = Date.now();
    const notifications = await ctx.db
      .query("notifications")
      .withIndex("by_recipient", (q) => q.eq("recipientId", userId))
      .filter((q) => 
        q.and(
          q.eq(q.field("isRead"), false),
          q.or(
            q.eq(q.field("expiresAt"), undefined),
            q.gt(q.field("expiresAt"), now)
          )
        )
      )
      .collect();

    const counts = notifications.reduce((acc, notification) => {
      acc[notification.type] = (acc[notification.type] || 0) + 1;
      acc.total = (acc.total || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total: counts.total || 0,
      song_request: counts.song_request || 0,
      session_status: counts.session_status || 0,
      audience_interaction: counts.audience_interaction || 0,
      system_alert: counts.system_alert || 0,
      engagement_milestone: counts.engagement_milestone || 0,
      lastUpdated: Date.now(),
    };
  },
});

// Send notification for new song request
export const notifyNewSongRequest = mutation({
  args: {
    songRequestId: v.id("songRequests"),
  },
  handler: async (ctx, args) => {
    const songRequest = await ctx.db.get(args.songRequestId);
    if (!songRequest) {
      return;
    }

    const session = await ctx.db.get(songRequest.sessionId);
    if (!session) {
      return;
    }

    // Get the DJ for this session
    if (!session.userId) {
      return;
    }

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", session.userId!))
      .first();

    if (!djProfile) {
      return;
    }

    // Create notification for the DJ
    await ctx.db.insert("notifications", {
      recipientId: session.userId!,
      type: "song_request",
      title: "New Song Request",
      message: `${songRequest.requesterName} requested "${songRequest.songTitle}" by ${songRequest.artistName}`,
      priority: "medium",
      actionData: {
        sessionId: session._id,
        songRequestId: songRequest._id,
        actionType: "review_request",
        actionUrl: `/dashboard/sessions/${session._id}/requests`,
      },
      isRead: false,
      isDelivered: false,
      createdAt: Date.now(),
      expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
    });

    return { success: true };
  },
});

// Send notification for session milestone
export const notifySessionMilestone = mutation({
  args: {
    sessionId: v.id("sessions"),
    milestoneType: v.union(
      v.literal("listeners_milestone"),
      v.literal("requests_milestone"),
      v.literal("duration_milestone"),
    ),
    milestoneValue: v.number(),
    message: v.string(),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return;
    }

    await ctx.db.insert("notifications", {
      recipientId: session.userId!,
      type: "engagement_milestone",
      title: "Session Milestone Reached!",
      message: args.message,
      priority: "medium",
      actionData: {
        sessionId: args.sessionId,
        actionType: "view_analytics",
        actionUrl: `/dashboard/sessions/${args.sessionId}/analytics`,
        metadata: {
          milestoneType: args.milestoneType,
          milestoneValue: args.milestoneValue,
        },
      },
      isRead: false,
      isDelivered: false,
      createdAt: Date.now(),
    });

    return { success: true };
  },
});

// Clean up expired notifications
export const cleanupExpiredNotifications = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    
    const expiredNotifications = await ctx.db
      .query("notifications")
      .filter((q) => 
        q.and(
          q.neq(q.field("expiresAt"), undefined),
          q.lt(q.field("expiresAt"), now)
        )
      )
      .collect();

    for (const notification of expiredNotifications) {
      await ctx.db.delete(notification._id);
    }

    return {
      success: true,
      deletedCount: expiredNotifications.length,
    };
  },
});

// Get real-time notification feed
export const getNotificationFeed = query({
  args: {
    sessionId: v.optional(v.id("sessions")),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    let query = ctx.db
      .query("notifications")
      .withIndex("by_recipient", (q) => q.eq("recipientId", userId));

    // Filter by session if specified
    if (args.sessionId) {
      query = query.filter((q) => 
        q.eq(q.field("actionData.sessionId"), args.sessionId)
      );
    }

    // Only get recent notifications (last 24 hours)
    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    query = query.filter((q) => q.gte(q.field("createdAt"), oneDayAgo));

    const notifications = await query
      .order("desc")
      .take(args.limit || 20);

    return notifications.map(notification => ({
      ...notification,
      timeAgo: formatTimeAgo(Date.now() - notification.createdAt),
    }));
  },
});

// Helper function to format time ago
function formatTimeAgo(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days}d ago`;
  } else if (hours > 0) {
    return `${hours}h ago`;
  } else if (minutes > 0) {
    return `${minutes}m ago`;
  } else {
    return `${seconds}s ago`;
  }
}
