import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

// PlayBeg Convex Schema
// Migrated from Supabase PostgreSQL to Convex document-based architecture
// Maintains feature parity while leveraging Convex's real-time capabilities

export default defineSchema({
  ...authTables,

  // DJ Profiles - User profiles and onboarding status
  djProfiles: defineTable({
    userId: v.id("users"),
    displayName: v.optional(v.string()),
    completedOnboarding: v.boolean(),
    profilePictureStorageId: v.optional(v.id("_storage")),
    lastLoginAt: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_last_login", ["lastLoginAt"])
    .index("by_onboarding", ["completedOnboarding"]),

  // Sessions - DJ event sessions with comprehensive configuration
  sessions: defineTable({
    djId: v.id("djProfiles"),
    userId: v.optional(v.id("users")), // Add userId for compatibility
    name: v.string(),
    active: v.boolean(),
    acceptRequests: v.optional(v.boolean()),
    autoApproval: v.optional(v.boolean()),
    blockedGenres: v.optional(v.array(v.string())),
    durationMinutes: v.optional(v.number()),
    maxRequestsPerTimeframe: v.optional(v.number()),
    maxRequestsPerUser: v.optional(v.number()),
    enableIpLimiting: v.optional(v.boolean()),
    allowQuotaRequests: v.optional(v.boolean()),
    sponsorHeader: v.optional(v.string()),
    sponsorLogoStorageId: v.optional(v.id("_storage")),
    sponsorMessage: v.optional(v.string()),
    sponsorUrl: v.optional(v.string()),
    timeframeMinutes: v.optional(v.number()),

    // Enhanced session controls
    pausedAt: v.optional(v.number()),
    pauseReason: v.optional(v.string()),
    totalPausedTime: v.optional(v.number()),
    maxAudienceSize: v.optional(v.number()),
    requireApprovalToJoin: v.optional(v.boolean()),
    allowAnonymousRequests: v.optional(v.boolean()),
    allowedCountries: v.optional(v.array(v.string())),
    blockedCountries: v.optional(v.array(v.string())),
    restrictionMode: v.optional(v.union(
      v.literal("none"),
      v.literal("allowlist"),
      v.literal("blocklist"),
    )),
    sessionBranding: v.optional(v.object({
      primaryColor: v.optional(v.string()),
      secondaryColor: v.optional(v.string()),
      logoStorageId: v.optional(v.id("_storage")),
      backgroundImageStorageId: v.optional(v.id("_storage")),
      customMessage: v.optional(v.string()),
      customCSS: v.optional(v.string()),
    })),

    // Template and scheduling integration
    templateId: v.optional(v.id("sessionTemplates")),
    scheduledSessionId: v.optional(v.id("scheduledSessions")),

    // Wedding Mode Configuration
    weddingModeEnabled: v.optional(v.boolean()),
    weddingCoupleNames: v.optional(v.array(v.string())),
    weddingDate: v.optional(v.string()),
    weddingHashtag: v.optional(v.string()),
    weddingTemplate: v.optional(v.string()),
    weddingPrimaryColor: v.optional(v.string()),
    weddingSecondaryColor: v.optional(v.string()),
    weddingCustomMessage: v.optional(v.string()),
    weddingShowIcons: v.optional(v.boolean()),
    weddingBorderStyle: v.optional(v.string()),
    weddingBackgroundPattern: v.optional(v.string()),

    createdAt: v.number(),
    updatedAt: v.number(),
    endedAt: v.optional(v.number()),
  })
    .index("by_dj", ["djId"])
    .index("by_user", ["userId"])
    .index("by_active", ["active"])
    .index("by_created", ["createdAt"])
    .index("by_wedding_mode", ["weddingModeEnabled"])
    .index("by_dj_active", ["djId", "active"])
    .index("by_template", ["templateId"])
    .index("by_scheduled", ["scheduledSessionId"]),

  // Song Requests - Individual song requests from audience members
  songRequests: defineTable({
    sessionId: v.id("sessions"),
    songTitle: v.string(),
    artistName: v.string(),
    requesterName: v.string(),
    requesterIp: v.optional(v.string()),
    appleMusicId: v.optional(v.string()),
    albumArtwork: v.optional(v.string()),
    playlistId: v.optional(v.string()),
    appleMusicPlaylistId: v.optional(v.string()),
    genre: v.optional(v.string()),
    status: v.union(
      v.literal("pending"),
      v.literal("approved"),
      v.literal("auto-approved"),
      v.literal("declined"),
      v.literal("played")
    ),
    addedToPlaylist: v.optional(v.boolean()),
    createdAt: v.number(),
  })
    .index("by_session", ["sessionId"])
    .index("by_status", ["status"])
    .index("by_created", ["createdAt"])
    .index("by_session_status", ["sessionId", "status"])
    .index("by_session_created", ["sessionId", "createdAt"])
    .index("by_requester_ip", ["requesterIp"])
    .index("by_apple_music_id", ["appleMusicId"]),

  // Subscription Plans - Available pricing tiers and features
  subscriptionPlans: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    priceAmount: v.number(),
    priceCurrency: v.string(),
    durationHours: v.number(),
    stripePriceId: v.string(),
    active: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_active", ["active"])
    .index("by_stripe_price", ["stripePriceId"])
    .index("by_name", ["name"]),

  // DJ Subscriptions - Subscription management and billing
  djSubscriptions: defineTable({
    djId: v.id("djProfiles"),
    planId: v.optional(v.id("subscriptionPlans")),
    status: v.union(
      v.literal("active"),
      v.literal("canceled"),
      v.literal("past_due"),
      v.literal("unpaid"),
      v.literal("incomplete"),
      v.literal("incomplete_expired"),
      v.literal("trialing")
    ),
    currentPeriodStart: v.optional(v.number()),
    currentPeriodEnd: v.optional(v.number()),
    cancelAtPeriodEnd: v.optional(v.boolean()),
    stripeCustomerId: v.optional(v.string()),
    stripeSubscriptionId: v.optional(v.string()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_dj", ["djId"])
    .index("by_status", ["status"])
    .index("by_stripe_customer", ["stripeCustomerId"])
    .index("by_stripe_subscription", ["stripeSubscriptionId"])
    .index("by_plan", ["planId"])
    .index("by_period_end", ["currentPeriodEnd"]),

  // Apple Music Tokens - Secure Apple Music API token storage
  appleMusicTokens: defineTable({
    userId: v.id("users"),
    appleMusicToken: v.string(),
    expiresAt: v.number(),
    isValid: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_expires", ["expiresAt"])
    .index("by_valid", ["isValid"])
    .index("by_user_valid", ["userId", "isValid"]),

  // Blog Posts - Content management system for blog
  blogPosts: defineTable({
    title: v.string(),
    slug: v.string(),
    publishedDate: v.string(),
    author: v.string(),
    category: v.string(),
    excerpt: v.string(),
    coverImageStorageId: v.optional(v.id("_storage")),
    coverImageAlt: v.optional(v.string()),
    tags: v.array(v.string()),
    content: v.string(),
    status: v.union(
      v.literal("published"),
      v.literal("draft"),
      v.literal("archived")
    ),
    metaDescription: v.optional(v.string()),
    ogImageStorageId: v.optional(v.id("_storage")),
    viewCount: v.number(),
    featured: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_slug", ["slug"])
    .index("by_status", ["status"])
    .index("by_category", ["category"])
    .index("by_published_date", ["publishedDate"])
    .index("by_featured", ["featured"])
    .index("by_author", ["author"])
    .index("by_view_count", ["viewCount"])
    .index("by_status_featured", ["status", "featured"]),

  // Playlists - Apple Music playlist management
  playlists: defineTable({
    userId: v.id("users"),
    name: v.string(),
    description: v.optional(v.string()),
    appleMusicPlaylistId: v.optional(v.string()),
    active: v.optional(v.boolean()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_active", ["active"])
    .index("by_apple_music_id", ["appleMusicPlaylistId"])
    .index("by_user_active", ["userId", "active"]),

  // Recently Played Tracks - Track history and analytics
  recentlyPlayedTracks: defineTable({
    sessionId: v.id("sessions"),
    songTitle: v.string(),
    artistName: v.string(),
    albumArtwork: v.optional(v.string()),
    appleMusicId: v.optional(v.string()),
    playlistId: v.optional(v.id("playlists")),
    playedAt: v.number(),
    createdAt: v.number(),
  })
    .index("by_session", ["sessionId"])
    .index("by_playlist", ["playlistId"])
    .index("by_played_at", ["playedAt"])
    .index("by_session_played", ["sessionId", "playedAt"])
    .index("by_apple_music_id", ["appleMusicId"]),

  // Songs - Song catalog and metadata
  songs: defineTable({
    title: v.string(),
    artist: v.string(),
    album: v.optional(v.string()),
    appleMusicId: v.optional(v.string()),
    artworkUrl: v.optional(v.string()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_apple_music_id", ["appleMusicId"])
    .index("by_artist", ["artist"])
    .index("by_title", ["title"])
    .index("by_album", ["album"])
    .index("by_title_artist", ["title", "artist"]),

  // Missing tables that are referenced in functions but not defined in schema
  // These are temporary placeholders to fix TypeScript errors



  // File Metadata - Comprehensive file storage metadata
  fileMetadata: defineTable({
    storageId: v.id("_storage"),
    userId: v.id("users"),
    fileName: v.string(),
    fileType: v.string(),
    fileSize: v.number(),
    purpose: v.union(
      v.literal("profile_picture"),
      v.literal("sponsor_logo"),
      v.literal("session_artwork"),
      v.literal("other")
    ),
    description: v.optional(v.string()),
    uploadedAt: v.number(),
    isActive: v.boolean(),
    deletedAt: v.optional(v.number()),
  })
    .index("by_user", ["userId"])
    .index("by_storage", ["storageId"])
    .index("by_purpose", ["purpose"])
    .index("by_user_purpose", ["userId", "purpose"])
    .index("by_active", ["isActive"]),

  // Sponsor Logos - Sponsor logo management
  sponsorLogos: defineTable({
    userId: v.id("users"),
    sessionId: v.optional(v.id("sessions")),
    storageId: v.id("_storage"),
    fileMetadataId: v.id("fileMetadata"),
    active: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  }),

  // Session Connections - Track real-time connections to sessions
  sessionConnections: defineTable({
    sessionId: v.id("sessions"),
    userId: v.id("users"),
    userRole: v.union(v.literal("dj"), v.literal("audience")),
    joinedAt: v.number(),
    lastActiveAt: v.number(),
    leftAt: v.optional(v.number()),
    isActive: v.boolean(),
    connectionInfo: v.optional(v.object({
      userAgent: v.optional(v.string()),
      ipAddress: v.optional(v.string()),
      location: v.optional(v.string()),
    })),
  })
    .index("by_session", ["sessionId"])
    .index("by_user", ["userId"])
    .index("by_user_session", ["userId", "sessionId"])
    .index("by_active", ["isActive"])
    .index("by_last_active", ["lastActiveAt"]),

  // Analytics Events - Track user interactions and session events
  analyticsEvents: defineTable({
    sessionId: v.id("sessions"),
    eventType: v.union(
      v.literal("user_joined"),
      v.literal("user_left"),
      v.literal("song_requested"),
      v.literal("song_approved"),
      v.literal("song_declined"),
      v.literal("song_played"),
      v.literal("reaction_added"),
      v.literal("chat_message"),
      v.literal("vote_cast"),
      v.literal("session_paused"),
      v.literal("session_resumed"),
      v.literal("user_kicked"),
    ),
    eventData: v.optional(v.object({
      userId: v.optional(v.id("users")),
      songRequestId: v.optional(v.id("songRequests")),
      reactionType: v.optional(v.string()),
      messageContent: v.optional(v.string()),
      voteValue: v.optional(v.number()),
      pauseDuration: v.optional(v.number()),
      targetUserId: v.optional(v.id("users")),
      metadata: v.optional(v.any()),
    })),
    userId: v.optional(v.id("users")),
    timestamp: v.number(),
  })
    .index("by_session", ["sessionId"])
    .index("by_session_time", ["sessionId", "timestamp"])
    .index("by_user", ["userId"])
    .index("by_event_type", ["eventType"])
    .index("by_timestamp", ["timestamp"]),

  // Notifications - Real-time notifications for users
  notifications: defineTable({
    recipientId: v.id("users"),
    type: v.union(
      v.literal("song_request"),
      v.literal("session_status"),
      v.literal("audience_interaction"),
      v.literal("system_alert"),
      v.literal("engagement_milestone"),
    ),
    title: v.string(),
    message: v.string(),
    priority: v.union(
      v.literal("low"),
      v.literal("medium"),
      v.literal("high"),
      v.literal("urgent"),
    ),
    actionData: v.optional(v.object({
      sessionId: v.optional(v.id("sessions")),
      songRequestId: v.optional(v.id("songRequests")),
      actionType: v.optional(v.string()),
      actionUrl: v.optional(v.string()),
      metadata: v.optional(v.any()),
    })),
    isRead: v.boolean(),
    isDelivered: v.boolean(),
    createdAt: v.number(),
    readAt: v.optional(v.number()),
    expiresAt: v.optional(v.number()),
  })
    .index("by_recipient", ["recipientId"])
    .index("by_recipient_read", ["recipientId", "isRead"])
    .index("by_type", ["type"])
    .index("by_created", ["createdAt"])
    .index("by_priority", ["priority"]),

  // Song Request Votes - Audience voting on song requests
  songRequestVotes: defineTable({
    requestId: v.id("songRequests"),
    userId: v.id("users"),
    voteType: v.union(v.literal("upvote"), v.literal("downvote")),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_request", ["requestId"])
    .index("by_user", ["userId"])
    .index("by_user_request", ["userId", "requestId"]),

  // Session Reactions - Real-time reactions and emojis
  sessionReactions: defineTable({
    sessionId: v.id("sessions"),
    userId: v.id("users"),
    reactionType: v.union(
      v.literal("🔥"),
      v.literal("❤️"),
      v.literal("👏"),
      v.literal("🎵"),
      v.literal("🎉"),
      v.literal("😍"),
      v.literal("🤘"),
      v.literal("💯"),
    ),
    targetType: v.union(v.literal("session"), v.literal("song_request")),
    targetId: v.optional(v.string()),
    createdAt: v.number(),
  })
    .index("by_session", ["sessionId"])
    .index("by_user", ["userId"])
    .index("by_session_time", ["sessionId", "createdAt"]),

  // Chat Messages - Real-time chat for sessions
  chatMessages: defineTable({
    sessionId: v.id("sessions"),
    userId: v.id("users"),
    message: v.string(),
    messageType: v.union(v.literal("text"), v.literal("emoji"), v.literal("system")),
    createdAt: v.number(),
  })
    .index("by_session", ["sessionId"])
    .index("by_user", ["userId"])
    .index("by_session_time", ["sessionId", "createdAt"]),

  // Session Templates - Reusable session configurations
  sessionTemplates: defineTable({
    userId: v.id("users"),
    djProfileId: v.id("djProfiles"),
    name: v.string(),
    description: v.optional(v.string()),
    templateConfig: v.any(), // Flexible configuration object
    isPublic: v.boolean(),
    tags: v.array(v.string()),
    usageCount: v.number(),
    createdAt: v.number(),
    updatedAt: v.number(),
    isActive: v.boolean(),
    deletedAt: v.optional(v.number()),
  })
    .index("by_user", ["userId"])
    .index("by_dj_profile", ["djProfileId"])
    .index("by_public", ["isPublic"])
    .index("by_usage", ["usageCount"])
    .index("by_created", ["createdAt"])
    .index("by_active", ["isActive"]),

  // Scheduled Sessions - Future session scheduling
  scheduledSessions: defineTable({
    userId: v.id("users"),
    djProfileId: v.id("djProfiles"),
    name: v.string(),
    description: v.optional(v.string()),
    scheduledStartTime: v.number(),
    scheduledEndTime: v.number(),
    scheduledDuration: v.number(), // in minutes
    templateId: v.optional(v.id("sessionTemplates")),
    sessionConfig: v.any(),
    autoStart: v.boolean(),
    autoEnd: v.boolean(),
    reminderSettings: v.optional(v.any()),
    recurrence: v.any(),
    status: v.union(
      v.literal("scheduled"),
      v.literal("active"),
      v.literal("completed"),
      v.literal("cancelled"),
    ),
    sessionId: v.optional(v.id("sessions")), // Created when session starts
    actualStartTime: v.optional(v.number()),
    actualEndTime: v.optional(v.number()),
    cancellationReason: v.optional(v.string()),
    cancelledAt: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_user_time", ["userId", "scheduledStartTime"])
    .index("by_status", ["status"])
    .index("by_scheduled_time", ["scheduledStartTime"])
    .index("by_dj_profile", ["djProfileId"]),

  // Scheduled Reminders - Automated reminders for scheduled sessions
  scheduledReminders: defineTable({
    scheduledSessionId: v.id("scheduledSessions"),
    userId: v.id("users"),
    reminderTime: v.number(),
    reminderType: v.union(
      v.literal("session_starting"),
      v.literal("session_ending"),
      v.literal("custom"),
    ),
    minutesBefore: v.number(),
    sent: v.boolean(),
    sentAt: v.optional(v.number()),
    cancelledAt: v.optional(v.number()),
    createdAt: v.number(),
  })
    .index("by_scheduled_session", ["scheduledSessionId"])
    .index("by_user", ["userId"])
    .index("by_reminder_time", ["reminderTime"])
    .index("by_sent", ["sent"]),

  // Session Collaborators - Multi-DJ collaboration on sessions
  sessionCollaborators: defineTable({
    sessionId: v.id("sessions"),
    userId: v.id("users"),
    djProfileId: v.id("djProfiles"),
    invitedBy: v.id("users"),
    role: v.union(
      v.literal("co_host"),
      v.literal("moderator"),
      v.literal("guest_dj"),
      v.literal("observer"),
    ),
    permissions: v.object({
      canManageRequests: v.boolean(),
      canControlPlayback: v.boolean(),
      canModerateChat: v.boolean(),
      canInviteOthers: v.boolean(),
      canEditSessionSettings: v.boolean(),
      canViewAnalytics: v.boolean(),
    }),
    status: v.union(
      v.literal("invited"),
      v.literal("active"),
      v.literal("declined"),
      v.literal("removed"),
    ),
    invitedAt: v.number(),
    respondedAt: v.optional(v.number()),
    responseMessage: v.optional(v.string()),
    removedAt: v.optional(v.number()),
    removalReason: v.optional(v.string()),
    message: v.optional(v.string()),
  })
    .index("by_session", ["sessionId"])
    .index("by_user", ["userId"])
    .index("by_session_user", ["sessionId", "userId"])
    .index("by_status", ["status"])
    .index("by_invited_by", ["invitedBy"]),

  // Session Bans - User bans from sessions
  sessionBans: defineTable({
    sessionId: v.id("sessions"),
    userId: v.id("users"),
    bannedBy: v.id("users"),
    reason: v.optional(v.string()),
    bannedAt: v.number(),
    expiresAt: v.optional(v.number()),
    isActive: v.boolean(),
  })
    .index("by_session", ["sessionId"])
    .index("by_user", ["userId"])
    .index("by_session_user", ["sessionId", "userId"])
    .index("by_banned_by", ["bannedBy"])
    .index("by_active", ["isActive"]),

});
