import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

// PlayBeg Convex Schema
// Migrated from Supabase PostgreSQL to Convex document-based architecture
// Maintains feature parity while leveraging Convex's real-time capabilities

export default defineSchema({
  ...authTables,

  // DJ Profiles - User profiles and onboarding status
  djProfiles: defineTable({
    userId: v.id("users"),
    displayName: v.optional(v.string()),
    completedOnboarding: v.boolean(),
    profilePictureStorageId: v.optional(v.id("_storage")),
    lastLoginAt: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_last_login", ["lastLoginAt"])
    .index("by_onboarding", ["completedOnboarding"]),

  // Sessions - DJ event sessions with comprehensive configuration
  sessions: defineTable({
    djId: v.id("djProfiles"),
    name: v.string(),
    active: v.boolean(),
    acceptRequests: v.optional(v.boolean()),
    autoApproval: v.optional(v.boolean()),
    blockedGenres: v.optional(v.array(v.string())),
    durationMinutes: v.optional(v.number()),
    maxRequestsPerTimeframe: v.optional(v.number()),
    maxRequestsPerUser: v.optional(v.number()),
    enableIpLimiting: v.optional(v.boolean()),
    allowQuotaRequests: v.optional(v.boolean()),
    sponsorHeader: v.optional(v.string()),
    sponsorLogoStorageId: v.optional(v.id("_storage")),
    sponsorMessage: v.optional(v.string()),
    sponsorUrl: v.optional(v.string()),
    timeframeMinutes: v.optional(v.number()),
    // Wedding Mode Configuration
    weddingModeEnabled: v.optional(v.boolean()),
    weddingCoupleNames: v.optional(v.array(v.string())),
    weddingDate: v.optional(v.string()),
    weddingHashtag: v.optional(v.string()),
    weddingTemplate: v.optional(v.string()),
    weddingPrimaryColor: v.optional(v.string()),
    weddingSecondaryColor: v.optional(v.string()),
    weddingCustomMessage: v.optional(v.string()),
    weddingShowIcons: v.optional(v.boolean()),
    weddingBorderStyle: v.optional(v.string()),
    weddingBackgroundPattern: v.optional(v.string()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_dj", ["djId"])
    .index("by_active", ["active"])
    .index("by_created", ["createdAt"])
    .index("by_wedding_mode", ["weddingModeEnabled"])
    .index("by_dj_active", ["djId", "active"]),

  // Song Requests - Individual song requests from audience members
  songRequests: defineTable({
    sessionId: v.id("sessions"),
    songTitle: v.string(),
    artistName: v.string(),
    requesterName: v.string(),
    requesterIp: v.optional(v.string()),
    appleMusicId: v.optional(v.string()),
    albumArtwork: v.optional(v.string()),
    playlistId: v.optional(v.string()),
    appleMusicPlaylistId: v.optional(v.string()),
    genre: v.optional(v.string()),
    status: v.union(
      v.literal("pending"),
      v.literal("approved"),
      v.literal("auto-approved"),
      v.literal("declined"),
      v.literal("played")
    ),
    addedToPlaylist: v.optional(v.boolean()),
    createdAt: v.number(),
  })
    .index("by_session", ["sessionId"])
    .index("by_status", ["status"])
    .index("by_created", ["createdAt"])
    .index("by_session_status", ["sessionId", "status"])
    .index("by_session_created", ["sessionId", "createdAt"])
    .index("by_requester_ip", ["requesterIp"])
    .index("by_apple_music_id", ["appleMusicId"]),

  // Subscription Plans - Available pricing tiers and features
  subscriptionPlans: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    priceAmount: v.number(),
    priceCurrency: v.string(),
    durationHours: v.number(),
    stripePriceId: v.string(),
    active: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_active", ["active"])
    .index("by_stripe_price", ["stripePriceId"])
    .index("by_name", ["name"]),

  // DJ Subscriptions - Subscription management and billing
  djSubscriptions: defineTable({
    djId: v.id("djProfiles"),
    planId: v.optional(v.id("subscriptionPlans")),
    status: v.union(
      v.literal("active"),
      v.literal("canceled"),
      v.literal("past_due"),
      v.literal("unpaid"),
      v.literal("incomplete"),
      v.literal("incomplete_expired"),
      v.literal("trialing")
    ),
    currentPeriodStart: v.optional(v.number()),
    currentPeriodEnd: v.optional(v.number()),
    cancelAtPeriodEnd: v.optional(v.boolean()),
    stripeCustomerId: v.optional(v.string()),
    stripeSubscriptionId: v.optional(v.string()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_dj", ["djId"])
    .index("by_status", ["status"])
    .index("by_stripe_customer", ["stripeCustomerId"])
    .index("by_stripe_subscription", ["stripeSubscriptionId"])
    .index("by_plan", ["planId"])
    .index("by_period_end", ["currentPeriodEnd"]),

  // Apple Music Tokens - Secure Apple Music API token storage
  appleMusicTokens: defineTable({
    userId: v.id("users"),
    appleMusicToken: v.string(),
    expiresAt: v.number(),
    isValid: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_expires", ["expiresAt"])
    .index("by_valid", ["isValid"])
    .index("by_user_valid", ["userId", "isValid"]),

  // Blog Posts - Content management system for blog
  blogPosts: defineTable({
    title: v.string(),
    slug: v.string(),
    publishedDate: v.string(),
    author: v.string(),
    category: v.string(),
    excerpt: v.string(),
    coverImageStorageId: v.optional(v.id("_storage")),
    coverImageAlt: v.optional(v.string()),
    tags: v.array(v.string()),
    content: v.string(),
    status: v.union(
      v.literal("published"),
      v.literal("draft"),
      v.literal("archived")
    ),
    metaDescription: v.optional(v.string()),
    ogImageStorageId: v.optional(v.id("_storage")),
    viewCount: v.number(),
    featured: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_slug", ["slug"])
    .index("by_status", ["status"])
    .index("by_category", ["category"])
    .index("by_published_date", ["publishedDate"])
    .index("by_featured", ["featured"])
    .index("by_author", ["author"])
    .index("by_view_count", ["viewCount"])
    .index("by_status_featured", ["status", "featured"]),

  // Playlists - Apple Music playlist management
  playlists: defineTable({
    userId: v.id("users"),
    name: v.string(),
    description: v.optional(v.string()),
    appleMusicPlaylistId: v.optional(v.string()),
    active: v.optional(v.boolean()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_active", ["active"])
    .index("by_apple_music_id", ["appleMusicPlaylistId"])
    .index("by_user_active", ["userId", "active"]),

  // Recently Played Tracks - Track history and analytics
  recentlyPlayedTracks: defineTable({
    sessionId: v.id("sessions"),
    songTitle: v.string(),
    artistName: v.string(),
    albumArtwork: v.optional(v.string()),
    appleMusicId: v.optional(v.string()),
    playlistId: v.optional(v.id("playlists")),
    playedAt: v.number(),
    createdAt: v.number(),
  })
    .index("by_session", ["sessionId"])
    .index("by_playlist", ["playlistId"])
    .index("by_played_at", ["playedAt"])
    .index("by_session_played", ["sessionId", "playedAt"])
    .index("by_apple_music_id", ["appleMusicId"]),

  // Songs - Song catalog and metadata
  songs: defineTable({
    title: v.string(),
    artist: v.string(),
    album: v.optional(v.string()),
    appleMusicId: v.optional(v.string()),
    artworkUrl: v.optional(v.string()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_apple_music_id", ["appleMusicId"])
    .index("by_artist", ["artist"])
    .index("by_title", ["title"])
    .index("by_album", ["album"])
    .index("by_title_artist", ["title", "artist"]),

  // Missing tables that are referenced in functions but not defined in schema
  // These are temporary placeholders to fix TypeScript errors

  // Song Request Votes - Voting system for song requests
  songRequestVotes: defineTable({
    requestId: v.id("songRequests"),
    userId: v.id("users"),
    voteType: v.union(v.literal("upvote"), v.literal("downvote")),
    createdAt: v.number(),
  })
    .index("by_request", ["requestId"])
    .index("by_request_user", ["requestId", "userId"]),

  // Session Reactions - Live reactions during sessions
  sessionReactions: defineTable({
    sessionId: v.id("sessions"),
    userId: v.id("users"),
    reactionType: v.string(),
    createdAt: v.number(),
  })
    .index("by_session", ["sessionId"]),

  // File Metadata - File storage metadata
  fileMetadata: defineTable({
    userId: v.id("users"),
    fileType: v.string(),
    associatedId: v.optional(v.string()),
    storageId: v.id("_storage"),
    createdAt: v.number(),
  })
    .index("by_user_type", ["userId", "fileType"])
    .index("by_type_associated", ["fileType", "associatedId"]),

  // Sponsor Logos - Sponsor logo management
  sponsorLogos: defineTable({
    userId: v.id("users"),
    sessionId: v.optional(v.id("sessions")),
    storageId: v.id("_storage"),
    fileMetadataId: v.id("fileMetadata"),
    active: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  }),

});
