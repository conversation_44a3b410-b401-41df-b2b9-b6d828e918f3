/**
 * Mobile App API & PWA Features
 * 
 * This module provides mobile-optimized APIs and Progressive Web App
 * functionality for enhanced mobile experience.
 */

import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Query: Get mobile-optimized session data
export const getMobileSessionData = query({
  args: { sessionId: v.id("sessions") },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) return null;

    // Get essential data for mobile view
    const [recentRequests, activeConnections, djProfile] = await Promise.all([
      ctx.db
        .query("songRequests")
        .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
        .order("desc")
        .take(20), // Limit for mobile performance

      ctx.db
        .query("sessionConnections")
        .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
        .filter((q) => q.eq(q.field("isActive"), true))
        .collect(),

      ctx.db.get(session.djId),
    ]);

    // Mobile-optimized response with minimal data
    return {
      session: {
        _id: session._id,
        name: session.name,
        active: session.active,
        acceptRequests: session.acceptRequests,
        autoApproval: session.autoApproval,
        maxRequestsPerUser: session.maxRequestsPerUser,
        weddingModeEnabled: session.weddingModeEnabled,
        weddingCoupleNames: session.weddingCoupleNames,
        sponsorHeader: session.sponsorHeader,
        sponsorMessage: session.sponsorMessage,
      },
      dj: djProfile ? {
        name: djProfile.name,
        bio: djProfile.bio,
        profilePictureStorageId: djProfile.profilePictureStorageId,
      } : null,
      stats: {
        totalRequests: recentRequests.length,
        pendingRequests: recentRequests.filter(r => r.status === "pending").length,
        activeUsers: activeConnections.length,
      },
      recentRequests: recentRequests.slice(0, 10).map(request => ({
        _id: request._id,
        songTitle: request.songTitle,
        artistName: request.artistName,
        requesterName: request.requesterName,
        status: request.status,
        createdAt: request.createdAt,
        albumArtwork: request.albumArtwork,
      })),
    };
  },
});

// Query: Get mobile-optimized user dashboard
export const getMobileDashboard = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;

    const user = await ctx.db.get(userId);
    if (!user) return null;

    // Get DJ profile
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      return {
        user: {
          _id: user._id,
          name: user.name,
          email: user.email,
        },
        djProfile: null,
        sessions: [],
        stats: null,
      };
    }

    // Get recent sessions (mobile-optimized)
    const sessions = await ctx.db
      .query("sessions")
      .withIndex("by_dj", (q) => q.eq("djId", djProfile._id))
      .order("desc")
      .take(5);

    // Calculate basic stats
    const totalSessions = sessions.length;
    const activeSessions = sessions.filter(s => s.active).length;

    return {
      user: {
        _id: user._id,
        name: user.name,
        email: user.email,
      },
      djProfile: {
        _id: djProfile._id,
        name: djProfile.name,
        bio: djProfile.bio,
        profilePictureStorageId: djProfile.profilePictureStorageId,
        completedOnboarding: djProfile.completedOnboarding,
      },
      sessions: sessions.map(session => ({
        _id: session._id,
        name: session.name,
        active: session.active,
        createdAt: session.createdAt,
      })),
      stats: {
        totalSessions,
        activeSessions,
      },
    };
  },
});

// Mutation: Submit mobile song request
export const submitMobileSongRequest = mutation({
  args: {
    sessionId: v.id("sessions"),
    songTitle: v.string(),
    artistName: v.string(),
    requesterName: v.string(),
    appleMusicId: v.optional(v.string()),
    albumArtwork: v.optional(v.string()),
    deviceInfo: v.optional(v.object({
      userAgent: v.optional(v.string()),
      platform: v.optional(v.string()),
      screenSize: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    // Validate session
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    if (!session.active) {
      throw new Error("Session is not active");
    }

    if (!session.acceptRequests) {
      throw new Error("Session is not accepting requests");
    }

    // Create the request
    const requestId = await ctx.db.insert("songRequests", {
      sessionId: args.sessionId,
      songTitle: args.songTitle.trim(),
      artistName: args.artistName.trim(),
      requesterName: args.requesterName.trim(),
      appleMusicId: args.appleMusicId,
      albumArtwork: args.albumArtwork,
      status: session.autoApproval ? "auto-approved" : "pending",
      createdAt: Date.now(),
    });

    // Track analytics event for mobile request
    await ctx.db.insert("analyticsEvents", {
      sessionId: args.sessionId,
      eventType: "song_requested",
      eventData: {
        songRequestId: requestId,
        platform: "mobile",
        deviceInfo: args.deviceInfo,
      },
      timestamp: Date.now(),
    });

    return {
      requestId,
      status: session.autoApproval ? "auto-approved" : "pending",
      message: session.autoApproval 
        ? "Your request has been automatically approved!" 
        : "Your request has been submitted and is pending approval.",
    };
  },
});

// Query: Get mobile session QR code data
export const getMobileSessionQR = query({
  args: { sessionId: v.id("sessions") },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) return null;

    const djProfile = await ctx.db.get(session.djId);

    return {
      sessionId: args.sessionId,
      sessionName: session.name,
      djName: djProfile?.name || "Unknown DJ",
      active: session.active,
      acceptRequests: session.acceptRequests,
      url: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/session/${args.sessionId}`,
      weddingMode: session.weddingModeEnabled,
      weddingCoupleNames: session.weddingCoupleNames,
      sponsorInfo: session.sponsorHeader ? {
        header: session.sponsorHeader,
        message: session.sponsorMessage,
      } : null,
    };
  },
});

// Action: Generate PWA manifest
export const generatePWAManifest = action({
  args: {},
  handler: async (ctx) => {
    const manifest = {
      name: "PlayBeg - DJ Song Requests",
      short_name: "PlayBeg",
      description: "Request songs from your favorite DJs in real-time",
      start_url: "/",
      display: "standalone",
      background_color: "#1a1a2e",
      theme_color: "#8b5cf6",
      orientation: "portrait-primary",
      icons: [
        {
          src: "/icons/icon-72x72.png",
          sizes: "72x72",
          type: "image/png",
          purpose: "maskable any"
        },
        {
          src: "/icons/icon-96x96.png",
          sizes: "96x96",
          type: "image/png",
          purpose: "maskable any"
        },
        {
          src: "/icons/icon-128x128.png",
          sizes: "128x128",
          type: "image/png",
          purpose: "maskable any"
        },
        {
          src: "/icons/icon-144x144.png",
          sizes: "144x144",
          type: "image/png",
          purpose: "maskable any"
        },
        {
          src: "/icons/icon-152x152.png",
          sizes: "152x152",
          type: "image/png",
          purpose: "maskable any"
        },
        {
          src: "/icons/icon-192x192.png",
          sizes: "192x192",
          type: "image/png",
          purpose: "maskable any"
        },
        {
          src: "/icons/icon-384x384.png",
          sizes: "384x384",
          type: "image/png",
          purpose: "maskable any"
        },
        {
          src: "/icons/icon-512x512.png",
          sizes: "512x512",
          type: "image/png",
          purpose: "maskable any"
        }
      ],
      categories: ["music", "entertainment", "social"],
      screenshots: [
        {
          src: "/screenshots/mobile-1.png",
          sizes: "390x844",
          type: "image/png",
          form_factor: "narrow"
        },
        {
          src: "/screenshots/desktop-1.png",
          sizes: "1920x1080",
          type: "image/png",
          form_factor: "wide"
        }
      ]
    };

    return manifest;
  },
});

// Query: Get mobile-optimized session list
export const getMobileSessionList = query({
  args: {
    limit: v.optional(v.number()),
    activeOnly: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 20;
    
    let query = ctx.db.query("sessions");
    
    if (args.activeOnly) {
      query = query.withIndex("by_active", (q) => q.eq("active", true));
    } else {
      query = query.withIndex("by_created");
    }

    const sessions = await query
      .order("desc")
      .take(limit);

    // Get DJ profiles for all sessions
    const djProfiles = await Promise.all(
      sessions.map(session => ctx.db.get(session.djId))
    );

    return sessions.map((session, index) => ({
      _id: session._id,
      name: session.name,
      active: session.active,
      acceptRequests: session.acceptRequests,
      createdAt: session.createdAt,
      weddingModeEnabled: session.weddingModeEnabled,
      weddingCoupleNames: session.weddingCoupleNames,
      dj: djProfiles[index] ? {
        name: djProfiles[index]!.name,
        profilePictureStorageId: djProfiles[index]!.profilePictureStorageId,
      } : null,
    }));
  },
});

// Mutation: Track mobile app usage
export const trackMobileUsage = mutation({
  args: {
    event: v.string(),
    sessionId: v.optional(v.id("sessions")),
    deviceInfo: v.optional(v.object({
      userAgent: v.optional(v.string()),
      platform: v.optional(v.string()),
      screenSize: v.optional(v.string()),
      connectionType: v.optional(v.string()),
      isStandalone: v.optional(v.boolean()),
    })),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);

    await ctx.db.insert("mobileUsageTracking", {
      event: args.event,
      sessionId: args.sessionId,
      userId,
      deviceInfo: args.deviceInfo,
      timestamp: Date.now(),
    });

    return { success: true };
  },
});

// Query: Get mobile app statistics
export const getMobileAppStats = query({
  args: {
    timeRange: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const timeThreshold = args.timeRange 
      ? Date.now() - args.timeRange 
      : Date.now() - (7 * 24 * 60 * 60 * 1000); // Default: last 7 days

    const mobileUsage = await ctx.db
      .query("mobileUsageTracking")
      .withIndex("by_timestamp", (q) => q.gte("timestamp", timeThreshold))
      .collect();

    // Calculate mobile usage statistics
    const totalEvents = mobileUsage.length;
    const uniqueUsers = new Set(mobileUsage.map(u => u.userId).filter(Boolean)).size;
    const standaloneUsers = mobileUsage.filter(u => u.deviceInfo?.isStandalone).length;
    
    const platformBreakdown = mobileUsage.reduce((acc, usage) => {
      const platform = usage.deviceInfo?.platform || 'unknown';
      acc[platform] = (acc[platform] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const eventBreakdown = mobileUsage.reduce((acc, usage) => {
      acc[usage.event] = (acc[usage.event] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalEvents,
      uniqueUsers,
      standaloneUsers,
      pwaAdoptionRate: totalEvents > 0 ? (standaloneUsers / totalEvents * 100).toFixed(1) : "0",
      platformBreakdown,
      eventBreakdown,
      timeRange: args.timeRange || (7 * 24 * 60 * 60 * 1000),
    };
  },
});
