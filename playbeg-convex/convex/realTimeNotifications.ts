/**
 * Real-time Notifications System
 * 
 * This module provides comprehensive real-time notifications for DJs and users
 * including new song requests, session status changes, DJ activity notifications,
 * and system notifications with proper targeting and delivery.
 */

import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Query: Get user's notifications
export const getUserNotifications = query({
  args: {
    limit: v.optional(v.number()),
    unreadOnly: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    const limit = args.limit ?? 50;
    
    let notificationsQuery = ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", userId));

    if (args.unreadOnly) {
      notificationsQuery = notificationsQuery.filter((q) => q.eq(q.field("read"), false));
    }

    const notifications = await notificationsQuery
      .order("desc")
      .take(limit);

    return notifications.map(notification => ({
      ...notification,
      timeAgo: getTimeAgo(notification.createdAt),
    }));
  },
});

// Query: Get real-time notifications for DJ
export const getDjNotifications = query({
  args: {
    sessionId: v.optional(v.id("sessions")),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    const limit = args.limit ?? 25;

    // Get DJ profile
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      return [];
    }

    // Get notifications for DJ
    let notificationsQuery = ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .filter((q) => q.or(
        q.eq(q.field("type"), "new_song_request"),
        q.eq(q.field("type"), "session_activity"),
        q.eq(q.field("type"), "dj_mention"),
        q.eq(q.field("type"), "session_milestone")
      ));

    // Filter by session if specified
    if (args.sessionId) {
      notificationsQuery = notificationsQuery.filter((q) => 
        q.eq(q.field("metadata.sessionId"), args.sessionId)
      );
    }

    const notifications = await notificationsQuery
      .order("desc")
      .take(limit);

    return notifications.map(notification => ({
      ...notification,
      timeAgo: getTimeAgo(notification.createdAt),
    }));
  },
});

// Mutation: Create notification
export const createNotification = mutation({
  args: {
    userId: v.id("users"),
    type: v.union(
      v.literal("new_song_request"),
      v.literal("request_status_change"),
      v.literal("session_started"),
      v.literal("session_ended"),
      v.literal("session_paused"),
      v.literal("dj_activity"),
      v.literal("subscription_change"),
      v.literal("system_announcement"),
      v.literal("session_milestone"),
      v.literal("dj_mention")
    ),
    title: v.string(),
    message: v.string(),
    metadata: v.optional(v.object({
      sessionId: v.optional(v.id("sessions")),
      requestId: v.optional(v.id("songRequests")),
      djId: v.optional(v.id("djProfiles")),
      subscriptionId: v.optional(v.string()),
      actionUrl: v.optional(v.string()),
      additionalData: v.optional(v.any()),
    })),
    priority: v.optional(v.union(
      v.literal("low"),
      v.literal("normal"),
      v.literal("high"),
      v.literal("urgent")
    )),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    const notificationId = await ctx.db.insert("notifications", {
      userId: args.userId,
      type: args.type,
      title: args.title,
      message: args.message,
      metadata: args.metadata,
      priority: args.priority ?? "normal",
      read: false,
      createdAt: now,
      updatedAt: now,
    });

    return {
      notificationId,
      message: "Notification created successfully",
    };
  },
});

// Action: Send new song request notification
export const sendNewSongRequestNotification = action({
  args: {
    requestId: v.id("songRequests"),
  },
  handler: async (ctx, args) => {
    // Get request details
    const request = await ctx.db.get(args.requestId);
    if (!request) {
      throw new Error("Song request not found");
    }

    // Get session and DJ info
    const session = await ctx.db.get(request.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    const djProfile = await ctx.db.get(session.djId);
    if (!djProfile) {
      throw new Error("DJ profile not found");
    }

    // Get requester info
    const requester = await ctx.db.get(request.userId);
    const requesterName = requester?.name || "Someone";

    // Create notification for DJ
    await ctx.runMutation(api.realTimeNotifications.createNotification, {
      userId: djProfile.userId,
      type: "new_song_request",
      title: "New Song Request",
      message: `${requesterName} requested "${request.songTitle}" by ${request.artistName}`,
      metadata: {
        sessionId: session._id,
        requestId: args.requestId,
        djId: djProfile._id,
        actionUrl: `/sessions/${session._id}/requests`,
      },
      priority: "normal",
    });

    return {
      success: true,
      message: "New song request notification sent",
    };
  },
});

// Action: Send session status change notification
export const sendSessionStatusNotification = action({
  args: {
    sessionId: v.id("sessions"),
    status: v.union(
      v.literal("started"),
      v.literal("paused"),
      v.literal("resumed"),
      v.literal("ended")
    ),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    const djProfile = await ctx.db.get(session.djId);
    if (!djProfile) {
      throw new Error("DJ profile not found");
    }

    // Get session participants (users who made requests)
    const requests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    const participantIds = [...new Set(requests.map(r => r.userId))];

    // Create notifications for all participants
    const statusMessages = {
      started: `${djProfile.stageName} started their session "${session.title}"`,
      paused: `${djProfile.stageName} paused their session "${session.title}"`,
      resumed: `${djProfile.stageName} resumed their session "${session.title}"`,
      ended: `${djProfile.stageName} ended their session "${session.title}"`,
    };

    const notifications = participantIds.map(userId => 
      ctx.runMutation(api.realTimeNotifications.createNotification, {
        userId,
        type: `session_${args.status}` as any,
        title: `Session ${args.status.charAt(0).toUpperCase() + args.status.slice(1)}`,
        message: statusMessages[args.status],
        metadata: {
          sessionId: args.sessionId,
          djId: djProfile._id,
          actionUrl: `/sessions/${args.sessionId}`,
        },
        priority: args.status === "started" ? "normal" : "low",
      })
    );

    await Promise.all(notifications);

    return {
      success: true,
      notificationsSent: participantIds.length,
      message: `Session ${args.status} notifications sent`,
    };
  },
});

// Action: Send request status change notification
export const sendRequestStatusNotification = action({
  args: {
    requestId: v.id("songRequests"),
    newStatus: v.union(
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("played")
    ),
  },
  handler: async (ctx, args) => {
    const request = await ctx.db.get(args.requestId);
    if (!request) {
      throw new Error("Song request not found");
    }

    const session = await ctx.db.get(request.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    const djProfile = await ctx.db.get(session.djId);
    if (!djProfile) {
      throw new Error("DJ profile not found");
    }

    const statusMessages = {
      approved: `Your request "${request.songTitle}" was approved by ${djProfile.stageName}`,
      rejected: `Your request "${request.songTitle}" was declined by ${djProfile.stageName}`,
      played: `Your request "${request.songTitle}" is now playing!`,
    };

    const statusTitles = {
      approved: "Request Approved",
      rejected: "Request Declined",
      played: "Now Playing",
    };

    // Create notification for requester
    await ctx.runMutation(api.realTimeNotifications.createNotification, {
      userId: request.userId,
      type: "request_status_change",
      title: statusTitles[args.newStatus],
      message: statusMessages[args.newStatus],
      metadata: {
        sessionId: session._id,
        requestId: args.requestId,
        djId: djProfile._id,
        actionUrl: `/sessions/${session._id}`,
      },
      priority: args.newStatus === "played" ? "high" : "normal",
    });

    return {
      success: true,
      message: "Request status notification sent",
    };
  },
});

// Action: Send session milestone notification
export const sendSessionMilestoneNotification = action({
  args: {
    sessionId: v.id("sessions"),
    milestone: v.union(
      v.literal("10_requests"),
      v.literal("25_requests"),
      v.literal("50_requests"),
      v.literal("100_requests"),
      v.literal("1_hour"),
      v.literal("2_hours"),
      v.literal("5_hours")
    ),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    const djProfile = await ctx.db.get(session.djId);
    if (!djProfile) {
      throw new Error("DJ profile not found");
    }

    const milestoneMessages = {
      "10_requests": "Your session reached 10 song requests!",
      "25_requests": "Your session reached 25 song requests!",
      "50_requests": "Your session reached 50 song requests!",
      "100_requests": "Your session reached 100 song requests!",
      "1_hour": "Your session has been running for 1 hour!",
      "2_hours": "Your session has been running for 2 hours!",
      "5_hours": "Your session has been running for 5 hours!",
    };

    // Create notification for DJ
    await ctx.runMutation(api.realTimeNotifications.createNotification, {
      userId: djProfile.userId,
      type: "session_milestone",
      title: "Session Milestone",
      message: milestoneMessages[args.milestone],
      metadata: {
        sessionId: args.sessionId,
        djId: djProfile._id,
        actionUrl: `/sessions/${args.sessionId}/analytics`,
      },
      priority: "low",
    });

    return {
      success: true,
      message: "Session milestone notification sent",
    };
  },
});

// Mutation: Mark notification as read
export const markNotificationAsRead = mutation({
  args: {
    notificationId: v.id("notifications"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const notification = await ctx.db.get(args.notificationId);
    if (!notification) {
      throw new Error("Notification not found");
    }

    if (notification.userId !== userId) {
      throw new Error("Access denied: You can only mark your own notifications as read");
    }

    await ctx.db.patch(args.notificationId, {
      read: true,
      updatedAt: Date.now(),
    });

    return {
      success: true,
      message: "Notification marked as read",
    };
  },
});

// Mutation: Mark all notifications as read
export const markAllNotificationsAsRead = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const unreadNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .filter((q) => q.eq(q.field("read"), false))
      .collect();

    const now = Date.now();
    const updates = unreadNotifications.map(notification =>
      ctx.db.patch(notification._id, {
        read: true,
        updatedAt: now,
      })
    );

    await Promise.all(updates);

    return {
      success: true,
      markedCount: unreadNotifications.length,
      message: `Marked ${unreadNotifications.length} notifications as read`,
    };
  },
});

// Query: Get notification statistics
export const getNotificationStatistics = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const allNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();

    const unreadCount = allNotifications.filter(n => !n.read).length;
    const last24Hours = Date.now() - (24 * 60 * 60 * 1000);
    const recentCount = allNotifications.filter(n => n.createdAt > last24Hours).length;

    // Count by type
    const typeCount: Record<string, number> = {};
    allNotifications.forEach(notification => {
      typeCount[notification.type] = (typeCount[notification.type] || 0) + 1;
    });

    // Count by priority
    const priorityCount: Record<string, number> = {};
    allNotifications.forEach(notification => {
      priorityCount[notification.priority] = (priorityCount[notification.priority] || 0) + 1;
    });

    return {
      total: allNotifications.length,
      unread: unreadCount,
      recent24h: recentCount,
      typeDistribution: typeCount,
      priorityDistribution: priorityCount,
      oldestNotification: allNotifications.length > 0 
        ? Math.min(...allNotifications.map(n => n.createdAt))
        : null,
      newestNotification: allNotifications.length > 0
        ? Math.max(...allNotifications.map(n => n.createdAt))
        : null,
    };
  },
});

// Helper function to calculate time ago
function getTimeAgo(timestamp: number): string {
  const now = Date.now();
  const diff = now - timestamp;
  
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (minutes < 1) return "Just now";
  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  if (days < 7) return `${days}d ago`;
  
  return new Date(timestamp).toLocaleDateString();
}
