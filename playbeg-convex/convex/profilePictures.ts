/**
 * Profile Picture Management
 * 
 * This module handles profile picture operations including upload, update,
 * deletion, and retrieval with proper validation and user ownership checks.
 */

import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Query: Get current user's profile picture
export const getCurrentUserProfilePicture = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    // Get user's DJ profile
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile || !djProfile.profilePictureStorageId) {
      return null;
    }

    // Get file metadata and URL
    const fileMetadata = await ctx.runQuery(api.fileStorage.getFileMetadata, {
      storageId: djProfile.profilePictureStorageId,
    });

    return fileMetadata;
  },
});

// Query: Get profile picture by DJ profile ID
export const getProfilePictureByDjId = query({
  args: {
    djId: v.id("djProfiles"),
  },
  handler: async (ctx, args) => {
    const djProfile = await ctx.db.get(args.djId);
    if (!djProfile || !djProfile.profilePictureStorageId) {
      return null;
    }

    // Get file metadata and URL
    const fileMetadata = await ctx.runQuery(api.fileStorage.getFileMetadata, {
      storageId: djProfile.profilePictureStorageId,
    });

    return fileMetadata;
  },
});

// Action: Complete profile picture upload workflow
export const uploadProfilePictureComplete = action({
  args: {
    storageId: v.id("_storage"),
    fileName: v.string(),
    mimeType: v.string(),
    fileSize: v.number(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get user's DJ profile
    const djProfile = await ctx.runQuery(api.djProfiles.getCurrentDjProfile, {});
    if (!djProfile) {
      throw new Error("DJ profile required to upload profile picture");
    }

    // Delete old profile picture if exists
    if (djProfile.profilePictureStorageId) {
      try {
        await ctx.runMutation(api.fileStorage.deleteFile, {
          storageId: djProfile.profilePictureStorageId,
        });
      } catch (error) {
        // Continue if old file deletion fails
        console.log("Failed to delete old profile picture:", error);
      }
    }

    // Store new file metadata
    const fileResult = await ctx.runMutation(api.fileStorage.storeFileMetadata, {
      storageId: args.storageId,
      fileName: args.fileName,
      fileType: "profile_picture",
      mimeType: args.mimeType,
      fileSize: args.fileSize,
      associatedId: djProfile._id,
    });

    // Update DJ profile with new profile picture
    await ctx.runMutation(api.djProfiles.updateDjProfile, {
      profilePictureStorageId: args.storageId,
    });

    // Get the updated profile picture metadata
    const updatedProfilePicture = await ctx.runQuery(api.fileStorage.getFileMetadata, {
      storageId: args.storageId,
    });

    return {
      ...fileResult,
      profilePicture: updatedProfilePicture,
      profileUpdated: true,
      message: "Profile picture uploaded and profile updated successfully",
    };
  },
});

// Mutation: Remove profile picture
export const removeProfilePicture = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get user's DJ profile
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      throw new Error("DJ profile not found");
    }

    if (!djProfile.profilePictureStorageId) {
      return {
        success: true,
        message: "No profile picture to remove",
      };
    }

    // Delete file from storage
    try {
      await ctx.runMutation(api.fileStorage.deleteFile, {
        storageId: djProfile.profilePictureStorageId,
      });
    } catch (error) {
      // Continue even if file deletion fails
      console.log("Failed to delete profile picture file:", error);
    }

    // Remove reference from DJ profile
    await ctx.db.patch(djProfile._id, {
      profilePictureStorageId: undefined,
      updatedAt: Date.now(),
    });

    return {
      success: true,
      message: "Profile picture removed successfully",
    };
  },
});

// Query: Get profile picture upload status
export const getProfilePictureUploadStatus = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return {
        isAuthenticated: false,
        hasProfilePicture: false,
        canUpload: false,
      };
    }

    // Get user's DJ profile
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      return {
        isAuthenticated: true,
        hasProfilePicture: false,
        canUpload: false,
        needsDjProfile: true,
      };
    }

    const hasProfilePicture = !!djProfile.profilePictureStorageId;

    return {
      isAuthenticated: true,
      hasProfilePicture,
      canUpload: true,
      djProfileId: djProfile._id,
      currentProfilePictureId: djProfile.profilePictureStorageId,
    };
  },
});

// Action: Get profile picture upload URL
export const getProfilePictureUploadUrl = action({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Verify DJ profile exists
    const djProfile = await ctx.runQuery(api.djProfiles.getCurrentDjProfile, {});
    if (!djProfile) {
      throw new Error("DJ profile required to upload profile picture");
    }

    // Generate upload URL
    const uploadUrl = await ctx.runMutation(api.fileStorage.generateUploadUrl, {});

    return {
      uploadUrl,
      djProfileId: djProfile._id,
      maxFileSize: 5 * 1024 * 1024, // 5MB
      allowedTypes: ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"],
      instructions: "Upload your profile picture (max 5MB, JPEG/PNG/GIF/WebP)",
    };
  },
});

// Query: Get multiple profile pictures by DJ IDs
export const getProfilePicturesByDjIds = query({
  args: {
    djIds: v.array(v.id("djProfiles")),
  },
  handler: async (ctx, args) => {
    const profilePictures: Record<string, any> = {};

    for (const djId of args.djIds) {
      try {
        const profilePicture = await ctx.runQuery(api.profilePictures.getProfilePictureByDjId, {
          djId,
        });
        
        if (profilePicture) {
          profilePictures[djId] = profilePicture;
        }
      } catch (error) {
        // Continue if individual profile picture fetch fails
        console.log(`Failed to get profile picture for DJ ${djId}:`, error);
      }
    }

    return profilePictures;
  },
});

// Query: Get profile picture statistics
export const getProfilePictureStatistics = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get all profile pictures
    const profilePictures = await ctx.db
      .query("fileMetadata")
      .withIndex("by_type", (q) => q.eq("fileType", "profile_picture"))
      .collect();

    // Get total file sizes
    const totalSize = profilePictures.reduce((sum, file) => sum + file.fileSize, 0);
    const averageSize = profilePictures.length > 0 ? totalSize / profilePictures.length : 0;

    // Count by MIME type
    const mimeTypeCounts: Record<string, number> = {};
    for (const file of profilePictures) {
      mimeTypeCounts[file.mimeType] = (mimeTypeCounts[file.mimeType] || 0) + 1;
    }

    // Get recent uploads (last 7 days)
    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    const recentUploads = profilePictures.filter(file => file.uploadedAt > sevenDaysAgo);

    return {
      total: profilePictures.length,
      totalSizeMB: Math.round(totalSize / 1024 / 1024 * 100) / 100,
      averageSizeMB: Math.round(averageSize / 1024 / 1024 * 100) / 100,
      mimeTypeCounts,
      recentUploads: recentUploads.length,
      oldestUpload: profilePictures.length > 0 
        ? Math.min(...profilePictures.map(f => f.uploadedAt))
        : null,
      newestUpload: profilePictures.length > 0
        ? Math.max(...profilePictures.map(f => f.uploadedAt))
        : null,
    };
  },
});
