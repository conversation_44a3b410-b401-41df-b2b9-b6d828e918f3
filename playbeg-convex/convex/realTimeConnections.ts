/**
 * Real-time Connection Management
 * 
 * Handles optimized subscriptions, connection tracking, and efficient
 * real-time data delivery for DJ sessions and audience interactions.
 */

import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Track active connections for sessions
export const joinSession = mutation({
  args: {
    sessionId: v.id("sessions"),
    userRole: v.union(v.literal("dj"), v.literal("audience")),
    connectionInfo: v.optional(v.object({
      userAgent: v.optional(v.string()),
      ipAddress: v.optional(v.string()),
      location: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required to join session");
    }

    // Check if session exists and is active
    const session = await ctx.db.get(args.sessionId);
    if (!session || !session.active) {
      throw new Error("Session not found or inactive");
    }

    // Check for existing connection
    const existingConnection = await ctx.db
      .query("sessionConnections")
      .withIndex("by_user_session", (q) => 
        q.eq("userId", userId).eq("sessionId", args.sessionId)
      )
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    if (existingConnection) {
      // Update existing connection
      await ctx.db.patch(existingConnection._id, {
        lastActiveAt: Date.now(),
        connectionInfo: args.connectionInfo,
      });
      return existingConnection._id;
    }

    // Create new connection
    const connectionId = await ctx.db.insert("sessionConnections", {
      sessionId: args.sessionId,
      userId,
      userRole: args.userRole,
      joinedAt: Date.now(),
      lastActiveAt: Date.now(),
      isActive: true,
      connectionInfo: args.connectionInfo,
    });

    return connectionId;
  },
});

// Leave session and cleanup connection
export const leaveSession = mutation({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return; // Silent fail for unauthenticated users
    }

    // Find and deactivate connection
    const connection = await ctx.db
      .query("sessionConnections")
      .withIndex("by_user_session", (q) => 
        q.eq("userId", userId).eq("sessionId", args.sessionId)
      )
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    if (connection) {
      await ctx.db.patch(connection._id, {
        isActive: false,
        leftAt: Date.now(),
      });
    }
  },
});

// Heartbeat to maintain connection
export const updateHeartbeat = mutation({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return;
    }

    const connection = await ctx.db
      .query("sessionConnections")
      .withIndex("by_user_session", (q) => 
        q.eq("userId", userId).eq("sessionId", args.sessionId)
      )
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    if (connection) {
      await ctx.db.patch(connection._id, {
        lastActiveAt: Date.now(),
      });
    }
  },
});

// Get active connections for a session
export const getSessionConnections = query({
  args: {
    sessionId: v.id("sessions"),
    includeInactive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("sessionConnections")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId));

    if (!args.includeInactive) {
      query = query.filter((q) => q.eq(q.field("isActive"), true));
    }

    const connections = await query.collect();

    // Get user details for each connection
    const connectionsWithUsers = await Promise.all(
      connections.map(async (connection) => {
        const user = await ctx.db.get(connection.userId);
        return {
          ...connection,
          user: user ? {
            _id: user._id,
            name: user.name,
            email: user.email,
          } : null,
        };
      })
    );

    return connectionsWithUsers;
  },
});

// Get live session statistics
export const getLiveSessionStats = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    // Get active connections
    const activeConnections = await ctx.db
      .query("sessionConnections")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    // Count by role
    const djCount = activeConnections.filter(c => c.userRole === "dj").length;
    const audienceCount = activeConnections.filter(c => c.userRole === "audience").length;

    // Get recent song requests (last hour)
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    const recentRequests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .filter((q) => q.gte(q.field("createdAt"), oneHourAgo))
      .collect();

    // Get session info
    const session = await ctx.db.get(args.sessionId);

    return {
      sessionId: args.sessionId,
      isActive: session?.active || false,
      totalConnections: activeConnections.length,
      djCount,
      audienceCount,
      recentRequestsCount: recentRequests.length,
      sessionDuration: session ? Date.now() - session.createdAt : 0,
      lastUpdated: Date.now(),
    };
  },
});

// Cleanup inactive connections (background task)
export const cleanupInactiveConnections = mutation({
  args: {
    timeoutMinutes: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const timeoutMs = (args.timeoutMinutes || 5) * 60 * 1000; // Default 5 minutes
    const cutoffTime = Date.now() - timeoutMs;

    // Find connections that haven't been active recently
    const inactiveConnections = await ctx.db
      .query("sessionConnections")
      .filter((q) => 
        q.and(
          q.eq(q.field("isActive"), true),
          q.lt(q.field("lastActiveAt"), cutoffTime)
        )
      )
      .collect();

    // Deactivate them
    for (const connection of inactiveConnections) {
      await ctx.db.patch(connection._id, {
        isActive: false,
        leftAt: Date.now(),
      });
    }

    return {
      cleanedUp: inactiveConnections.length,
      cutoffTime,
    };
  },
});

// Get optimized session data for real-time updates
export const getOptimizedSessionData = query({
  args: {
    sessionId: v.id("sessions"),
    lastUpdateTime: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return null;
    }

    // Only return data that's newer than lastUpdateTime
    const since = args.lastUpdateTime || 0;

    // Get recent song requests
    const recentRequests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .filter((q) => q.gte(q.field("createdAt"), since))
      .order("desc")
      .take(20);

    // Get live stats directly
    const activeConnections = await ctx.db
      .query("sessionConnections")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    const stats = {
      sessionId: args.sessionId,
      isActive: session.active,
      totalConnections: activeConnections.length,
      djCount: activeConnections.filter(c => c.userRole === "dj").length,
      audienceCount: activeConnections.filter(c => c.userRole === "audience").length,
      recentRequestsCount: recentRequests.length,
      sessionDuration: Date.now() - session.createdAt,
      lastUpdated: Date.now(),
    };

    return {
      session: {
        _id: session._id,
        name: session.name,
        active: session.active,
        updatedAt: session.updatedAt,
      },
      recentRequests,
      stats,
      timestamp: Date.now(),
    };
  },
});

// Subscribe to session updates with optimized polling
export const subscribeToSession = query({
  args: {
    sessionId: v.id("sessions"),
    subscriptionType: v.union(
      v.literal("full"), // All updates
      v.literal("stats_only"), // Just statistics
      v.literal("requests_only"), // Just song requests
    ),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return null;
    }

    switch (args.subscriptionType) {
      case "stats_only":
        const activeConnections = await ctx.db
          .query("sessionConnections")
          .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
          .filter((q) => q.eq(q.field("isActive"), true))
          .collect();

        return {
          sessionId: args.sessionId,
          isActive: session.active,
          totalConnections: activeConnections.length,
          djCount: activeConnections.filter(c => c.userRole === "dj").length,
          audienceCount: activeConnections.filter(c => c.userRole === "audience").length,
          sessionDuration: Date.now() - session.createdAt,
          lastUpdated: Date.now(),
        };

      case "requests_only":
        const requests = await ctx.db
          .query("songRequests")
          .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
          .order("desc")
          .take(10);
        return { requests, timestamp: Date.now() };

      case "full":
      default:
        // Return full session data
        const recentRequests = await ctx.db
          .query("songRequests")
          .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
          .order("desc")
          .take(20);

        const connections = await ctx.db
          .query("sessionConnections")
          .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
          .filter((q) => q.eq(q.field("isActive"), true))
          .collect();

        return {
          session: {
            _id: session._id,
            name: session.name,
            active: session.active,
            updatedAt: session.updatedAt,
          },
          recentRequests,
          stats: {
            sessionId: args.sessionId,
            isActive: session.active,
            totalConnections: connections.length,
            djCount: connections.filter(c => c.userRole === "dj").length,
            audienceCount: connections.filter(c => c.userRole === "audience").length,
            recentRequestsCount: recentRequests.length,
            sessionDuration: Date.now() - session.createdAt,
            lastUpdated: Date.now(),
          },
          timestamp: Date.now(),
        };
    }
  },
});
