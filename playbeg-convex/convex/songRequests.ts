/**
 * Song Request CRUD Operations
 * 
 * This module handles all database operations for song requests including
 * creation, reading, updating, and deletion with real-time capabilities,
 * status workflow management, and rate limiting.
 */

import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { isValidRequestStatus } from "./validation";

// Query: Get song requests for a session (public access)
export const getSessionRequests = query({
  args: {
    sessionId: v.id("sessions"),
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("approved"),
      v.literal("auto-approved"),
      v.literal("declined"),
      v.literal("played")
    )),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Verify session exists and is active
    const session = await ctx.db.get(args.sessionId);
    if (!session || !session.active) {
      return [];
    }

    const limit = args.limit ?? 50;
    
    if (args.status) {
      return await ctx.db
        .query("songRequests")
        .withIndex("by_session_status", (q) => 
          q.eq("sessionId", args.sessionId).eq("status", args.status!)
        )
        .order("desc")
        .take(limit);
    } else {
      return await ctx.db
        .query("songRequests")
        .withIndex("by_session_created", (q) => 
          q.eq("sessionId", args.sessionId)
        )
        .order("desc")
        .take(limit);
    }
  },
});

// Query: Get song requests for DJ (authenticated)
export const getDjSessionRequests = query({
  args: {
    sessionId: v.id("sessions"),
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("approved"),
      v.literal("auto-approved"),
      v.literal("declined"),
      v.literal("played")
    )),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Verify ownership
    const djProfile = await ctx.db.get(session.djId);
    if (!djProfile || djProfile.userId !== userId) {
      throw new Error("Access denied: You can only view requests for your own sessions");
    }

    const limit = args.limit ?? 100;
    
    if (args.status) {
      return await ctx.db
        .query("songRequests")
        .withIndex("by_session_status", (q) => 
          q.eq("sessionId", args.sessionId).eq("status", args.status!)
        )
        .order("desc")
        .take(limit);
    } else {
      return await ctx.db
        .query("songRequests")
        .withIndex("by_session_created", (q) => 
          q.eq("sessionId", args.sessionId)
        )
        .order("desc")
        .take(limit);
    }
  },
});

// Query: Get pending requests for DJ dashboard
export const getPendingRequests = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Verify ownership
    const djProfile = await ctx.db.get(session.djId);
    if (!djProfile || djProfile.userId !== userId) {
      throw new Error("Access denied: You can only view requests for your own sessions");
    }

    return await ctx.db
      .query("songRequests")
      .withIndex("by_session_status", (q) => 
        q.eq("sessionId", args.sessionId).eq("status", "pending")
      )
      .order("asc") // Oldest first for FIFO processing
      .collect();
  },
});

// Mutation: Create song request (public)
export const createSongRequest = mutation({
  args: {
    sessionId: v.id("sessions"),
    songTitle: v.string(),
    artistName: v.string(),
    requesterName: v.string(),
    requesterIp: v.optional(v.string()),
    appleMusicId: v.optional(v.string()),
    albumArtwork: v.optional(v.string()),
    genre: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Verify session exists and accepts requests
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    if (!session.active) {
      throw new Error("Session is not active");
    }

    if (!session.acceptRequests) {
      throw new Error("Session is not accepting requests");
    }

    // Validate required fields
    if (args.songTitle.trim().length === 0) {
      throw new Error("Song title is required");
    }
    if (args.artistName.trim().length === 0) {
      throw new Error("Artist name is required");
    }
    if (args.requesterName.trim().length === 0) {
      throw new Error("Requester name is required");
    }

    // Check for blocked genres
    if (args.genre && session.blockedGenres && session.blockedGenres.includes(args.genre.toLowerCase())) {
      throw new Error(`Genre "${args.genre}" is not allowed for this session`);
    }

    // Rate limiting checks
    const now = Date.now();
    const timeframeMs = (session.timeframeMinutes ?? 60) * 60 * 1000;
    const cutoffTime = now - timeframeMs;

    // Check IP-based rate limiting
    if (session.enableIpLimiting && args.requesterIp) {
      const recentRequestsByIp = await ctx.db
        .query("songRequests")
        .withIndex("by_requester_ip", (q) => q.eq("requesterIp", args.requesterIp))
        .filter((q) => 
          q.and(
            q.eq(q.field("sessionId"), args.sessionId),
            q.gte(q.field("createdAt"), cutoffTime)
          )
        )
        .collect();

      if (session.maxRequestsPerTimeframe && recentRequestsByIp.length >= session.maxRequestsPerTimeframe) {
        throw new Error(`Rate limit exceeded: Maximum ${session.maxRequestsPerTimeframe} requests per ${session.timeframeMinutes} minutes from same IP`);
      }
    }

    // Check user-based rate limiting
    if (session.maxRequestsPerUser) {
      const recentRequestsByUser = await ctx.db
        .query("songRequests")
        .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
        .filter((q) => 
          q.and(
            q.eq(q.field("requesterName"), args.requesterName),
            q.gte(q.field("createdAt"), cutoffTime)
          )
        )
        .collect();

      if (recentRequestsByUser.length >= session.maxRequestsPerUser) {
        throw new Error(`Rate limit exceeded: Maximum ${session.maxRequestsPerUser} requests per ${session.timeframeMinutes} minutes per user`);
      }
    }

    // Determine initial status
    let status: "pending" | "auto-approved" = "pending";
    if (session.autoApproval) {
      status = "auto-approved";
    }

    const requestId = await ctx.db.insert("songRequests", {
      sessionId: args.sessionId,
      songTitle: args.songTitle.trim(),
      artistName: args.artistName.trim(),
      requesterName: args.requesterName.trim(),
      requesterIp: args.requesterIp,
      appleMusicId: args.appleMusicId,
      albumArtwork: args.albumArtwork,
      genre: args.genre,
      status,
      addedToPlaylist: false,
      createdAt: now,
    });

    return {
      requestId,
      status,
      message: status === "auto-approved" ? "Request auto-approved!" : "Request submitted successfully!",
    };
  },
});

// Mutation: Update song request status (DJ only)
export const updateRequestStatus = mutation({
  args: {
    requestId: v.id("songRequests"),
    status: v.union(
      v.literal("pending"),
      v.literal("approved"),
      v.literal("declined"),
      v.literal("played")
    ),
    addedToPlaylist: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const request = await ctx.db.get(args.requestId);
    if (!request) {
      throw new Error("Song request not found");
    }

    const session = await ctx.db.get(request.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Verify ownership
    const djProfile = await ctx.db.get(session.djId);
    if (!djProfile || djProfile.userId !== userId) {
      throw new Error("Access denied: You can only manage requests for your own sessions");
    }

    // Validate status transition
    const validTransitions: Record<string, string[]> = {
      "pending": ["approved", "declined"],
      "auto-approved": ["declined", "played"],
      "approved": ["declined", "played"],
      "declined": ["approved", "pending"],
      "played": [], // Cannot change from played
    };

    if (!validTransitions[request.status]?.includes(args.status)) {
      throw new Error(`Cannot change status from ${request.status} to ${args.status}`);
    }

    const updateData: any = {
      status: args.status,
    };

    if (args.addedToPlaylist !== undefined) {
      updateData.addedToPlaylist = args.addedToPlaylist;
    }

    await ctx.db.patch(args.requestId, updateData);

    return {
      requestId: args.requestId,
      status: args.status,
      message: `Request ${args.status}`,
    };
  },
});

// Mutation: Bulk update request statuses
export const bulkUpdateRequestStatus = mutation({
  args: {
    requestIds: v.array(v.id("songRequests")),
    status: v.union(
      v.literal("approved"),
      v.literal("declined"),
      v.literal("played")
    ),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    if (args.requestIds.length === 0) {
      throw new Error("No requests specified");
    }

    if (args.requestIds.length > 50) {
      throw new Error("Cannot update more than 50 requests at once");
    }

    const results = [];
    let sessionId: string | null = null;

    for (const requestId of args.requestIds) {
      try {
        const request = await ctx.db.get(requestId);
        if (!request) {
          results.push({ requestId, success: false, error: "Request not found" });
          continue;
        }

        // Verify all requests belong to the same session
        if (sessionId === null) {
          sessionId = request.sessionId;
        } else if (sessionId !== request.sessionId) {
          results.push({ requestId, success: false, error: "All requests must belong to the same session" });
          continue;
        }

        const session = await ctx.db.get(request.sessionId);
        if (!session) {
          results.push({ requestId, success: false, error: "Session not found" });
          continue;
        }

        // Verify ownership
        const djProfile = await ctx.db.get(session.djId);
        if (!djProfile || djProfile.userId !== userId) {
          results.push({ requestId, success: false, error: "Access denied" });
          continue;
        }

        await ctx.db.patch(requestId, { status: args.status });
        results.push({ requestId, success: true, status: args.status });

      } catch (error) {
        results.push({ requestId, success: false, error: String(error) });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;

    return {
      results,
      summary: {
        total: results.length,
        successful: successCount,
        failed: failureCount,
      },
      message: `Updated ${successCount} requests, ${failureCount} failed`,
    };
  },
});

// Mutation: Delete song request
export const deleteSongRequest = mutation({
  args: {
    requestId: v.id("songRequests"),
    confirmDelete: v.boolean(),
  },
  handler: async (ctx, args) => {
    if (!args.confirmDelete) {
      throw new Error("Delete confirmation required");
    }

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const request = await ctx.db.get(args.requestId);
    if (!request) {
      throw new Error("Song request not found");
    }

    const session = await ctx.db.get(request.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Verify ownership
    const djProfile = await ctx.db.get(session.djId);
    if (!djProfile || djProfile.userId !== userId) {
      throw new Error("Access denied: You can only delete requests from your own sessions");
    }

    await ctx.db.delete(args.requestId);

    return {
      success: true,
      message: "Song request deleted successfully",
    };
  },
});

// Query: Get request statistics for session
export const getSessionRequestStats = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Verify ownership
    const djProfile = await ctx.db.get(session.djId);
    if (!djProfile || djProfile.userId !== userId) {
      throw new Error("Access denied: You can only view stats for your own sessions");
    }

    const allRequests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    const stats = {
      total: allRequests.length,
      pending: allRequests.filter(r => r.status === "pending").length,
      approved: allRequests.filter(r => r.status === "approved").length,
      autoApproved: allRequests.filter(r => r.status === "auto-approved").length,
      declined: allRequests.filter(r => r.status === "declined").length,
      played: allRequests.filter(r => r.status === "played").length,
      addedToPlaylist: allRequests.filter(r => r.addedToPlaylist).length,
    };

    // Get recent activity (last hour)
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    const recentRequests = allRequests.filter(r => r.createdAt > oneHourAgo);

    return {
      stats,
      recentActivity: {
        lastHour: recentRequests.length,
        averagePerHour: allRequests.length > 0 ?
          Math.round((allRequests.length / ((Date.now() - session.createdAt) / (60 * 60 * 1000))) * 100) / 100 : 0,
      },
      topRequesters: getTopRequesters(allRequests),
      topGenres: getTopGenres(allRequests),
    };
  },
});

// Helper function to get top requesters
function getTopRequesters(requests: any[]) {
  const requesterCounts: Record<string, number> = {};

  for (const request of requests) {
    requesterCounts[request.requesterName] = (requesterCounts[request.requesterName] || 0) + 1;
  }

  return Object.entries(requesterCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)
    .map(([name, count]) => ({ name, count }));
}

// Helper function to get top genres
function getTopGenres(requests: any[]) {
  const genreCounts: Record<string, number> = {};

  for (const request of requests) {
    if (request.genre) {
      genreCounts[request.genre] = (genreCounts[request.genre] || 0) + 1;
    }
  }

  return Object.entries(genreCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)
    .map(([genre, count]) => ({ genre, count }));
}

// Query: Search song requests
export const searchSongRequests = query({
  args: {
    sessionId: v.id("sessions"),
    searchTerm: v.string(),
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("approved"),
      v.literal("auto-approved"),
      v.literal("declined"),
      v.literal("played")
    )),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Verify ownership
    const djProfile = await ctx.db.get(session.djId);
    if (!djProfile || djProfile.userId !== userId) {
      throw new Error("Access denied: You can only search requests for your own sessions");
    }

    const limit = args.limit ?? 50;
    const searchTerm = args.searchTerm.toLowerCase().trim();

    let requests;
    if (args.status) {
      requests = await ctx.db
        .query("songRequests")
        .withIndex("by_session_status", (q) =>
          q.eq("sessionId", args.sessionId).eq("status", args.status)
        )
        .take(200); // Get more to filter
    } else {
      requests = await ctx.db
        .query("songRequests")
        .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
        .take(200);
    }

    // Filter by search term
    const filteredRequests = requests.filter(request =>
      request.songTitle.toLowerCase().includes(searchTerm) ||
      request.artistName.toLowerCase().includes(searchTerm) ||
      request.requesterName.toLowerCase().includes(searchTerm) ||
      (request.genre && request.genre.toLowerCase().includes(searchTerm))
    );

    return filteredRequests.slice(0, limit);
  },
});
