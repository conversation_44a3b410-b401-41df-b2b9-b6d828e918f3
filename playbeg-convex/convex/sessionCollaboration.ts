/**
 * Session Collaboration System
 * 
 * Enables multiple DJs to collaborate on sessions with role-based permissions,
 * shared control, and coordinated request management.
 */

import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Invite a DJ to collaborate on a session
export const inviteCollaborator = mutation({
  args: {
    sessionId: v.id("sessions"),
    inviteeEmail: v.string(),
    role: v.union(
      v.literal("co_host"), // Full control except deleting session
      v.literal("moderator"), // Can manage requests and audience
      v.literal("guest_dj"), // Can add songs and basic controls
      v.literal("observer"), // Read-only access
    ),
    permissions: v.optional(v.object({
      canManageRequests: v.optional(v.boolean()),
      canControlPlayback: v.optional(v.boolean()),
      canModerateChat: v.optional(v.boolean()),
      canInviteOthers: v.optional(v.boolean()),
      canEditSessionSettings: v.optional(v.boolean()),
      canViewAnalytics: v.optional(v.boolean()),
    })),
    message: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    if (session.userId !== userId) {
      throw new Error("Only the session owner can invite collaborators");
    }

    // Find the invitee by email
    const allUsers = await ctx.db.query("users").collect();
    const invitee = allUsers.find(user => user.email === args.inviteeEmail);

    if (!invitee) {
      throw new Error("User not found with that email address");
    }

    // Check if user has a DJ profile
    const inviteeDjProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", invitee._id))
      .first();

    if (!inviteeDjProfile) {
      throw new Error("Invitee must have a DJ profile to collaborate");
    }

    // Check if already invited or collaborating
    const existingCollaboration = await ctx.db
      .query("sessionCollaborators")
      .withIndex("by_session_user", (q) => 
        q.eq("sessionId", args.sessionId).eq("userId", invitee._id)
      )
      .first();

    if (existingCollaboration) {
      throw new Error("User is already invited or collaborating on this session");
    }

    // Set default permissions based on role
    const defaultPermissions = getDefaultPermissions(args.role);
    const finalPermissions = { ...defaultPermissions, ...args.permissions };

    // Create collaboration invitation
    const collaborationId = await ctx.db.insert("sessionCollaborators", {
      sessionId: args.sessionId,
      userId: invitee._id,
      djProfileId: inviteeDjProfile._id,
      invitedBy: userId,
      role: args.role,
      permissions: finalPermissions,
      status: "invited",
      invitedAt: Date.now(),
      message: args.message,
    });

    // Create notification for invitee
    await ctx.db.insert("notifications", {
      recipientId: invitee._id,
      type: "session_status",
      title: "Session Collaboration Invitation",
      message: `You've been invited to collaborate on "${session.name}" as a ${args.role.replace('_', ' ')}`,
      priority: "medium",
      actionData: {
        sessionId: args.sessionId,
        actionType: "accept_collaboration",
        actionUrl: `/dashboard/sessions/${args.sessionId}/collaborate`,
        metadata: { collaborationId },
      },
      isRead: false,
      isDelivered: false,
      createdAt: Date.now(),
    });

    return collaborationId;
  },
});

// Accept or decline a collaboration invitation
export const respondToCollaborationInvite = mutation({
  args: {
    collaborationId: v.id("sessionCollaborators"),
    response: v.union(v.literal("accept"), v.literal("decline")),
    message: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const collaboration = await ctx.db.get(args.collaborationId);
    if (!collaboration) {
      throw new Error("Collaboration invitation not found");
    }

    if (collaboration.userId !== userId) {
      throw new Error("You can only respond to your own invitations");
    }

    if (collaboration.status !== "invited") {
      throw new Error("This invitation has already been responded to");
    }

    const newStatus = args.response === "accept" ? "active" : "declined";
    const responseTime = Date.now();

    await ctx.db.patch(args.collaborationId, {
      status: newStatus,
      respondedAt: responseTime,
      responseMessage: args.message,
    });

    // Notify the session owner
    const session = await ctx.db.get(collaboration.sessionId);
    if (session) {
      await ctx.db.insert("notifications", {
        recipientId: collaboration.invitedBy,
        type: "session_status",
        title: `Collaboration ${args.response === "accept" ? "Accepted" : "Declined"}`,
        message: `Your collaboration invitation for "${session.name}" was ${args.response}ed`,
        priority: "medium",
        actionData: {
          sessionId: collaboration.sessionId,
          actionType: "view_collaborators",
          actionUrl: `/dashboard/sessions/${collaboration.sessionId}/collaborate`,
        },
        isRead: false,
        isDelivered: false,
        createdAt: Date.now(),
      });
    }

    return { success: true, status: newStatus };
  },
});

// Get session collaborators
export const getSessionCollaborators = query({
  args: {
    sessionId: v.id("sessions"),
    includeInvited: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return [];
    }

    // Check if user has access to view collaborators
    const isOwner = session.userId === userId;
    const isCollaborator = await ctx.db
      .query("sessionCollaborators")
      .withIndex("by_session_user", (q) => 
        q.eq("sessionId", args.sessionId).eq("userId", userId)
      )
      .filter((q) => q.eq(q.field("status"), "active"))
      .first();

    if (!isOwner && !isCollaborator) {
      throw new Error("Access denied: You must be the owner or an active collaborator");
    }

    let query = ctx.db
      .query("sessionCollaborators")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId));

    if (!args.includeInvited) {
      query = query.filter((q) => q.eq(q.field("status"), "active"));
    }

    const collaborators = await query.collect();

    // Get user and DJ profile details
    const collaboratorsWithDetails = await Promise.all(
      collaborators.map(async (collab) => {
        const user = await ctx.db.get(collab.userId);
        const djProfile = await ctx.db.get(collab.djProfileId);
        const inviter = await ctx.db.get(collab.invitedBy);

        return {
          ...collab,
          user: user ? {
            _id: user._id,
            name: user.name,
            email: user.email,
          } : null,
          djProfile: djProfile ? {
            _id: djProfile._id,
            displayName: djProfile.displayName,
          } : null,
          inviter: inviter ? {
            _id: inviter._id,
            name: inviter.name,
          } : null,
        };
      })
    );

    return collaboratorsWithDetails;
  },
});

// Update collaborator permissions
export const updateCollaboratorPermissions = mutation({
  args: {
    collaborationId: v.id("sessionCollaborators"),
    role: v.optional(v.union(
      v.literal("co_host"),
      v.literal("moderator"),
      v.literal("guest_dj"),
      v.literal("observer"),
    )),
    permissions: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const collaboration = await ctx.db.get(args.collaborationId);
    if (!collaboration) {
      throw new Error("Collaboration not found");
    }

    const session = await ctx.db.get(collaboration.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Only session owner can update permissions
    if (session.userId !== userId) {
      throw new Error("Only the session owner can update collaborator permissions");
    }

    const updates: any = {};

    if (args.role !== undefined) {
      updates.role = args.role;
      // Update permissions based on new role
      const defaultPermissions = getDefaultPermissions(args.role);
      updates.permissions = { ...defaultPermissions, ...args.permissions };
    } else if (args.permissions !== undefined) {
      updates.permissions = { ...collaboration.permissions, ...args.permissions };
    }

    if (Object.keys(updates).length > 0) {
      await ctx.db.patch(args.collaborationId, updates);
    }

    return { success: true };
  },
});

// Remove a collaborator
export const removeCollaborator = mutation({
  args: {
    collaborationId: v.id("sessionCollaborators"),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const collaboration = await ctx.db.get(args.collaborationId);
    if (!collaboration) {
      throw new Error("Collaboration not found");
    }

    const session = await ctx.db.get(collaboration.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Session owner can remove anyone, collaborators can remove themselves
    const canRemove = session.userId === userId || collaboration.userId === userId;
    if (!canRemove) {
      throw new Error("Access denied: You can only remove yourself or be removed by the session owner");
    }

    await ctx.db.patch(args.collaborationId, {
      status: "removed",
      removedAt: Date.now(),
      removalReason: args.reason,
    });

    // Notify the removed collaborator (if not removing themselves)
    if (session.userId === userId && collaboration.userId !== userId) {
      await ctx.db.insert("notifications", {
        recipientId: collaboration.userId,
        type: "session_status",
        title: "Removed from Session Collaboration",
        message: `You have been removed from collaboration on "${session.name}"`,
        priority: "medium",
        actionData: {
          sessionId: collaboration.sessionId,
          actionType: "view_session",
          actionUrl: `/dashboard/sessions/${collaboration.sessionId}`,
        },
        isRead: false,
        isDelivered: false,
        createdAt: Date.now(),
      });
    }

    return { success: true };
  },
});

// Check if user can perform an action on a session
export const checkCollaborationPermission = query({
  args: {
    sessionId: v.id("sessions"),
    action: v.union(
      v.literal("manage_requests"),
      v.literal("control_playback"),
      v.literal("moderate_chat"),
      v.literal("invite_others"),
      v.literal("edit_settings"),
      v.literal("view_analytics"),
    ),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return false;
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return false;
    }

    // Session owner has all permissions
    if (session.userId === userId) {
      return true;
    }

    // Check collaborator permissions
    const collaboration = await ctx.db
      .query("sessionCollaborators")
      .withIndex("by_session_user", (q) => 
        q.eq("sessionId", args.sessionId).eq("userId", userId)
      )
      .filter((q) => q.eq(q.field("status"), "active"))
      .first();

    if (!collaboration) {
      return false;
    }

    const permissions = collaboration.permissions;
    switch (args.action) {
      case "manage_requests":
        return permissions.canManageRequests || false;
      case "control_playback":
        return permissions.canControlPlayback || false;
      case "moderate_chat":
        return permissions.canModerateChat || false;
      case "invite_others":
        return permissions.canInviteOthers || false;
      case "edit_settings":
        return permissions.canEditSessionSettings || false;
      case "view_analytics":
        return permissions.canViewAnalytics || false;
      default:
        return false;
    }
  },
});

// Helper function to get default permissions based on role
function getDefaultPermissions(role: string) {
  switch (role) {
    case "co_host":
      return {
        canManageRequests: true,
        canControlPlayback: true,
        canModerateChat: true,
        canInviteOthers: true,
        canEditSessionSettings: true,
        canViewAnalytics: true,
      };
    case "moderator":
      return {
        canManageRequests: true,
        canControlPlayback: false,
        canModerateChat: true,
        canInviteOthers: false,
        canEditSessionSettings: false,
        canViewAnalytics: true,
      };
    case "guest_dj":
      return {
        canManageRequests: true,
        canControlPlayback: true,
        canModerateChat: false,
        canInviteOthers: false,
        canEditSessionSettings: false,
        canViewAnalytics: false,
      };
    case "observer":
      return {
        canManageRequests: false,
        canControlPlayback: false,
        canModerateChat: false,
        canInviteOthers: false,
        canEditSessionSettings: false,
        canViewAnalytics: true,
      };
    default:
      return {
        canManageRequests: false,
        canControlPlayback: false,
        canModerateChat: false,
        canInviteOthers: false,
        canEditSessionSettings: false,
        canViewAnalytics: false,
      };
  }
}
