/**
 * Feature Access Control
 * 
 * This module provides feature access control based on subscription status
 * and plan features, ensuring users can only access features they've paid for.
 */

import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Query: Check if user can create session (simplified for event-based model)
export const canCreateSession = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return {
        allowed: false,
        reason: "Authentication required",
      };
    }

    const subscriptionStatus = await getSubscriptionStatus(ctx, userId);

    if (!subscriptionStatus.isActive) {
      return {
        allowed: false,
        reason: "Active subscription or pass required to create sessions",
        upgradeRequired: true,
      };
    }

    return {
      allowed: true,
      planName: subscriptionStatus.planName,
      durationMinutes: subscriptionStatus.durationMinutes,
      maxRequestsPerSession: subscriptionStatus.maxRequestsPerSession,
    };
  },
});

// Note: Wedding mode and sponsor branding are now available to all users with active subscriptions
// These features are no longer tier-restricted in the event-based model

// Query: Check session request limits (updated for event-based model)
export const checkSessionRequestLimits = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return {
        allowed: false,
        reason: "Session not found",
      };
    }

    const djProfile = await ctx.db.get(session.djId);
    if (!djProfile) {
      return {
        allowed: false,
        reason: "DJ profile not found",
      };
    }

    const subscriptionStatus = await getSubscriptionStatus(ctx, djProfile.userId);

    // Get current request count for this session
    const currentRequests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    const maxRequests = subscriptionStatus.maxRequestsPerSession;

    // Handle "unlimited" requests
    if (maxRequests === "unlimited") {
      return {
        allowed: true,
        currentCount: currentRequests.length,
        maxAllowed: "unlimited",
        remaining: "unlimited",
      };
    }

    if (currentRequests.length >= maxRequests) {
      return {
        allowed: false,
        reason: `Maximum ${maxRequests} requests per session reached`,
        currentCount: currentRequests.length,
        maxAllowed: maxRequests,
        upgradeRequired: !subscriptionStatus.isActive,
      };
    }

    return {
      allowed: true,
      currentCount: currentRequests.length,
      maxAllowed: maxRequests,
      remaining: maxRequests - currentRequests.length,
    };
  },
});

// Query: Get user's subscription summary (simplified for event-based model)
export const getUserSubscriptionSummary = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return {
        isAuthenticated: false,
        subscription: getFreePlanDetails(),
      };
    }

    const subscriptionStatus = await getSubscriptionStatus(ctx, userId);

    // Get current usage statistics
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    let currentUsage = {
      activeSessions: 0,
      totalSessions: 0,
      totalRequests: 0,
    };

    if (djProfile) {
      const activeSessions = await ctx.db
        .query("sessions")
        .withIndex("by_dj_active", (q) =>
          q.eq("djId", djProfile._id).eq("active", true)
        )
        .collect();

      const totalSessions = await ctx.db
        .query("sessions")
        .withIndex("by_dj", (q) => q.eq("djId", djProfile._id))
        .collect();

      let totalRequests = 0;
      for (const session of totalSessions) {
        const requests = await ctx.db
          .query("songRequests")
          .withIndex("by_session", (q) => q.eq("sessionId", session._id))
          .collect();
        totalRequests += requests.length;
      }

      currentUsage = {
        activeSessions: activeSessions.length,
        totalSessions: totalSessions.length,
        totalRequests,
      };
    }

    return {
      isAuthenticated: true,
      subscription: {
        isActive: subscriptionStatus.isActive,
        planName: subscriptionStatus.planName,
        durationMinutes: subscriptionStatus.durationMinutes,
        maxRequestsPerSession: subscriptionStatus.maxRequestsPerSession,
        daysRemaining: subscriptionStatus.daysRemaining,
      },
      currentUsage,
    };
  },
});

// Mutation: Validate subscription access (simplified for event-based model)
export const validateSubscriptionAccess = mutation({
  args: {
    action: v.union(
      v.literal("create_session"),
      v.literal("use_features")
    ),
    sessionId: v.optional(v.id("sessions")),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const subscriptionStatus = await getSubscriptionStatus(ctx, userId);

    switch (args.action) {
      case "create_session":
        const canCreate = await ctx.runQuery("featureAccess:canCreateSession", {});
        if (!canCreate.allowed) {
          throw new Error(canCreate.reason);
        }
        break;

      case "use_features":
        if (!subscriptionStatus.isActive) {
          throw new Error("Active subscription or pass required to use features");
        }
        break;

      default:
        throw new Error("Unknown action");
    }

    return {
      success: true,
      message: "Subscription access validated",
    };
  },
});

// Helper function to get subscription status (updated for event-based model)
async function getSubscriptionStatus(ctx: any, userId: string) {
  // Get user's DJ profile
  const djProfile = await ctx.db
    .query("djProfiles")
    .withIndex("by_user", (q) => q.eq("userId", userId))
    .first();

  if (!djProfile) {
    return getFreePlanDetails();
  }

  // Get subscription
  const subscription = await ctx.db
    .query("djSubscriptions")
    .withIndex("by_dj", (q) => q.eq("djId", djProfile._id))
    .first();

  if (!subscription) {
    return getFreePlanDetails();
  }

  const isActive = isSubscriptionActive(subscription);

  // Get plan details if subscription is active
  let plan = null;
  let planDetails = getFreePlanDetails();

  if (isActive && subscription.planId) {
    plan = await ctx.db.get(subscription.planId);
    if (plan) {
      planDetails = {
        isActive: true,
        planName: plan.name,
        durationMinutes: plan.durationMinutes,
        maxRequestsPerSession: plan.maxRequestsPerSession,
        daysRemaining: getDaysRemaining(subscription),
      };
    }
  }

  return planDetails;
}

// Helper functions (duplicated for now, should be shared)
function isSubscriptionActive(subscription: any): boolean {
  if (!subscription) return false;
  
  const now = Date.now();
  const isStatusActive = ["active", "trialing"].includes(subscription.status);
  const isNotExpired = !subscription.currentPeriodEnd || subscription.currentPeriodEnd > now;
  
  return isStatusActive && isNotExpired;
}

function getDaysRemaining(subscription: any): number | null {
  if (!subscription?.currentPeriodEnd) return null;
  
  const now = Date.now();
  const msRemaining = subscription.currentPeriodEnd - now;
  
  if (msRemaining <= 0) return 0;
  
  return Math.ceil(msRemaining / (24 * 60 * 60 * 1000));
}

function getFreePlanDetails() {
  return {
    isActive: false,
    planName: "Free",
    durationMinutes: 20,
    maxRequestsPerSession: 3,
    daysRemaining: null,
  };
}
