/**
 * File Storage Operations
 * 
 * This module handles file uploads, storage, and management using Convex's
 * built-in file storage system. Supports profile pictures, sponsor logos,
 * and other media files with validation and processing.
 */

import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// File upload mutation - generates upload URL
export const generateUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required for file uploads");
    }

    // Generate upload URL for authenticated users
    return await ctx.storage.generateUploadUrl();
  },
});

// Store file metadata after upload
export const storeFileMetadata = mutation({
  args: {
    storageId: v.id("_storage"),
    fileName: v.string(),
    fileType: v.string(),
    fileSize: v.number(),
    purpose: v.union(
      v.literal("profile_picture"),
      v.literal("sponsor_logo"),
      v.literal("session_artwork"),
      v.literal("other")
    ),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (args.fileSize > maxSize) {
      throw new Error("File size exceeds 10MB limit");
    }

    // Validate file type for images
    const allowedImageTypes = [
      "image/jpeg",
      "image/jpg", 
      "image/png",
      "image/webp",
      "image/gif"
    ];

    if (args.purpose !== "other" && !allowedImageTypes.includes(args.fileType)) {
      throw new Error("Invalid file type. Only JPEG, PNG, WebP, and GIF images are allowed.");
    }

    // Store file metadata
    const fileId = await ctx.db.insert("fileMetadata", {
      storageId: args.storageId,
      userId,
      fileName: args.fileName,
      fileType: args.fileType,
      fileSize: args.fileSize,
      purpose: args.purpose,
      description: args.description,
      uploadedAt: Date.now(),
      isActive: true,
    });

    return fileId;
  },
});

// Get file URL for display
export const getFileUrl = query({
  args: {
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    // Get file URL from storage
    return await ctx.storage.getUrl(args.storageId);
  },
});

// Get user's files
export const getUserFiles = query({
  args: {
    purpose: v.optional(v.union(
      v.literal("profile_picture"),
      v.literal("sponsor_logo"),
      v.literal("session_artwork"),
      v.literal("other")
    )),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    let query = ctx.db
      .query("fileMetadata")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .filter((q) => q.eq(q.field("isActive"), true));

    if (args.purpose) {
      query = query.filter((q) => q.eq(q.field("purpose"), args.purpose));
    }

    const files = await query
      .order("desc")
      .take(args.limit || 50);

    // Get URLs for each file
    const filesWithUrls = await Promise.all(
      files.map(async (file) => {
        const url = await ctx.storage.getUrl(file.storageId);
        return {
          ...file,
          url,
        };
      })
    );

    return filesWithUrls;
  },
});

// Update DJ profile picture
export const updateProfilePicture = mutation({
  args: {
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get DJ profile
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      throw new Error("DJ profile not found");
    }

    // Update DJ profile with new picture
    await ctx.db.patch(djProfile._id, {
      profilePictureStorageId: args.storageId,
      updatedAt: Date.now(),
    });

    return {
      success: true,
      message: "Profile picture updated successfully",
    };
  },
});

// Delete file
export const deleteFile = mutation({
  args: {
    fileId: v.id("fileMetadata"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get file metadata
    const file = await ctx.db.get(args.fileId);
    if (!file) {
      throw new Error("File not found");
    }

    // Check ownership
    if (file.userId !== userId) {
      throw new Error("Access denied: You can only delete your own files");
    }

    // Mark file as inactive (soft delete)
    await ctx.db.patch(args.fileId, {
      isActive: false,
      deletedAt: Date.now(),
    });

    // Delete from storage
    await ctx.storage.delete(file.storageId);

    return {
      success: true,
      message: "File deleted successfully",
    };
  },
});

// Get file metadata by storage ID
export const getFileMetadata = query({
  args: {
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    const file = await ctx.db
      .query("fileMetadata")
      .withIndex("by_storage", (q) => q.eq("storageId", args.storageId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    if (!file) {
      return null;
    }

    // Get file URL
    const url = await ctx.storage.getUrl(file.storageId);

    return {
      ...file,
      url,
    };
  },
});

// Clean up orphaned files (admin function)
export const cleanupOrphanedFiles = action({
  args: {},
  handler: async (ctx) => {
    // This would be an admin-only function in production
    // For now, it's a placeholder for cleanup operations
    
    console.log("Cleanup orphaned files - placeholder implementation");
    
    return {
      success: true,
      message: "Cleanup completed",
      filesProcessed: 0,
    };
  },
});

// Get storage usage statistics
export const getStorageStats = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const userFiles = await ctx.db
      .query("fileMetadata")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    const totalSize = userFiles.reduce((sum, file) => sum + file.fileSize, 0);
    const fileCount = userFiles.length;

    const filesByPurpose = userFiles.reduce((acc, file) => {
      acc[file.purpose] = (acc[file.purpose] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalFiles: fileCount,
      totalSize,
      totalSizeMB: Math.round(totalSize / (1024 * 1024) * 100) / 100,
      filesByPurpose,
      maxSizeMB: 10, // Current limit
    };
  },
});
