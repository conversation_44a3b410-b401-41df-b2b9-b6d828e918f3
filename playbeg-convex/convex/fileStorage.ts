/**
 * File Storage Management
 * 
 * This module handles file upload operations for profile pictures and sponsor logos
 * using Convex file storage with validation, processing, and metadata management.
 */

import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Query: Get file by storage ID
export const getFileById = query({
  args: {
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    const file = await ctx.storage.get(args.storageId);
    return file;
  },
});

// Query: Get file URL by storage ID
export const getFileUrl = query({
  args: {
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    const url = await ctx.storage.getUrl(args.storageId);
    return url;
  },
});

// Mutation: Generate upload URL for file
export const generateUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required to upload files");
    }

    return await ctx.storage.generateUploadUrl();
  },
});

// Mutation: Store file metadata after upload
export const storeFileMetadata = mutation({
  args: {
    storageId: v.id("_storage"),
    fileName: v.string(),
    fileType: v.union(
      v.literal("profile_picture"),
      v.literal("sponsor_logo"),
      v.literal("session_image"),
      v.literal("other")
    ),
    mimeType: v.string(),
    fileSize: v.number(),
    associatedId: v.optional(v.string()), // DJ profile ID, session ID, etc.
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Validate file type and size
    const validation = validateFileUpload(args.mimeType, args.fileSize, args.fileType);
    if (!validation.isValid) {
      // Delete the uploaded file if validation fails
      await ctx.storage.delete(args.storageId);
      throw new Error(`File validation failed: ${validation.errors.join(", ")}`);
    }

    const now = Date.now();
    const fileMetadataId = await ctx.db.insert("fileMetadata", {
      storageId: args.storageId,
      userId,
      fileName: args.fileName.trim(),
      fileType: args.fileType,
      mimeType: args.mimeType,
      fileSize: args.fileSize,
      associatedId: args.associatedId,
      uploadedAt: now,
      updatedAt: now,
    });

    return {
      fileMetadataId,
      storageId: args.storageId,
      message: "File uploaded and metadata stored successfully",
    };
  },
});

// Mutation: Delete file and metadata
export const deleteFile = mutation({
  args: {
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get file metadata to verify ownership
    const fileMetadata = await ctx.db
      .query("fileMetadata")
      .withIndex("by_storage_id", (q) => q.eq("storageId", args.storageId))
      .first();

    if (!fileMetadata) {
      throw new Error("File metadata not found");
    }

    if (fileMetadata.userId !== userId) {
      throw new Error("Access denied: You can only delete your own files");
    }

    // Delete file from storage
    await ctx.storage.delete(args.storageId);

    // Delete metadata
    await ctx.db.delete(fileMetadata._id);

    return {
      success: true,
      message: "File deleted successfully",
    };
  },
});

// Query: Get user's files by type
export const getUserFilesByType = query({
  args: {
    fileType: v.union(
      v.literal("profile_picture"),
      v.literal("sponsor_logo"),
      v.literal("session_image"),
      v.literal("other")
    ),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    const limit = args.limit ?? 50;
    
    const files = await ctx.db
      .query("fileMetadata")
      .withIndex("by_user_type", (q) => 
        q.eq("userId", userId).eq("fileType", args.fileType)
      )
      .order("desc")
      .take(limit);

    // Get URLs for each file
    const filesWithUrls = await Promise.all(
      files.map(async (file) => {
        const url = await ctx.storage.getUrl(file.storageId);
        return {
          ...file,
          url,
        };
      })
    );

    return filesWithUrls;
  },
});

// Query: Get file metadata by storage ID
export const getFileMetadata = query({
  args: {
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    const fileMetadata = await ctx.db
      .query("fileMetadata")
      .withIndex("by_storage_id", (q) => q.eq("storageId", args.storageId))
      .first();

    if (!fileMetadata) {
      return null;
    }

    const url = await ctx.storage.getUrl(args.storageId);
    
    return {
      ...fileMetadata,
      url,
    };
  },
});

// Action: Upload profile picture
export const uploadProfilePicture = action({
  args: {
    storageId: v.id("_storage"),
    fileName: v.string(),
    mimeType: v.string(),
    fileSize: v.number(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get user's DJ profile
    const djProfile = await ctx.runQuery("djProfiles:getCurrentDjProfile", {});
    if (!djProfile) {
      throw new Error("DJ profile required to upload profile picture");
    }

    // Store file metadata
    const fileResult = await ctx.runMutation("fileStorage:storeFileMetadata", {
      storageId: args.storageId,
      fileName: args.fileName,
      fileType: "profile_picture",
      mimeType: args.mimeType,
      fileSize: args.fileSize,
      associatedId: djProfile._id,
    });

    // Update DJ profile with new profile picture
    await ctx.runMutation("djProfiles:updateDjProfile", {
      profilePictureStorageId: args.storageId,
    });

    return {
      ...fileResult,
      profileUpdated: true,
      message: "Profile picture uploaded and profile updated successfully",
    };
  },
});

// Action: Upload sponsor logo
export const uploadSponsorLogo = action({
  args: {
    storageId: v.id("_storage"),
    fileName: v.string(),
    mimeType: v.string(),
    fileSize: v.number(),
    sessionId: v.optional(v.id("sessions")),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // If sessionId provided, verify ownership
    if (args.sessionId) {
      const session = await ctx.runQuery("sessions:getSessionById", {
        sessionId: args.sessionId,
      });
      
      if (!session) {
        throw new Error("Session not found");
      }
    }

    // Store file metadata
    const fileResult = await ctx.runMutation("fileStorage:storeFileMetadata", {
      storageId: args.storageId,
      fileName: args.fileName,
      fileType: "sponsor_logo",
      mimeType: args.mimeType,
      fileSize: args.fileSize,
      associatedId: args.sessionId,
    });

    return {
      ...fileResult,
      message: "Sponsor logo uploaded successfully",
    };
  },
});

// Helper function to validate file uploads
function validateFileUpload(mimeType: string, fileSize: number, fileType: string) {
  const errors: string[] = [];

  // Validate MIME type
  const allowedImageTypes = [
    "image/jpeg",
    "image/jpg", 
    "image/png",
    "image/gif",
    "image/webp"
  ];

  if (!allowedImageTypes.includes(mimeType.toLowerCase())) {
    errors.push(`Invalid file type: ${mimeType}. Allowed types: ${allowedImageTypes.join(", ")}`);
  }

  // Validate file size (different limits for different types)
  const maxSizes = {
    profile_picture: 5 * 1024 * 1024, // 5MB
    sponsor_logo: 2 * 1024 * 1024,    // 2MB
    session_image: 3 * 1024 * 1024,   // 3MB
    other: 1 * 1024 * 1024,           // 1MB
  };

  const maxSize = maxSizes[fileType as keyof typeof maxSizes] || maxSizes.other;
  
  if (fileSize > maxSize) {
    errors.push(`File size ${Math.round(fileSize / 1024 / 1024 * 100) / 100}MB exceeds maximum ${Math.round(maxSize / 1024 / 1024)}MB for ${fileType}`);
  }

  if (fileSize < 1024) { // Minimum 1KB
    errors.push("File size too small (minimum 1KB)");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
