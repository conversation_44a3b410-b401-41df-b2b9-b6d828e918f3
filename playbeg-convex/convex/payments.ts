/**
 * Payment Processing Convex Functions
 * 
 * This module handles payment processing, pass activation, and subscription management
 * for PlayBeg's time-limited pass system.
 */

import { v } from "convex/values";
import { action, mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Action: Create Stripe checkout session
export const createCheckoutSession = action({
  args: {
    planId: v.union(
      v.literal("24hour"),
      v.literal("48hour"),
      v.literal("7day")
    ),
    successUrl: v.string(),
    cancelUrl: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    try {
      const { createCheckoutSession } = await import("../lib/stripe");
      
      const session = await createCheckoutSession(
        args.planId,
        userId,
        args.successUrl,
        args.cancelUrl
      );

      return {
        success: true,
        sessionId: session.id,
        url: session.url,
      };
    } catch (error: any) {
      console.error('Error creating checkout session:', error);
      return {
        success: false,
        error: error.message || 'Failed to create checkout session',
      };
    }
  },
});

// Action: Verify payment and activate pass
export const verifyPaymentAndActivatePass = action({
  args: {
    sessionId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      const { getCheckoutSession, processSuccessfulPayment } = await import("../lib/stripe");
      
      const checkoutSession = await getCheckoutSession(args.sessionId);
      
      if (checkoutSession.payment_status !== 'paid') {
        return {
          success: false,
          error: 'Payment not completed',
        };
      }

      const activationData = await processSuccessfulPayment(checkoutSession);
      
      // Activate the pass in the database
      const passId = await ctx.runMutation("payments:activatePass", {
        userId: activationData.userId,
        planId: activationData.planId,
        planName: activationData.planName,
        durationHours: activationData.durationHours,
        maxRequests: activationData.maxRequests,
        amountPaid: activationData.amountPaid,
        activatedAt: activationData.activatedAt,
        expiresAt: activationData.expiresAt,
        stripeSessionId: activationData.stripeSessionId,
        stripePaymentIntentId: activationData.stripePaymentIntentId,
      });

      return {
        success: true,
        passId,
        activationData,
      };
    } catch (error: any) {
      console.error('Error verifying payment:', error);
      return {
        success: false,
        error: error.message || 'Failed to verify payment',
      };
    }
  },
});

// Mutation: Activate a pass
export const activatePass = mutation({
  args: {
    userId: v.string(),
    planId: v.string(),
    planName: v.string(),
    durationHours: v.number(),
    maxRequests: v.number(),
    amountPaid: v.number(),
    activatedAt: v.number(),
    expiresAt: v.number(),
    stripeSessionId: v.string(),
    stripePaymentIntentId: v.string(),
  },
  handler: async (ctx, args) => {
    // Get or create DJ profile
    let djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!djProfile) {
      const djProfileId = await ctx.db.insert("djProfiles", {
        userId: args.userId,
        completedOnboarding: false,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });
      djProfile = await ctx.db.get(djProfileId);
    }

    if (!djProfile) {
      throw new Error("Failed to create DJ profile");
    }

    // Create subscription plan if it doesn't exist
    let plan = await ctx.db
      .query("subscriptionPlans")
      .withIndex("by_name", (q) => q.eq("name", args.planName))
      .first();

    if (!plan) {
      const planId = await ctx.db.insert("subscriptionPlans", {
        name: args.planName,
        description: `${args.durationHours}h pass with ${args.maxRequests} requests`,
        priceAmount: args.amountPaid,
        priceCurrency: "usd",
        durationHours: args.durationHours,
        stripePriceId: args.stripeSessionId, // Temporary
        active: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });
      plan = await ctx.db.get(planId);
    }

    // Create or update DJ subscription
    const existingSubscription = await ctx.db
      .query("djSubscriptions")
      .withIndex("by_dj", (q) => q.eq("djId", djProfile._id))
      .filter((q) => q.eq(q.field("status"), "active"))
      .first();

    if (existingSubscription) {
      // Update existing subscription
      await ctx.db.patch(existingSubscription._id, {
        planId: plan?._id,
        status: "active",
        currentPeriodStart: args.activatedAt,
        currentPeriodEnd: args.expiresAt,
        cancelAtPeriodEnd: false,
        stripeSubscriptionId: args.stripePaymentIntentId,
        updatedAt: Date.now(),
      });
      return existingSubscription._id;
    } else {
      // Create new subscription
      return await ctx.db.insert("djSubscriptions", {
        djId: djProfile._id,
        planId: plan?._id,
        status: "active",
        currentPeriodStart: args.activatedAt,
        currentPeriodEnd: args.expiresAt,
        cancelAtPeriodEnd: false,
        stripeSubscriptionId: args.stripePaymentIntentId,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });
    }
  },
});

// Query: Get user's active pass
export const getUserActivePass = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      return null;
    }

    const activeSubscription = await ctx.db
      .query("djSubscriptions")
      .withIndex("by_dj", (q) => q.eq("djId", djProfile._id))
      .filter((q) => q.eq(q.field("status"), "active"))
      .first();

    if (!activeSubscription) {
      return null;
    }

    // Check if subscription is still valid
    const now = Date.now();
    if (activeSubscription.currentPeriodEnd && activeSubscription.currentPeriodEnd < now) {
      // Subscription expired, mark as past_due
      await ctx.db.patch(activeSubscription._id, {
        status: "past_due",
        updatedAt: now,
      });
      return null;
    }

    const plan = activeSubscription.planId 
      ? await ctx.db.get(activeSubscription.planId)
      : null;

    return {
      subscription: activeSubscription,
      plan,
      isActive: true,
      timeRemaining: activeSubscription.currentPeriodEnd 
        ? activeSubscription.currentPeriodEnd - now
        : 0,
    };
  },
});

// Query: Get user's pass history
export const getUserPassHistory = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      return [];
    }

    const subscriptions = await ctx.db
      .query("djSubscriptions")
      .withIndex("by_dj", (q) => q.eq("djId", djProfile._id))
      .order("desc")
      .take(args.limit || 10);

    const subscriptionsWithPlans = await Promise.all(
      subscriptions.map(async (subscription) => {
        const plan = subscription.planId 
          ? await ctx.db.get(subscription.planId)
          : null;
        return { subscription, plan };
      })
    );

    return subscriptionsWithPlans;
  },
});

// Mutation: Activate free pass
export const activateFreePass = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get or create DJ profile
    let djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      const djProfileId = await ctx.db.insert("djProfiles", {
        userId,
        completedOnboarding: false,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });
      djProfile = await ctx.db.get(djProfileId);
    }

    if (!djProfile) {
      throw new Error("Failed to create DJ profile");
    }

    // Check if user already has an active pass
    const activeSubscription = await ctx.db
      .query("djSubscriptions")
      .withIndex("by_dj", (q) => q.eq("djId", djProfile._id))
      .filter((q) => q.eq(q.field("status"), "active"))
      .first();

    if (activeSubscription) {
      throw new Error("You already have an active pass");
    }

    // Create free pass (20 minutes)
    const now = Date.now();
    const expiresAt = now + (20 * 60 * 1000); // 20 minutes

    return await ctx.db.insert("djSubscriptions", {
      djId: djProfile._id,
      status: "active",
      currentPeriodStart: now,
      currentPeriodEnd: expiresAt,
      cancelAtPeriodEnd: false,
      createdAt: now,
      updatedAt: now,
    });
  },
});
