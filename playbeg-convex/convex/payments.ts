/**
 * Payment System for PlayBeg
 *
 * Handles time-limited passes, Stripe integration, and subscription management
 * for the DJ request platform with event-based pricing model.
 */

import { v } from "convex/values";
import { action, mutation, query, internalMutation } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { internal } from "./_generated/api";

// Pass configuration
const PASS_CONFIGS = {
  free: { duration: 20 * 60 * 1000, maxRequests: 3, price: 0 }, // 20 minutes
  "24hour": { duration: 24 * 60 * 60 * 1000, maxRequests: 100, price: 999 }, // 24 hours, $9.99
  "48hour": { duration: 48 * 60 * 60 * 1000, maxRequests: 100, price: 1799 }, // 48 hours, $17.99
  "7day": { duration: 7 * 24 * 60 * 60 * 1000, maxRequests: 100, price: 4999 }, // 7 days, $49.99
} as const;

// Query: Get user's active pass
export const getUserActivePass = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const now = Date.now();

    // Find active pass that hasn't expired
    const activePass = await ctx.db
      .query("djPasses" as any)
      .withIndex("by_user_status" as any, (q: any) =>
        q.eq("userId", userId).eq("status", "active")
      )
      .filter((q: any) => q.gt(q.field("expiresAt"), now))
      .first();

    return activePass;
  },
});

// Query: Get user's pass history
export const getUserPassHistory = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    return await ctx.db
      .query("djPasses" as any)
      .withIndex("by_user" as any, (q: any) => q.eq("userId", userId))
      .order("desc")
      .take(args.limit || 10);
  },
});

// Query: Get available pass types
export const getAvailablePassTypes = query({
  args: {},
  handler: async () => {
    return [
      {
        id: "free",
        name: "Free Pass",
        description: "20 minutes, 3 requests",
        price: 0,
        duration: 20 * 60 * 1000, // 20 minutes in milliseconds
        maxRequests: 3,
        features: ["Basic session management", "3 song requests", "20 minute limit"]
      },
      {
        id: "24hour",
        name: "24-Hour Pass",
        description: "24 hours, 100 requests",
        price: 999, // $9.99 in cents
        duration: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
        maxRequests: 100,
        features: ["Extended session time", "100 song requests", "Priority support"]
      },
      {
        id: "48hour",
        name: "48-Hour Pass",
        description: "48 hours, 100 requests",
        price: 1799, // $17.99 in cents
        duration: 48 * 60 * 60 * 1000, // 48 hours in milliseconds
        maxRequests: 100,
        features: ["Extended session time", "100 song requests", "Priority support", "Advanced analytics"]
      },
      {
        id: "7day",
        name: "7-Day Pass",
        description: "7 days, 100 requests",
        price: 4999, // $49.99 in cents
        duration: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
        maxRequests: 100,
        features: ["Week-long access", "100 song requests", "Priority support", "Advanced analytics", "Custom branding"]
      }
    ];
  },
});

// Mutation: Activate a free pass
export const activateFreePass = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Check if user already has an active pass
    const existingPass = await ctx.db
      .query("djPasses" as any)
      .withIndex("by_user_status" as any, (q: any) =>
        q.eq("userId", userId).eq("status", "active")
      )
      .filter((q: any) => q.gt(q.field("expiresAt"), Date.now()))
      .first();

    if (existingPass) {
      throw new Error("You already have an active pass");
    }

    const now = Date.now();
    const config = PASS_CONFIGS.free;
    const expiresAt = now + config.duration;

    const passId = await ctx.db.insert("djPasses" as any, {
      userId,
      passType: "free",
      status: "active",
      purchaseDate: now,
      expiresAt,
      maxRequests: config.maxRequests,
      usedRequests: 0,
      createdAt: now,
      updatedAt: now,
    });

    return passId;
  },
});

// Mutation: Use a request from the active pass
export const usePassRequest = mutation({
  args: {
    passId: v.string(), // Using string for now due to type generation issues
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const pass = await ctx.db.get(args.passId as any);
    if (!pass) {
      throw new Error("Pass not found");
    }

    if ((pass as any).userId !== userId) {
      throw new Error("Unauthorized");
    }

    if ((pass as any).status !== "active") {
      throw new Error("Pass is not active");
    }

    if (Date.now() > (pass as any).expiresAt) {
      throw new Error("Pass has expired");
    }

    if ((pass as any).usedRequests >= (pass as any).maxRequests) {
      throw new Error("Pass request limit reached");
    }

    // Increment used requests
    await ctx.db.patch(args.passId as any, {
      usedRequests: (pass as any).usedRequests + 1,
      updatedAt: Date.now(),
    });

    return {
      success: true,
      remainingRequests: (pass as any).maxRequests - ((pass as any).usedRequests + 1),
    };
  },
});

// Query: Check if user can make a request
export const canMakeRequest = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return { canMake: false, reason: "Authentication required" };
    }

    const now = Date.now();

    // Find active pass
    const activePass = await ctx.db
      .query("djPasses" as any)
      .withIndex("by_user_status" as any, (q: any) =>
        q.eq("userId", userId).eq("status", "active")
      )
      .filter((q: any) => q.gt(q.field("expiresAt"), now))
      .first();

    if (!activePass) {
      return {
        canMake: false,
        reason: "No active pass. Please purchase a pass to make requests."
      };
    }

    if ((activePass as any).usedRequests >= (activePass as any).maxRequests) {
      return {
        canMake: false,
        reason: "Request limit reached for current pass."
      };
    }

    return {
      canMake: true,
      remainingRequests: (activePass as any).maxRequests - (activePass as any).usedRequests,
      passType: (activePass as any).passType,
      expiresAt: (activePass as any).expiresAt
    };
  },
});

// Action: Create Stripe checkout session for pass purchase
export const createCheckoutSession = action({
  args: {
    passType: v.union(v.literal("24hour"), v.literal("48hour"), v.literal("7day")),
    successUrl: v.string(),
    cancelUrl: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const config = PASS_CONFIGS[args.passType];
    if (!config) {
      throw new Error("Invalid pass type");
    }

    // TODO: Implement Stripe checkout session creation
    // This would require Stripe API integration
    throw new Error("Stripe integration not yet implemented. Please check back later.");
  },
});

// Action: Handle Stripe webhook for successful payment
export const handleStripeWebhook = action({
  args: {
    sessionId: v.string(),
    paymentIntentId: v.string(),
    passType: v.union(v.literal("24hour"), v.literal("48hour"), v.literal("7day")),
    userId: v.string(),
  },
  handler: async (ctx, args): Promise<{ success: boolean; passId?: string }> => {
    // Verify the webhook signature (in production)
    // For now, we'll create the pass directly

    const config = PASS_CONFIGS[args.passType];
    const now = Date.now();
    const expiresAt = now + config.duration;

    // Create the pass directly in this action for now
    const passId = await ctx.runMutation(internal.payments.createPaidPass, {
      userId: args.userId,
      passType: args.passType,
      paymentIntentId: args.paymentIntentId,
      expiresAt,
      maxRequests: config.maxRequests,
    });

    return { success: true, passId: passId as string };
  },
});

// Internal mutation: Create a paid pass (called from webhook)
export const createPaidPass = internalMutation({
  args: {
    userId: v.string(),
    passType: v.union(v.literal("24hour"), v.literal("48hour"), v.literal("7day")),
    paymentIntentId: v.string(),
    expiresAt: v.number(),
    maxRequests: v.number(),
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    const passId = await ctx.db.insert("djPasses" as any, {
      userId: args.userId,
      passType: args.passType,
      status: "active",
      purchaseDate: now,
      expiresAt: args.expiresAt,
      maxRequests: args.maxRequests,
      usedRequests: 0,
      stripePaymentIntentId: args.paymentIntentId,
      createdAt: now,
      updatedAt: now,
    });

    return passId;
  },
});

// Query: Get pass pricing information
export const getPassPricing = query({
  args: {},
  handler: async () => {
    return {
      free: {
        name: "Free Pass",
        duration: "20 minutes",
        maxRequests: 3,
        price: 0,
        description: "Perfect for trying out PlayBeg",
      },
      "24hour": {
        name: "24-Hour Pass",
        duration: "24 hours",
        maxRequests: 100,
        price: 999, // $9.99 in cents
        description: "Great for a single event or party",
      },
      "48hour": {
        name: "48-Hour Pass",
        duration: "48 hours",
        maxRequests: 100,
        price: 1799, // $17.99 in cents
        description: "Perfect for weekend events",
      },
      "7day": {
        name: "7-Day Pass",
        duration: "7 days",
        maxRequests: 100,
        price: 4999, // $49.99 in cents
        description: "Best value for extended use",
      },
    };
  },
});

// Mutation: Expire old passes (cleanup function)
export const expireOldPasses = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();

    // Find all active passes that have expired
    const expiredPasses = await ctx.db
      .query("djPasses" as any)
      .withIndex("by_status" as any, (q: any) => q.eq("status", "active"))
      .filter((q: any) => q.lt(q.field("expiresAt"), now))
      .collect();

    // Update their status to expired
    for (const pass of expiredPasses) {
      await ctx.db.patch((pass as any)._id, {
        status: "expired",
        updatedAt: now,
      });
    }

    return { expiredCount: expiredPasses.length };
  },
});

// Query: Get user's current pass status
export const getUserPassStatus = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return { hasActivePass: false };
    }

    const now = Date.now();
    const activePass = await ctx.db
      .query("djPasses" as any)
      .withIndex("by_user_status" as any, (q: any) =>
        q.eq("userId", userId).eq("status", "active")
      )
      .filter((q: any) => q.gt(q.field("expiresAt"), now))
      .first();

    if (!activePass) {
      return { hasActivePass: false };
    }

    return {
      hasActivePass: true,
      passType: (activePass as any).passType,
      expiresAt: (activePass as any).expiresAt,
      remainingRequests: (activePass as any).maxRequests - (activePass as any).usedRequests,
      usedRequests: (activePass as any).usedRequests,
      maxRequests: (activePass as any).maxRequests,
    };
  },
});


