/**
 * DJ Subscription Management
 * 
 * This module handles all database operations for DJ subscriptions including
 * creation, reading, updating, and deletion with Stripe integration,
 * status tracking, and feature access control.
 */

import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { isValidSubscriptionStatus } from "./validation";

// Query: Get current user's subscription
export const getCurrentUserSubscription = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    // Get user's DJ profile
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      return null;
    }

    // Get subscription
    const subscription = await ctx.db
      .query("djSubscriptions")
      .withIndex("by_dj", (q) => q.eq("djId", djProfile._id))
      .first();

    if (!subscription) {
      return null;
    }

    // Get subscription plan details
    let plan = null;
    if (subscription.planId) {
      plan = await ctx.db.get(subscription.planId);
    }

    return {
      subscription,
      plan,
      isActive: isSubscriptionActive(subscription),
      daysRemaining: getDaysRemaining(subscription),
    };
  },
});

// Query: Get subscription by DJ profile ID
export const getSubscriptionByDjId = query({
  args: {
    djId: v.id("djProfiles"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Verify ownership
    const djProfile = await ctx.db.get(args.djId);
    if (!djProfile || djProfile.userId !== userId) {
      throw new Error("Access denied: You can only view your own subscription");
    }

    const subscription = await ctx.db
      .query("djSubscriptions")
      .withIndex("by_dj", (q) => q.eq("djId", args.djId))
      .first();

    if (!subscription) {
      return null;
    }

    // Get subscription plan details
    let plan = null;
    if (subscription.planId) {
      plan = await ctx.db.get(subscription.planId);
    }

    return {
      subscription,
      plan,
      isActive: isSubscriptionActive(subscription),
      daysRemaining: getDaysRemaining(subscription),
    };
  },
});

// Query: Check subscription status and features
export const checkSubscriptionStatus = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return {
        hasSubscription: false,
        isActive: false,
        planName: "Free",
        features: getFreePlanFeatures(),
      };
    }

    // Get user's DJ profile
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      return {
        hasSubscription: false,
        isActive: false,
        planName: "Free",
        features: getFreePlanFeatures(),
      };
    }

    // Get subscription
    const subscription = await ctx.db
      .query("djSubscriptions")
      .withIndex("by_dj", (q) => q.eq("djId", djProfile._id))
      .first();

    if (!subscription) {
      return {
        hasSubscription: false,
        isActive: false,
        planName: "Free",
        features: getFreePlanFeatures(),
      };
    }

    const isActive = isSubscriptionActive(subscription);
    
    // Get plan details if subscription is active
    let plan = null;

    if (isActive && subscription.planId) {
      plan = await ctx.db.get(subscription.planId);
    }

    return {
      hasSubscription: true,
      isActive,
      planName: plan?.name || "Free",
      subscription: {
        id: subscription._id,
        status: subscription.status,
        currentPeriodStart: subscription.currentPeriodStart,
        currentPeriodEnd: subscription.currentPeriodEnd,
        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
      },
      plan: plan ? {
        id: plan._id,
        name: plan.name,
        durationMinutes: plan.durationMinutes,
        maxRequestsPerSession: plan.maxRequestsPerSession,
        priceAmount: plan.priceAmount,
        priceCurrency: plan.priceCurrency,
      } : null,
      daysRemaining: getDaysRemaining(subscription),
    };
  },
});

// Query: Get subscriptions by status (admin only)
export const getSubscriptionsByStatus = query({
  args: {
    status: v.union(
      v.literal("active"),
      v.literal("canceled"),
      v.literal("past_due"),
      v.literal("unpaid"),
      v.literal("incomplete"),
      v.literal("incomplete_expired"),
      v.literal("trialing")
    ),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // TODO: Add admin role check when admin system is implemented

    const limit = args.limit ?? 50;
    
    const subscriptions = await ctx.db
      .query("djSubscriptions")
      .withIndex("by_status", (q) => q.eq("status", args.status))
      .take(limit);

    // Get plan details for each subscription
    const subscriptionsWithPlans = await Promise.all(
      subscriptions.map(async (subscription) => {
        let plan = null;
        if (subscription.planId) {
          plan = await ctx.db.get(subscription.planId);
        }

        return {
          subscription,
          plan,
          isActive: isSubscriptionActive(subscription),
          daysRemaining: getDaysRemaining(subscription),
        };
      })
    );

    return subscriptionsWithPlans;
  },
});

// Mutation: Create subscription
export const createSubscription = mutation({
  args: {
    planId: v.id("subscriptionPlans"),
    stripeCustomerId: v.string(),
    stripeSubscriptionId: v.string(),
    currentPeriodStart: v.number(),
    currentPeriodEnd: v.number(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required to create subscription");
    }

    // Get user's DJ profile
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      throw new Error("DJ profile required to create subscription");
    }

    // Check if subscription already exists
    const existingSubscription = await ctx.db
      .query("djSubscriptions")
      .withIndex("by_dj", (q) => q.eq("djId", djProfile._id))
      .first();

    if (existingSubscription) {
      throw new Error("Subscription already exists for this DJ");
    }

    // Verify plan exists
    const plan = await ctx.db.get(args.planId);
    if (!plan || !plan.active) {
      throw new Error("Invalid or inactive subscription plan");
    }

    // Check for duplicate Stripe subscription ID
    const duplicateStripeSubscription = await ctx.db
      .query("djSubscriptions")
      .withIndex("by_stripe_subscription", (q) => q.eq("stripeSubscriptionId", args.stripeSubscriptionId))
      .first();

    if (duplicateStripeSubscription) {
      throw new Error("Stripe subscription ID already in use");
    }

    const now = Date.now();
    const subscriptionId = await ctx.db.insert("djSubscriptions", {
      djId: djProfile._id,
      planId: args.planId,
      status: "active",
      currentPeriodStart: args.currentPeriodStart,
      currentPeriodEnd: args.currentPeriodEnd,
      cancelAtPeriodEnd: false,
      stripeCustomerId: args.stripeCustomerId,
      stripeSubscriptionId: args.stripeSubscriptionId,
      createdAt: now,
      updatedAt: now,
    });

    return {
      subscriptionId,
      message: "Subscription created successfully",
    };
  },
});

// Mutation: Update subscription status (Stripe webhook)
export const updateSubscriptionStatus = mutation({
  args: {
    stripeSubscriptionId: v.string(),
    status: v.union(
      v.literal("active"),
      v.literal("canceled"),
      v.literal("past_due"),
      v.literal("unpaid"),
      v.literal("incomplete"),
      v.literal("incomplete_expired"),
      v.literal("trialing")
    ),
    currentPeriodStart: v.optional(v.number()),
    currentPeriodEnd: v.optional(v.number()),
    cancelAtPeriodEnd: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // This function is typically called by Stripe webhooks
    // TODO: Add webhook signature verification for production

    const subscription = await ctx.db
      .query("djSubscriptions")
      .withIndex("by_stripe_subscription", (q) => q.eq("stripeSubscriptionId", args.stripeSubscriptionId))
      .first();

    if (!subscription) {
      throw new Error("Subscription not found");
    }

    const updateData: any = {
      status: args.status,
      updatedAt: Date.now(),
    };

    if (args.currentPeriodStart !== undefined) {
      updateData.currentPeriodStart = args.currentPeriodStart;
    }

    if (args.currentPeriodEnd !== undefined) {
      updateData.currentPeriodEnd = args.currentPeriodEnd;
    }

    if (args.cancelAtPeriodEnd !== undefined) {
      updateData.cancelAtPeriodEnd = args.cancelAtPeriodEnd;
    }

    await ctx.db.patch(subscription._id, updateData);

    return {
      subscriptionId: subscription._id,
      status: args.status,
      message: "Subscription status updated successfully",
    };
  },
});

// Mutation: Cancel subscription
export const cancelSubscription = mutation({
  args: {
    cancelAtPeriodEnd: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Get user's DJ profile
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      throw new Error("DJ profile not found");
    }

    const subscription = await ctx.db
      .query("djSubscriptions")
      .withIndex("by_dj", (q) => q.eq("djId", djProfile._id))
      .first();

    if (!subscription) {
      throw new Error("No subscription found");
    }

    if (subscription.status === "canceled") {
      throw new Error("Subscription is already canceled");
    }

    const cancelAtPeriodEnd = args.cancelAtPeriodEnd ?? true;

    await ctx.db.patch(subscription._id, {
      cancelAtPeriodEnd,
      updatedAt: Date.now(),
    });

    return {
      subscriptionId: subscription._id,
      cancelAtPeriodEnd,
      message: cancelAtPeriodEnd 
        ? "Subscription will be canceled at the end of the current period"
        : "Subscription canceled immediately",
    };
  },
});

// Helper function to check if subscription is active
function isSubscriptionActive(subscription: any): boolean {
  if (!subscription) return false;
  
  const now = Date.now();
  const isStatusActive = ["active", "trialing"].includes(subscription.status);
  const isNotExpired = !subscription.currentPeriodEnd || subscription.currentPeriodEnd > now;
  
  return isStatusActive && isNotExpired;
}

// Helper function to get days remaining
function getDaysRemaining(subscription: any): number | null {
  if (!subscription?.currentPeriodEnd) return null;
  
  const now = Date.now();
  const msRemaining = subscription.currentPeriodEnd - now;
  
  if (msRemaining <= 0) return 0;
  
  return Math.ceil(msRemaining / (24 * 60 * 60 * 1000));
}

// Helper function to get free plan details (simplified for event-based model)
function getFreePlanDetails() {
  return {
    planName: "Free",
    durationMinutes: 20,
    maxRequestsPerSession: 3,
  };
}
