/**
 * Test Data Creation Functions
 * 
 * This module provides functions to create test data for development and testing.
 * These functions should only be used in development environments.
 */

import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Mutation: Create a test session for development
export const createTestSession = mutation({
  args: {
    name: v.optional(v.string()),
    acceptRequests: v.optional(v.boolean()),
    autoApproval: v.optional(v.boolean()),
    weddingModeEnabled: v.optional(v.boolean()),
    weddingCoupleNames: v.optional(v.array(v.string())),
    weddingDate: v.optional(v.string()),
    weddingHashtag: v.optional(v.string()),
    sponsorHeader: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required to create test session");
    }

    // Get or create DJ profile
    let djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      // Create a test DJ profile
      const djProfileId = await ctx.db.insert("djProfiles", {
        userId,
        displayName: "Test DJ",
        completedOnboarding: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });
      djProfile = await ctx.db.get(djProfileId);
    }

    if (!djProfile) {
      throw new Error("Failed to create DJ profile");
    }

    const now = Date.now();
    const sessionId = await ctx.db.insert("sessions", {
      djId: djProfile._id,
      name: args.name || "Test DJ Session",
      active: true,
      acceptRequests: args.acceptRequests ?? true,
      autoApproval: args.autoApproval ?? false,
      timeframeMinutes: 60,
      weddingModeEnabled: args.weddingModeEnabled ?? false,
      weddingCoupleNames: args.weddingCoupleNames,
      weddingDate: args.weddingDate,
      weddingHashtag: args.weddingHashtag,
      sponsorHeader: args.sponsorHeader,
      createdAt: now,
      updatedAt: now,
    });

    return {
      sessionId,
      message: "Test session created successfully",
      sessionUrl: `/session/${sessionId}`,
    };
  },
});

// Mutation: Create test song requests for a session
export const createTestRequests = mutation({
  args: {
    sessionId: v.id("sessions"),
    count: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    const count = args.count || 5;
    const testRequests = [
      { songTitle: "Bohemian Rhapsody", artistName: "Queen", requesterName: "Alice" },
      { songTitle: "Sweet Child O' Mine", artistName: "Guns N' Roses", requesterName: "Bob" },
      { songTitle: "Hotel California", artistName: "Eagles", requesterName: "Charlie" },
      { songTitle: "Stairway to Heaven", artistName: "Led Zeppelin", requesterName: "Diana" },
      { songTitle: "Imagine", artistName: "John Lennon", requesterName: "Eve" },
      { songTitle: "Billie Jean", artistName: "Michael Jackson", requesterName: "Frank" },
      { songTitle: "Like a Rolling Stone", artistName: "Bob Dylan", requesterName: "Grace" },
      { songTitle: "Smells Like Teen Spirit", artistName: "Nirvana", requesterName: "Henry" },
    ];

    const requestIds = [];
    const now = Date.now();

    for (let i = 0; i < Math.min(count, testRequests.length); i++) {
      const request = testRequests[i];
      const requestId = await ctx.db.insert("songRequests", {
        sessionId: args.sessionId,
        songTitle: request.songTitle,
        artistName: request.artistName,
        requesterName: request.requesterName,
        status: i % 3 === 0 ? "pending" : i % 3 === 1 ? "approved" : "auto-approved",
        addedToPlaylist: false,
        createdAt: now - (i * 60000), // Spread requests over time
      });
      requestIds.push(requestId);
    }

    return {
      requestIds,
      message: `Created ${requestIds.length} test requests`,
    };
  },
});

// Query: Get all test sessions for cleanup
export const getTestSessions = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    // Get user's DJ profile
    const djProfile = await ctx.db
      .query("djProfiles")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!djProfile) {
      return [];
    }

    return await ctx.db
      .query("sessions")
      .withIndex("by_dj", (q) => q.eq("djId", djProfile._id))
      .collect();
  },
});

// Mutation: Clean up test data
export const cleanupTestData = mutation({
  args: {
    sessionId: v.id("sessions"),
    confirmCleanup: v.boolean(),
  },
  handler: async (ctx, args) => {
    if (!args.confirmCleanup) {
      throw new Error("Cleanup confirmation required");
    }

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Verify ownership
    const djProfile = await ctx.db.get(session.djId);
    if (!djProfile || djProfile.userId !== userId) {
      throw new Error("Access denied: You can only cleanup your own test sessions");
    }

    // Delete all song requests for this session
    const requests = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    for (const request of requests) {
      await ctx.db.delete(request._id);
    }

    // Delete the session
    await ctx.db.delete(args.sessionId);

    return {
      success: true,
      message: `Cleaned up session and ${requests.length} requests`,
    };
  },
});

// Query: Get session info for testing
export const getSessionInfo = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return null;
    }

    const djProfile = await ctx.db.get(session.djId);
    const requestCount = await ctx.db
      .query("songRequests")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .collect();

    return {
      session,
      djProfile,
      requestCount: requestCount.length,
      sessionUrl: `/session/${session._id}`,
    };
  },
});
