#!/usr/bin/env node

/**
 * Test runner for PlayBeg Convex migration validation
 */

import { ConvexHttpClient } from "convex/browser";
import { api } from "./convex/_generated/api.js";

// Get the Convex URL from environment
const CONVEX_URL = process.env.NEXT_PUBLIC_CONVEX_URL || "https://lovely-cormorant-474.convex.cloud";

const client = new ConvexHttpClient(CONVEX_URL);

async function runPhase1Tests() {
  console.log("🧪 Starting Phase 1 Validation Tests...");
  console.log("=" .repeat(50));

  try {
    // Run comprehensive CRUD tests
    console.log("📋 Running comprehensive CRUD tests...");
    const crudResults = await client.action(api.testSuite.runPhase1ValidationTests, {});
    
    console.log("\n📊 CRUD Test Results:");
    console.log(`Total Tests: ${crudResults.summary.total}`);
    console.log(`Passed: ${crudResults.summary.passed}`);
    console.log(`Failed: ${crudResults.summary.failed}`);
    console.log(`Success Rate: ${((crudResults.summary.passed / crudResults.summary.total) * 100).toFixed(1)}%`);

    console.log("\n📝 Detailed Results:");
    crudResults.tests.forEach((test, index) => {
      const status = test.status === "PASS" ? "✅" : "❌";
      console.log(`${index + 1}. ${status} ${test.name}`);
      if (test.details) console.log(`   Details: ${test.details}`);
      if (test.error) console.log(`   Error: ${test.error}`);
    });

    // Run real-time tests
    console.log("\n🔄 Running real-time feature tests...");
    const realTimeResults = await client.action(api.testSuite.testRealTimeFeatures, {});
    
    console.log("\n📊 Real-time Test Results:");
    realTimeResults.realTimeTests.forEach((test, index) => {
      const status = test.status === "PASS" ? "✅" : "❌";
      console.log(`${index + 1}. ${status} ${test.feature}`);
      console.log(`   Details: ${test.details}`);
    });

    // Summary
    const totalTests = crudResults.summary.total + realTimeResults.realTimeTests.length;
    const totalPassed = crudResults.summary.passed + realTimeResults.realTimeTests.filter(t => t.status === "PASS").length;
    
    console.log("\n" + "=" .repeat(50));
    console.log("🎯 PHASE 1 VALIDATION SUMMARY");
    console.log("=" .repeat(50));
    console.log(`Total Tests Run: ${totalTests}`);
    console.log(`Tests Passed: ${totalPassed}`);
    console.log(`Tests Failed: ${totalTests - totalPassed}`);
    console.log(`Overall Success Rate: ${((totalPassed / totalTests) * 100).toFixed(1)}%`);

    if (totalPassed === totalTests) {
      console.log("\n🎉 ALL TESTS PASSED! Phase 1 validation complete.");
      return true;
    } else {
      console.log("\n⚠️  Some tests failed. Please review the results above.");
      return false;
    }

  } catch (error) {
    console.error("❌ Test execution failed:", error);
    return false;
  }
}

async function testEndToEndWorkflow() {
  console.log("\n🔄 Testing End-to-End Workflow...");
  console.log("=" .repeat(50));

  try {
    console.log("1. 👤 Creating test user...");
    const user = await client.mutation(api.users.createTestUser, {
      email: "<EMAIL>",
      name: "E2E Test User",
    });
    console.log(`   ✅ User created: ${user}`);

    console.log("2. 🎧 Creating DJ profile...");
    const djProfile = await client.mutation(api.djProfiles.createDjProfile, {
      userId: user,
      displayName: "E2E Test DJ",
      bio: "End-to-end testing DJ",
      genres: ["Electronic", "House"],
    });
    console.log(`   ✅ DJ Profile created: ${djProfile}`);

    console.log("3. 🎵 Creating session...");
    const session = await client.mutation(api.sessions.createSession, {
      djProfileId: djProfile,
      name: "E2E Test Session",
      description: "End-to-end test session",
      scheduledStartTime: Date.now() + 3600000,
      maxRequests: 25,
      isPrivate: false,
    });
    console.log(`   ✅ Session created: ${session}`);

    console.log("4. 🎶 Creating song request...");
    const songRequest = await client.mutation(api.songRequests.createSongRequest, {
      sessionId: session,
      songTitle: "E2E Test Song",
      artistName: "E2E Test Artist",
      requesterName: "E2E Test User",
      appleMusicId: "e2e-test-id",
      message: "End-to-end test request",
    });
    console.log(`   ✅ Song request created: ${songRequest}`);

    console.log("5. 💳 Testing subscription query...");
    const subscription = await client.query(api.djSubscriptions.getCurrentUserSubscription, {});
    console.log(`   ✅ Subscription query works: ${subscription ? "has subscription" : "no subscription"}`);

    console.log("\n🎉 End-to-End Workflow PASSED!");
    return true;

  } catch (error) {
    console.error("❌ End-to-End Workflow FAILED:", error);
    return false;
  }
}

// Main execution
async function main() {
  console.log("🚀 PlayBeg Convex Migration - Phase 1 Validation");
  console.log("=" .repeat(60));

  const phase1Success = await runPhase1Tests();
  const e2eSuccess = await testEndToEndWorkflow();

  console.log("\n" + "=" .repeat(60));
  console.log("🏁 FINAL RESULTS");
  console.log("=" .repeat(60));
  console.log(`Phase 1 Validation: ${phase1Success ? "✅ PASSED" : "❌ FAILED"}`);
  console.log(`End-to-End Workflow: ${e2eSuccess ? "✅ PASSED" : "❌ FAILED"}`);

  if (phase1Success && e2eSuccess) {
    console.log("\n🎉 PHASE 1 COMPLETE! Ready to proceed to Phase 2.");
    process.exit(0);
  } else {
    console.log("\n⚠️  Phase 1 validation incomplete. Please address failures before proceeding.");
    process.exit(1);
  }
}

main().catch(console.error);
