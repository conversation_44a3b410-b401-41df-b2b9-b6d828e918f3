{"name": "playbeg-convex", "version": "0.1.0", "private": true, "scripts": {"dev": "npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "next dev --hostname localhost", "dev:backend": "convex dev", "predev": "convex dev --until-success && convex dev --once --run-sh \"node setup.mjs --once\" && convex dashboard", "build": "next build", "build:staging": "NODE_ENV=staging next build", "build:production": "NODE_ENV=production next build", "start": "next start", "lint": "next lint", "deploy:staging": "./scripts/deploy-staging.sh", "deploy:production": "./scripts/deploy-production.sh", "env:staging": "convex env list --prod", "env:production": "convex env list --prod"}, "dependencies": {"@convex-dev/auth": "^0.0.81", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-form": "^0.1.7", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.23.0", "lucide-react": "^0.522.0", "next": "15.2.3", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.58.1", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.4.7", "eslint": "^9", "eslint-config-next": "15.2.3", "npm-run-all": "^4.1.5", "prettier": "^3.5.3", "tailwindcss": "^4", "typescript": "^5"}}