"use client";

import React, { useState } from 'react';
import { useConvexAuth, useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Button } from '../../components/ui/button';
import { Card } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { useRouter } from 'next/navigation';

export default function TestSessionPage() {
  const { isAuthenticated } = useConvexAuth();
  const router = useRouter();
  
  const [sessionName, setSessionName] = useState('Test DJ Session');
  const [weddingMode, setWeddingMode] = useState(false);
  const [coupleNames, setCoupleNames] = useState(['John', 'Jane']);
  const [weddingDate, setWeddingDate] = useState('2024-06-23');
  const [isCreating, setIsCreating] = useState(false);
  const [createdSession, setCreatedSession] = useState<any>(null);

  // Queries and mutations
  const testSessions = useQuery(api.testData.getTestSessions);
  const createTestSession = useMutation(api.testData.createTestSession);
  const createTestRequests = useMutation(api.testData.createTestRequests);
  const cleanupTestData = useMutation(api.testData.cleanupTestData);

  const handleCreateSession = async () => {
    if (!isAuthenticated) {
      router.push('/signin');
      return;
    }

    setIsCreating(true);
    try {
      const result = await createTestSession({
        name: sessionName,
        acceptRequests: true,
        autoApproval: false,
        weddingModeEnabled: weddingMode,
        weddingCoupleNames: weddingMode ? coupleNames : undefined,
        weddingDate: weddingMode ? weddingDate : undefined,
        weddingHashtag: weddingMode ? 'TestWedding2024' : undefined,
        sponsorHeader: 'Powered by PlayBeg - Test Session',
      });

      setCreatedSession(result);

      // Create some test requests
      await createTestRequests({
        sessionId: result.sessionId,
        count: 5,
      });

    } catch (error: any) {
      console.error('Failed to create test session:', error);
      alert('Failed to create test session: ' + error.message);
    } finally {
      setIsCreating(false);
    }
  };

  const handleCleanupSession = async (sessionId: string) => {
    try {
      await cleanupTestData({
        sessionId: sessionId as any,
        confirmCleanup: true,
      });
      setCreatedSession(null);
    } catch (error: any) {
      console.error('Failed to cleanup session:', error);
      alert('Failed to cleanup session: ' + error.message);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-8 text-center max-w-md">
          <h1 className="text-2xl font-bold text-white mb-4">Authentication Required</h1>
          <p className="text-white/80 mb-6">
            Please sign in to create test sessions.
          </p>
          <Button 
            onClick={() => router.push('/signin')}
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            Sign In
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-4">🧪 Test Session Creator</h1>
          <p className="text-white/80">Create test sessions for development and testing</p>
        </div>

        {/* Session Creation Form */}
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
          <h2 className="text-2xl font-bold text-white mb-6">Create Test Session</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                Session Name
              </label>
              <Input
                type="text"
                value={sessionName}
                onChange={(e) => setSessionName(e.target.value)}
                className="w-full bg-white/20 border-white/30 text-white placeholder-white/60"
                placeholder="Enter session name"
              />
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="weddingMode"
                checked={weddingMode}
                onChange={(e) => setWeddingMode(e.target.checked)}
                className="rounded"
              />
              <label htmlFor="weddingMode" className="text-white">
                Enable Wedding Mode
              </label>
            </div>

            {weddingMode && (
              <div className="space-y-4 pl-6 border-l-2 border-purple-500">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">
                      Partner 1 Name
                    </label>
                    <Input
                      type="text"
                      value={coupleNames[0]}
                      onChange={(e) => setCoupleNames([e.target.value, coupleNames[1]])}
                      className="w-full bg-white/20 border-white/30 text-white placeholder-white/60"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">
                      Partner 2 Name
                    </label>
                    <Input
                      type="text"
                      value={coupleNames[1]}
                      onChange={(e) => setCoupleNames([coupleNames[0], e.target.value])}
                      className="w-full bg-white/20 border-white/30 text-white placeholder-white/60"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Wedding Date
                  </label>
                  <Input
                    type="date"
                    value={weddingDate}
                    onChange={(e) => setWeddingDate(e.target.value)}
                    className="w-full bg-white/20 border-white/30 text-white"
                  />
                </div>
              </div>
            )}

            <Button
              onClick={handleCreateSession}
              disabled={isCreating}
              className="w-full bg-purple-600 hover:bg-purple-700 text-white py-3"
            >
              {isCreating ? 'Creating Session...' : 'Create Test Session'}
            </Button>
          </div>
        </Card>

        {/* Created Session Info */}
        {createdSession && (
          <Card className="bg-green-600/20 border-green-500/30 p-6">
            <h3 className="text-xl font-bold text-green-200 mb-4">✅ Session Created Successfully!</h3>
            <div className="space-y-3">
              <div>
                <span className="text-green-300 font-medium">Session ID:</span>
                <span className="text-white ml-2 font-mono">{createdSession.sessionId}</span>
              </div>
              <div>
                <span className="text-green-300 font-medium">Public URL:</span>
                <a 
                  href={`/session/${createdSession.sessionId}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-300 hover:text-blue-200 ml-2 underline"
                >
                  {window.location.origin}/session/{createdSession.sessionId}
                </a>
              </div>
              <div className="flex space-x-4 mt-4">
                <Button
                  onClick={() => window.open(`/session/${createdSession.sessionId}`, '_blank')}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Open Public Page
                </Button>
                <Button
                  onClick={() => router.push('/dashboard')}
                  className="bg-purple-600 hover:bg-purple-700 text-white"
                >
                  Go to Dashboard
                </Button>
                <Button
                  onClick={() => handleCleanupSession(createdSession.sessionId)}
                  variant="outline"
                  className="text-red-300 border-red-500/30 hover:bg-red-600/20"
                >
                  Delete Session
                </Button>
              </div>
            </div>
          </Card>
        )}

        {/* Existing Test Sessions */}
        {testSessions && testSessions.length > 0 && (
          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
            <h3 className="text-xl font-bold text-white mb-4">Existing Test Sessions</h3>
            <div className="space-y-3">
              {testSessions.map((session) => (
                <div key={session._id} className="bg-white/10 rounded-lg p-4 flex items-center justify-between">
                  <div>
                    <div className="font-semibold text-white">{session.name}</div>
                    <div className="text-white/80 text-sm">
                      Status: {session.active ? 'Active' : 'Inactive'} • 
                      Created: {new Date(session.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      onClick={() => window.open(`/session/${session._id}`, '_blank')}
                      size="sm"
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      View
                    </Button>
                    <Button
                      onClick={() => handleCleanupSession(session._id)}
                      size="sm"
                      variant="outline"
                      className="text-red-300 border-red-500/30 hover:bg-red-600/20"
                    >
                      Delete
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        )}

        {/* Navigation */}
        <div className="text-center">
          <Button
            onClick={() => router.push('/')}
            variant="outline"
            className="text-white border-white/30 hover:bg-white/10"
          >
            ← Back to Home
          </Button>
        </div>
      </div>
    </div>
  );
}
