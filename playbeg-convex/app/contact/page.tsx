"use client";

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { Button } from '../../components/ui/button';
import { Card } from '../../components/ui/card';

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    type: 'general'
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', formData);
    alert('Thank you for your message! We\'ll get back to you within 24 hours.');
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const contactMethods = [
    {
      icon: '📧',
      title: 'Email Support',
      description: 'Get help with technical issues or general questions',
      contact: '<EMAIL>',
      responseTime: 'Usually within 24 hours'
    },
    {
      icon: '💬',
      title: 'Live Chat',
      description: 'Chat with our team during business hours',
      contact: 'Available in-app',
      responseTime: 'Instant during business hours'
    },
    {
      icon: '📞',
      title: 'Phone Support',
      description: 'For urgent issues or enterprise customers',
      contact: '+1 (555) 123-PLAY',
      responseTime: 'Mon-Fri, 9AM-6PM PST'
    },
    {
      icon: '🐦',
      title: 'Social Media',
      description: 'Follow us for updates and community support',
      contact: '@PlayBegApp',
      responseTime: 'Usually within a few hours'
    }
  ];

  const faqs = [
    {
      question: 'How quickly do you respond to support requests?',
      answer: 'We aim to respond to all support requests within 24 hours. For urgent issues, we offer phone support for enterprise customers.'
    },
    {
      question: 'Do you offer training or onboarding?',
      answer: 'Yes! We provide free onboarding sessions for new users and comprehensive training materials in our help center.'
    },
    {
      question: 'Can you help with event setup?',
      answer: 'Absolutely! Our team can help you configure PlayBeg for your specific event needs. Contact us at least 48 hours before your event.'
    },
    {
      question: 'Do you have enterprise support?',
      answer: 'Yes, we offer dedicated enterprise support with priority response times, phone support, and custom training sessions.'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Navigation */}
      <nav className="bg-black/20 backdrop-blur-md border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-white">🎵 PlayBeg</Link>
            </div>
            <div className="hidden md:flex space-x-8">
              <Link href="/about" className="text-white/80 hover:text-white transition-colors">About</Link>
              <Link href="/pricing" className="text-white/80 hover:text-white transition-colors">Pricing</Link>
              <Link href="/blog" className="text-white/80 hover:text-white transition-colors">Blog</Link>
              <Link href="/help" className="text-white/80 hover:text-white transition-colors">Help</Link>
              <Link href="/contact" className="text-white font-medium">Contact</Link>
            </div>
            <div className="flex space-x-4">
              <Link href="/signin">
                <Button variant="outline" className="text-white border-white/30 hover:bg-white/10">
                  Sign In
                </Button>
              </Link>
              <Link href="/signin">
                <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Get in
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              {" "}Touch
            </span>
          </h1>
          <p className="text-xl text-white/80 mb-8 max-w-3xl mx-auto">
            Have questions about PlayBeg? Need help with your event? 
            Our team is here to help you create amazing musical experiences.
          </p>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">How Can We Help?</h2>
            <p className="text-xl text-white/80">
              Choose the best way to reach us based on your needs
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {contactMethods.map((method, index) => (
              <Card key={index} className="bg-white/10 backdrop-blur-md border-white/20 p-6 text-center hover:bg-white/15 transition-colors">
                <div className="text-4xl mb-4">{method.icon}</div>
                <h3 className="text-xl font-semibold text-white mb-2">{method.title}</h3>
                <p className="text-white/80 mb-4 text-sm">{method.description}</p>
                <div className="text-purple-400 font-medium mb-2">{method.contact}</div>
                <div className="text-white/60 text-sm">{method.responseTime}</div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Send Us a Message</h2>
            <p className="text-xl text-white/80">
              Fill out the form below and we'll get back to you as soon as possible
            </p>
          </div>
          
          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-8">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-white font-medium mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    value={formData.name}
                    onChange={handleChange}
                    className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-400"
                    placeholder="Your full name"
                  />
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-white font-medium mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    value={formData.email}
                    onChange={handleChange}
                    className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-400"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="type" className="block text-white font-medium mb-2">
                  Inquiry Type
                </label>
                <select
                  id="type"
                  name="type"
                  value={formData.type}
                  onChange={handleChange}
                  className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-400"
                >
                  <option value="general">General Question</option>
                  <option value="technical">Technical Support</option>
                  <option value="billing">Billing Question</option>
                  <option value="feature">Feature Request</option>
                  <option value="partnership">Partnership Inquiry</option>
                  <option value="press">Press/Media</option>
                </select>
              </div>
              
              <div>
                <label htmlFor="subject" className="block text-white font-medium mb-2">
                  Subject *
                </label>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  required
                  value={formData.subject}
                  onChange={handleChange}
                  className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-400"
                  placeholder="Brief description of your inquiry"
                />
              </div>
              
              <div>
                <label htmlFor="message" className="block text-white font-medium mb-2">
                  Message *
                </label>
                <textarea
                  id="message"
                  name="message"
                  required
                  rows={6}
                  value={formData.message}
                  onChange={handleChange}
                  className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-400 resize-vertical"
                  placeholder="Please provide as much detail as possible..."
                />
              </div>
              
              <div className="text-center">
                <Button 
                  type="submit"
                  size="lg"
                  className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-4 text-lg"
                >
                  Send Message
                </Button>
              </div>
            </form>
          </Card>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Support FAQ</h2>
            <p className="text-xl text-white/80">
              Quick answers to common support questions
            </p>
          </div>
          
          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <Card key={index} className="bg-white/10 backdrop-blur-md border-white/20 p-6">
                <h3 className="text-lg font-semibold text-white mb-3">{faq.question}</h3>
                <p className="text-white/80">{faq.answer}</p>
              </Card>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <p className="text-white/80 mb-4">
              Can't find what you're looking for?
            </p>
            <Link href="/help">
              <Button variant="outline" className="text-white border-white/30 hover:bg-white/10">
                Visit Help Center
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Office Info */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Our Office</h2>
            <p className="text-xl text-white/80">
              Visit us or send mail to our headquarters
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <Card className="bg-white/10 backdrop-blur-md border-white/20 p-8">
              <h3 className="text-2xl font-semibold text-white mb-6">PlayBeg Headquarters</h3>
              <div className="space-y-4 text-white/80">
                <div className="flex items-start">
                  <span className="text-purple-400 mr-3 mt-1">📍</span>
                  <div>
                    <div className="font-medium text-white">Address</div>
                    <div>123 Music Street<br />San Francisco, CA 94102<br />United States</div>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <span className="text-purple-400 mr-3 mt-1">🕒</span>
                  <div>
                    <div className="font-medium text-white">Business Hours</div>
                    <div>Monday - Friday: 9:00 AM - 6:00 PM PST<br />Saturday - Sunday: Closed</div>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <span className="text-purple-400 mr-3 mt-1">📧</span>
                  <div>
                    <div className="font-medium text-white">General Inquiries</div>
                    <div><EMAIL></div>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <span className="text-purple-400 mr-3 mt-1">🛠️</span>
                  <div>
                    <div className="font-medium text-white">Technical Support</div>
                    <div><EMAIL></div>
                  </div>
                </div>
              </div>
            </Card>
            
            <div className="bg-white/10 backdrop-blur-md rounded-lg p-8 border border-white/20">
              <div className="text-center">
                <div className="text-6xl mb-4">🏢</div>
                <h3 className="text-2xl font-semibold text-white mb-4">Visit Us</h3>
                <p className="text-white/80 mb-6">
                  We love meeting DJs and music enthusiasts! If you're in the San Francisco area, 
                  feel free to stop by our office. We recommend scheduling a visit in advance.
                </p>
                <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                  Schedule a Visit
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black/40 backdrop-blur-md border-t border-white/10 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="text-2xl font-bold text-white mb-4">🎵 PlayBeg</div>
              <p className="text-white/60">
                The ultimate platform for interactive DJ experiences and audience engagement.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Product</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/about" className="hover:text-white transition-colors">About</Link></li>
                <li><Link href="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
                <li><Link href="/help" className="hover:text-white transition-colors">Help Center</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Resources</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/blog" className="hover:text-white transition-colors">Blog</Link></li>
                <li><Link href="/help" className="hover:text-white transition-colors">FAQ</Link></li>
                <li><Link href="/contact" className="hover:text-white transition-colors">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Legal</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/terms" className="hover:text-white transition-colors">Terms of Service</Link></li>
                <li><Link href="/privacy" className="hover:text-white transition-colors">Privacy Policy</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-white/10 mt-8 pt-8 text-center text-white/60">
            <p>&copy; 2025 PlayBeg. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
