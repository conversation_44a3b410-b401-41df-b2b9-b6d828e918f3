"use client";

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { Button } from '../../components/ui/button';
import { Card } from '../../components/ui/card';

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'All Topics', icon: '📚' },
    { id: 'getting-started', name: 'Getting Started', icon: '🚀' },
    { id: 'sessions', name: 'Session Management', icon: '🎛️' },
    { id: 'requests', name: 'Song Requests', icon: '🎵' },
    { id: 'analytics', name: 'Analytics', icon: '📊' },
    { id: 'collaboration', name: 'Collaboration', icon: '👥' },
    { id: 'billing', name: 'Billing & Pricing', icon: '💳' },
    { id: 'technical', name: 'Technical Issues', icon: '🔧' }
  ];

  const faqs = [
    {
      id: 1,
      category: 'getting-started',
      question: 'How do I create my first DJ session?',
      answer: 'To create your first session, sign in to PlayBeg and click "Create Session" on your dashboard. Choose a session name, set your preferences, and click "Start Session". You\'ll get a unique link to share with your audience.',
      popular: true
    },
    {
      id: 2,
      category: 'getting-started',
      question: 'What do I need to get started with PlayBeg?',
      answer: 'All you need is a device with internet access and a web browser. PlayBeg works on computers, tablets, and smartphones. No special software or hardware required.',
      popular: true
    },
    {
      id: 3,
      category: 'sessions',
      question: 'How do I share my session with my audience?',
      answer: 'Each session has a unique URL that you can share via QR code, social media, or direct link. Your audience can join by visiting the link - no app download required.',
      popular: true
    },
    {
      id: 4,
      category: 'requests',
      question: 'How do song requests work?',
      answer: 'Audience members can submit song requests through your session page. You\'ll see all requests in real-time and can approve, decline, or add them to your queue with one click.',
      popular: true
    },
    {
      id: 5,
      category: 'sessions',
      question: 'Can I customize my session appearance?',
      answer: 'Yes! With paid passes, you can customize colors, add your logo, set custom messages, and even add custom CSS for complete branding control.',
      popular: false
    },
    {
      id: 6,
      category: 'collaboration',
      question: 'How do I invite other DJs to collaborate?',
      answer: 'In your session settings, go to "Collaboration" and enter the email addresses of DJs you want to invite. You can set different permission levels for each collaborator.',
      popular: false
    },
    {
      id: 7,
      category: 'analytics',
      question: 'What analytics does PlayBeg provide?',
      answer: 'PlayBeg provides real-time and historical analytics including audience size, song request trends, popular genres, engagement metrics, and session performance data.',
      popular: false
    },
    {
      id: 8,
      category: 'billing',
      question: 'How does the pass system work?',
      answer: 'Instead of monthly subscriptions, PlayBeg uses time-limited passes. Purchase a pass (24-hour, 48-hour, or 7-day) and get full access for that duration. Perfect for event-based work.',
      popular: true
    },
    {
      id: 9,
      category: 'technical',
      question: 'What browsers are supported?',
      answer: 'PlayBeg works on all modern browsers including Chrome, Firefox, Safari, and Edge. We recommend using the latest version for the best experience.',
      popular: false
    },
    {
      id: 10,
      category: 'sessions',
      question: 'Can I schedule sessions in advance?',
      answer: 'Yes! With 48-hour and 7-day passes, you can schedule sessions in advance, set automatic start/end times, and configure reminder notifications.',
      popular: false
    },
    {
      id: 11,
      category: 'requests',
      question: 'How do I handle inappropriate song requests?',
      answer: 'You can decline any request with a reason, block specific songs or artists, and set up automatic filters for explicit content. You have complete control over what gets played.',
      popular: false
    },
    {
      id: 12,
      category: 'technical',
      question: 'What if I lose internet connection during a session?',
      answer: 'PlayBeg automatically tries to reconnect when your connection is restored. Your session data is preserved, and audience members will see a "DJ reconnecting" message.',
      popular: false
    }
  ];

  const quickLinks = [
    { title: 'Getting Started Guide', description: 'Complete walkthrough for new users', icon: '📖', href: '/help/getting-started' },
    { title: 'Video Tutorials', description: 'Step-by-step video guides', icon: '🎥', href: '/help/videos' },
    { title: 'API Documentation', description: 'For developers and integrations', icon: '⚙️', href: '/help/api' },
    { title: 'Contact Support', description: 'Get help from our team', icon: '💬', href: '/contact' }
  ];

  const filteredFaqs = faqs.filter(faq => {
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    const matchesSearch = searchQuery === '' || 
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const popularFaqs = faqs.filter(faq => faq.popular);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Navigation */}
      <nav className="bg-black/20 backdrop-blur-md border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-white">🎵 PlayBeg</Link>
            </div>
            <div className="hidden md:flex space-x-8">
              <Link href="/about" className="text-white/80 hover:text-white transition-colors">About</Link>
              <Link href="/pricing" className="text-white/80 hover:text-white transition-colors">Pricing</Link>
              <Link href="/blog" className="text-white/80 hover:text-white transition-colors">Blog</Link>
              <Link href="/help" className="text-white font-medium">Help</Link>
              <Link href="/contact" className="text-white/80 hover:text-white transition-colors">Contact</Link>
            </div>
            <div className="flex space-x-4">
              <Link href="/signin">
                <Button variant="outline" className="text-white border-white/30 hover:bg-white/10">
                  Sign In
                </Button>
              </Link>
              <Link href="/signin">
                <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Help
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              {" "}Center
            </span>
          </h1>
          <p className="text-xl text-white/80 mb-8 max-w-3xl mx-auto">
            Find answers to your questions, learn how to use PlayBeg effectively, 
            and get the most out of your DJ sessions.
          </p>
          
          {/* Search Bar */}
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <input
                type="text"
                placeholder="Search for help articles..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-6 py-4 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-400 text-lg"
              />
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/60">
                🔍
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Links */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Quick Links</h2>
            <p className="text-xl text-white/80">
              Jump to the most helpful resources
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {quickLinks.map((link, index) => (
              <Link key={index} href={link.href}>
                <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6 text-center hover:bg-white/15 transition-colors cursor-pointer">
                  <div className="text-4xl mb-4">{link.icon}</div>
                  <h3 className="text-lg font-semibold text-white mb-2">{link.title}</h3>
                  <p className="text-white/80 text-sm">{link.description}</p>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Popular FAQs */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Popular Questions</h2>
            <p className="text-xl text-white/80">
              The most frequently asked questions by our users
            </p>
          </div>
          
          <div className="space-y-6">
            {popularFaqs.map((faq) => (
              <Card key={faq.id} className="bg-white/10 backdrop-blur-md border-white/20 p-6">
                <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
                  <span className="text-purple-400 mr-2">❓</span>
                  {faq.question}
                </h3>
                <p className="text-white/80 pl-6">{faq.answer}</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* All FAQs with Categories */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">All Help Topics</h2>
            <p className="text-xl text-white/80">
              Browse by category or search for specific topics
            </p>
          </div>
          
          <div className="flex flex-col lg:flex-row gap-12">
            {/* Categories Sidebar */}
            <div className="lg:w-1/4">
              <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6 sticky top-8">
                <h3 className="text-xl font-semibold text-white mb-4">Categories</h3>
                <div className="space-y-2">
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-colors flex items-center ${
                        selectedCategory === category.id
                          ? 'bg-purple-600 text-white'
                          : 'text-white/80 hover:bg-white/10'
                      }`}
                    >
                      <span className="mr-3">{category.icon}</span>
                      <span>{category.name}</span>
                    </button>
                  ))}
                </div>
              </Card>
            </div>

            {/* FAQ Content */}
            <div className="lg:w-3/4">
              <div className="mb-8">
                <h3 className="text-2xl font-bold text-white mb-2">
                  {selectedCategory === 'all' ? 'All Topics' : categories.find(cat => cat.id === selectedCategory)?.name}
                </h3>
                <p className="text-white/80">
                  {filteredFaqs.length} article{filteredFaqs.length !== 1 ? 's' : ''} found
                  {searchQuery && ` for "${searchQuery}"`}
                </p>
              </div>
              
              <div className="space-y-6">
                {filteredFaqs.map((faq) => (
                  <Card key={faq.id} className="bg-white/10 backdrop-blur-md border-white/20 p-6">
                    <h4 className="text-lg font-semibold text-white mb-3">{faq.question}</h4>
                    <p className="text-white/80">{faq.answer}</p>
                    <div className="mt-4 flex items-center justify-between">
                      <span className="text-purple-400 text-sm">
                        {categories.find(cat => cat.id === faq.category)?.name}
                      </span>
                      {faq.popular && (
                        <span className="bg-purple-600 text-white px-2 py-1 rounded text-xs">
                          Popular
                        </span>
                      )}
                    </div>
                  </Card>
                ))}
              </div>
              
              {filteredFaqs.length === 0 && (
                <Card className="bg-white/10 backdrop-blur-md border-white/20 p-12 text-center">
                  <div className="text-6xl mb-4">🔍</div>
                  <h3 className="text-xl font-semibold text-white mb-2">No results found</h3>
                  <p className="text-white/80 mb-6">
                    Try adjusting your search terms or browse different categories.
                  </p>
                  <Link href="/contact">
                    <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                      Contact Support
                    </Button>
                  </Link>
                </Card>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Still Need Help */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-purple-600 to-pink-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-4">
            Still Need Help?
          </h2>
          <p className="text-xl text-white/90 mb-8">
            Can't find what you're looking for? Our support team is here to help.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact">
              <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold">
                Contact Support
              </Button>
            </Link>
            <Button size="lg" variant="outline" className="text-white border-white hover:bg-white/10 px-8 py-4 text-lg">
              Live Chat
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black/40 backdrop-blur-md border-t border-white/10 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="text-2xl font-bold text-white mb-4">🎵 PlayBeg</div>
              <p className="text-white/60">
                The ultimate platform for interactive DJ experiences and audience engagement.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Product</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/about" className="hover:text-white transition-colors">About</Link></li>
                <li><Link href="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
                <li><Link href="/help" className="hover:text-white transition-colors">Help Center</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Resources</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/blog" className="hover:text-white transition-colors">Blog</Link></li>
                <li><Link href="/help" className="hover:text-white transition-colors">FAQ</Link></li>
                <li><Link href="/contact" className="hover:text-white transition-colors">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Legal</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/terms" className="hover:text-white transition-colors">Terms of Service</Link></li>
                <li><Link href="/privacy" className="hover:text-white transition-colors">Privacy Policy</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-white/10 mt-8 pt-8 text-center text-white/60">
            <p>&copy; 2025 PlayBeg. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
