import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { ConvexAuthNextjsServerProvider } from "@convex-dev/auth/nextjs/server";
import ConvexClientProvider from "@/components/ConvexClientProvider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap", // Optimize font loading
  preload: true,
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap", // Optimize font loading
  preload: false, // Only preload if actually used
});

export const metadata: Metadata = {
  title: "PlayBeg - Interactive DJ Platform",
  description: "The ultimate platform for interactive DJ experiences and real-time audience engagement. Connect with your audience like never before.",
  keywords: ["DJ", "music", "interactive", "requests", "audience engagement", "events"],
  authors: [{ name: "PlayBeg Team" }],
  creator: "PlayBeg Inc.",
  publisher: "PlayBeg Inc.",
  icons: {
    icon: "/convex.svg",
    shortcut: "/convex.svg",
    apple: "/convex.svg",
  },
  openGraph: {
    title: "PlayBeg - Interactive DJ Platform",
    description: "The ultimate platform for interactive DJ experiences and real-time audience engagement.",
    url: "https://playbeg.com",
    siteName: "PlayBeg",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "PlayBeg - Interactive DJ Platform",
    description: "The ultimate platform for interactive DJ experiences and real-time audience engagement.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ConvexAuthNextjsServerProvider>
      <html lang="en" className={`${geistSans.variable} ${geistMono.variable}`}>
        <body className="antialiased font-sans">
          <ConvexClientProvider>{children}</ConvexClientProvider>
        </body>
      </html>
    </ConvexAuthNextjsServerProvider>
  );
}
