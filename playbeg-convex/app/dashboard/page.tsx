"use client";

import { useConvexAuth, useQuery } from "convex/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { api } from "@/convex/_generated/api";
import { useAuthActions } from "@convex-dev/auth/react";
import { Button } from "@/components/ui/button";
import { Music, Plus, Settings, LogOut } from "lucide-react";

export default function Dashboard() {
  const { isAuthenticated, isLoading } = useConvexAuth();
  const { signOut } = useAuthActions();
  const router = useRouter();

  // Get current user and their DJ profile
  const currentUser = useQuery(api.users.getCurrentUser);
  const djProfile = useQuery(
    api.djProfiles.getByUserId,
    currentUser ? { userId: currentUser._id } : "skip"
  );

  // Get user's sessions
  const sessions = useQuery(
    api.sessions.getByDj,
    djProfile ? { djId: djProfile._id } : "skip"
  );

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/signin");
    }
  }, [isAuthenticated, isLoading, router]);

  const handleSignOut = async () => {
    await signOut();
    router.push("/signin");
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center">
        <div className="text-white text-lg">Loading...</div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect to signin
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900">
      {/* Header */}
      <header className="bg-gray-900/50 backdrop-blur-md border-b border-purple-500/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center mr-3">
                <Music className="w-5 h-5 text-white" />
              </div>
              <h1 className="text-xl font-bold text-white">PlayBeg</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-300">
                Welcome, {currentUser?.name || currentUser?.email || "DJ"}
              </span>
              <Button
                onClick={handleSignOut}
                variant="ghost"
                size="sm"
                className="text-gray-300 hover:text-white"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-white mb-2">DJ Dashboard</h2>
          <p className="text-gray-400">Manage your sessions and song requests</p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">Create Session</h3>
              <Plus className="w-6 h-6 text-purple-400" />
            </div>
            <p className="text-gray-400 mb-4">Start a new DJ session to receive song requests</p>
            <Button className="w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700">
              New Session
            </Button>
          </div>

          <div className="bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">Active Sessions</h3>
              <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            </div>
            <p className="text-gray-400 mb-4">
              {sessions?.filter(s => s.active).length || 0} active sessions
            </p>
            <Button variant="outline" className="w-full border-gray-600 text-gray-300 hover:bg-gray-700">
              View All
            </Button>
          </div>

          <div className="bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">Settings</h3>
              <Settings className="w-6 h-6 text-purple-400" />
            </div>
            <p className="text-gray-400 mb-4">Configure your DJ profile and preferences</p>
            <Button variant="outline" className="w-full border-gray-600 text-gray-300 hover:bg-gray-700">
              Manage
            </Button>
          </div>
        </div>

        {/* Recent Sessions */}
        <div className="bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6">
          <h3 className="text-xl font-semibold text-white mb-4">Recent Sessions</h3>
          {sessions && sessions.length > 0 ? (
            <div className="space-y-4">
              {sessions.slice(0, 5).map((session) => (
                <div
                  key={session._id}
                  className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg"
                >
                  <div>
                    <h4 className="font-medium text-white">{session.name}</h4>
                    <p className="text-sm text-gray-400">
                      {session.active ? "Active" : "Inactive"} • 
                      Created {new Date(session.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    {session.active && (
                      <span className="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full">
                        Live
                      </span>
                    )}
                    <Button size="sm" variant="outline" className="border-gray-600 text-gray-300">
                      View
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Music className="w-12 h-12 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400 mb-4">No sessions yet</p>
              <Button className="bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700">
                Create Your First Session
              </Button>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
