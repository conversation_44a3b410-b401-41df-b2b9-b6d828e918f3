"use client";

import { useConvexAuth, useQuery } from "convex/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { api } from "@/convex/_generated/api";
import { useAuthActions } from "@convex-dev/auth/react";
import { Button } from "@/components/ui/button";
import { Music, LogOut, BarChart3 } from "lucide-react";
import { DJProfileCard } from "@/components/dashboard/DJProfileCard";
import { SessionManager } from "@/components/dashboard/SessionManager";
import SongRequestManager from "@/components/SongRequestManager";
import { NotificationCenter } from "@/components/dashboard/NotificationCenter";
import PassPurchase from "@/components/PassPurchase";
import SongSearchInput from "@/components/SongSearchInput";
import IntegrationTestSuite from "@/components/testing/IntegrationTestSuite";
import RealtimeTestSuite from "@/components/testing/RealtimeTestSuite";
import SessionManagementHub from "@/components/session/SessionManagementHub";
import BlogManagement from "@/components/admin/BlogManagement";
import AdvancedSearchInterface from "@/components/search/AdvancedSearchInterface";
import PerformanceMonitor from "@/components/performance/PerformanceMonitor";
import AdvancedAnalyticsDashboard from "@/components/analytics/AdvancedAnalyticsDashboard";
import SecurityDashboard from "@/components/security/SecurityDashboard";
import PWAManager from "@/components/mobile/PWAManager";

export default function Dashboard() {
  const { isAuthenticated, isLoading } = useConvexAuth();
  const { signOut } = useAuthActions();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'overview' | 'requests' | 'billing' | 'analytics' | 'spotify-test' | 'integration-test' | 'realtime-test' | 'session-management' | 'blog-admin' | 'advanced-search' | 'performance' | 'security' | 'mobile-pwa'>('overview');

  // Get current user and their DJ profile
  const currentUser = useQuery(api.users.getCurrentUser);
  const userWithProfile = useQuery(api.users.getCurrentUserWithProfile);
  const djProfile = userWithProfile?.djProfile;

  // Get user status
  const userStatus = useQuery(api.users.checkUserStatus);

  // Get DJ profiles list (for testing)
  const allDjProfiles = useQuery(api.djProfiles.listDjProfiles, {
    limit: 10,
    onboardingStatus: true,
  });

  // Get active sessions for the current user
  const activeSessions = useQuery(api.sessions.getCurrentUserSessions,
    currentUser ? { activeOnly: true, limit: 10 } : "skip"
  );

  // Get the first active session for song request management
  const activeSession = activeSessions && activeSessions.length > 0 ? activeSessions[0] : null;

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/signin");
    }
  }, [isAuthenticated, isLoading, router]);

  const handleSignOut = async () => {
    await signOut();
    router.push("/signin");
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center">
        <div className="text-white text-lg">Loading...</div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect to signin
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900">
      {/* Header */}
      <header className="bg-gray-900/50 backdrop-blur-md border-b border-purple-500/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center mr-3">
                <Music className="w-5 h-5 text-white" />
              </div>
              <h1 className="text-xl font-bold text-white">PlayBeg</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-300">
                Welcome, {currentUser?.name || currentUser?.email || "DJ"}
              </span>
              <NotificationCenter />
              <Button
                onClick={handleSignOut}
                variant="ghost"
                size="sm"
                className="text-gray-300 hover:text-white"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-white mb-2">DJ Dashboard</h2>
          <p className="text-gray-400">Manage your sessions, song requests, and audience engagement</p>

          {/* Tab Navigation */}
          <div className="mt-6 border-b border-gray-700">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('overview')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'overview'
                    ? 'border-purple-500 text-purple-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                Overview
              </button>
              <button
                onClick={() => setActiveTab('requests')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'requests'
                    ? 'border-purple-500 text-purple-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                Song Requests
              </button>
              <button
                onClick={() => setActiveTab('billing')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'billing'
                    ? 'border-purple-500 text-purple-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                Billing & Passes
              </button>
              <button
                onClick={() => setActiveTab('analytics')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'analytics'
                    ? 'border-purple-500 text-purple-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                Analytics
              </button>
              <button
                onClick={() => setActiveTab('spotify-test')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'spotify-test'
                    ? 'border-purple-500 text-purple-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                🎵 Spotify Test
              </button>
              <button
                onClick={() => setActiveTab('integration-test')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'integration-test'
                    ? 'border-purple-500 text-purple-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                🧪 Integration Test
              </button>
              <button
                onClick={() => setActiveTab('realtime-test')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'realtime-test'
                    ? 'border-purple-500 text-purple-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                ⚡ Real-time Test
              </button>
              <button
                onClick={() => setActiveTab('session-management')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'session-management'
                    ? 'border-purple-500 text-purple-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                🎛️ Sessions
              </button>
              <button
                onClick={() => setActiveTab('blog-admin')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'blog-admin'
                    ? 'border-purple-500 text-purple-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                📝 Blog Admin
              </button>
              <button
                onClick={() => setActiveTab('advanced-search')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'advanced-search'
                    ? 'border-purple-500 text-purple-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                🔍 Advanced Search
              </button>
              <button
                onClick={() => setActiveTab('performance')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'performance'
                    ? 'border-purple-500 text-purple-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                ⚡ Performance
              </button>
              <button
                onClick={() => setActiveTab('security')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'security'
                    ? 'border-purple-500 text-purple-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                🛡️ Security
              </button>
              <button
                onClick={() => setActiveTab('mobile-pwa')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'mobile-pwa'
                    ? 'border-purple-500 text-purple-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                📱 Mobile & PWA
              </button>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - DJ Profile */}
            <div className="lg:col-span-1">
              <DJProfileCard />
            </div>

            {/* Right Column - Session Management */}
            <div className="lg:col-span-2">
              <SessionManager />
            </div>
          </div>
        )}

        {activeTab === 'requests' && (
          <div>
            {activeSession ? (
              <SongRequestManager sessionId={activeSession._id} />
            ) : (
              <div className="bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-8 text-center">
                <div className="text-4xl mb-4">🎵</div>
                <h3 className="text-xl font-semibold text-white mb-2">No Active Session</h3>
                <p className="text-gray-400 mb-6">
                  You need to have an active session to manage song requests.
                </p>
                <Button
                  onClick={() => setActiveTab('overview')}
                  className="bg-purple-600 hover:bg-purple-700 text-white"
                >
                  Create a Session
                </Button>
              </div>
            )}
          </div>
        )}

        {activeTab === 'billing' && (
          <PassPurchase />
        )}

        {activeTab === 'analytics' && (
          <AdvancedAnalyticsDashboard />
        )}

        {activeTab === 'spotify-test' && (
          <div className="bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6">
            <h2 className="text-2xl font-bold text-white mb-4">🎵 Spotify Integration Test</h2>
            <p className="text-gray-400 mb-6">
              Test the Spotify search functionality to verify the integration is working properly.
            </p>
            <div className="max-w-md">
              <SongSearchInput
                onSongSelect={(song) => {
                  console.log('Selected song:', song);
                  alert(`Selected: ${song.title} by ${song.artist}`);
                }}
                placeholder="Search for a song on Spotify..."
              />
            </div>
          </div>
        )}

        {activeTab === 'integration-test' && (
          <IntegrationTestSuite />
        )}

        {activeTab === 'realtime-test' && (
          <RealtimeTestSuite />
        )}

        {activeTab === 'session-management' && (
          <SessionManagementHub
            onSessionSelect={(sessionId) => {
              console.log('Selected session:', sessionId);
              // Could navigate to session detail page or open session in new tab
            }}
          />
        )}

        {activeTab === 'blog-admin' && (
          <BlogManagement />
        )}

        {activeTab === 'advanced-search' && (
          <AdvancedSearchInterface />
        )}

        {activeTab === 'performance' && (
          <PerformanceMonitor />
        )}

        {activeTab === 'security' && (
          <SecurityDashboard />
        )}

        {activeTab === 'mobile-pwa' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-white">Mobile & PWA Features</h2>
            </div>
            <PWAManager />
          </div>
        )}

        {/* Analytics Section */}
        <div className="mt-8">
          <div className="bg-gray-800/50 backdrop-blur-md border border-purple-500/20 rounded-lg p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-white flex items-center">
                <BarChart3 className="w-5 h-5 mr-2" />
                Quick Stats
              </h3>
              <Button variant="outline" size="sm" className="border-gray-600 text-gray-300 hover:bg-gray-700">
                View Full Analytics
              </Button>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-white mb-1">{allDjProfiles?.length || 0}</div>
                <p className="text-sm text-gray-400">Total DJs</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white mb-1">0</div>
                <p className="text-sm text-gray-400">Active Sessions</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white mb-1">0</div>
                <p className="text-sm text-gray-400">Song Requests</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white mb-1">
                  {userStatus?.isAuthenticated ? '100%' : '0%'}
                </div>
                <p className="text-sm text-gray-400">System Status</p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
