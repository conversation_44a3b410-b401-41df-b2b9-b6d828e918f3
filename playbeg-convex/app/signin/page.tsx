"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useAuthActions } from "@convex-dev/auth/react";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Loader2, Music } from "lucide-react";

const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export default function SignIn() {
  const { signIn } = useAuthActions();
  const router = useRouter();
  const [flow, setFlow] = useState<"signIn" | "signUp">("signIn");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (data: LoginFormValues) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.set("email", data.email);
      formData.set("password", data.password);
      formData.set("flow", flow);

      await signIn("password", formData);
      router.push("/");
    } catch (error: any) {
      setError(error.message || "Authentication failed");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center p-4">
      <div className="md:mx-auto md:max-w-md md:p-8 md:rounded-2xl md:bg-gray-900/50 md:border md:border-purple-500/20 md:backdrop-blur-md md:shadow-xl w-full max-w-md">
        {/* PlayBeg Branding Element */}
        <div className="flex items-center justify-center mb-8">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center">
              <Music className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-xl font-bold text-white">Welcome to PlayBeg</h2>
            <p className="text-gray-400 text-sm">The DJ's song request platform</p>
          </div>
        </div>

        <div className="space-y-6">
          <div className="space-y-2 text-center">
            <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-500 to-cyan-400 bg-clip-text text-transparent">
              {flow === "signIn" ? "Sign In to PlayBeg" : "Join PlayBeg"}
            </h1>
            <p className="text-gray-400 text-base mt-2">
              {flow === "signIn" ? "Access your DJ dashboard" : "Create your DJ account"}
            </p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="block text-sm font-medium text-white mb-2">
                        Email Address
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          autoComplete="email"
                          {...field}
                          className="w-full h-12 px-3 rounded-lg bg-gray-800 border border-gray-700 text-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500/50 focus:outline-none transition-colors"
                          required
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="block text-sm font-medium text-white mb-2">
                        Password
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="••••••••"
                          autoComplete={flow === "signIn" ? "current-password" : "new-password"}
                          {...field}
                          className="w-full h-12 px-3 rounded-lg bg-gray-800 border border-gray-700 text-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500/50 focus:outline-none transition-colors"
                          required
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Button
                type="submit"
                className="w-full h-12 text-lg font-semibold tracking-wide rounded-lg bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white transition-transform duration-200 flex items-center justify-center"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {flow === "signIn" ? "Signing in..." : "Creating account..."}
                  </>
                ) : (
                  <span>{flow === "signIn" ? "Sign In" : "Sign Up"}</span>
                )}
              </Button>

              {error && (
                <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3">
                  <p className="text-red-200 text-sm text-center">{error}</p>
                </div>
              )}
            </form>
          </Form>

          <div className="flex justify-center mt-6 text-sm">
            <span className="text-gray-400">
              {flow === "signIn" ? "Don't have an account?" : "Already have an account?"}
            </span>
            <button
              onClick={() => setFlow(flow === "signIn" ? "signUp" : "signIn")}
              className="ml-2 text-purple-400 hover:text-purple-300 transition-colors font-medium"
            >
              {flow === "signIn" ? "Sign up" : "Sign in"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
