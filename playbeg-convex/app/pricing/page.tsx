"use client";

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { Button } from '../../components/ui/button';
import { Card } from '../../components/ui/card';

export default function PricingPage() {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'annual'>('monthly');

  const plans = [
    {
      name: 'Free',
      description: 'Perfect for trying out PlayBeg',
      price: { monthly: 0, annual: 0 },
      features: [
        '20 minutes per session',
        'Up to 3 song requests',
        'Basic analytics',
        'Community support',
        'Standard session controls'
      ],
      limitations: [
        'No session templates',
        'No scheduling',
        'No collaboration features',
        'No custom branding'
      ],
      cta: 'Start Free',
      popular: false
    },
    {
      name: '24-Hour Pass',
      description: 'Great for single events',
      price: { monthly: 9.99, annual: 9.99 },
      features: [
        '24 hours of unlimited sessions',
        'Up to 100 song requests',
        'Full analytics dashboard',
        'Email support',
        'Advanced session controls',
        'Basic templates',
        'Custom branding'
      ],
      limitations: [
        'No collaboration features',
        'No scheduling',
        'Single-use pass'
      ],
      cta: 'Buy 24-Hour Pass',
      popular: false
    },
    {
      name: '48-Hour Pass',
      description: 'Perfect for weekend events',
      price: { monthly: 17.99, annual: 17.99 },
      features: [
        '48 hours of unlimited sessions',
        'Up to 100 song requests',
        'Full analytics dashboard',
        'Priority email support',
        'Advanced session controls',
        'Session templates',
        'Custom branding',
        'Basic collaboration (2 DJs)'
      ],
      limitations: [
        'Limited collaboration features',
        'No advanced scheduling'
      ],
      cta: 'Buy 48-Hour Pass',
      popular: true
    },
    {
      name: '7-Day Pass',
      description: 'Ideal for event series or tours',
      price: { monthly: 49.99, annual: 49.99 },
      features: [
        '7 days of unlimited sessions',
        'Up to 100 song requests per session',
        'Full analytics dashboard',
        'Priority support',
        'All session controls',
        'Unlimited templates',
        'Full custom branding',
        'Full collaboration features',
        'Session scheduling',
        'Advanced analytics',
        'Export capabilities'
      ],
      limitations: [],
      cta: 'Buy 7-Day Pass',
      popular: false
    }
  ];

  const faqs = [
    {
      question: 'How does the time-limited pass system work?',
      answer: 'Our passes give you full access to PlayBeg for a specific duration. Once activated, you can create unlimited sessions and use all included features until the pass expires.'
    },
    {
      question: 'Can I upgrade my pass if I need more time?',
      answer: 'Yes! You can purchase additional passes at any time. If you have an active pass, the new pass time will be added to your remaining time.'
    },
    {
      question: 'What happens to my data when my pass expires?',
      answer: 'Your session data and analytics are preserved for 30 days after your pass expires. You can reactivate access by purchasing a new pass.'
    },
    {
      question: 'Do you offer refunds?',
      answer: 'We offer a 24-hour money-back guarantee on all passes. If you\'re not satisfied within the first 24 hours of activation, contact us for a full refund.'
    },
    {
      question: 'Can multiple DJs use the same pass?',
      answer: 'Collaboration features are included in 48-Hour and 7-Day passes, allowing multiple DJs to work together on sessions. Each pass is tied to the primary account holder.'
    },
    {
      question: 'Is there a limit on audience size?',
      answer: 'No! All our passes support unlimited audience members. Your sessions can scale from intimate gatherings to massive events.'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Navigation */}
      <nav className="bg-black/20 backdrop-blur-md border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-white">🎵 PlayBeg</Link>
            </div>
            <div className="hidden md:flex space-x-8">
              <Link href="/about" className="text-white/80 hover:text-white transition-colors">About</Link>
              <Link href="/pricing" className="text-white font-medium">Pricing</Link>
              <Link href="/blog" className="text-white/80 hover:text-white transition-colors">Blog</Link>
              <Link href="/help" className="text-white/80 hover:text-white transition-colors">Help</Link>
              <Link href="/contact" className="text-white/80 hover:text-white transition-colors">Contact</Link>
            </div>
            <div className="flex space-x-4">
              <Link href="/signin">
                <Button variant="outline" className="text-white border-white/30 hover:bg-white/10">
                  Sign In
                </Button>
              </Link>
              <Link href="/signin">
                <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Simple, Event-Based
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              {" "}Pricing
            </span>
          </h1>
          <p className="text-xl text-white/80 mb-8 max-w-3xl mx-auto">
            No monthly subscriptions. No hidden fees. Just pay for what you need, 
            when you need it. Perfect for DJs who work events, not 9-to-5 schedules.
          </p>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {plans.map((plan, index) => (
              <Card 
                key={index} 
                className={`relative bg-white/10 backdrop-blur-md border-white/20 p-6 ${
                  plan.popular ? 'ring-2 ring-purple-400 bg-white/15' : ''
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-purple-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                  <p className="text-white/60 mb-4">{plan.description}</p>
                  <div className="text-4xl font-bold text-white mb-2">
                    ${plan.price.monthly}
                    {plan.price.monthly > 0 && <span className="text-lg text-white/60">/pass</span>}
                  </div>
                </div>

                <div className="space-y-3 mb-6">
                  {plan.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-start">
                      <span className="text-green-400 mr-2 mt-1">✓</span>
                      <span className="text-white/80 text-sm">{feature}</span>
                    </div>
                  ))}
                  {plan.limitations.map((limitation, limitationIndex) => (
                    <div key={limitationIndex} className="flex items-start">
                      <span className="text-red-400 mr-2 mt-1">✗</span>
                      <span className="text-white/60 text-sm">{limitation}</span>
                    </div>
                  ))}
                </div>

                <Link href="/signin">
                  <Button 
                    className={`w-full ${
                      plan.popular 
                        ? 'bg-purple-600 hover:bg-purple-700 text-white' 
                        : 'bg-white/20 hover:bg-white/30 text-white border border-white/30'
                    }`}
                  >
                    {plan.cta}
                  </Button>
                </Link>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Comparison */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Why Event-Based Pricing?</h2>
            <p className="text-xl text-white/80">
              Traditional monthly subscriptions don't make sense for event-based work
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6 text-center">
              <div className="text-4xl mb-4">💰</div>
              <h3 className="text-xl font-semibold text-white mb-2">Pay Only When You Work</h3>
              <p className="text-white/80">
                No wasted money on months when you're not doing events. 
                Only pay for the time you actually need the platform.
              </p>
            </Card>
            
            <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6 text-center">
              <div className="text-4xl mb-4">🎯</div>
              <h3 className="text-xl font-semibold text-white mb-2">Perfect for Event DJs</h3>
              <p className="text-white/80">
                Whether you do one wedding a month or multiple events per week, 
                our pricing scales with your business.
              </p>
            </Card>
            
            <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6 text-center">
              <div className="text-4xl mb-4">🚀</div>
              <h3 className="text-xl font-semibold text-white mb-2">No Long-Term Commitment</h3>
              <p className="text-white/80">
                Try PlayBeg for your next event without worrying about 
                canceling subscriptions or long-term contracts.
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Frequently Asked Questions</h2>
            <p className="text-xl text-white/80">
              Everything you need to know about PlayBeg pricing
            </p>
          </div>
          
          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <Card key={index} className="bg-white/10 backdrop-blur-md border-white/20 p-6">
                <h3 className="text-lg font-semibold text-white mb-3">{faq.question}</h3>
                <p className="text-white/80">{faq.answer}</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-purple-600 to-pink-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-4">
            Ready to Transform Your Next Event?
          </h2>
          <p className="text-xl text-white/90 mb-8">
            Start with our free plan and upgrade when you're ready for your first paid event.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/signin">
              <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold">
                Start Free Today
              </Button>
            </Link>
            <Link href="/contact">
              <Button size="lg" variant="outline" className="text-white border-white hover:bg-white/10 px-8 py-4 text-lg">
                Contact Sales
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black/40 backdrop-blur-md border-t border-white/10 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="text-2xl font-bold text-white mb-4">🎵 PlayBeg</div>
              <p className="text-white/60">
                The ultimate platform for interactive DJ experiences and audience engagement.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Product</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/about" className="hover:text-white transition-colors">About</Link></li>
                <li><Link href="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
                <li><Link href="/help" className="hover:text-white transition-colors">Help Center</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Resources</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/blog" className="hover:text-white transition-colors">Blog</Link></li>
                <li><Link href="/help" className="hover:text-white transition-colors">FAQ</Link></li>
                <li><Link href="/contact" className="hover:text-white transition-colors">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Legal</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/terms" className="hover:text-white transition-colors">Terms of Service</Link></li>
                <li><Link href="/privacy" className="hover:text-white transition-colors">Privacy Policy</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-white/10 mt-8 pt-8 text-center text-white/60">
            <p>&copy; 2025 PlayBeg. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
