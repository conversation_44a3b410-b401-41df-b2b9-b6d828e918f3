"use client";

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '../../components/ui/button';
import { Card } from '../../components/ui/card';

export default function AboutPage() {
  const teamMembers = [
    {
      name: '<PERSON>',
      role: 'Founder & CEO',
      bio: 'Former DJ with 15+ years of experience in the music industry. Passionate about connecting artists with their audiences.',
      avatar: '🎧'
    },
    {
      name: '<PERSON>',
      role: 'CTO',
      bio: 'Full-stack engineer with expertise in real-time systems and audio technology. Previously at Spotify and SoundCloud.',
      avatar: '💻'
    },
    {
      name: '<PERSON>',
      role: 'Head of Product',
      bio: 'Product designer focused on creating intuitive experiences for creative professionals. Former Apple design team member.',
      avatar: '🎨'
    }
  ];

  const milestones = [
    {
      year: '2023',
      title: 'PlayBeg Founded',
      description: 'Started as a simple song request app for local DJs and small events.'
    },
    {
      year: '2024',
      title: 'Real-Time Features',
      description: 'Launched live analytics and audience engagement features, serving 1,000+ DJs.'
    },
    {
      year: '2025',
      title: 'Advanced Platform',
      description: 'Introduced collaboration tools, session scheduling, and enterprise features.'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Navigation */}
      <nav className="bg-black/20 backdrop-blur-md border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-white">🎵 PlayBeg</Link>
            </div>
            <div className="hidden md:flex space-x-8">
              <Link href="/about" className="text-white font-medium">About</Link>
              <Link href="/pricing" className="text-white/80 hover:text-white transition-colors">Pricing</Link>
              <Link href="/blog" className="text-white/80 hover:text-white transition-colors">Blog</Link>
              <Link href="/help" className="text-white/80 hover:text-white transition-colors">Help</Link>
              <Link href="/contact" className="text-white/80 hover:text-white transition-colors">Contact</Link>
            </div>
            <div className="flex space-x-4">
              <Link href="/signin">
                <Button variant="outline" className="text-white border-white/30 hover:bg-white/10">
                  Sign In
                </Button>
              </Link>
              <Link href="/signin">
                <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Revolutionizing DJ-Audience
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              {" "}Interaction
            </span>
          </h1>
          <p className="text-xl text-white/80 mb-8 max-w-3xl mx-auto">
            PlayBeg was born from a simple idea: what if DJs could connect with their audience 
            in real-time, creating truly interactive musical experiences? Today, we're making 
            that vision a reality for thousands of DJs worldwide.
          </p>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold text-white mb-6">Our Mission</h2>
              <p className="text-lg text-white/80 mb-6">
                We believe that music is most powerful when it brings people together. 
                PlayBeg bridges the gap between DJs and their audiences, creating 
                collaborative musical experiences that engage everyone in the room.
              </p>
              <p className="text-lg text-white/80 mb-6">
                Whether it's a wedding, club night, corporate event, or house party, 
                our platform empowers DJs with the tools they need to read the room, 
                respond to their audience, and create unforgettable moments.
              </p>
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-400">10,000+</div>
                  <div className="text-white/60">Active DJs</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-400">500K+</div>
                  <div className="text-white/60">Song Requests</div>
                </div>
              </div>
            </div>
            <div className="bg-white/10 backdrop-blur-md rounded-lg p-8 border border-white/20">
              <h3 className="text-2xl font-semibold text-white mb-4">What We Stand For</h3>
              <ul className="space-y-4 text-white/80">
                <li className="flex items-start">
                  <span className="text-purple-400 mr-3">🎵</span>
                  <span><strong>Accessibility:</strong> Making professional DJ tools available to everyone</span>
                </li>
                <li className="flex items-start">
                  <span className="text-purple-400 mr-3">🤝</span>
                  <span><strong>Connection:</strong> Bringing DJs and audiences closer together</span>
                </li>
                <li className="flex items-start">
                  <span className="text-purple-400 mr-3">🚀</span>
                  <span><strong>Innovation:</strong> Pushing the boundaries of music technology</span>
                </li>
                <li className="flex items-start">
                  <span className="text-purple-400 mr-3">🌍</span>
                  <span><strong>Community:</strong> Supporting the global DJ and music community</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Meet Our Team</h2>
            <p className="text-xl text-white/80">
              Passionate musicians, engineers, and designers working to transform the DJ industry
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <Card key={index} className="bg-white/10 backdrop-blur-md border-white/20 p-6 text-center">
                <div className="text-6xl mb-4">{member.avatar}</div>
                <h3 className="text-xl font-semibold text-white mb-2">{member.name}</h3>
                <div className="text-purple-400 font-medium mb-4">{member.role}</div>
                <p className="text-white/80">{member.bio}</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Our Journey</h2>
            <p className="text-xl text-white/80">
              From a simple idea to a platform serving thousands of DJs worldwide
            </p>
          </div>
          <div className="space-y-8">
            {milestones.map((milestone, index) => (
              <div key={index} className="flex items-start">
                <div className="bg-purple-600 text-white rounded-full w-16 h-16 flex items-center justify-center font-bold text-sm mr-6 flex-shrink-0">
                  {milestone.year}
                </div>
                <div className="bg-white/10 backdrop-blur-md rounded-lg p-6 border border-white/20 flex-grow">
                  <h3 className="text-xl font-semibold text-white mb-2">{milestone.title}</h3>
                  <p className="text-white/80">{milestone.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Why Choose PlayBeg?</h2>
            <p className="text-xl text-white/80">
              We're not just building software – we're building the future of live music
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card className="bg-white/10 backdrop-blur-md border-white/20 p-8">
              <h3 className="text-2xl font-semibold text-white mb-4">🎯 DJ-First Design</h3>
              <p className="text-white/80">
                Every feature is designed by DJs, for DJs. We understand the unique challenges 
                of live performance and build tools that actually help in real-world scenarios.
              </p>
            </Card>
            <Card className="bg-white/10 backdrop-blur-md border-white/20 p-8">
              <h3 className="text-2xl font-semibold text-white mb-4">⚡ Real-Time Everything</h3>
              <p className="text-white/80">
                From song requests to analytics, everything happens in real-time. No delays, 
                no lag – just instant connection between you and your audience.
              </p>
            </Card>
            <Card className="bg-white/10 backdrop-blur-md border-white/20 p-8">
              <h3 className="text-2xl font-semibold text-white mb-4">🔒 Privacy & Security</h3>
              <p className="text-white/80">
                Your data and your audience's data are protected with enterprise-grade security. 
                We believe privacy is a fundamental right, not a luxury.
              </p>
            </Card>
            <Card className="bg-white/10 backdrop-blur-md border-white/20 p-8">
              <h3 className="text-2xl font-semibold text-white mb-4">🌱 Constantly Evolving</h3>
              <p className="text-white/80">
                We ship new features every week based on feedback from our DJ community. 
                Your input directly shapes the future of the platform.
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-purple-600 to-pink-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-4">
            Ready to Join the Revolution?
          </h2>
          <p className="text-xl text-white/90 mb-8">
            Become part of the PlayBeg community and transform how you connect with your audience.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/signin">
              <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold">
                Start Your Free Trial
              </Button>
            </Link>
            <Link href="/contact">
              <Button size="lg" variant="outline" className="text-white border-white hover:bg-white/10 px-8 py-4 text-lg">
                Get in Touch
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black/40 backdrop-blur-md border-t border-white/10 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="text-2xl font-bold text-white mb-4">🎵 PlayBeg</div>
              <p className="text-white/60">
                The ultimate platform for interactive DJ experiences and audience engagement.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Product</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/about" className="hover:text-white transition-colors">About</Link></li>
                <li><Link href="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
                <li><Link href="/help" className="hover:text-white transition-colors">Help Center</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Resources</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/blog" className="hover:text-white transition-colors">Blog</Link></li>
                <li><Link href="/help" className="hover:text-white transition-colors">FAQ</Link></li>
                <li><Link href="/contact" className="hover:text-white transition-colors">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Legal</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/terms" className="hover:text-white transition-colors">Terms of Service</Link></li>
                <li><Link href="/privacy" className="hover:text-white transition-colors">Privacy Policy</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-white/10 mt-8 pt-8 text-center text-white/60">
            <p>&copy; 2025 PlayBeg. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
