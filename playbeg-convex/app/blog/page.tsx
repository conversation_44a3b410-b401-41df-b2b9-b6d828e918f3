"use client";

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { Button } from '../../components/ui/button';
import { Card } from '../../components/ui/card';

export default function BlogPage() {
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'All Posts', count: 12 },
    { id: 'tips', name: 'DJ Tips', count: 5 },
    { id: 'technology', name: 'Technology', count: 3 },
    { id: 'events', name: 'Events', count: 2 },
    { id: 'updates', name: 'Platform Updates', count: 2 }
  ];

  const blogPosts = [
    {
      id: 1,
      title: '10 Essential Tips for Reading Your Audience',
      excerpt: 'Learn how to gauge crowd energy and adjust your set in real-time for maximum engagement.',
      author: '<PERSON>',
      date: '2025-01-15',
      category: 'tips',
      readTime: '5 min read',
      image: '🎧',
      featured: true
    },
    {
      id: 2,
      title: 'The Future of DJ Technology: Real-Time Audience Interaction',
      excerpt: 'Exploring how platforms like PlayBeg are revolutionizing the relationship between DJs and their audiences.',
      author: '<PERSON>',
      date: '2025-01-12',
      category: 'technology',
      readTime: '8 min read',
      image: '🚀',
      featured: true
    },
    {
      id: 3,
      title: 'How to Handle Difficult Song Requests Like a Pro',
      excerpt: 'Strategies for managing inappropriate or impossible requests while keeping your audience happy.',
      author: 'Sam Chen',
      date: '2025-01-10',
      category: 'tips',
      readTime: '6 min read',
      image: '🎵',
      featured: false
    },
    {
      id: 4,
      title: 'PlayBeg 2.0: Advanced Session Management Features',
      excerpt: 'Introducing session templates, scheduling, and collaboration tools for professional DJs.',
      author: 'PlayBeg Team',
      date: '2025-01-08',
      category: 'updates',
      readTime: '4 min read',
      image: '📊',
      featured: false
    },
    {
      id: 5,
      title: 'Wedding DJ Success: Creating Memorable Moments',
      excerpt: 'Tips and strategies for wedding DJs to create unforgettable experiences for couples and guests.',
      author: 'Sarah Johnson',
      date: '2025-01-05',
      category: 'events',
      readTime: '7 min read',
      image: '💒',
      featured: false
    },
    {
      id: 6,
      title: 'Building Your DJ Brand in the Digital Age',
      excerpt: 'How to leverage social media and technology platforms to grow your DJ business.',
      author: 'Mike Rodriguez',
      date: '2025-01-03',
      category: 'tips',
      readTime: '9 min read',
      image: '📱',
      featured: false
    },
    {
      id: 7,
      title: 'The Science Behind Crowd Psychology and Music',
      excerpt: 'Understanding how different genres and tempos affect crowd energy and engagement.',
      author: 'Dr. Emma Wilson',
      date: '2024-12-28',
      category: 'technology',
      readTime: '12 min read',
      image: '🧠',
      featured: false
    },
    {
      id: 8,
      title: 'Corporate Event DJing: A Complete Guide',
      excerpt: 'Everything you need to know about DJing corporate events, from music selection to audience management.',
      author: 'Alex Rivera',
      date: '2024-12-25',
      category: 'events',
      readTime: '10 min read',
      image: '🏢',
      featured: false
    }
  ];

  const filteredPosts = selectedCategory === 'all' 
    ? blogPosts 
    : blogPosts.filter(post => post.category === selectedCategory);

  const featuredPosts = blogPosts.filter(post => post.featured);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Navigation */}
      <nav className="bg-black/20 backdrop-blur-md border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-white">🎵 PlayBeg</Link>
            </div>
            <div className="hidden md:flex space-x-8">
              <Link href="/about" className="text-white/80 hover:text-white transition-colors">About</Link>
              <Link href="/pricing" className="text-white/80 hover:text-white transition-colors">Pricing</Link>
              <Link href="/blog" className="text-white font-medium">Blog</Link>
              <Link href="/help" className="text-white/80 hover:text-white transition-colors">Help</Link>
              <Link href="/contact" className="text-white/80 hover:text-white transition-colors">Contact</Link>
            </div>
            <div className="flex space-x-4">
              <Link href="/signin">
                <Button variant="outline" className="text-white border-white/30 hover:bg-white/10">
                  Sign In
                </Button>
              </Link>
              <Link href="/signin">
                <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            PlayBeg
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              {" "}Blog
            </span>
          </h1>
          <p className="text-xl text-white/80 mb-8 max-w-3xl mx-auto">
            Insights, tips, and stories from the world of DJing and music technology. 
            Stay updated with the latest trends and best practices.
          </p>
        </div>
      </section>

      {/* Featured Posts */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Featured Articles</h2>
            <p className="text-xl text-white/80">
              Our most popular and impactful content
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {featuredPosts.map((post) => (
              <Card key={post.id} className="bg-white/10 backdrop-blur-md border-white/20 overflow-hidden hover:bg-white/15 transition-colors">
                <div className="p-8">
                  <div className="text-6xl mb-4 text-center">{post.image}</div>
                  <div className="flex items-center justify-between mb-4">
                    <span className="bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                      {categories.find(cat => cat.id === post.category)?.name}
                    </span>
                    <span className="text-white/60 text-sm">{post.readTime}</span>
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-3">{post.title}</h3>
                  <p className="text-white/80 mb-4">{post.excerpt}</p>
                  <div className="flex items-center justify-between">
                    <div className="text-white/60 text-sm">
                      By {post.author} • {new Date(post.date).toLocaleDateString()}
                    </div>
                    <Button variant="outline" className="text-white border-white/30 hover:bg-white/10">
                      Read More
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Categories and Posts */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col lg:flex-row gap-12">
            {/* Sidebar */}
            <div className="lg:w-1/4">
              <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6 sticky top-8">
                <h3 className="text-xl font-semibold text-white mb-4">Categories</h3>
                <div className="space-y-2">
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                        selectedCategory === category.id
                          ? 'bg-purple-600 text-white'
                          : 'text-white/80 hover:bg-white/10'
                      }`}
                    >
                      <div className="flex justify-between items-center">
                        <span>{category.name}</span>
                        <span className="text-sm opacity-60">({category.count})</span>
                      </div>
                    </button>
                  ))}
                </div>
                
                <div className="mt-8">
                  <h3 className="text-xl font-semibold text-white mb-4">Newsletter</h3>
                  <p className="text-white/80 text-sm mb-4">
                    Get the latest DJ tips and PlayBeg updates delivered to your inbox.
                  </p>
                  <div className="space-y-3">
                    <input
                      type="email"
                      placeholder="Your email"
                      className="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-400"
                    />
                    <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white">
                      Subscribe
                    </Button>
                  </div>
                </div>
              </Card>
            </div>

            {/* Main Content */}
            <div className="lg:w-3/4">
              <div className="mb-8">
                <h2 className="text-3xl font-bold text-white mb-4">
                  {selectedCategory === 'all' ? 'All Posts' : categories.find(cat => cat.id === selectedCategory)?.name}
                </h2>
                <p className="text-white/80">
                  {filteredPosts.length} article{filteredPosts.length !== 1 ? 's' : ''} found
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {filteredPosts.map((post) => (
                  <Card key={post.id} className="bg-white/10 backdrop-blur-md border-white/20 overflow-hidden hover:bg-white/15 transition-colors">
                    <div className="p-6">
                      <div className="text-4xl mb-4 text-center">{post.image}</div>
                      <div className="flex items-center justify-between mb-3">
                        <span className="bg-purple-600/80 text-white px-2 py-1 rounded text-xs font-medium">
                          {categories.find(cat => cat.id === post.category)?.name}
                        </span>
                        <span className="text-white/60 text-xs">{post.readTime}</span>
                      </div>
                      <h3 className="text-lg font-bold text-white mb-2 line-clamp-2">{post.title}</h3>
                      <p className="text-white/80 text-sm mb-4 line-clamp-3">{post.excerpt}</p>
                      <div className="flex items-center justify-between">
                        <div className="text-white/60 text-xs">
                          {post.author} • {new Date(post.date).toLocaleDateString()}
                        </div>
                        <Button size="sm" variant="outline" className="text-white border-white/30 hover:bg-white/10">
                          Read
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
              
              {/* Pagination */}
              <div className="mt-12 flex justify-center">
                <div className="flex space-x-2">
                  <Button variant="outline" className="text-white border-white/30 hover:bg-white/10">
                    Previous
                  </Button>
                  <Button className="bg-purple-600 text-white">1</Button>
                  <Button variant="outline" className="text-white border-white/30 hover:bg-white/10">
                    2
                  </Button>
                  <Button variant="outline" className="text-white border-white/30 hover:bg-white/10">
                    3
                  </Button>
                  <Button variant="outline" className="text-white border-white/30 hover:bg-white/10">
                    Next
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-purple-600 to-pink-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-4">
            Ready to Put These Tips into Practice?
          </h2>
          <p className="text-xl text-white/90 mb-8">
            Join PlayBeg and start creating amazing interactive DJ experiences today.
          </p>
          <Link href="/signin">
            <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold">
              Start Your Free Trial
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black/40 backdrop-blur-md border-t border-white/10 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="text-2xl font-bold text-white mb-4">🎵 PlayBeg</div>
              <p className="text-white/60">
                The ultimate platform for interactive DJ experiences and audience engagement.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Product</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/about" className="hover:text-white transition-colors">About</Link></li>
                <li><Link href="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
                <li><Link href="/help" className="hover:text-white transition-colors">Help Center</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Resources</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/blog" className="hover:text-white transition-colors">Blog</Link></li>
                <li><Link href="/help" className="hover:text-white transition-colors">FAQ</Link></li>
                <li><Link href="/contact" className="hover:text-white transition-colors">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Legal</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/terms" className="hover:text-white transition-colors">Terms of Service</Link></li>
                <li><Link href="/privacy" className="hover:text-white transition-colors">Privacy Policy</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-white/10 mt-8 pt-8 text-center text-white/60">
            <p>&copy; 2025 PlayBeg. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
