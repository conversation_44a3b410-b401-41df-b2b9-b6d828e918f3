"use client";

import { Preloaded, useMutation, usePreloadedQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

export default function Home({
  preloaded,
}: {
  preloaded: Preloaded<typeof api.djProfiles.listDjProfiles>;
}) {
  const data = usePreloadedQuery(preloaded);
  const createDjProfile = useMutation(api.djProfiles.createDjProfile);
  return (
    <>
      <div className="flex flex-col gap-4 bg-slate-200 dark:bg-slate-800 p-4 rounded-md">
        <h2 className="text-xl font-bold">DJ Profiles Data</h2>
        <div>
          <p>Total profiles: {data?.length || 0}</p>
          {data?.map((profile) => (
            <div key={profile.id} className="p-2 border rounded mb-2">
              <strong>{profile.displayName}</strong>
              <span className={`ml-2 px-2 py-1 rounded text-xs ${
                profile.completedOnboarding ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
              }`}>
                {profile.completedOnboarding ? 'Onboarded' : 'Pending'}
              </span>
            </div>
          ))}
        </div>
      </div>
      <button
        className="bg-foreground text-background px-4 py-2 rounded-md mx-auto"
        onClick={() => {
          void createDjProfile({
            displayName: `Test DJ ${Math.floor(Math.random() * 1000)}`
          });
        }}
      >
        Create Test DJ Profile
      </button>
    </>
  );
}
