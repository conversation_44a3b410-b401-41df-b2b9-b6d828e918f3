"use client";

import React from 'react';
import <PERSON> from 'next/link';
import { useConvexAuth } from "convex/react";
import { Button } from '../components/ui/button';
import { Card } from '../components/ui/card';

export default function HomePage() {
  const { isAuthenticated, isLoading } = useConvexAuth();

  const features = [
    {
      icon: '🎵',
      title: 'Real-Time Song Requests',
      description: 'Audience members can request songs instantly through our intuitive interface, creating an interactive experience.'
    },
    {
      icon: '🎛️',
      title: 'Advanced DJ Controls',
      description: 'Professional-grade session management with templates, scheduling, and collaboration tools.'
    },
    {
      icon: '📊',
      title: 'Live Analytics',
      description: 'Track audience engagement, popular songs, and session performance in real-time.'
    },
    {
      icon: '👥',
      title: 'Multi-DJ Collaboration',
      description: 'Work with other DJs seamlessly with role-based permissions and shared session control.'
    },
    {
      icon: '📅',
      title: 'Session Scheduling',
      description: 'Plan and automate your events with advanced scheduling and reminder systems.'
    },
    {
      icon: '🎨',
      title: 'Custom Branding',
      description: 'Personalize your sessions with custom colors, logos, and messages for events.'
    }
  ];

  const testimonials = [
    {
      name: '<PERSON>',
      role: 'Wedding DJ',
      content: 'PlayBeg transformed how I interact with wedding guests. The real-time requests keep everyone engaged!',
      avatar: '👰'
    },
    {
      name: '<PERSON> <PERSON>',
      role: 'Club DJ',
      content: 'The analytics help me understand what my audience loves. My sets have never been better.',
      avatar: '🎧'
    },
    {
      name: 'Emma Chen',
      role: 'Event Coordinator',
      content: 'Managing multiple DJs for corporate events is so much easier with PlayBeg\'s collaboration features.',
      avatar: '🎪'
    }
  ];

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-white text-lg">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Navigation */}
      <nav className="bg-black/20 backdrop-blur-md border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <div className="text-2xl font-bold text-white">🎵 PlayBeg</div>
            </div>
            <div className="hidden md:flex space-x-8">
              <Link href="/about" className="text-white/80 hover:text-white transition-colors">About</Link>
              <Link href="/pricing" className="text-white/80 hover:text-white transition-colors">Pricing</Link>
              <Link href="/blog" className="text-white/80 hover:text-white transition-colors">Blog</Link>
              <Link href="/help" className="text-white/80 hover:text-white transition-colors">Help</Link>
              <Link href="/contact" className="text-white/80 hover:text-white transition-colors">Contact</Link>
            </div>
            <div className="flex space-x-4">
              {isAuthenticated ? (
                <Link href="/dashboard">
                  <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                    Go to Dashboard
                  </Button>
                </Link>
              ) : (
                <>
                  <Link href="/signin">
                    <Button variant="outline" className="text-white border-white/30 hover:bg-white/10">
                      Sign In
                    </Button>
                  </Link>
                  <Link href="/signin">
                    <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                      Get Started
                    </Button>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
            The Future of
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              {" "}DJ Interaction
            </span>
          </h1>
          <p className="text-xl md:text-2xl text-white/80 mb-8 max-w-3xl mx-auto">
            Connect with your audience like never before. Real-time song requests,
            advanced analytics, and professional DJ tools all in one platform.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {isAuthenticated ? (
              <Link href="/dashboard">
                <Button size="lg" className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-4 text-lg">
                  Go to Dashboard
                </Button>
              </Link>
            ) : (
              <Link href="/signin">
                <Button size="lg" className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-4 text-lg">
                  Start Free Trial
                </Button>
              </Link>
            )}
            <Link href="/about">
              <Button size="lg" variant="outline" className="text-white border-white/30 hover:bg-white/10 px-8 py-4 text-lg">
                Learn More
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              Everything You Need to Elevate Your DJ Experience
            </h2>
            <p className="text-xl text-white/80 max-w-2xl mx-auto">
              From intimate gatherings to massive events, PlayBeg provides the tools
              to create unforgettable musical experiences.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="bg-white/10 backdrop-blur-md border-white/20 p-6 hover:bg-white/15 transition-colors">
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-white mb-2">{feature.title}</h3>
                <p className="text-white/80">{feature.description}</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              Loved by DJs Worldwide
            </h2>
            <p className="text-xl text-white/80">
              See what professional DJs are saying about PlayBeg
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="bg-white/10 backdrop-blur-md border-white/20 p-6">
                <div className="flex items-center mb-4">
                  <div className="text-3xl mr-3">{testimonial.avatar}</div>
                  <div>
                    <div className="font-semibold text-white">{testimonial.name}</div>
                    <div className="text-white/60">{testimonial.role}</div>
                  </div>
                </div>
                <p className="text-white/80 italic">"{testimonial.content}"</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-purple-600 to-pink-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-4">
            Ready to Transform Your DJ Experience?
          </h2>
          <p className="text-xl text-white/90 mb-8">
            Join thousands of DJs who are already creating amazing experiences with PlayBeg.
          </p>
          {isAuthenticated ? (
            <Link href="/dashboard">
              <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold">
                Go to Your Dashboard
              </Button>
            </Link>
          ) : (
            <Link href="/signin">
              <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold">
                Start Your Free Trial Today
              </Button>
            </Link>
          )}
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black/40 backdrop-blur-md border-t border-white/10 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="text-2xl font-bold text-white mb-4">🎵 PlayBeg</div>
              <p className="text-white/60">
                The ultimate platform for interactive DJ experiences and audience engagement.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Product</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/about" className="hover:text-white transition-colors">About</Link></li>
                <li><Link href="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
                <li><Link href="/help" className="hover:text-white transition-colors">Help Center</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Resources</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/blog" className="hover:text-white transition-colors">Blog</Link></li>
                <li><Link href="/help" className="hover:text-white transition-colors">FAQ</Link></li>
                <li><Link href="/contact" className="hover:text-white transition-colors">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-white mb-4">Legal</h3>
              <ul className="space-y-2 text-white/60">
                <li><Link href="/terms" className="hover:text-white transition-colors">Terms of Service</Link></li>
                <li><Link href="/privacy" className="hover:text-white transition-colors">Privacy Policy</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-white/10 mt-8 pt-8 text-center text-white/60">
            <p>&copy; 2025 PlayBeg. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}


