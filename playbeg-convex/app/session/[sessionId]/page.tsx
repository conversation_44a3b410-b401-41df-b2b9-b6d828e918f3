"use client";

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";
import { Button } from '../../../components/ui/button';
import { Card } from '../../../components/ui/card';
import { Input } from '../../../components/ui/input';
import { Textarea } from '../../../components/ui/textarea';
import { QRCodeSVG } from 'qrcode.react';

interface SongRequestForm {
  songTitle: string;
  artistName: string;
  requesterName: string;
  message?: string;
}

export default function PublicSessionPage() {
  const params = useParams();
  const sessionId = params.sessionId as Id<"sessions">;
  
  const [formData, setFormData] = useState<SongRequestForm>({
    songTitle: '',
    artistName: '',
    requesterName: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);
  const [showQR, setShowQR] = useState(false);

  // Queries
  const session = useQuery(api.sessions.getPublicSession, { sessionId });
  const requests = useQuery(api.songRequests.getSessionRequests, { 
    sessionId, 
    status: "approved",
    limit: 10 
  });

  // Mutations
  const createRequest = useMutation(api.songRequests.createSongRequest);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!session || !session.acceptRequests) return;

    setIsSubmitting(true);
    setSubmitMessage(null);

    try {
      const result = await createRequest({
        sessionId,
        songTitle: formData.songTitle.trim(),
        artistName: formData.artistName.trim(),
        requesterName: formData.requesterName.trim(),
      });

      setSubmitMessage({
        type: 'success',
        text: result.message || 'Request submitted successfully!'
      });

      // Clear form
      setFormData({
        songTitle: '',
        artistName: '',
        requesterName: '',
        message: ''
      });

    } catch (error: any) {
      setSubmitMessage({
        type: 'error',
        text: error.message || 'Failed to submit request'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof SongRequestForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Loading state
  if (session === undefined) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-white text-lg">Loading session...</div>
      </div>
    );
  }

  // Session not found or inactive
  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
        <Card className="bg-white/10 backdrop-blur-md border-white/20 p-8 text-center max-w-md">
          <div className="text-6xl mb-4">🎵</div>
          <h1 className="text-2xl font-bold text-white mb-4">Session Not Found</h1>
          <p className="text-white/80 mb-6">
            This session is either inactive or doesn't exist. Please check the link and try again.
          </p>
          <Button 
            onClick={() => window.location.href = '/'}
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            Go to Home
          </Button>
        </Card>
      </div>
    );
  }

  const currentUrl = typeof window !== 'undefined' ? window.location.href : '';

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Header */}
      <header className="bg-black/20 backdrop-blur-md border-b border-white/10 p-4">
        <div className="max-w-4xl mx-auto flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-white">{session.name}</h1>
            {session.weddingModeEnabled && session.weddingCoupleNames && (
              <p className="text-white/80">
                {session.weddingCoupleNames.join(' & ')}'s Wedding
              </p>
            )}
          </div>
          <Button
            onClick={() => setShowQR(!showQR)}
            variant="outline"
            className="text-white border-white/30 hover:bg-white/10"
          >
            {showQR ? 'Hide QR' : 'Share QR'}
          </Button>
        </div>
      </header>

      <div className="max-w-4xl mx-auto p-4 space-y-8">
        {/* QR Code Modal */}
        {showQR && (
          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6 text-center">
            <h3 className="text-xl font-semibold text-white mb-4">Share This Session</h3>
            <div className="bg-white p-4 rounded-lg inline-block mb-4">
              <QRCodeSVG value={currentUrl} size={200} />
            </div>
            <p className="text-white/80 text-sm">
              Scan this QR code to join the session
            </p>
          </Card>
        )}

        {/* Wedding Mode Header */}
        {session.weddingModeEnabled && (
          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6 text-center">
            <div className="text-4xl mb-4">💒</div>
            <h2 className="text-2xl font-bold text-white mb-2">
              {session.weddingCoupleNames?.join(' & ')}'s Wedding
            </h2>
            {session.weddingDate && (
              <p className="text-white/80 mb-2">{session.weddingDate}</p>
            )}
            {session.weddingHashtag && (
              <p className="text-purple-300">#{session.weddingHashtag}</p>
            )}
            {session.weddingCustomMessage && (
              <p className="text-white/90 mt-4 italic">"{session.weddingCustomMessage}"</p>
            )}
          </Card>
        )}

        {/* Sponsor Header */}
        {session.sponsorHeader && (
          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-4 text-center">
            <p className="text-white/90">{session.sponsorHeader}</p>
            {session.sponsorMessage && (
              <p className="text-white/70 text-sm mt-2">{session.sponsorMessage}</p>
            )}
          </Card>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Song Request Form */}
          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
              <span className="mr-3">🎵</span>
              Request a Song
            </h2>

            {!session.acceptRequests ? (
              <div className="text-center py-8">
                <div className="text-4xl mb-4">⏸️</div>
                <p className="text-white/80">
                  This session is not currently accepting requests.
                </p>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Song Title *
                  </label>
                  <Input
                    type="text"
                    value={formData.songTitle}
                    onChange={(e) => handleInputChange('songTitle', e.target.value)}
                    placeholder="Enter song title"
                    className="w-full bg-white/20 border-white/30 text-white placeholder-white/60"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Artist Name *
                  </label>
                  <Input
                    type="text"
                    value={formData.artistName}
                    onChange={(e) => handleInputChange('artistName', e.target.value)}
                    placeholder="Enter artist name"
                    className="w-full bg-white/20 border-white/30 text-white placeholder-white/60"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Your Name *
                  </label>
                  <Input
                    type="text"
                    value={formData.requesterName}
                    onChange={(e) => handleInputChange('requesterName', e.target.value)}
                    placeholder="Enter your name"
                    className="w-full bg-white/20 border-white/30 text-white placeholder-white/60"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Message (Optional)
                  </label>
                  <Textarea
                    value={formData.message}
                    onChange={(e) => handleInputChange('message', e.target.value)}
                    placeholder="Add a message with your request..."
                    className="w-full bg-white/20 border-white/30 text-white placeholder-white/60"
                    rows={3}
                  />
                </div>

                {submitMessage && (
                  <div className={`p-3 rounded-lg ${
                    submitMessage.type === 'success' 
                      ? 'bg-green-600/20 border border-green-500/30 text-green-200'
                      : 'bg-red-600/20 border border-red-500/30 text-red-200'
                  }`}>
                    {submitMessage.text}
                  </div>
                )}

                <Button
                  type="submit"
                  disabled={isSubmitting || !formData.songTitle.trim() || !formData.artistName.trim() || !formData.requesterName.trim()}
                  className="w-full bg-purple-600 hover:bg-purple-700 text-white py-3"
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Request'}
                </Button>
              </form>
            )}
          </Card>

          {/* Recent Approved Requests */}
          <Card className="bg-white/10 backdrop-blur-md border-white/20 p-6">
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
              <span className="mr-3">✅</span>
              Recent Requests
            </h2>

            {requests && requests.length > 0 ? (
              <div className="space-y-3">
                {requests.map((request) => (
                  <div key={request._id} className="bg-white/10 rounded-lg p-4">
                    <div className="font-semibold text-white">{request.songTitle}</div>
                    <div className="text-white/80 text-sm">by {request.artistName}</div>
                    <div className="text-white/60 text-xs mt-1">
                      Requested by {request.requesterName}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="text-4xl mb-4">🎶</div>
                <p className="text-white/80">
                  No approved requests yet. Be the first to request a song!
                </p>
              </div>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
}
