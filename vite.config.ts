import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import commonjs from '@rollup/plugin-commonjs';
import path from "path";
import removeConsole from "vite-plugin-remove-console";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "127.0.0.1", // Localhost only - not accessible from network
    port: 8080,
    strictPort: true, // Fail if port is already in use
  },
  plugins: [
    // first transform CommonJS modules
    commonjs(),
    // then handle React/JSX
    react(),
    // Remove console.log, console.info and console.warn in production
    mode === 'production' &&
      removeConsole({
        includes: ['log', 'info', 'warn'],
      }),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  // Pre-bundle these so Rollup sees ESM versions
  optimizeDeps: {
    include: ['react-markdown', 'react-helmet'],
    exclude: ['fs', 'path', 'os', 'crypto', 'stream', 'util', 'url', 'http', 'https']
  },
  // Force them into the SSR/build bundle
  ssr: {
    noExternal: ['react-markdown', 'react-helmet']
  },
  build: {
    // Ensure Rollup's CJS plugin pulls in these modules
    commonjsOptions: {
      include: [
        /node_modules\/react-markdown/,
        /node_modules\/remark-.*$/,
        /node_modules\/react-helmet/
      ]
    },
    // Handle Node.js modules for browser compatibility
    rollupOptions: {
      external: ['react-helmet'],
      output: {
        globals: {
          'react-helmet': 'ReactHelmet'
        }
      }
    }
  },
  // Define Node.js polyfills for browser compatibility
  define: {
    global: 'globalThis',
  }
}));
